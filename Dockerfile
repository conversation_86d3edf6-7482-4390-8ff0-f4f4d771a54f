FROM harbor.teamway.com/common/java:8

MAINTAINER gaven

# 统一容器与服务器时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

#复制target/docker-spring-boot.jar到容器app目录下
COPY ./target/gather-1.0.0.jar app/gather-1.0.0.jar
EXPOSE 8080

ENTRYPOINT ["java","-jar","app/gather-1.0.0.jar","--spring.config.location=/app/"]
# docker build -t docker-spring-boot/latest .
