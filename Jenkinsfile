pipeline {
    agent { label getLabelForBranch(env.BRANCH_NAME) }
    environment {
        // JDK version to use, 8 or 21
        JDKVERSION = '21'
        // CD Build WebHookToken
        CDTOKEN = 'haimen-smart-infrastructure'
        // Other variables are automatically populated
        PROJECTNAME = 'NONE'
        VERSION = 'NONE'
        TAG = 'NONE'
        BRANCHNAME = "${env.BRANCH_NAME}"
        IMAGENAME = 'NONE'
        COMMITMSG = 'NONE'
        COMMITER = 'NONE'
        COMMITHASH = 'NONE'
        COMMITEMAIL = 'NONE'
    }
    parameters { 
        string(name: 'groupName', defaultValue: 'haimen-smart-infrastructure', description: 'Docker repository project name, recommended to match the actual project name')
        string(name: 'port', defaultValue: '8086', description: 'Container mapping port')
        booleanParam(name: 'MULTI_PLATFORM',defaultValue: false,description: '是否启用多平台构建,开发及测试分支不建议开启')
    }

    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        stage('Get Info') {
            steps {
                script {
                    def commit_info = sh(script: "git log -1 --pretty=format:'%H%n%an%n%ae%n%s'", returnStdout: true).trim()
                    def (commit_hash, commit_author, commit_author_email, commit_subject) = commit_info.split('\n')
                    echo "Commit Hash: ${commit_hash}"
                    echo "Commit Author: ${commit_author}"
                    echo "Commit Author Email: ${commit_author_email}"
                    echo "Commit Subject: ${commit_subject}"
                    COMMITMSG = commit_subject
                    COMMITER = commit_author
                    COMMITEMAIL = commit_author_email
                    COMMITHASH = commit_hash
                    PROJECTNAME = sh(script: "yq e '.service-name' gather/src/main/resources/bootstrap.yml", returnStdout: true).trim()
                    VERSION = sh(script: "yq e '.service-version' gather/src/main/resources/bootstrap.yml", returnStdout: true).trim()
                    echo "Current Branch: ${BRANCHNAME}"

                }
            }
        }
        stage('Build Images') {
            steps {
                script {
                    def CONFIGURE_TYPE="dev"
                    if (BRANCHNAME == "release") {
                        CONFIGURE_TYPE="test"
                    } else if (BRANCHNAME == "master" || BRANCHNAME.startsWith("tag")) {
                        CONFIGURE_TYPE="prod"
                    }
                    def entrypointContent = """#!/bin/bash
set -e
if [ ! -d "/conf" ]; then
    mkdir /conf
fi
if [ "\$(ls -A /conf | wc -l)" -eq 0 ]; then
    cp /tmp/*.yml /conf/
fi
sed -i 's/profiles-active: .*/profiles-active: ${CONFIGURE_TYPE}/' /conf/bootstrap.yml
ulimit -c unlimited
ldconfig
exec "\$@"
""".trim()

                    writeFile file: 'entrypoint.sh', text: entrypointContent
                    def dockerfileContent = """
FROM harbor.teamway.com/compilers/openjdk:${JDKVERSION} AS build
WORKDIR /app
COPY . /app
RUN mvn -v
RUN java -version
RUN mvn clean -q
RUN mvn install -q -Dmaven.test.skip=true

FROM harbor.teamway.com/compilers/openjdk:${JDKVERSION}-jre-slim

RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo Asia/Shanghai > /etc/timezone
WORKDIR /app

EXPOSE ${params.port}
COPY --from=build /app/gather/target/${PROJECTNAME}-${VERSION}.jar /app/
COPY gather/src/main/resources/*.yml /tmp/
COPY entrypoint.sh /
RUN cp /etc/apt/sources.list /etc/apt/sources.list.bak && \
    echo "deb http://mirrors.aliyun.com/debian bullseye main contrib non-free non-free-firmware" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian-security bullseye-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian bullseye-updates main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
    apt update && \
    apt install -y fonts-dejavu fontconfig && \
    apt clean && \
    rm -rf /var/lib/apt/lists/*
RUN chmod +x /entrypoint.sh
LABEL version="${VERSION}"
ENTRYPOINT ["/entrypoint.sh"]
CMD [ "java","-jar","-Xms512m","-Xmx2g","${PROJECTNAME}-${VERSION}.jar","--spring.config.location=/conf/"]
""".trim()
                    writeFile file: 'Dockerfile', text: dockerfileContent
                    if (BRANCHNAME.startsWith("tag")) {
                        IMAGENAME = "harbor.teamway.com/${params.groupName}/${PROJECTNAME}:${BRANCHNAME}"
                        TAG = "${BRANCHNAME}"
                    } else {
                        suffix = getVersionSuffix(env.BRANCH_NAME)
                        IMAGENAME = "harbor.teamway.com/${params.groupName}/${PROJECTNAME}:${VERSION}-${suffix}"
                        TAG = "${VERSION}-${suffix}"
                    }
                    if (params.MULTI_PLATFORM) {
                        sh """
                            docker buildx build \
                            --push \
                            --platform linux/amd64,linux/arm64 \
                            --tag ${IMAGENAME.toLowerCase()} \
                            .
                        """
                    } else {
                            sh """
                            docker buildx build \
                            --push \
                            --tag ${IMAGENAME.toLowerCase()} \
                            .
                        """
                    }

                    echo "[INFO] --------------Image ${IMAGENAME} build and push successfully--------------"
                }
            }
        }
        stage('Deploy') {
            when {
                expression {
                    return BRANCHNAME != 'master' && !BRANCHNAME.startsWith("tag")
                }
            }
            steps {
                script {
                    def payload = """{
                        "branch": "${BRANCHNAME}",
                        "image": "${IMAGENAME.toLowerCase()}",
                        "version": "${VERSION}",
                        "tag": "${TAG}",
                        "author": "${COMMITER}",
                        "authoremail": "${COMMITEMAIL}",
                        "projectname": "${PROJECTNAME}"
                    }""".trim()
                    def jobUrl = 'http://**********:8080/generic-webhook-trigger/invoke'
                    sh "curl -X POST -H 'Content-Type: application/json' --data '${payload}' '${jobUrl}?token=${CDTOKEN}'"
                }
            }
        }
    }
    post {
        always {
            script {
                def emailBody = '''
                    <head>
                        <meta charset="UTF-8">
                        <title>${JOB_NAME}-Build #${BUILD_NUMBER} Log</title>
                    </head>
                    <body leftmargin="8" marginwidth="0" topmargin="8" marginheight="4" offset="0">
                        <table width="95%" cellpadding="0" cellspacing="0"  style="font-size: 11pt; font-family: Tahoma, Arial, Helvetica, sans-serif">
                            <tr>
                                 This email is automatically generated by the system, no need to reply!<br/>
                                Dear colleagues, here is the build information for project ${PROJECT_NAME}</br>
                                <td><font color="#CC0000">Build Result - ${BUILD_STATUS}</font></td>
                            </tr>
                            <tr>
                                <td><br />
                                <b><font color="#0B610B">Build Information</font></b>
                                <hr size="2" width="100%" align="center" /></td>
                            </tr>
                            <tr>
                                <td>
                                    <ul>
                                        <li>Project Name: ${PROJECT_NAME}</li>
                                        <li>Build Number: ${BUILD_NUMBER}</li>
                                        <li>Trigger Cause: ${CAUSE}</li>'''+
                                        """
                                        <li>Commit Author: ${COMMITER} </li>
                                        <li>Commit Message: ${COMMITMSG}</li>
                                        <li>Commit Hash: ${COMMITHASH} </li>
                                        <li>Image Url: ${IMAGENAME.toLowerCase()}
                                        """ + '''
                                        <li>Build Status:  ${BUILD_STATUS}</li>
                                        <li>Build Log: <a href="${BUILD_URL}console">${BUILD_URL}console</a></li>
                                        <li>Build URL:  <a href="${BUILD_URL}">${BUILD_URL}</a></li>
                                        <li>Workspace Directory: <a href="${PROJECT_URL}ws">${PROJECT_URL}ws</a></li>
                                        <li>Project URL: <a href="${PROJECT_URL}">${PROJECT_URL}</a></li>
                                    </ul>
                                </td>
                            </tr>
                        </table>
                    </body>
                    '''
                emailext mimeType: 'text/html',
                    body: emailBody,
                        subject: '[Build Notification] ${PROJECT_NAME} - Build # ${BUILD_NUMBER} - ${BUILD_STATUS}!',
                        to: "${COMMITEMAIL}",
                        from: '"Jenkins Admin" <<EMAIL>>'
                deleteDir()
            }
        }
    }
}
    

def getLabelForBranch(branchName) {
    if (branchName.startsWith("tag")) {

        return 'any'
    }else{
        def branchLabelMap = [
            'release' : 'any',
            'master' :'any',
        ]
    return branchLabelMap.get(branchName, 'any')
    }
}

def getConfigureType(branchName) {
    if (branchName == 'release') {
        return 'test'
    } else if (branchName == 'master' || branchName.startsWith('tag')) {
        return 'prod'
    } else {
        return 'dev'
    }
}

def getVersionSuffix(branchName) {
    if (branchName == 'release') {
        return 'release'
    } else if (branchName == 'master' || branchName.startsWith('tag')) {
        return 'production'
    } else {
        return 'snapshot'
    }
}
