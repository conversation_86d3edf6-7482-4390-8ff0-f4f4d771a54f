<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.teamway</groupId>
        <artifactId>businessServer-hm</artifactId>
        <version>2.0.1.RELEASE</version>
    </parent>

    <packaging>jar</packaging>
    <artifactId>base-service-hm</artifactId>
    <description>基础业务模块</description>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jwt.version>0.9.1</jwt.version>
        <jaxb-api.version>2.3.1</jaxb-api.version>
        <onvif.version>2.6.3-SNAPSHOT</onvif.version>
        <servlet.version>4.0.1</servlet.version>
        <soap-api.version>1.4.0</soap-api.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-el</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>${freemarker.version}</version>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>common-hm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>exception</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shenzhen.teamway</groupId>
            <artifactId>onvif-common</artifactId>
            <version>${onvif.version}</version>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>algorithm-config-service</artifactId>
            <version>${algorithm-config-service.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shenzhen.teamway</groupId>
                    <artifactId>onvif-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>general-thermal-spring-boot-starter</artifactId>
            <version>${general-thermal.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shenzhen.teamway</groupId>
                    <artifactId>onvif-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>${jwt.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>${jaxb-api.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>${servlet.version}</version>
        </dependency>
        <!-- onvif组件使用       -->
        <dependency>
            <groupId>javax.xml.soap</groupId>
            <artifactId>javax.xml.soap-api</artifactId>
            <version>${soap-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>common-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>

</project>