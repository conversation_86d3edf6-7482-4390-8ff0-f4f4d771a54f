package com.teamway.base.controller;


import com.teamway.base.dto.config.AlarmConditionQueryDto;
import com.teamway.base.dto.config.AlarmConfigQueryDto;
import com.teamway.base.dto.voice.AlarmKeepDto;
import com.teamway.base.entity.AlarmConfig;
import com.teamway.base.service.AlarmConfigService;
import com.teamway.base.vo.AlarmAllConfigVo;
import com.teamway.base.vo.AlarmConditionVo;
import com.teamway.common.entity.ResultModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 告警配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-08
 */
@RestController
@RequestMapping("/insAlarmConfig")
public class AlarmConfigController {

    @Autowired
    private AlarmConfigService alarmConfigService;

    @PostMapping("/addOrUpdate")
    public ResultModel<String> updateAlarmConfig(@RequestBody @Validated AlarmKeepDto alarmKeepDto) {
        boolean result = alarmConfigService.updateAlarmConfig(alarmKeepDto);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/selectDetailByPoint")
    public ResultModel<AlarmConfig> selectDetailByPoint(@RequestBody @Validated AlarmConfigQueryDto alarmConfigQueryDto) {
        return ResultModel.success(alarmConfigService.selectDetailByPoint(alarmConfigQueryDto));
    }

    @PostMapping("/selectCondition")
    public ResultModel<List<AlarmConditionVo>> selectCondition(@RequestBody @Validated AlarmConditionQueryDto alarmConditionQueryDto) {
        return ResultModel.success(alarmConfigService.selectCondition(alarmConditionQueryDto));
    }

    @PostMapping("/selectList")
    public ResultModel<AlarmAllConfigVo> selectList(@RequestBody @Validated AlarmConfigQueryDto alarmConfigQueryDto) {
        return ResultModel.success(alarmConfigService.selectList(alarmConfigQueryDto));
    }

}
