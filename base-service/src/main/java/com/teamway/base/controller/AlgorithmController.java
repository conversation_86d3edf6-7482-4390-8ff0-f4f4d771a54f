package com.teamway.base.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.teamway.base.dto.algorithm.AlgorithmQueryDto;
import com.teamway.base.entity.Algorithm;
import com.teamway.base.service.AlgorithmService;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> thh
 * @date : 2022/7/22
 */
@RequestMapping("/algorithmInfo")
@RestController
public class AlgorithmController {

    @Autowired
    private AlgorithmService algorithmService;

    @PostMapping("/add")
    public ResultModel<String> addAlgorithmType(@RequestBody @Valid Algorithm algorithm) {
        return algorithmService.add(algorithm) > 0 ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/batchDel")
    public ResultModel<String> deleteAlgorithmTypeByIds(@RequestBody @Valid BatchIds<Long> batchIds) {
        return algorithmService.batchDel(batchIds.getIds()) > 0 ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/update")
    public ResultModel<String> update(@RequestBody @Valid Algorithm algorithm) {
        return algorithmService.update(algorithm) > 0 ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/selectPageList")
    public ResultModel<List<Algorithm>> selectPageList(@RequestBody @Valid AlgorithmQueryDto algorithmQueryDto) {
        IPage<Algorithm> page = algorithmService.selectAlgorithm(algorithmQueryDto);
        return ResultModel.success(page);
    }

    @GetMapping("/selectAllList/{flag}")
    public ResultModel<List<Algorithm>> selectAllList(@PathVariable Integer flag) {
        return ResultModel.success(algorithmService.selectAll(flag));
    }

    @GetMapping("/getAlgorithmByName/{algCode}")
    public ResultModel<Algorithm> getAlgorithmByName(@PathVariable String algCode) {
        return ResultModel.success(algorithmService.getAlgorithmByCode(algCode));
    }

    @GetMapping("/export")
    public void exportExcel(@Valid AlgorithmQueryDto algorithmQueryDto, HttpServletResponse response) {
        algorithmService.exportExcel(algorithmQueryDto, response);
    }

    @GetMapping("/import")
    public ResultModel<String> importExcel(MultipartFile file, HttpServletResponse response) {
        algorithmService.importExcel(file, response);
        return ResultModel.success();
    }

    @GetMapping("/getAlgorithmById/{id}")
    public ResultModel<Algorithm> getAlgorithmByName(@PathVariable Long id) {
        return ResultModel.success(algorithmService.getAlgorithmById(id));
    }

}