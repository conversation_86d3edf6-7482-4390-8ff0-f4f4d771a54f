package com.teamway.base.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.teamway.base.dto.algorithm.AlgorithmGroupQueryDto;
import com.teamway.base.entity.Algorithm;
import com.teamway.base.entity.AlgorithmGroup;
import com.teamway.base.service.AlgorithmGroupService;
import com.teamway.base.service.AlgorithmService;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.entity.ResultModel;
import com.teamway.exception.ServiceException;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 算法组配置
 *
 * <AUTHOR>
 * @since 2024-03-05
 */
@RestController
@RequestMapping("/algorithmGroup")
public class AlgorithmGroupController {

    @Autowired
    private AlgorithmGroupService algorithmGroupService;
    @Autowired
    private AlgorithmService algorithmService;

    @PostMapping("/add")
    public ResultModel<String> add(@RequestBody @Valid AlgorithmGroup algorithmGroup) {
        List<AlgorithmGroup> algorithmGroupList = algorithmGroupService.list(new LambdaQueryWrapper<AlgorithmGroup>().eq(AlgorithmGroup::getName, algorithmGroup.getName()));
        if (CollUtil.isNotEmpty(algorithmGroupList)) {
            throw new ServiceException(ResultCode.EXIST, "算法组名称");
        }
        return algorithmGroupService.save(algorithmGroup) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/batchDel")
    public ResultModel<String> batchDel(@RequestBody @Valid BatchIds<Long> batchIds) {
        List<Algorithm> algorithmList = algorithmService.list(new LambdaQueryWrapper<Algorithm>().in(Algorithm::getAlgorithmGroupId, batchIds.getIds()));
        if (CollUtil.isNotEmpty(algorithmList)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "算法组下包含算法，不能删除");
        }
        return algorithmGroupService.removeByIds(batchIds.getIds()) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/update")
    public ResultModel<String> update(@RequestBody @Valid AlgorithmGroup algorithmGroup) {
        return algorithmGroupService.updateById(algorithmGroup) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/selectPageList")
    public ResultModel<List<AlgorithmGroup>> selectPageList(@RequestBody @Valid AlgorithmGroupQueryDto queryDto) {
        IPage<AlgorithmGroup> page = new Page<>(queryDto.getPageIndex(), queryDto.getPageSize());
        LambdaQueryWrapper<AlgorithmGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(queryDto.getName()), AlgorithmGroup::getName, queryDto.getName());
        algorithmGroupService.page(page, queryWrapper);
        return ResultModel.success(page);
    }

}
