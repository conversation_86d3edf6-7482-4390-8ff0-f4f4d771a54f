package com.teamway.base.controller;

import com.teamway.base.entity.Announcement;
import com.teamway.base.service.AnnouncementService;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 首页公告表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20
 */
@Slf4j
@RestController
@RequestMapping("/announcement")
public class AnnouncementController {

    @Autowired
    private AnnouncementService announcementService;

    /**
     * 添加公告
     * @param announcement 公告信息
     * @return 添加结果
     */
    @PostMapping("/add")
    public ResultModel<Boolean> add(@RequestBody Announcement announcement) {
        return ResultModel.success(announcementService.addAnnouncement(announcement));
    }

    /**
     * 更新公告
     * @param announcement 公告信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public ResultModel<Boolean> update(@RequestBody Announcement announcement) {
        return ResultModel.success(announcementService.updateAnnouncement(announcement));
    }

    /**
     * 批量删除公告
     *
     * @param request 删除请求参数
     * @return ResultModel<Boolean> true-删除成功 false-删除失败
     */
    @PostMapping("/batchDelete")
    public ResultModel<Boolean> batchDelete(@RequestBody BatchIds request) {
        return ResultModel.success(announcementService.batchDeleteAnnouncement(request));
    }

    /**
     * 获取公告详情
     * @param id 公告ID
     * @return 公告信息
     */
    @GetMapping("/get/{id}")
    public ResultModel<Announcement> getById(@PathVariable("id") Long id) {
        return ResultModel.success(announcementService.getAnnouncementById(id));
    }

    /**
     * 获取公告列表
     * @return 公告列表
     */
    @GetMapping("/list")
    public ResultModel<List<Announcement>> list() {
        return ResultModel.success(announcementService.getAnnouncementList());
    }

    /**
     * 更新公告滚动状态
     * @param id 公告ID
     * @param scrollStatus 滚动状态：0-不滚动，1-滚动中，2-暂停
     * @return 更新结果
     */
    @PostMapping("/updateScrollStatus")
    public ResultModel<Boolean> updateScrollStatus(@RequestParam("id") Long id,
                                                   @RequestParam("scrollStatus") Integer scrollStatus) {
        return ResultModel.success(announcementService.updateScrollStatus(id, scrollStatus));
    }
}
