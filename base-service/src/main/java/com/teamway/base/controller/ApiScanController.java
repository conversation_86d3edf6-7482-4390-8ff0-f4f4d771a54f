package com.teamway.base.controller;

import com.teamway.common.entity.ResultModel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
/**
* 获取api列表
*
* @date : 2024/10/21
* <AUTHOR> liucheng
*/
@RestController
public class ApiScanController {

    /**
     * 匹配扫描的包前缀
     **/
    private final String scanPackage = "com.teamway";
    private final ApplicationContext applicationContext;

    public ApiScanController(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 获取api列表
     *
     * @param
     * @return : com.teamway.common.entity.ResultModel<java.util.List<com.teamway.personnel.service.impl.ApiEndpointInspector.ApiInfo>>
     * @date : 2024/10/21
     * <AUTHOR> liucheng
     */
    @GetMapping("/getApiList")
    public ResultModel<List<ApiInfo>> getApiList() {
        return ResultModel.success(inspectApiEndpoints());
    }

    /**
     * 扫描指定包下的请求路径列表
     *
     * @param
     * @return : void
     * @date : 2024/10/21
     * <AUTHOR> liucheng
     */
    public List<ApiInfo> inspectApiEndpoints() {
        List<ApiInfo> apiInfoList = new ArrayList<>();
        RequestMappingHandlerMapping requestMappingHandlerMapping = applicationContext.getBean(RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
        for (Map.Entry<RequestMappingInfo, HandlerMethod> requestMappingInfoHandlerMethodEntry : handlerMethods.entrySet()) {
            HandlerMethod value = requestMappingInfoHandlerMethodEntry.getValue();
            String packageName = value.getBeanType().getPackageName();
            if (matchPackageName(packageName)) {
                RequestMappingInfo key = requestMappingInfoHandlerMethodEntry.getKey();
                Set<String> patternValues = key.getPatternValues();
                String url = getFirstElement(patternValues);
                Set<RequestMethod> methods = key.getMethodsCondition().getMethods();
                RequestMethod firstElement2 = getFirstElement(methods);
                if (firstElement2 != null) {
                    String requestMethodName = firstElement2.name();
                    apiInfoList.add(new ApiInfo(url, requestMethodName));
                }
            }
        }
        return apiInfoList;
    }

    /**
     * 获取集合中的第一个元素
     *
     * @param set 集合
     * @return : T
     * @date : 2024/10/21
     * <AUTHOR> liucheng
     */
    public static <T> T getFirstElement(Set<T> set) {
        Iterator<T> iterator = set.iterator();

        if (iterator.hasNext()) {
            return iterator.next();
        }
        // 如果集合为空，返回null
        return null;
    }

    /**
     * 判断是否是扫描的包
     *
     * @param packageName 包名称
     * @return : boolean
     * @date : 2024/10/21
     * <AUTHOR> liucheng
     */
    private boolean matchPackageName(String packageName) {
        if (null != packageName && !packageName.isEmpty()) {
            return packageName.startsWith(scanPackage);
        }
        return false;
    }

    /**
     * api信息
     *
     * <AUTHOR> liucheng
     * @date : 2024/10/21
     */
    @Setter
    @Getter
    public class ApiInfo {
        private String url;
        private String method;

        public ApiInfo(String url, String method) {
            this.url = url;
            this.method = method;
        }
    }
}
