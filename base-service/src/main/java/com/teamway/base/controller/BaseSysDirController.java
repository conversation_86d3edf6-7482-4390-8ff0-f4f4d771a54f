package com.teamway.base.controller;

import com.teamway.base.dto.dir.DirDto;
import com.teamway.base.entity.BaseSysDir;
import com.teamway.base.service.BaseSysDirService;
import com.teamway.base.vo.DirExportVo;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> yiju
 * @since 2025-03-03
 * @description 基础字典控制器
 */
@RestController
@RequestMapping("/baseSysDir")
public class BaseSysDirController {

    @Autowired
    private BaseSysDirService baseSysDirService;

    /**
     * 新增或修改字典
     * @param baseSysDir
     * @return
     */
    @PostMapping("/saveDir")
    public ResultModel<String> saveDir(@RequestBody @Valid BaseSysDir  baseSysDir){
        return baseSysDirService.saveDir(baseSysDir) ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 删除或批量删除字典
     */
    @PostMapping("/batchDeleteDir")
    public ResultModel<String> deleteDir(@RequestBody @Valid BatchIds<Long> ids){
        return baseSysDirService.deleteDir(ids) ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 查询字典列表
     * @param dirDto
     * @return
     */
    @PostMapping("/pageListDir")
    public ResultModel<List<BaseSysDir>> pageListDir(@RequestBody @Valid DirDto dirDto){
        return ResultModel.success(baseSysDirService.listDir(dirDto));
    }

    /**
     * 导出字典 json格式
     * @param exportIds
     * @param response
     * @return
     */
    @PostMapping("/exportDir")
    public void exportDir(@RequestBody @Valid BatchIds<Long> exportIds, HttpServletResponse response){
        baseSysDirService.exportDir(exportIds,response);
    }

    /**
     * 导入字典 全量导入 增量导入 追加或更新 json格式
     */
    @PostMapping("/importDir")
    public ResultModel<String> importDirFull(@RequestParam("type") @NotEmpty(message = "导入类型不能为空") @Pattern(regexp = "1|2|3",message = "导入类型错误") String type  ,
                                             @RequestParam("file") MultipartFile file){
        return baseSysDirService.importDir(type,file) ? ResultModel.success() : ResultModel.fail();
    }


    /**
     * 返回字典数据  从redis获取
     * @return
     */
    @GetMapping("/getDirList")
    public ResultModel<List<BaseSysDir>> getDirList(){
        return ResultModel.success(baseSysDirService.getDirList());
    }


    /**
     *刷新缓存
     */
    @GetMapping("/refreshDirCache")
    public ResultModel<String> refreshDirCache(){
        return  baseSysDirService.refreshDirCache() ? ResultModel.success() : ResultModel.fail();
    }

}
