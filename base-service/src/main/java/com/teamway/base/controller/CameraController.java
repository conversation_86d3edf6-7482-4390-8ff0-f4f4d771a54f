package com.teamway.base.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.teamway.base.common.constant.CameraTypeConstant;
import com.teamway.base.dto.camera.*;
import com.teamway.base.entity.Camera;
import com.teamway.base.service.CameraService;
import com.teamway.base.vo.CameraChannelVo;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 摄像机控制层
 *
 * <AUTHOR> liucheng
 * @date : 2022/4/14
 */
@RestController
@RequestMapping("/camera")
public class CameraController {

    @Autowired
    private CameraService cameraService;

    /**
     * 摄像机列表
     *
     * @param cameraQueryDto
     * @return : com.teamway.base.common.ResultModel<java.util.List<com.teamway.base.entity.Camera>>
     * @date : 2022/7/14
     * <AUTHOR> gss
     */
    @PostMapping("/selectByCondition")
    public ResultModel<List<Camera>> findPageList(@RequestBody @Valid CameraQueryDto cameraQueryDto) {
        return ResultModel.success(cameraService.findPageList(cameraQueryDto));
    }

    /**
     * 获取所有摄像机
     *
     * @return : com.teamway.base.common.ResultModel<java.util.List<com.teamway.base.entity.Camera>>
     * @date : 2023/3/15
     * <AUTHOR> zhangsai
     */
    @GetMapping("/getAll")
    public ResultModel<List<Camera>> getAll() {
        return ResultModel.success(cameraService.list());
    }


    /**
     * 摄像机详情
     *
     * @param id
     * @return : com.teamway.base.common.ResultModel<com.teamway.base.entity.Camera>
     * @date : 2022/7/14
     * <AUTHOR> gss
     */
    @GetMapping("/getDetail/{id}")
    public ResultModel<Camera> getDetail(@PathVariable("id") Long id) {
        return ResultModel.success(cameraService.getCameraById(id));
    }

    /**
     * 新增摄像机信息
     *
     * @param camera
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/14
     * <AUTHOR> gss
     */
    @PostMapping("/add")
    public ResultModel<Camera> add(@RequestBody @Valid Camera camera) {
        Camera saveCamera = cameraService.addCamera(camera);
        return ObjectUtil.isNotNull(saveCamera) ? ResultModel.success(saveCamera) : ResultModel.fail();
    }

    /**
     * 批量添加摄像机
     *
     * @param saveCameraListDto
     * @return
     */
    @PostMapping("/batchAdd")
    public ResultModel<String> batchAdd(@RequestBody @Valid SaveCameraListDto saveCameraListDto) {
        boolean result = cameraService.batchAdd(saveCameraListDto);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 批量修改摄像机
     *
     * @param updateCameraDto
     * @return
     */
    @PostMapping("/batchUpdate")
    public ResultModel<String> batchUpdate(@RequestBody @Valid UpdateCameraDto updateCameraDto) {
        boolean result = cameraService.batchUpdate(updateCameraDto);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 删除摄像机信息
     *
     * @param batchIds
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/4/14
     * <AUTHOR> liucheng
     */
    @PostMapping("/batchDel")
    public ResultModel<String> batchDel(@RequestBody @Valid BatchIds<Long> batchIds) {
        boolean result = cameraService.delBatchCamera(batchIds.getIds());
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 更新摄像机信息
     *
     * @param camera
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/14
     * <AUTHOR> gss
     */
    @PostMapping("/update")
    public ResultModel<String> update(@RequestBody @Valid Camera camera) {
        boolean result = cameraService.updateCamera(camera);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 摄像机抓图
     *
     * @param cameraSnapshotDto
     * @return : com.teamway.base.common.ResultModel<java.lang.String>
     * @date : 2022/7/14
     * <AUTHOR> gss
     */
    @PostMapping("/snapshot")
    public ResultModel<String> snapshot(@RequestBody @Valid CameraSnapshotDto cameraSnapshotDto) {
        String snapshot = cameraService.snapshot(cameraSnapshotDto);
        return StrUtil.isNotBlank(snapshot) ? ResultModel.success(snapshot) : ResultModel.fail();
    }

    /**
     * 获取摄像机抓图地址
     *
     * @param id
     * @return : com.teamway.base.common.ResultModel<java.lang.String>
     * @date : 2023/6/8
     * <AUTHOR> zhangsai
     */
    @GetMapping("/snapshotUrl/{id}")
    public ResultModel<String> snapshotUrl(@PathVariable("id") Long id) {
        String snapshot = cameraService.snapshotUrl(id);
        return StrUtil.isNotBlank(snapshot) ? ResultModel.success(snapshot) : ResultModel.fail();
    }

    /**
     * 获取摄像机地址  onvif、rtsp地址
     *
     * @param cameraUrlDto
     * @return
     */
    @PostMapping("/getCameraUrl")
    public ResultModel<String> getCameraUrl(@RequestBody CameraUrlDto cameraUrlDto) {
        String snapshot = cameraService.getCameraUrl(cameraUrlDto);
        return StrUtil.isNotBlank(snapshot) ? ResultModel.success(snapshot) : ResultModel.fail();
    }

    /**
     * 刷新摄像机状态
     *
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/12
     * <AUTHOR> Gaven
     */
    @PostMapping("/reflushStatus")
    public ResultModel<String> reflushStatus() {
        cameraService.reflushStatus();
        return ResultModel.success();
    }

    /**
     * 导入摄像机
     *
     * @param file
     * @return
     * @date : 2022/7/12
     * <AUTHOR> zhangsai
     */
    @PostMapping("/import")
    public ResultModel<CameraImportDto> importExcel(MultipartFile file) {
        return ResultModel.success(cameraService.importExcel(file));
    }

    /**
     * 导出摄像机
     *
     * @param cameraQueryDto
     * @param response
     * @return
     * @date : 2022/7/12
     * <AUTHOR> zhangsai
     */
    @PostMapping("/export")
    public void exportExcel(@RequestBody @Valid CameraQueryDto cameraQueryDto, HttpServletResponse response) {
        cameraService.exportExcel(cameraQueryDto, response);
    }

    /**
     * 下载导入模板
     *
     * @param response
     * @return
     * @date : 2022/7/12
     * <AUTHOR> zhangsai
     */
    @PostMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        cameraService.downloadTemplate(response);
    }


    /**
     * 根据区域id查询摄像机信息
     *
     * @param id
     * @return : com.teamway.base.common.ResultModel<java.util.List<com.teamway.base.entity.Camera>>
     * @date : 2022/7/21
     * <AUTHOR> zhangsai
     */
    @GetMapping("/selectByRegion/{id}")
    public ResultModel<List<Camera>> selectByRegion(@PathVariable("id") Long id) {
        LambdaQueryWrapper<Camera> cameraQueryWrapper = new LambdaQueryWrapper<>();
        cameraQueryWrapper.eq(Camera::getRegionId, id);
        return ResultModel.success(cameraService.list(cameraQueryWrapper));
    }

    /**
     * 获取摄像机在线离线数量
     *
     * @return
     */
    @GetMapping("/selectCameraOnAndOffNum")
    public ResultModel<CameraDto> selectCameraOnAndOffNum() {
        return ResultModel.success(cameraService.selectCameraOnAndOffNum());
    }

    /**
     * 查询摄像机通道信息
     *
     * @return
     */
    @GetMapping("/getChannels")
    public ResultModel<List<CameraChannelVo>> getChannels() {
        List<Camera> cameraList = cameraService.list();
        List<CameraChannelVo> cameraChannelVoList = new ArrayList<>();
        cameraList.forEach(camera -> {
            CameraChannelVo cameraChannelVo = new CameraChannelVo();
            cameraChannelVo.setId(camera.getId());
            cameraChannelVo.setCameraId(camera.getId());
            cameraChannelVo.setName(camera.getName());
            cameraChannelVo.setMainId(camera.getMainId());
            cameraChannelVo.setSubId(camera.getSubId());
            if (CameraTypeConstant.TYPE_BALL.equals(camera.getType())
                    || CameraTypeConstant.TYPE_PLATFORM.equals(camera.getType())) {
                cameraChannelVo.setCanPtz(1);
            } else {
                cameraChannelVo.setCanPtz(0);
            }
            cameraChannelVoList.add(cameraChannelVo);
        });
        return ResultModel.success(cameraChannelVoList);
    }

    /**
     * 根据摄像机id查询该摄像机下所有类型的摄像机信息
     */
    @GetMapping("/getCameraAndTypeById/{cameraId}")
    public ResultModel<List<Camera>> getCameraAndTypeById(@PathVariable Long cameraId) {
        return ResultModel.success(cameraService.getCameraAndTypeById(cameraId));
    }

}
