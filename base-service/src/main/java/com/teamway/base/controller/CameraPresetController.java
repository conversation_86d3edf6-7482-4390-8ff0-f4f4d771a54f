package com.teamway.base.controller;

import com.teamway.base.dto.preset.CameraPresetQueryDto;
import com.teamway.base.dto.preset.CameraPresetRefreshDto;
import com.teamway.base.entity.CameraPreset;
import com.teamway.base.service.CameraPresetService;
import com.teamway.base.vo.CameraPresetVo;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 摄像机预置位控制层
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-25
 */
@RestController
@RequestMapping("/preset")
public class CameraPresetController {
    @Autowired
    private CameraPresetService cameraPresetService;
    /**
     * 查询摄像机预置位列表
     *
     * @param cameraPresetQueryDto
     * @return : com.teamway.base.common.ResultModel<java.util.List<com.teamway.base.entity.Camera>>
     * @date : 2022/7/25
     * <AUTHOR> gss
     */
    @PostMapping("/selectByCamera")
    public ResultModel<List<CameraPreset>> findList(@RequestBody @Valid CameraPresetQueryDto cameraPresetQueryDto) {
        List<CameraPreset> cameraPresets = cameraPresetService.findList(cameraPresetQueryDto);
        return ResultModel.success(cameraPresets);
    }

    /**
     * 根据多个摄像机查询预置位
     *
     * @param ids
     * @return : com.teamway.base.common.ResultModel<java.util.List<com.teamway.base.entity.Camera>>
     * @date : 2024/8/19
     */
    @PostMapping("/selectByCameraIds")
    public ResultModel<List<CameraPresetVo>> selectByCameraIds(@RequestBody @Valid BatchIds<Long> ids) {
        return ResultModel.success(cameraPresetService.selectByCameraIds(ids));
    }


    /**
     * 新增摄像机预置位信息
     *
     * @param cameraPreset
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/25
     * <AUTHOR> gss
     */
    @PostMapping("/add")
    public ResultModel<String> add(@RequestBody @Valid CameraPreset cameraPreset) {
        boolean result = cameraPresetService.addCameraPreset(cameraPreset);
        return result ? ResultModel.success() : ResultModel.fail();
    }


    /**
     * 更新摄像机预置位信息
     *
     * @param cameraPreset
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/25
     * <AUTHOR> gss
     */
    @PostMapping("/update")
    public ResultModel<String> update(@RequestBody @Valid CameraPreset cameraPreset) {
        boolean result = cameraPresetService.updateCameraPreset(cameraPreset);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 批量删除摄像机预置位信息
     *
     * @param batchIds
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/25
     * <AUTHOR> gss
     */
    @PostMapping("/delBatch")
    public ResultModel<String> delBatch(@RequestBody @Valid BatchIds<Long> batchIds) {
        boolean result = cameraPresetService.delBatchCameraPreset(batchIds);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 手动刷新摄像机中预置位
     *
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/25
     * <AUTHOR> gss
     */
    @PostMapping("/refresh")
    public ResultModel<String> refresh(@RequestBody @Valid CameraPresetRefreshDto cameraPresetRefreshDto) {
        boolean result = cameraPresetService.refresh(cameraPresetRefreshDto);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 手动刷新所有预置位
     *
     * @return : com.teamway.base.common.ResultModel
     * @date : 2025/01/09
     * <AUTHOR> zhangsai
     */
    @GetMapping("/refreshAll")
    public ResultModel<String> refreshAll() {
        cameraPresetService.timingRefresh();
        return ResultModel.success();
    }

}
