package com.teamway.base.controller;

import com.teamway.base.dto.CameraView.CameraViewDto;
import com.teamway.base.dto.CameraView.CameraViewIdsDto;
import com.teamway.base.dto.CameraView.CameraViewListDto;
import com.teamway.base.service.CameraViewService;
import com.teamway.base.vo.CameraViewListVo;
import com.teamway.common.entity.ResultModel;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cameraView")
public class CameraViewController {

    @Autowired
    CameraViewService cameraViewService;

    /**
     * 存储个人或公共视图
     * @param dto
     * @return
     */
    @PostMapping("/add")
    public ResultModel<Boolean> viewStorage(@RequestBody @Valid CameraViewDto dto) {
        Boolean result = cameraViewService.viewStorage(dto);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 修改个人或公共视图
     * @param dto
     * @return
     */
    @PostMapping("/update")
    public ResultModel<Boolean> updateView(@RequestBody @Valid CameraViewDto dto) {
        Boolean result = cameraViewService.updateView(dto);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 删除个人或公共视图
     * @param dto
     * @return
     */
    @PostMapping("/delete")
    public ResultModel<Boolean> deleteView(@RequestBody @Valid CameraViewIdsDto dto) {
        Boolean result = cameraViewService.deleteView(dto);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 查看个人或公共视图列表
     * @param dto
     * @return
     */
    @PostMapping("/selectCameraViewList")
    public ResultModel<CameraViewListVo> selectCameraViewList(@RequestBody @Valid CameraViewListDto dto) {
        return ResultModel.success(cameraViewService.selectCameraViewList(dto));
    }





}
