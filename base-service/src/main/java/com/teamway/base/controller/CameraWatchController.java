package com.teamway.base.controller;

import com.teamway.base.entity.CameraWatch;
import com.teamway.base.service.CameraWatchService;
import com.teamway.common.entity.ResultModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 摄像机监控配置控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cameraWatch")
public class CameraWatchController {

    @Autowired
    private CameraWatchService cameraWatchService;

    /**
     * 保存或修改守望位
     * @param cameraWatch
     * @return
     */
    @PostMapping("/addWatch")
    public ResultModel<String> addWatch(@RequestBody CameraWatch cameraWatch){
        Boolean result = cameraWatchService.addWatch(cameraWatch);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 查询守望位信息
     */
    @GetMapping("/getWatchById/{id}")
    public ResultModel<CameraWatch> getWatchById(@PathVariable Long id){
        return ResultModel.success(cameraWatchService.getWatchById(id));
    }
}