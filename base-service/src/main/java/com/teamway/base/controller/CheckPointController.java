package com.teamway.base.controller;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.teamway.base.dto.check.CheckPointBatchUpdateDto;
import com.teamway.base.dto.check.CheckPointDto;
import com.teamway.base.dto.check.CheckPointListDto;
import com.teamway.base.dto.check.CheckSaveDto;
import com.teamway.base.dto.check.DeleteCheckDto;
import com.teamway.base.dto.check.UrlChangeBaseDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.service.CheckPointService;
import com.teamway.base.vo.AlgListVo;
import com.teamway.base.vo.CheckPointVo;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.entity.ResultModel;
import com.teamway.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/checkPoint")
public class CheckPointController {

    @Autowired
    private CheckPointService checkPointService;

    /**
     * 新增检测点
     * @param saveDto
     * @return
     */
    @PostMapping("/saveCheck")
    public ResultModel<String> saveCheck(@RequestBody CheckSaveDto saveDto){
        Boolean result = checkPointService.saveCheck(saveDto);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 查询检测点列表
     * @param pointDto
     * @return
     */
    @PostMapping("/listCheckPoint")
    public ResultModel<List<CheckPointVo>> listCheckPoint(@RequestBody CheckPointListDto pointDto){
        Page<CheckPointVo> page = checkPointService.listCheckPoint(pointDto);
        return ResultModel.success(page);
    }



    /**
     * 根据摄像机id查询算法名称
     * @param cameraId
     * @return
     */
    @GetMapping("/queryAlgNameByCameraId/{cameraId}")
    public ResultModel<AlgListVo> queryAlgNameByCameraId(@PathVariable("cameraId") Long cameraId){
        return ResultModel.success(checkPointService.queryAlgNameByCameraId(cameraId));
    }

    /**
     * 修改检测点
     * @param pointDto
     * @return
     */
    @PostMapping("/updateCheckPoint")
    public ResultModel<String> updateCheckPoint(@RequestBody CheckPointDto pointDto){
        Boolean result = checkPointService.updateCheckPoint(pointDto);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 删除检测点
     * @param checkDto
     * @return
     */
    @PostMapping("/deleteCheckPoint")
    public ResultModel<String> deleteCheckPoint(@RequestBody DeleteCheckDto checkDto){
        Boolean result = checkPointService.deleteCheckPoint(checkDto);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/urlChangeBase")
    public ResultModel<String> urlChangeBase(@RequestBody UrlChangeBaseDto urlChangeBaseDto) throws IOException {
        return StringUtils.isEmpty(checkPointService.urlChangeBase(urlChangeBaseDto))?ResultModel.fail():ResultModel.success(checkPointService.urlChangeBase(urlChangeBaseDto));
    }

    @PostMapping("/batchUpdate")
    public ResultModel<String> batchUpdate(@RequestBody CheckPointBatchUpdateDto dto) {
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new ServiceException(ResultCode.NOT_NULL, "id");
        }
        checkPointService.batchUpdate(dto);
        return ResultModel.success();
    }

    /**
     * 查询摄像机下检测点
     */
    @GetMapping("/getCameraCheckPoint")
    public ResultModel<List<Camera>> getCameraCheckPoint() {
        return ResultModel.success(checkPointService.getCameraCheckPoint());
    }

}