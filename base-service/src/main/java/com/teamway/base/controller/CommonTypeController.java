package com.teamway.base.controller;

import com.teamway.base.dto.other.CommonTypeDto;
import com.teamway.base.service.CommonTypeService;
import com.teamway.common.entity.ResultModel;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 通用类型表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@RestController
@RequestMapping("/base/commonType")
public class CommonTypeController {

    @Autowired
    private CommonTypeService commonTypeService;
    /**
     *获取工种列表
     * @return
     */
    @GetMapping("/getPersonJobList")
    public ResultModel getPersonJobList() {
        return ResultModel.success(commonTypeService.getPersonJobList());
    }

    /**
     *获取部门列表
     * @return
     */
    @GetMapping("/getPersonDepartmentList")
    public ResultModel getPersonDepartmentList() {
        return ResultModel.success(commonTypeService.getPersonDepartmentList());
    }

    /**
     *获取岗位列表
     * @return
     */
    @GetMapping("/getPersonPostList")
    public ResultModel getPersonPostList() {
        return ResultModel.success(commonTypeService.getPersonPostList());
    }

    /**
     *获取人员单位列表
     * @return
     */
    @GetMapping("/getPersonUnitList")
    public ResultModel getPersonUnitList() {
        return ResultModel.success(commonTypeService.getPersonUnitList());
    }

    /**
     *获取车辆颜色列表
     * @return
     */
    @GetMapping("/getCarColorList")
    public ResultModel getCarColorList() {
        return ResultModel.success(commonTypeService.getCarColorList());
    }

    /**
     *获取类型列表
     * @return
     */
    @PostMapping("/getTypeListByCategory")
    public ResultModel getTypeListByCategory(@RequestBody @Valid CommonTypeDto commonTypeDto) {
        return ResultModel.success(commonTypeService.getTypeListByCategory(commonTypeDto));
    }
}
