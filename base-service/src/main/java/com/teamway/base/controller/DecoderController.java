package com.teamway.base.controller;

import com.teamway.base.dto.tv.decoder.DecoderDelDto;
import com.teamway.base.entity.Decoder;
import com.teamway.base.service.DecoderService;
import com.teamway.common.entity.ResultModel;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 解码器
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@RestController
@RequestMapping("/decoder")
public class DecoderController {
    @Autowired
    private DecoderService decoderService;

    @GetMapping("/get")
    public ResultModel<List<Decoder>> get() {
        return ResultModel.success(decoderService.listAndSynthesisLayout());
    }

    @PostMapping("/add")
    public ResultModel<String> add(@RequestBody @Valid Decoder decoder) {
        return decoderService.add(decoder) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/update")
    public ResultModel<String> update(@RequestBody @Valid Decoder decoder) {
        return decoderService.updateDecoder(decoder) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/batchDel")
    public ResultModel<String> del(@RequestBody @Valid DecoderDelDto decoderDelDto) {
        return decoderService.del(decoderDelDto);
    }
}
