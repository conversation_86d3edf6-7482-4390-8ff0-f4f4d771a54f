package com.teamway.base.controller;

import com.teamway.base.dto.element.QueryElementDto;
import com.teamway.base.dto.element.UpdatePositionDto;
import com.teamway.base.entity.ElementInfo;
import com.teamway.base.service.ElementInfoService;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 地图元素机控制层
 *
 * <AUTHOR> thh
 * @date : 2022/7/15
 */
@RestController
@RequestMapping("elementInfo")
public class ElementInfoController {

    @Autowired
    private ElementInfoService elementInfoService;

    @PostMapping("/add")
    public ResultModel<String> add(@RequestBody @Valid List<ElementInfo> elementInfoList) {
        return elementInfoService.add(elementInfoList) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/update")
    public ResultModel<String> update(@RequestBody @Valid UpdatePositionDto positionDto) {
        return elementInfoService.update(positionDto) > 0 ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/delete")
    public ResultModel<String> batchDel(@RequestBody BatchIds<Long> batchIds) {
        return elementInfoService.delete(batchIds.getIds()) > 0 ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("findByParam/{id}")
    public ResultModel<List<ElementInfo>> findByParam(@PathVariable("id") Long id) {
        return ResultModel.success(elementInfoService.findByParam(id));
    }

    @PostMapping("selectList")
    public ResultModel<List<ElementInfo>> selectList(@RequestBody QueryElementDto queryElementDto) {
        return ResultModel.success(elementInfoService.selectList(queryElementDto));
    }
}
