package com.teamway.base.controller;

import com.teamway.base.entity.HScreenContent;
import com.teamway.base.service.HScreenContentService;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 横屏显示内容配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Slf4j
@RestController
@RequestMapping("/hScreenContent")
public class HScreenContentController {

    @Autowired
    private HScreenContentService hScreenContentService;

    /**
     * 添加横屏显示内容配置
     * @param hScreenContent 横屏显示内容配置信息
     * @return ResultModel<Boolean> 添加结果
     */
    @PostMapping("/add")
    public ResultModel<Boolean> add(@RequestBody @Valid HScreenContent hScreenContent) {
        return ResultModel.success(hScreenContentService.addHScreenContent(hScreenContent));
    }

    /**
     * 更新横屏显示内容配置
     * @param hScreenContent 横屏显示内容配置信息
     * @return ResultModel<Boolean> 更新结果
     */
    @PostMapping("/update")
    public ResultModel<Boolean> update(@RequestBody @Valid  HScreenContent hScreenContent) {
        return ResultModel.success(hScreenContentService.updateHScreenContent(hScreenContent));
    }

    /**
     * 批量删除横屏显示内容配置
     * @param request 删除请求参数
     * @return ResultModel<Boolean> 删除结果
     */
    @PostMapping("/batchDelete")
    public ResultModel<Boolean> batchDelete(@RequestBody BatchIds request) {
        return ResultModel.success(hScreenContentService.batchDeleteHScreenContent(request));
    }

    /**
     * 获取横屏显示内容配置详情
     * @param id 配置ID
     * @return ResultModel<HScreenContent> 横屏显示内容配置详情
     */
    @GetMapping("/get/{id}")
    public ResultModel<HScreenContent> getById(@PathVariable("id") Long id) {
        return ResultModel.success(hScreenContentService.getHScreenContentById(id));
    }

    /**
     * 获取横屏显示内容配置列表
     * @param screenId 横屏配置ID
     * @return ResultModel<List<HScreenContent>> 横屏显示内容配置列表
     */
    @GetMapping("/list/{screenId}")
    public ResultModel<List<HScreenContent>> list(@PathVariable("screenId") Long screenId) {
        return ResultModel.success(hScreenContentService.getHScreenContentList(screenId));
    }
}