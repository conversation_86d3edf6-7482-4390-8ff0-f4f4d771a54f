package com.teamway.base.controller;

import com.teamway.base.dto.screen.HScreenQueryDto;
import com.teamway.base.entity.HScreen;
import com.teamway.base.service.HScreenService;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 横屏配置表 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("/hScreen")
public class HScreenController {

    @Autowired
    private HScreenService hScreenService;

    /**
     * 添加横屏配置
     */
    @PostMapping("/add")
    public ResultModel<Boolean> add(@RequestBody HScreen hScreen) {
        return ResultModel.success(hScreenService.addHScreen(hScreen));
    }

    /**
     * 更新横屏配置
     */
    @PostMapping("/update")
    public ResultModel<Boolean> update(@RequestBody HScreen hScreen) {
        return ResultModel.success(hScreenService.updateHScreen(hScreen));
    }

    /**
     * 批量删除横屏配置
     */
    @PostMapping("/batchDelete")
    public ResultModel<Boolean> batchDelete(@RequestBody BatchIds request) {
        return ResultModel.success(hScreenService.batchDeleteHScreen(request));
    }

    /**
     * 获取横屏配置详情
     */
    @GetMapping("/get/{id}")
    public ResultModel<HScreen> getById(@PathVariable("id") Long id) {
        return ResultModel.success(hScreenService.getHScreenById(id));
    }

    /**
     * 分页查询横屏配置列表
     */
    @PostMapping("/selectByCondition")
    public ResultModel<List<HScreen>> findPageList(@RequestBody @Valid HScreenQueryDto queryDto) {
        return ResultModel.success(hScreenService.findPageList(queryDto));
    }
}