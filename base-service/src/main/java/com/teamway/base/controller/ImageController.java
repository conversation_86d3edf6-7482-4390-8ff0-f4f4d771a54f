package com.teamway.base.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.teamway.base.dto.screenshot.ImageQueryDto;
import com.teamway.base.entity.Image;
import com.teamway.base.service.ImageService;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022-10-20
 * @Description: 摄像机图片管理
 */
@RequestMapping("/image")
@RestController
public class ImageController {

    @Autowired
    private ImageService imageService;

    /**
     * 分页查询告图片
     * @param imageQueryDto
     * @return
     */
    @PostMapping("/selectPageList")
    public ResultModel<List<Image>> selectPageList(@RequestBody ImageQueryDto imageQueryDto) {
        IPage<Image> page = imageService.selectPageList(imageQueryDto);
        return ResultModel.success(page);
    }

    /**
     * 根据摄像机ID拍照
     * @param id
     * @return
     */
    @GetMapping("/takePotos/{id}")
    public ResultModel takePotos(@PathVariable("id") Long id) {
        return ResultModel.success(imageService.takePotos(id));
    }

    /**
     * 保存备注信息
     * @param image
     * @return
     */
    @PostMapping("/saveNote")
    public ResultModel<String> saveNote(@RequestBody Image image){
        Boolean result = imageService.saveNote(image);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 根据数据ID删除图片数据
     * @param batchIds
     * @return
     */
    @PostMapping("/batchDel")
    public ResultModel<String> batchDel(@RequestBody @Valid BatchIds<Long> batchIds) {
        imageService.batchDel(batchIds.getIds());
        return ResultModel.success();
    }

    /**
     * 导出截图管理信息
     * @param batchIds
     * @param response
     */
    @PostMapping("/export")
    public void export(@RequestBody @Valid BatchIds<Long> batchIds, HttpServletResponse response) {
        imageService.export(batchIds, response);
    }

}