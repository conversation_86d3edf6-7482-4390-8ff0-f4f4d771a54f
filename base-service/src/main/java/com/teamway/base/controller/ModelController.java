package com.teamway.base.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.teamway.base.dto.model.ModelParamQueryDto;
import com.teamway.base.dto.model.ModelParamVo;
import com.teamway.base.dto.model.ModelQueryDto;
import com.teamway.base.entity.Model;
import com.teamway.base.service.ModelService;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.entity.ResultModel;
import com.teamway.exception.ServiceException;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RequestMapping("/model")
@RestController
public class ModelController {

    @Autowired
    private ModelService modelService;

    @PostMapping("/add")
    public ResultModel<String> add(@RequestBody @Valid Model model) {
        return modelService.addModel(model) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/batchDel")
    public ResultModel<String> batchDel(@RequestBody @Valid BatchIds<Long> batchIds) {
        if (CollectionUtil.isEmpty(batchIds.getIds())) {
            throw new ServiceException(ResultCode.NOT_NULL, "id");
        }
        return modelService.removeBatchByIds(batchIds.getIds()) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/update")
    public ResultModel<String> update(@RequestBody @Valid Model model) {
        return modelService.updateModel(model) ?  ResultModel.success() : ResultModel.fail();
    }

    @GetMapping("/selectPageList")
    public ResultModel<List<Model>> selectPageList(ModelQueryDto queryDto) {
        IPage<Model> page = modelService.selectPageList(queryDto);
        return ResultModel.success(page);
    }

    @GetMapping("/getAll")
    public ResultModel<List<Model>> getAll() {
        List<Model> all = modelService.getAll();
        return ResultModel.success(all);
    }

    @GetMapping("/getParameter")
    public ResultModel<ModelParamVo> getParameter(@Valid ModelParamQueryDto query) {
        ModelParamVo vo = modelService.getParameter(query);
        return ResultModel.success(vo);
    }

}