package com.teamway.base.controller;

import com.teamway.base.dto.poll.PollDto;
import com.teamway.base.entity.Poll;
import com.teamway.base.service.PollService;
import com.teamway.common.entity.ResultModel;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 视频轮巡控制类
 *
 * <AUTHOR> thh
 * @date : 2022/7/25
 */
@RestController
@RequestMapping("/poll")
public class PollController {

    @Autowired
    private PollService pollService;

    /**
     * 添加视频轮巡组
     *
     * @param poll
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/25
     * <AUTHOR> thh
     */
    @PostMapping("/add")
    public ResultModel<String> add(@RequestBody @Valid Poll poll) {
        return pollService.add(poll) > 0 ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 删除视频轮巡组
     *
     * @param id
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/25
     * <AUTHOR> thh
     */
    @DeleteMapping("/delete/{id}")
    public ResultModel<String> delete(@PathVariable @Valid Long id) {
        return pollService.delete(id) > 0 ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 修改视频轮巡组
     *
     * @param poll
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/25
     * <AUTHOR> thh
     */
    @PostMapping("/update")
    public ResultModel<String> update(@RequestBody @Valid Poll poll) {
        return pollService.update(poll) > 0 ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/selectList")
    public ResultModel<List<Poll>> selectList(@RequestBody PollDto pollDto) {
        return ResultModel.success(pollService.selectList(pollDto));
    }
}
