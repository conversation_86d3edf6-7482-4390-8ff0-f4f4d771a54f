package com.teamway.base.controller;

import com.teamway.base.dto.camera.CameraTreeDto;
import com.teamway.base.entity.Region;
import com.teamway.base.service.RegionService;
import com.teamway.base.vo.RegionTreeVo;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 区域控制类
 *
 * <AUTHOR> liucheng
 * @date : 2022/4/14
 */
@RestController
@RequestMapping("/region")
public class RegionController {

    @Autowired
    private RegionService regionService;

    /**
     * 查询区域
     *
     * @param
     * @return : com.teamway.base.common.ResultModel<java.util.List<com.teamway.base.vo.RegionTreeVo>>
     * @date : 2022/7/14
     * <AUTHOR> gss
     */
    @GetMapping("/findTree")
    public ResultModel<List<RegionTreeVo>> findTree() {
        List<RegionTreeVo> regionTree = regionService.getRegionTree();
        return ResultModel.success(regionTree);
    }

    /**
     * 查询区域下摄像机
     *
     * @param cameraTreeDto
     * @return : com.teamway.base.common.ResultModel<java.util.List<com.teamway.base.vo.RegionTreeVo>>
     * @date : 2022/7/17
     * <AUTHOR> zhangsai
     */
    @PostMapping("/findTreeByCamera")
    public ResultModel<List<RegionTreeVo>> findTreeByCamera(@RequestBody CameraTreeDto cameraTreeDto) {
        return ResultModel.success(regionService.findTreeByCamera(cameraTreeDto));
    }

    /**
     * 添加区域
     *
     * @param region
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/14
     * <AUTHOR> gss
     */
    @PostMapping("/add")
    public ResultModel<String> add(@RequestBody @Valid Region region) {
        boolean result = regionService.addRegion(region);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 批量删除区域
     *
     * @param batchIds
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/14
     * <AUTHOR> gss
     */
    @PostMapping("/batchDel")
    public ResultModel<String> batchDel(@RequestBody @Valid BatchIds<Long> batchIds) {
        boolean result = regionService.delBatchRegion(batchIds.getIds());
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 修改区域
     *
     * @param region
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/14
     * <AUTHOR> gss
     */
    @PostMapping("/update")
    public ResultModel<String> update(@RequestBody @Valid Region region) {
        return regionService.updateRegion(region) ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 获取第一级的区域列表
     * @return
     */
    @GetMapping("/getParentTree")
    public ResultModel<List<Region>> getParentTree(){
        return ResultModel.success(regionService.getParentTree());
    }
}
