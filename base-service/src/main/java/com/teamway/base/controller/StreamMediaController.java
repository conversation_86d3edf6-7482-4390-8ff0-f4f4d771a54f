package com.teamway.base.controller;


import com.teamway.base.entity.StreamMedia;
import com.teamway.base.service.StreamMediaService;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 流媒体配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@RestController
public class StreamMediaController {

    @Autowired
    private StreamMediaService streamMediaService;

    /**
     * 提供给流媒体接口
     *
     * @return
     */
    @GetMapping("/videoPlay/getMediaServerInfo")
    public StreamMedia getServiceInfo() {
        return streamMediaService.getById(1);
    }

    @PostMapping("/streamInfo/getStreamInfos")
    public ResultModel<List<StreamMedia>> getStreamInfos() {
        return ResultModel.success(streamMediaService.list());
    }

    @PostMapping("/streamInfo/update")
    public ResultModel<String> update(@RequestBody @Valid StreamMedia streamMedia) {
        return streamMediaService.updateById(streamMedia) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/streamInfo/add")
    public ResultModel<String> add(@RequestBody @Valid StreamMedia streamMedia) {
        return streamMediaService.save(streamMedia) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/streamInfo/batchDel")
    public ResultModel<String> del(@RequestBody @Valid BatchIds<Long> batchIds) {
        return streamMediaService.removeByIds(batchIds.getIds()) ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 根据id获取流媒体信息，1-流媒体配置、2-目标识别视频配置
     *
     * @param id
     * @return
     */
    @GetMapping("/streamInfo/getStreamInfoById/{id}")
    public ResultModel<StreamMedia> getStreamInfoById(@PathVariable Long id) {
        return ResultModel.success(streamMediaService.getById(id));
    }

}
