package com.teamway.base.controller;


import com.teamway.base.dto.superior.CascadeEquipmentDto;
import com.teamway.base.dto.superior.QueryCameraRelationDto;
import com.teamway.base.dto.superior.UpdateNationalRelationDto;
import com.teamway.base.entity.SuperiorCameraRelation;
import com.teamway.base.service.SuperiorCameraRelationService;
import com.teamway.common.entity.ResultModel;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 上级平台摄像机关联表 前端控制器
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Slf4j
@RestController
public class SuperiorCameraRelationController {

    @Autowired
    private SuperiorCameraRelationService relationService;

    /**
     * 获取国标平台关联摄像机    提供给视频
     *
     * @return
     */
    @GetMapping("/platform/getCascadeInfo")
    public List<CascadeEquipmentDto> getCascadeInfo() {
        return relationService.getCascadeInfo();
    }


    @PostMapping("/superiorCameraRelation/selectPageList")
    public ResultModel<List<SuperiorCameraRelation>> selectPageList(@RequestBody @Valid QueryCameraRelationDto queryCameraRelationDto) {
        return ResultModel.success(relationService.selectPageList(queryCameraRelationDto));
    }

    @PostMapping("/superiorCameraRelation/updateBatchById")
    public ResultModel<String> updateBatchById(@RequestBody @Valid UpdateNationalRelationDto relationDto) {
        return relationService.updateBatch(relationDto.getRelationList()) ? ResultModel.success() : ResultModel.fail();
    }

}
