package com.teamway.base.controller;


import com.teamway.base.dto.superior.SuperiorPlatformDto;
import com.teamway.base.dto.superior.SuperiorStatusDto;
import com.teamway.base.entity.SuperiorPlatform;
import com.teamway.base.service.SuperiorPlatformService;
import com.teamway.common.entity.ResultModel;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 上级平台配置
 *
 * <AUTHOR>
 * @since 2022-05-16
 */
@RestController
public class SuperiorPlatformController {

    @Autowired
    private SuperiorPlatformService superiorPlatformService;


    /**
     * 获取上级平台配置  提供给视频
     *
     * @return
     */
    @GetMapping("/platform/getUpPlatformInfo")
    public List<SuperiorPlatformDto> getUpPlatformInfo() {
        return superiorPlatformService.getNationalStandardList();
    }


    @PostMapping("/nationalStandard/update")
    public ResultModel<String> update(@RequestBody @Valid SuperiorPlatform standard) {
        return superiorPlatformService.update(standard) ? ResultModel.success() : ResultModel.fail();
    }

    @GetMapping("/nationalStandard/getById/{id}")
    public ResultModel<SuperiorPlatform> getById(@PathVariable("id") Long id) {
        SuperiorPlatform superiorPlatform = superiorPlatformService.getById(id);
        try {
            //查询上级平台状态
            SuperiorStatusDto status = superiorPlatformService.getStatus(id);
            superiorPlatform.setStatus(status.getStatus());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResultModel.success(superiorPlatform);
    }

    @GetMapping("/nationalStandard/getStatus/{id}")
    public ResultModel<SuperiorStatusDto> getStatus(@PathVariable("id") Long id) {
        return ResultModel.success(superiorPlatformService.getStatus(id));
    }

    @GetMapping("/nationalStandard/sendDeviceInfo/{id}")
    public ResultModel<String> sendDeviceInfo(@PathVariable("id") Long id) {
        return superiorPlatformService.sendDeviceInfo(id) ? ResultModel.success() : ResultModel.fail();
    }

}
