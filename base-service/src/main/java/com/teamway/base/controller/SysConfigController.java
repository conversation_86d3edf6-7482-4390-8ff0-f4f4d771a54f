package com.teamway.base.controller;

import com.teamway.base.entity.SysConfig;
import com.teamway.base.service.SysConfigService;
import com.teamway.base.util.IpUtils;
import com.teamway.common.entity.ResultModel;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @date 2022-10-21
 * @Description: 系统配置
 */
@RequestMapping("/sysConfig")
@RestController
public class SysConfigController {

    @Autowired
    private SysConfigService sysConfigService;

    /**
     * 保存系统配置
     *
     * @param sysConfig
     * @return
     */
    @PostMapping("/saveSysConfig")
    public ResultModel saveSysConfig(@RequestBody @Valid SysConfig sysConfig) {
        sysConfigService.saveSysConfig(sysConfig);
        return ResultModel.success();
    }

    /**
     * 获取系统配置信息
     *
     * @return
     */
    @GetMapping("/selectSysConfig")
    public ResultModel<SysConfig> selectSysConfig(HttpServletRequest request) {
        SysConfig sysConfig = sysConfigService.selectSysConfig();
        if (sysConfig != null) {
            sysConfig.setLocalIp(IpUtils.getClientIp(request));
        }
        return ResultModel.success(sysConfig);
    }

}