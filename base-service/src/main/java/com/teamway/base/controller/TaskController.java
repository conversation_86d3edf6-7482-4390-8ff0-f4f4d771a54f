package com.teamway.base.controller;

import com.teamway.base.service.CameraPresetService;
import com.teamway.base.service.CameraRateRecordService;
import com.teamway.base.service.CameraService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * @description: 定时任务
 * @author: G<PERSON>_Yang
 * @date: 2022/7/12
 **/
@Configuration
@EnableScheduling
@Slf4j
public class TaskController {

    @Autowired
    private CameraService cameraService;
    @Autowired
    private CameraPresetService cameraPresetService;
    @Autowired
    private CameraRateRecordService cameraRateRecordService;

    /**
     * 五分钟同步摄像机状态
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void syncCameraStatus() {
        log.debug("--------开始同步摄像机状态-----------");
        cameraService.reflushStatus();
        log.debug("--------同步摄像机状态结束-----------");
    }

    /**
     * 每天凌晨0点5分   同步预置位信息
     */
    @Scheduled(cron = "0 5 0 * * ?")
    public void syncCameraPresetStatus() {
        log.debug("--------开始同步摄像机预置位信息-----------");
        cameraPresetService.timingRefresh();
        log.debug("--------同步摄像机预置位信息结束-----------");
    }

    /**
     * 一小时   记录摄像机在线率
     */
    @Scheduled(cron = "1 0 * * * ? ")
    public void saveCameraOnLine() {
        log.debug("--------开始记录摄像机在线率-----------");
        boolean save = cameraRateRecordService.save();
        log.debug("--------记录摄像机在线率结束-----------{}", save);
    }

}
