package com.teamway.base.controller;

import com.teamway.base.dto.temperature.GetByCameraIdDto;
import com.teamway.base.dto.temperature.GetTemperatureDrawLineForInsDto;
import com.teamway.base.dto.temperature.TemperatureDrawLineUpdateDto;
import com.teamway.base.service.TemperatureDrawLineService;
import com.teamway.base.vo.GetTemperatureDrawLineForInsVo;
import com.teamway.common.entity.ResultModel;
import com.teamway.thermal.entity.TemperatureRule;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.rmi.ServerException;
import java.util.List;

/**
 * 热成像测温项区域绘制
 *
 * <AUTHOR> liu<PERSON>
 * @date : 2022/6/30
 */
@RestController
public class TemperatureDrawLineController {
    @Autowired
    private TemperatureDrawLineService temperatureDrawLineService;

    /**
     * 批量添加测温项区域
     */
    @PostMapping("/temperatureDrawLine/addBatchOrUpdate")
    public ResultModel<String> addBatchOrUpdate(@Valid @RequestBody TemperatureDrawLineUpdateDto drawLineUpdateDto) {
        boolean result = temperatureDrawLineService.addBatchOrUpdate(drawLineUpdateDto);
        return result ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 获取测温项区域列表
     */
    @PostMapping("/temperatureDrawLine/getByCameraId")
    public ResultModel<TemperatureDrawLineUpdateDto> getByCameraId(@RequestBody GetByCameraIdDto getByCameraIdDto) throws ServerException, InterruptedException {
        TemperatureDrawLineUpdateDto byCameraId = temperatureDrawLineService.getByCameraId(getByCameraIdDto);
        return ResultModel.success(byCameraId);
    }

    /**
     * 获取测温项区域列表
     */
    @PostMapping("/temperatureDrawLine/getTemperatureDrawLineForIns")
    public ResultModel<GetTemperatureDrawLineForInsVo> getTemperatureDrawLineForIns(@RequestBody GetTemperatureDrawLineForInsDto getTemperatureDrawLineForInsDto){
        GetTemperatureDrawLineForInsVo getTemperatureDrawLineForInsVo = temperatureDrawLineService.getTemperatureDrawLineForIns(getTemperatureDrawLineForInsDto);
        return ResultModel.success(getTemperatureDrawLineForInsVo);
    }

    /**
     * 获取测温项区域列表
     */
    @GetMapping("getTemperature")
    public ResultModel<List<TemperatureRule>> getTemperature(String cameraId, String presetId){
        GetTemperatureDrawLineForInsDto getTemperatureDrawLineForInsDto = new GetTemperatureDrawLineForInsDto();
        getTemperatureDrawLineForInsDto.setId(cameraId);
        getTemperatureDrawLineForInsDto.setPresetId(presetId);
        return ResultModel.success(temperatureDrawLineService.getTemperature(getTemperatureDrawLineForInsDto));
    }

}
