package com.teamway.base.controller;

import com.teamway.base.dto.tv.tv.AddSceneDto;
import com.teamway.base.dto.tv.tv.ChangeTvWallWithWindowDto;
import com.teamway.base.dto.tv.tv.DelTvWallWithWindowDto;
import com.teamway.base.dto.tv.tv.EditSceneNameDto;
import com.teamway.base.dto.tv.tv.GetSceneByIdVo;
import com.teamway.base.dto.tv.tv.SelectSceneVo;
import com.teamway.base.dto.tv.tv.UpdateSceneDto;
import com.teamway.base.service.TvWallService;
import com.teamway.common.entity.ResultModel;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/videoManage/tvWall")
public class TvWallController {

    @Autowired
    private TvWallService tvWallService;

    @PostMapping("/addScene")
    public ResultModel<String> addScene(@RequestBody @Valid AddSceneDto addSceneDto) {
        return tvWallService.addScene(addSceneDto) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/updateScene")
    public ResultModel<String> updateScene(@RequestBody @Valid UpdateSceneDto updateSceneDto) {
        return tvWallService.updateScene(updateSceneDto) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/editSceneName")
    public ResultModel<String> editSceneName(@RequestBody @Valid EditSceneNameDto editSceneNameDto) {
        return tvWallService.editSceneName(editSceneNameDto) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/deleteScene/{sceneId}")
    public ResultModel<String> deleteScene(@PathVariable("sceneId") String sceneId) {
        return tvWallService.deleteScene(sceneId) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/selectScene/{decoderDeviceId}")
    public ResultModel<List<SelectSceneVo>> selectScene(@PathVariable("decoderDeviceId") String decoderDeviceId) {
        return ResultModel.success(tvWallService.selectScene(decoderDeviceId));
    }

    @PostMapping("/getSceneById/{sceneId}")
    public ResultModel<GetSceneByIdVo> getSceneById(@PathVariable("sceneId") String sceneId) {
        return ResultModel.success(tvWallService.getSceneById(sceneId));
    }

    @PostMapping("/changeTV/{sceneId}")
    public ResultModel<String> changeTv(@PathVariable("sceneId") String sceneId) {
        return tvWallService.changeTv(sceneId) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/changeTvWallWithWindow")
    public ResultModel<String> changeTvWallWithWindow(@RequestBody @Valid ChangeTvWallWithWindowDto changeTvWallWithWindowDto) {
        return tvWallService.changeTvWallWithWindow(changeTvWallWithWindowDto) ? ResultModel.success() : ResultModel.fail();
    }

    @PostMapping("/delTvWallWithWindow")
    public ResultModel<String> delTvWallWithWindow(@RequestBody @Valid DelTvWallWithWindowDto delTvWallWithWindowDto) {
        return tvWallService.delTvWallWithWindow(delTvWallWithWindowDto) ? ResultModel.success() : ResultModel.fail();
    }
}
