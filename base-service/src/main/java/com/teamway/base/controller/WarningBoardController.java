package com.teamway.base.controller;

import com.teamway.base.entity.WarningBoard;
import com.teamway.base.service.WarningBoardService;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 首页红黄黑榜单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20
 */
@Slf4j
@RestController
@RequestMapping("/warningBoard")
public class WarningBoardController {

    @Autowired
    private WarningBoardService warningBoardService;

    /**
     * 添加红黄黑榜记录
     * @param warningBoard 红黄黑榜记录信息
     * @return ResultModel<Boolean> 添加结果
     */
    @PostMapping("/add")
    public ResultModel<Boolean> add(@RequestBody WarningBoard warningBoard) {
        return ResultModel.success(warningBoardService.addWarningBoard(warningBoard));
    }

    /**
     * 更新红黄黑榜记录
     * @param warningBoard 红黄黑榜记录信息
     * @return ResultModel<Boolean> 更新结果
     */
    @PostMapping("/update")
    public ResultModel<Boolean> update(@RequestBody WarningBoard warningBoard) {
        return ResultModel.success(warningBoardService.updateWarningBoard(warningBoard));
    }

    /**
     * 批量删除红黄黑榜记录
     * @param request 删除请求参数
     * @return ResultModel<Boolean> 删除结果
     */
    @PostMapping("/batchDelete")
    public ResultModel<Boolean> batchDelete(@RequestBody BatchIds request) {
        return ResultModel.success(warningBoardService.batchDeleteWarningBoard(request));
    }

    /**
     * 获取红黄黑榜记录详情
     * @param id 记录ID
     * @return ResultModel<WarningBoard> 红黄黑榜记录详情
     */
    @GetMapping("/get/{id}")
    public ResultModel<WarningBoard> getById(@PathVariable("id") Long id) {
        return ResultModel.success(warningBoardService.getWarningBoardById(id));
    }

    /**
     * 获取红黄黑榜记录列表
     * @param warningType 红黄黑榜类型：1-黄牌，2-红牌，3-黑牌，不传则查询所有
     * @return ResultModel<List<WarningBoard>> 红黄黑榜记录列表
     */
    @GetMapping("/list")
    public ResultModel<List<WarningBoard>> list(@RequestParam(value = "warningType", required = false) Integer warningType) {
        return ResultModel.success(warningBoardService.getWarningBoardList(warningType));
    }
}

