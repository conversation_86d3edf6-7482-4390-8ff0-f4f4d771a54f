package com.teamway.base.factory;

import com.teamway.base.common.constant.CameraTypeConstant;
import com.teamway.base.common.constant.IconTypeConstant;
import com.teamway.base.entity.ElementInfo;
import com.teamway.base.enums.ElementTypeEnum;
import com.teamway.base.mapper.CameraMapper;
import com.teamway.common.entity.ResultCode;
import com.teamway.commonapi.api.security.SecurityDeviceApi;
import com.teamway.commonapi.entity.SecurityDeviceInfo;
import com.teamway.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName ElementProcessorFactory
 * @Description 元素处理器工厂，基于策略模式实现不同类型元素的处理
 * <AUTHOR>
 * @createDate 2025/5/14
 * @version 1.0
 **/
@Component
@Slf4j
public class ElementProcessorFactory {

    @Autowired
    private CameraMapper cameraMapper;

    @Autowired
    private SecurityDeviceApi securityDeviceApi;

    /**
     * 元素处理器映射
     */
    private final Map<ElementTypeEnum, Consumer<ElementInfo>> processorMap = new HashMap<>();

    /**
     * 初始化处理器映射
     */
    @PostConstruct
    public void init() {
        processorMap.put(ElementTypeEnum.CAMERA, this::processCameraElement);
        processorMap.put(ElementTypeEnum.ACCESS_CONTROL, element -> processSecurityDeviceElement(element, ElementTypeEnum.ACCESS_CONTROL));
        processorMap.put(ElementTypeEnum.BARRIER, element -> processSecurityDeviceElement(element, ElementTypeEnum.BARRIER));
        processorMap.put(ElementTypeEnum.ENVIRONMENT, this::processOtherElement);
        processorMap.put(ElementTypeEnum.FIRE, this::processOtherElement);
        processorMap.put(ElementTypeEnum.SECURITY, this::processOtherElement);
    }

    /**
     * 获取指定类型的元素处理器
     *
     * @param type 元素类型
     * @return 处理器函数
     */
    public Consumer<ElementInfo> getProcessor(ElementTypeEnum type) {
        return Optional.ofNullable(processorMap.get(type))
                .orElseThrow(() -> new ServiceException(ResultCode.OPERATION_FAILURE,
                        "未找到类型为 " + type.getDesc() + " 的处理器"));
    }

    /**
     * 处理摄像机类型元素
     *
     * @param elementInfo 元素信息
     */
    private void processCameraElement(ElementInfo elementInfo) {
        // 使用Optional避免空指针异常
        Optional.ofNullable(cameraMapper.selectById(elementInfo.getObjectId()))
                .ifPresentOrElse(
                        camera -> {
                            // 设置区域ID
                            elementInfo.setRegionId(camera.getRegionId());

                            // 设置图标类型 - 默认球机，如果是枪机则修改
                            String iconType = CameraTypeConstant.TYPE_GUN.equals(camera.getType())
                                    ? IconTypeConstant.GUN
                                    : IconTypeConstant.BALL;
                            elementInfo.setIconType(iconType);
                        },
                        () -> {
                            throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
                        }
                );
    }

    /**
     * 处理安防设备元素（门禁、道闸）
     *
     * @param elementInfo 元素信息
     * @param elementType 元素类型
     */
    private void processSecurityDeviceElement(ElementInfo elementInfo, ElementTypeEnum elementType) {
        // 设备类型名称（用于错误消息）
        String deviceTypeName = elementType.getDesc();

        // 通过公共接口获取设备信息
        SecurityDeviceInfo deviceInfo = securityDeviceApi.getSecurityDeviceById(elementInfo.getObjectId());

        // 使用Optional处理可能为null的情况
        Optional.ofNullable(deviceInfo)
                .ifPresentOrElse(
                        device -> {
                            // 设置区域ID，注意处理可能的类型转换
                            if (device.getRegionId() != null) {
                                elementInfo.setRegionId(device.getRegionId());
                            }

                            // 设置图标类型
                            String iconType = elementType == ElementTypeEnum.ACCESS_CONTROL
                                    ? IconTypeConstant.DOOR
                                    : IconTypeConstant.BARRIER_GATE;
                            elementInfo.setIconType(iconType);
                        },
                        () -> {
                            throw new ServiceException(ResultCode.NOT_EXIST, deviceTypeName);
                        }
                );
    }

    /**
     * 处理其他类型的元素
     *
     * @param elementInfo 元素信息
     */
    private void processOtherElement(ElementInfo elementInfo) {
    }
}