package com.teamway.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teamway.base.entity.ElementInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 地图元素表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-15
 */
public interface ElementInfoMapper extends BaseMapper<ElementInfo> {

    /**
     * 根据区域id查询元素和摄像机
     * @param regionId
     * @return
     */
    List<ElementInfo> findByParam(@Param("regionId") Long regionId);
}
