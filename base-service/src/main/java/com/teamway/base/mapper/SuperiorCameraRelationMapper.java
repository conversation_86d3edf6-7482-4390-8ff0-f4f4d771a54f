package com.teamway.base.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.teamway.base.entity.SuperiorCameraRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 摄像机国标平台关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
public interface SuperiorCameraRelationMapper extends BaseMapper<SuperiorCameraRelation> {

    /**
     * 根据条件分页查询
     *
     * @param wrapper 查询条件
     * @return 巡检点数据
     */
    Page<SuperiorCameraRelation> selectPageList(IPage<?> page, @Param(Constants.WRAPPER) Wrapper<SuperiorCameraRelation> wrapper);

    /**
     * 根据条件查询所有 摄像机并关联查询主子ID
     *
     * @param wrapper 查询条件
     * @return
     */
    List<SuperiorCameraRelation> selectAll(Wrapper<SuperiorCameraRelation> wrapper);

}
