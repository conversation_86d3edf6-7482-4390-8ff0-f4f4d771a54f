package com.teamway.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teamway.base.dto.temperature.DrawLineCameraDto;
import com.teamway.base.entity.TemperatureDrawLine;

import java.util.List;

/**
 * 针对表【temperature_draw_line(红外热成像摄像机测温框)】的数据库操作Mapper
 *
 * <AUTHOR> liu<PERSON>
 * @date : 2022/6/30
 */
public interface TemperatureDrawLineMapper extends BaseMapper<TemperatureDrawLine> {
    /**
     * 查询摄像机阈值
     * @return
     */
    List<DrawLineCameraDto> listThresholdValueCamera();
}




