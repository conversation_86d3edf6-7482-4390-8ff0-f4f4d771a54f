package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.config.AlarmConditionQueryDto;
import com.teamway.base.dto.config.AlarmConfigQueryDto;
import com.teamway.base.dto.voice.AlarmKeepDto;
import com.teamway.base.entity.AlarmConfig;
import com.teamway.base.vo.AlarmAllConfigVo;
import com.teamway.base.vo.AlarmConditionVo;

import java.util.List;

/**
 * <p>
 * 告警配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-08
 */
public interface AlarmConfigService extends IService<AlarmConfig> {

    /**
     * 添加或修改
     *
     * @param alarmKeepDto@return
     */
    boolean updateAlarmConfig(AlarmKeepDto alarmKeepDto);

    /**
     * 根据巡检点查询告警配置
     *
     * @param alarmConfigQueryDto
     * @return
     */
    AlarmConfig selectDetailByPoint(AlarmConfigQueryDto alarmConfigQueryDto);

    /**
     * 根据巡检点位算法ID查询配置信息
     *
     * @param insPointAlgId
     * @param enable
     * @return
     */
    List<AlarmConfig> selectInsAlarmConfigByInsPointAlgId(Long insPointAlgId, String enable);

    /**
     * 根据算法ID查询配置信息
     *
     * @param algId
     * @return
     */
    List<AlarmConfig> selectInsAlarmConfigByAlgId(Long algId);

    /**
     * 查询告警配置  条件
     *
     * @param alarmConditionQueryDto
     * @return
     */
    List<AlarmConditionVo> selectCondition(AlarmConditionQueryDto alarmConditionQueryDto);

    /**
     * 查询告警配置
     *
     * @param alarmConfigQueryDto
     * @return
     */
    AlarmAllConfigVo selectList(AlarmConfigQueryDto alarmConfigQueryDto);

}
