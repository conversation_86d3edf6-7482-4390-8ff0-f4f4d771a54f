package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.entity.AlgorithmDetail;

import java.util.List;

/**
 * <AUTHOR> hlc
 * @date : 2022/4/14
 */
public interface AlgorithmDetailService extends IService<AlgorithmDetail> {


    /**
     * 根据类型名称和算法id查询算法详情
     * @param typeName
     * @param algId
     * @return
     */
    AlgorithmDetail selectByTypeNameAndAlgId(String typeName, Long algId);


    /**
     * 根据算法ID查询算法明细
     *
     * @param algorithmId
     * @return
     */
    List<AlgorithmDetail> selectByAlgorithmId(Long algorithmId);

    /**
     * 根据算法ID与算法名称查询数据
     *
     * @param algId
     * @param algName
     * @return
     */
    AlgorithmDetail selectByAlgIdAndAlgName(Long algId, String algName);

}
