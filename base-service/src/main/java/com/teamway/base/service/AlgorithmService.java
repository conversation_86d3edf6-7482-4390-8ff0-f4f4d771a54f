package com.teamway.base.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.algorithm.AlgorithmQueryDto;
import com.teamway.base.entity.Algorithm;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> hlc
 * @date : 2022/4/14
 */
public interface AlgorithmService extends IService<Algorithm> {


    /**
     * 新增算法类型
     *
     * @param algorithm
     * @return
     */
    int add(Algorithm algorithm);

    /**
     * 根据算法名称查询算法
     *
     * @param algName
     * @return
     */
    Algorithm selectAlgorithmByAlgName(String algName);

    /**
     * 根据算法描述查询算法
     *
     * @param algCode
     * @return
     */
    Algorithm selectAlgorithmByAlgCode(String algCode);

    /**
     * 删除算法类型
     *
     * @param ids
     * @return
     */
    int batchDel(List<Long> ids);

    /**
     * 更新算法类型
     *
     * @param algorithm
     * @return
     */
    int update(Algorithm algorithm);

    /**
     * 分页查询算法配置信息
     *
     * @param algorithmQueryDto
     * @return
     */
    IPage<Algorithm> selectAlgorithm(AlgorithmQueryDto algorithmQueryDto);

    /**
     * 查询所有算法配置信息
     *
     * @param flag 算法类型  1-巡检类 2-人员类 3-输煤类 4-红外测温 5-巡视+红外
     * @return
     */
    List<Algorithm> selectAll(Integer flag);

    /**
     * 根据算法名称查询算法详情信息
     *
     * @param algCode
     * @return
     */
    Algorithm getAlgorithmByCode(String algCode);

    /**
     * 导出
     *
     * @param algorithmQueryDto
     * @param response
     */
    void exportExcel(AlgorithmQueryDto algorithmQueryDto, HttpServletResponse response);

    /**
     * 导入
     *
     * @param file
     * @param response
     */
    void importExcel(MultipartFile file, HttpServletResponse response);

    /**
     * 根据算法id查询算法信息
     *
     * @param id
     * @return
     */
    Algorithm getAlgorithmById(Long id);
}
