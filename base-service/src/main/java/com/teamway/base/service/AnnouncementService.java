package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.entity.Announcement;
import com.teamway.common.entity.BatchIds;

import java.util.List;

/**
 * <p>
 * 首页公告表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20
 */
public interface AnnouncementService extends IService<Announcement> {
    /**
     * 添加公告
     * @param announcement 公告信息
     * @return 添加结果
     */
    Boolean addAnnouncement(Announcement announcement);

    /**
     * 更新公告
     * @param announcement 公告信息
     * @return 更新结果
     */
    Boolean updateAnnouncement(Announcement announcement);

    /**
     * 批量删除公告
     *
     * @param request 删除请求参数
     * @return Boolean true-删除成功 false-删除失败
     */
    Boolean batchDeleteAnnouncement(BatchIds request);

    /**
     * 获取公告详情
     * @param id 公告ID
     * @return 公告信息
     */
    Announcement getAnnouncementById(Long id);

    /**
     * 获取公告列表
     * @return 公告列表
     */
    List<Announcement> getAnnouncementList();

    /**
     * 更新公告滚动状态
     * @param id 公告ID
     * @param scrollStatus 滚动状态
     * @return 更新结果
     */
    Boolean updateScrollStatus(Long id, Integer scrollStatus);
}
