package com.teamway.base.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.dir.DirDto;
import com.teamway.base.entity.BaseSysDir;
import com.teamway.base.entity.BaseSysSubDir;
import com.teamway.base.vo.DirExportVo;
import com.teamway.common.entity.BatchIds;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yiju
 * @date 2025-03-03
 * @description 系统字典类服务接口
 */
public interface BaseSysDirService extends IService<BaseSysDir> {

    /**
     * 新增字典
     * @param baseSysDir
     * @return
     */
    boolean saveDir(BaseSysDir baseSysDir);

    /**
     * 删除字典
     * @param ids
     * @return
     */
    boolean deleteDir(BatchIds<Long> ids);

    /**
     * 查询字典列表
     * @param dirDto
     * @return
     */
    IPage<BaseSysDir> listDir(DirDto dirDto);

    /**
     * 导出字典 json
     * @param exportIds
     * @param response
     * @return
     */
    void exportDir(BatchIds<Long> exportIds, HttpServletResponse response);


    /**
     * 根据id查询子字典
     * @param ids
     * @return
     */
    Map<Long,List<BaseSysSubDir>> getSubDirList(List<Long> ids);


    /**
     * 全量导入字典
     * @param type
     * @param file
     * @return
     */
    boolean importDir(String type,MultipartFile file);

    /**
     * 返回字典数据
     * @return
     */
    List<BaseSysDir> getDirList();

    /**
     * 刷新redis缓存
     * @return
     */
    boolean refreshDirCache();
}
