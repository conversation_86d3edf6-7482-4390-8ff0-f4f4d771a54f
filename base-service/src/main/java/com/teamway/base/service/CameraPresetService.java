package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.preset.CameraPresetQueryDto;
import com.teamway.base.dto.preset.CameraPresetRefreshDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CameraPreset;
import com.teamway.base.vo.CameraPresetVo;
import com.teamway.common.entity.BatchIds;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-25
 */
public interface CameraPresetService extends IService<CameraPreset> {

    /**
     * 查询摄像机预置位列表
     *
     * @param cameraPresetQueryDto
     * @return List<CameraPreset>
     */
    List<CameraPreset> findList(CameraPresetQueryDto cameraPresetQueryDto);

    /**
     * 查询摄像机预置位列表
     *
     * @param ids
     * @return List<CameraPresetListVo>
     */
    List<CameraPresetVo> selectByCameraIds(BatchIds<Long> ids);

    /**
     * 新增摄像机预置位信息
     *
     * @param cameraPreset
     * @return boolean
     */
    boolean addCameraPreset(CameraPreset cameraPreset);

    /**
     * 更新摄像机预置位信息
     *
     * @param cameraPreset
     * @return boolean
     */
    boolean updateCameraPreset(CameraPreset cameraPreset);

    /**
     * 删除摄像机预置位信息
     *
     * @param batchIds
     * @return boolean
     */
    boolean delBatchCameraPreset(BatchIds<Long> batchIds);

    /**
     * 手动刷新摄像机中预置位
     *
     * @param cameraPresetRefreshDto
     * @return
     */
    boolean refresh(CameraPresetRefreshDto cameraPresetRefreshDto);

    /**
     * 刷新摄像机中预置位
     *
     * @param camera
     * @return
     */
    boolean refresh(Camera camera);

    /**
     * 定时刷新摄像机中预置位
     */
    void timingRefresh();

}
