package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.entity.CameraRateRecord;

import java.util.List;

/**
 * <p>
 * 摄像机在线率记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-13
 */
public interface CameraRateRecordService extends IService<CameraRateRecord> {

    /**
     * 保存摄像机在线率
     * @return
     */
    boolean save();

    /**
     * 根据时间类型  查询摄像机在线率记录
     *
     * @param type 1-当天、2-当周、3-当月、4当年
     * @return
     */
    List<CameraRateRecord> getCameraOnLineRate(Integer type);

}
