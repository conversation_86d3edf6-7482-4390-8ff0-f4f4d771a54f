package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.camera.*;
import com.teamway.base.entity.Camera;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 针对表【camera(摄像机配置表)】的数据库操作Service
 *
 * <AUTHOR> liucheng
 * @date : 2022/4/14
 */
public interface CameraService extends IService<Camera> {
    /**
     * 巡检组件提供摄像机配置查询功能，查询摄像机名称等信息。
     *
     * @param cameraQueryDto
     * @return : java.util.List<com.teamway.inspection.entity.Camera>
     * @date : 2022/4/14
     * <AUTHOR> liucheng
     */
    Page<Camera> findPageList(CameraQueryDto cameraQueryDto);

    /**
     * 巡检组件提供摄像机配置添加功能，添加摄像机名称等信息
     *
     * @param camera
     * @return : boolean
     * @date : 2022/4/14
     * <AUTHOR> liucheng
     */
    Camera addCamera(Camera camera);

    /**
     * 批量添加摄像机
     *
     * @param saveCameraListDto
     * @return : boolean
     * @date : 2023/3/15
     * <AUTHOR> zhangsai
     */
    boolean batchAdd(SaveCameraListDto saveCameraListDto);

    /**
     * 批量修改摄像机
     *
     * @param updateCameraDto
     * @return : boolean
     * @date : 2023/3/15
     * <AUTHOR> zhangsai
     */
    boolean batchUpdate(UpdateCameraDto updateCameraDto);

    /**
     * 批量删除摄像机
     *
     * @param ids
     * @return : boolean
     * @date : 2022/4/14
     * <AUTHOR> liucheng
     */
    boolean delBatchCamera(List<Long> ids);

    /**
     * 更新摄像机信息
     *
     * @param camera
     * @return : boolean
     * @date : 2022/4/14
     * <AUTHOR> liucheng
     */
    boolean updateCamera(Camera camera);

    /**
     * 导入摄像机
     *
     * @param file
     * @return CameraImportDto
     * @date : 2022/7/12
     * <AUTHOR> zhangsai
     */
    CameraImportDto importExcel(MultipartFile file);

    /**
     * 导出摄像机
     *
     * @param cameraQueryDto
     * @param response
     * @return
     * @date : 2022/7/12
     * <AUTHOR> zhangsai
     */
    void exportExcel(CameraQueryDto cameraQueryDto, HttpServletResponse response);

    /**
     * 下载模板
     *
     * @param response
     * @return
     * @date : 2022/7/12
     * <AUTHOR> zhangsai
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 摄像机抓图
     *
     * @param cameraSnapshotDto
     * @return : com.teamway.inspection.common.ResultModel<java.lang.String>
     * @date : 2022/4/14
     * <AUTHOR> liucheng
     */
    String snapshot(CameraSnapshotDto cameraSnapshotDto);

    /**
     * 获取摄像机抓图URL
     *
     * @param id
     * @return : java.lang.String
     * @date : 2023/6/8
     * <AUTHOR> zhangsai
     */
    String snapshotUrl(Long id);

    /**
     * 获取摄像机URL   onvif、rtsp
     *
     * @param cameraUrlDto
     * @return : java.lang.String
     * @date : 2025/2/19
     * <AUTHOR> zhangsai
     */
    String getCameraUrl(CameraUrlDto cameraUrlDto);

    /**
     * 刷新摄像机状态
     *
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/12
     * <AUTHOR> Gaven
     */
    void reflushStatus();

    /**
     *  刷新摄像机视频url
     */
    void reflushVideoUrl();

    /**
     * 获取摄像机详情
     *
     * @param id
     * @return com.teamway.base.entity.Camera
     * <AUTHOR>
     * @createTime 2022-11-03
     **/
    Camera getCameraById(Long id);

    /**
     * 根据摄像机状态查询数量
     *
     * @param state
     * @return
     */
    Long selectCameraNumByState(Integer state);


    /**
     * 查询摄像机在线离线数量
     *
     * @return
     */
    CameraDto selectCameraOnAndOffNum();

    /**
     * 获取区域下的摄像机列表
     *
     * @param regionIdList
     * @return
     */
    List<Long> getCameraIdByRegionId(List<Long> regionIdList);


    /**
     * 根据摄像机id查询该摄像机下所有类型的摄像机信息
     * @param cameraId
     * @return
     */
    List<Camera> getCameraAndTypeById(Long cameraId);
}
