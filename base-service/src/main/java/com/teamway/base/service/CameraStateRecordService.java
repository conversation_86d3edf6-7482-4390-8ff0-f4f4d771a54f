package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.entity.CameraStateRecord;

import java.util.List;

/**
 * <p>
 * 摄像机状态记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
public interface CameraStateRecordService extends IService<CameraStateRecord> {

    /**
     * 根据摄像机id集合  批量保存记录
     *
     * @param cameraIds
     * @param state 1-在线  0-离线
     * @return
     */
    boolean batchSave(List<Long> cameraIds, String state);

}
