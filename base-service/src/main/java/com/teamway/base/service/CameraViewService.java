package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.CameraView.CameraViewDto;
import com.teamway.base.dto.CameraView.CameraViewIdsDto;
import com.teamway.base.dto.CameraView.CameraViewListDto;
import com.teamway.base.entity.CameraView;
import com.teamway.base.vo.CameraViewListVo;

/**
 * <AUTHOR>
 */
public interface CameraViewService extends IService<CameraView> {
    /**
     * 存储个人或公共视图
     * @param dto
     * @return
     */
    Boolean viewStorage(CameraViewDto dto);

    /**
     * 修改个人或公共视图
     * @param dto
     * @return
     */
    Boolean updateView(CameraViewDto dto);

    /**
     * 删除个人或公共视图
     * @param dto
     * @return
     */
    Boolean deleteView(CameraViewIdsDto dto);

    /**
     * 查看个人或公共视图列表
     *
     * @param dto
     * @return
     */
    CameraViewListVo selectCameraViewList(CameraViewListDto dto);
}
