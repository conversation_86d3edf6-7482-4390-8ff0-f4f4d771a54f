package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.entity.CameraWatch;

/**
 * 摄像机监控配置服务接口
 * <AUTHOR>
 */
public interface CameraWatchService extends IService<CameraWatch> {

    /**
     * @param cameraWatch
     * @return
     */
    Boolean addWatch(CameraWatch cameraWatch);

    /**
     * 根据id查询守望位信息
     * @param id
     * @return
     */
    CameraWatch getWatchById(Long id);
}