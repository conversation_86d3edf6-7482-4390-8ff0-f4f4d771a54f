package com.teamway.base.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.check.CheckPointBatchUpdateDto;
import com.teamway.base.dto.check.CheckPointDto;
import com.teamway.base.dto.check.CheckPointListDto;
import com.teamway.base.dto.check.CheckSaveDto;
import com.teamway.base.dto.check.DeleteCheckDto;
import com.teamway.base.dto.check.UrlChangeBaseDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CheckPoint;
import com.teamway.base.vo.AlgListVo;
import com.teamway.base.vo.CheckPointVo;
import com.teamway.model.entity.AlgorithmConfig;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CheckPointService extends IService<CheckPoint> {

    /**
     * 查询检测点列表
     *
     * @param pointDto
     * @return
     */
    Page<CheckPointVo> listCheckPoint(CheckPointListDto pointDto);

    /**
     * 根据摄像机id查询该摄像机下绑定算法
     * @param cameraId
     * @return
     */
    List<AlgorithmConfig> analyticStructure(Long cameraId);

    /**
     * 修改检测点
     * @param pointDto
     * @return
     */
    Boolean updateCheckPoint(CheckPointDto pointDto);

    /**
     * 删除检测点
     *
     * @param checkDto
     * @return
     */
    Boolean deleteCheckPoint(DeleteCheckDto checkDto);

    /**
     * 根据摄像机id查询算法名称
     *
     * @param cameraId
     * @return
     */
    AlgListVo queryAlgNameByCameraId(Long cameraId);

    /**
     * 保存检测点信息
     *
     * @param saveDto
     * @return
     */
    Boolean saveCheck(CheckSaveDto saveDto);

    /**
     * 将 URL 转换为 Base64 编码
     * @param urlChangeBaseDto
     * @return
     * @throws IOException
     */
    String urlChangeBase(UrlChangeBaseDto urlChangeBaseDto) throws IOException;

    /**
     * 批量修改
     * @param dto
     */
    void batchUpdate(CheckPointBatchUpdateDto dto);

    /**
     * 查询摄像机下检测点
     *
     * @return
     */
    List<Camera> getCameraCheckPoint();
}