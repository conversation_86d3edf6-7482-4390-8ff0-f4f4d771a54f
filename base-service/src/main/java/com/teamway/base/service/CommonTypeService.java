package com.teamway.base.service;

import com.teamway.base.dto.other.CommonTypeDto;
import com.teamway.base.entity.CommonType;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 通用类型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface CommonTypeService extends IService<CommonType> {

    /**
     *获取工种列表
     * @return
     */
    List<CommonType> getPersonJobList();
    /**
     *获取部门列表
     * @return
     */
    List<CommonType> getPersonDepartmentList();

    /**
     *获取岗位列表
     * @return
     */
    List<CommonType> getPersonPostList();

    /**
     *获取人员单位列表
     * @return
     */
    List<CommonType> getPersonUnitList();

    List<CommonType> getCarColorList();

    List<CommonType> getTypeListByCategory(CommonTypeDto commonTypeDto);

    String getNameByTypeCodeAndCategory(String personUnitCode, String personUnit);
}
