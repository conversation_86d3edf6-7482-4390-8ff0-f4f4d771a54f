package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.tv.decoder.DecoderDelDto;
import com.teamway.base.entity.Decoder;
import com.teamway.common.entity.ResultModel;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
public interface DecoderService extends IService<Decoder> {

    /**
     * 新增解码器
     * @param decoder
     * @return
     */
    boolean add(Decoder decoder);

    /**
     * 修改解码器
     * @param decoder
     * @return
     */
    boolean updateDecoder(Decoder decoder);

    /**
     * 删除解码器
     * @param idDto
     * @return
     */
    ResultModel<String> del(DecoderDelDto idDto);

    /**
     * 查询解码器信息
     * @return
     */
    List<Decoder> listAndSynthesisLayout();
}
