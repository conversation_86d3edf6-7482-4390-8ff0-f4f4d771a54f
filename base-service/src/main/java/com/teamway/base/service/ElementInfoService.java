package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.element.QueryElementDto;
import com.teamway.base.dto.element.UpdatePositionDto;
import com.teamway.base.entity.ElementInfo;

import java.util.List;

/**
 * <p>
 * 地图元素表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-15
 */
public interface ElementInfoService extends IService<ElementInfo> {

    /**
     * 新增元素
     *
     * @param elementInfoList
     * @return
     */
    boolean add(List<ElementInfo> elementInfoList);

    /**
     * 修改元素坐标
     *
     * @param positionDto
     * @return
     */
    int update(UpdatePositionDto positionDto);

    /**
     * 批量删除元素
     *
     * @param ids
     * @return
     */
    int delete(List<Long> ids);

    /**
     * 根据区域id查询元素
     *
     * @param regionId
     * @return
     */
    List<ElementInfo> findByParam(Long regionId);

    /**
     * 根据条件查询元素
     *
     * @param queryElementDto
     * @return
     */
    List<ElementInfo> selectList(QueryElementDto queryElementDto);


}
