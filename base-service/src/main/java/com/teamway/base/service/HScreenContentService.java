package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.entity.HScreenContent;
import com.teamway.common.entity.BatchIds;

import java.util.List;

/**
 * <p>
 * 横屏显示内容配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface HScreenContentService extends IService<HScreenContent> {

    /**
     * 添加横屏显示内容配置
     * @param hScreenContent 横屏显示内容配置信息
     * @return Boolean 添加结果
     */
    Boolean addHScreenContent(HScreenContent hScreenContent);

    /**
     * 更新横屏显示内容配置
     * @param hScreenContent 横屏显示内容配置信息
     * @return Boolean 更新结果
     */
    Boolean updateHScreenContent(HScreenContent hScreenContent);

    /**
     * 批量删除横屏显示内容配置
     * @param request 删除请求参数
     * @return Boolean 删除结果
     */
    Boolean batchDeleteHScreenContent(BatchIds request);

    /**
     * 获取横屏显示内容配置详情
     * @param id 配置ID
     * @return HScreenContent 横屏显示内容配置详情
     */
    HScreenContent getHScreenContentById(Long id);

    /**
     * 获取横屏显示内容配置列表
     * @param screenId 横屏配置ID
     * @return List<HScreenContent> 横屏显示内容配置列表
     */
    List<HScreenContent> getHScreenContentList(Long screenId);
}