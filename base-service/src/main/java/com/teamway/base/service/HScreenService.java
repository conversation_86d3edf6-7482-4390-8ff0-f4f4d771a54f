package com.teamway.base.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.screen.HScreenQueryDto;
import com.teamway.base.entity.HScreen;
import com.teamway.common.entity.BatchIds;

import java.util.List;

/**
 * <p>
 * 横屏配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface HScreenService extends IService<HScreen> {

    /**
     * 添加横屏配置
     */
    Boolean addHScreen(HScreen hScreen);

    /**
     * 更新横屏配置
     */
    Boolean updateHScreen(HScreen hScreen);

    /**
     * 批量删除横屏配置
     */
    Boolean batchDeleteHScreen(BatchIds request);

    /**
     * 获取横屏配置详情
     */
    HScreen getHScreenById(Long id);

    /**
     * 分页查询横屏配置列表
     */
    IPage<HScreen> findPageList(HScreenQueryDto queryDto);
}