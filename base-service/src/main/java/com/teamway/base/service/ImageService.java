package com.teamway.base.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.screenshot.ImageQueryDto;
import com.teamway.base.entity.Image;
import com.teamway.common.entity.BatchIds;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-20
 * @Description: 摄像机截图管理
 */
public interface ImageService extends IService<Image> {

    /**
     * 摄像机拍照
     * @param cameraId
     * @return
     */
    Image takePotos(Long cameraId);

    /**
     * 批量删除图片
     * @param ids
     * @return
     */
    int batchDel(List<Long> ids);

    /**
     * 分页查询摄像机图片
     * @param imageQueryDto
     * @return
     */
    IPage<Image> selectPageList(ImageQueryDto imageQueryDto);


    /**
     * 导出截图信息
     * @param batchIds
     * @param response
     */
    void export(BatchIds<Long> batchIds, HttpServletResponse response);


    /**
     * 下载图片
     * @param imaUrl
     * @return
     */
    byte[] downFile(String imaUrl);


    /**
     * 保存备注信息
     * @param image
     * @return
     */
    Boolean saveNote(Image image);
}
