package com.teamway.base.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.model.ModelParamQueryDto;
import com.teamway.base.dto.model.ModelParamVo;
import com.teamway.base.dto.model.ModelQueryDto;
import com.teamway.base.entity.Model;

import java.util.List;

/**
 * <AUTHOR> hlc
 * @date : 2022/4/14
 */
public interface ModelService extends IService<Model> {

    /**
     * 分页查询
     */
    IPage<Model> selectPageList(ModelQueryDto queryDto);

    /**
     * 获取所有
     */
    List<Model> getAll();

    /**
     * 组装流媒体推流接口参数
     */
    ModelParamVo getParameter(ModelParamQueryDto query);

    /**
     * 新增
     * @param model
     */
    boolean addModel(Model model);

    /**
     * 修改
     * @param model
     */
    boolean updateModel(Model model);
}
