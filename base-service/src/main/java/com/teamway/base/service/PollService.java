package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.poll.PollDto;
import com.teamway.base.entity.Poll;

import java.util.List;

/**
 * <p>
 * 视频轮巡表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-25
 */
public interface PollService extends IService<Poll> {

    /**
     * 新增视频轮巡信息
     *
     * @param poll
     * @return
     */
    int add(Poll poll);

    /**
     * 删除视频轮巡信息
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 修改视频轮巡信息
     *
     * @param poll
     * @return
     */
    int update(Poll poll);

    /**
     * 查询视频轮巡信息
     *
     * @param pollDto
     * @return
     */
    List<Poll> selectList(PollDto pollDto);
}


