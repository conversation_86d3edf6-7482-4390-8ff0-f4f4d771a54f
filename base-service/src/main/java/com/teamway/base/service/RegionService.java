package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.camera.CameraTreeDto;
import com.teamway.base.entity.Region;
import com.teamway.base.vo.RegionTreeVo;

import java.util.List;
import java.util.Map;

/**
 * 针对表【region(区域表)】的数据库操作Service
 *
 * <AUTHOR> liucheng
 * @date : 2022/4/14
 */
public interface RegionService extends IService<Region> {
    /**
     * 查询区域树
     *
     * @param
     * @return : java.util.List<com.teamway.inspection.vo.RegionTreeVo>
     * @date : 2022/4/14
     * <AUTHOR> liucheng
     */
    List<RegionTreeVo> getRegionTree();

    /**
     * 查询区域树
     *
     * @param cameraTreeDto@return : java.util.List<com.teamway.inspection.vo.RegionTreeVo>
     * @date : 2022/7/17
     * <AUTHOR> zhangsai
     */
    List<RegionTreeVo> findTreeByCamera(CameraTreeDto cameraTreeDto);

    /**
     * 添加区域
     *
     * @param region
     * @return : boolean
     * @date : 2022/4/14
     * <AUTHOR> liucheng
     */
    boolean addRegion(Region region);

    /**
     * 删除区域
     * 以下情况删除不能
     * 1 不存在该区域
     * 2 该区域下存在子区域
     * 3 根区域不能删除
     * 4 区域下存在设备不能删除
     * 5 区域下存在摄像机不能删除
     *
     * @param ids
     * @return : boolean
     * @date : 2022/4/14
     * <AUTHOR> liucheng
     */
    boolean delBatchRegion(List<Long> ids);

    /**
     * 更新区域信息
     *
     * @param region
     * @return : boolean
     * @date : 2022/4/15
     * <AUTHOR> liucheng
     */
    boolean updateRegion(Region region);

    /**
     * 获取区域ID树
     *
     * @param regionId
     * @return
     */
    List<RegionTreeVo> getRegionIdTree(String regionId);

    /**
     * 获取指定区域树节点的区域ID列表
     *
     * @param regionIdList
     * @param regionIdTreeDto
     * @return
     */
    void getRegionIdInOneTree(RegionTreeVo regionIdTreeDto, List<Long> regionIdList);

    /**
     * 获取第一级的区域列表
     *
     * @return
     */
    List<Region> getParentTree();

    /**
     * 获取该节点下所有ID
     *
     * @param id
     * @return
     */
    List<Long> getChildId(Long id);

    /**
     * 根据区域id查询区域Map
     *
     * @param ids
     * @return
     */
    Map<Long, Region> getRegionMap(List<Long> ids);

}
