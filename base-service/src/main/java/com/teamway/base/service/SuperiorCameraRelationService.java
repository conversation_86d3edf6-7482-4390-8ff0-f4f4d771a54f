package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.superior.CascadeEquipmentDto;
import com.teamway.base.dto.superior.QueryCameraRelationDto;
import com.teamway.base.entity.SuperiorCameraRelation;

import java.util.List;

/**
 * <p>
 * 摄像机国标平台关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
public interface SuperiorCameraRelationService extends IService<SuperiorCameraRelation> {

    /**
     * 批量修改摄像机关联国标平台
     *
     * @param relationList
     * @return
     */
    Boolean updateBatch(List<SuperiorCameraRelation> relationList);

    /**
     * 获取摄像机关联国标平台
     *
     * @return
     */
    List<CascadeEquipmentDto> getCascadeInfo();

    /**
     * 根据条件分页查询
     *
     * @param queryCameraRelationDto 查询条件
     * @return 国标平台关联摄像机
     */
    Page<SuperiorCameraRelation> selectPageList(QueryCameraRelationDto queryCameraRelationDto);


}
