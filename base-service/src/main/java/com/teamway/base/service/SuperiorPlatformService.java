package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.superior.SuperiorPlatformDto;
import com.teamway.base.dto.superior.SuperiorStatusDto;
import com.teamway.base.entity.SuperiorPlatform;

import java.util.List;

/**
 * 上级平台配置
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
public interface SuperiorPlatformService extends IService<SuperiorPlatform> {

    /**
     * 修改
     * @param standard
     * @return
     */
    Boolean update(SuperiorPlatform standard);

    /**
     * 获取国标平台配置
     * @return
     */
    List<SuperiorPlatformDto> getNationalStandardList();

    /**
     * 获取上级平台在线状态
     * @param id 平台id
     * @return
     */
    SuperiorStatusDto getStatus(Long id);

    /**
     * 向上级平台推送设备信息
     * @param id 平台id
     * @return
     */
    Boolean sendDeviceInfo(Long id);

}
