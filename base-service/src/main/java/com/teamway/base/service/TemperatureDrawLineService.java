package com.teamway.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.dto.temperature.GetByCameraIdDto;
import com.teamway.base.dto.temperature.GetIrRuleVo;
import com.teamway.base.dto.temperature.GetTemperatureDrawLineForInsDto;
import com.teamway.base.dto.temperature.SetIrRuleDto;
import com.teamway.base.dto.temperature.TemperatureDrawLineUpdateDto;
import com.teamway.base.entity.TemperatureDrawLine;
import com.teamway.base.vo.GetTemperatureDrawLineForInsVo;
import com.teamway.thermal.entity.TemperatureRule;

import java.rmi.ServerException;
import java.util.List;

/**
 * 针对表【temperature_draw_line(红外热成像摄像机测温框)】的数据库操作Service
 *
 * <AUTHOR> liucheng
 * @date : 2022/6/30
 */
public interface TemperatureDrawLineService extends IService<TemperatureDrawLine> {
    /**
     * 添加/更新/删除测温项区域列表
     *
     * @param drawLineUpdateDto  测温项区域列表
     * @return : boolean
     * @date : 2022/7/1
     * <AUTHOR> liucheng
     */
    boolean addBatchOrUpdate(TemperatureDrawLineUpdateDto drawLineUpdateDto);


    /**
     * 根据摄像机编号获取测温项区域列表
     * @param getByCameraIdDto
     * @return
     * @throws ServerException
     */
    TemperatureDrawLineUpdateDto getByCameraId(GetByCameraIdDto getByCameraIdDto) throws ServerException;

    /**
     * PG-设置红外测温规则
     * @param setIrRuleDto
     */
    void setIrRule(SetIrRuleDto setIrRuleDto);

    /**
     * PG-获取测温规则
     * @param channelId
     * @param deviceId
     * @param presetId
     * @return
     */
    GetIrRuleVo getIrRule(String deviceId, String channelId, int presetId);

    /**
     * 获取温度线
     * @param getTemperatureDrawLineForInsDto
     * @return
     */
    GetTemperatureDrawLineForInsVo getTemperatureDrawLineForIns(GetTemperatureDrawLineForInsDto getTemperatureDrawLineForInsDto);


    /**
     * 获取测温项
     * @param getTemperatureDrawLineForInsDto
     * @return
     */
    List<TemperatureRule> getTemperature(GetTemperatureDrawLineForInsDto getTemperatureDrawLineForInsDto);
}
