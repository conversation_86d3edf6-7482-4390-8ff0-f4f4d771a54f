package com.teamway.base.service;


import com.teamway.base.dto.tv.tv.AddSceneDto;
import com.teamway.base.dto.tv.tv.ChangeTvWallWithWindowDto;
import com.teamway.base.dto.tv.tv.DelTvWallWithWindowDto;
import com.teamway.base.dto.tv.tv.EditSceneNameDto;
import com.teamway.base.dto.tv.tv.GetSceneByIdVo;
import com.teamway.base.dto.tv.tv.SelectSceneVo;
import com.teamway.base.dto.tv.tv.UpdateSceneDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TvWallService {

    /**
     * 新增场景
     * @param addSceneDto
     * @return
     */
    boolean addScene(AddSceneDto addSceneDto);

    /**
     * 修改场景
     * @param updateSceneDto
     * @return
     */
    boolean updateScene(UpdateSceneDto updateSceneDto);

    /**
     * 修改场景名称
     * @param editSceneNameDto
     * @return
     */
    boolean editSceneName(EditSceneNameDto editSceneNameDto);

    /**
     * 删除场景
     * @param sceneId
     * @return
     */
    boolean deleteScene(String sceneId);

    /**
     * 查询场景信息
     * @param decoderDeviceId
     * @return
     */
    List<SelectSceneVo> selectScene(String decoderDeviceId);

    /**
     * 通过id查询场景信息
     * @param sceneId
     * @return
     */
    GetSceneByIdVo getSceneById(String sceneId);

    /**
     * 场景TV
     * @param sceneId
     * @return
     */
    boolean changeTv(String sceneId);

    /**
     * 修改电视墙
     * @param changeTvWallWithWindowDto
     * @return
     */
    boolean changeTvWallWithWindow(ChangeTvWallWithWindowDto changeTvWallWithWindowDto);

    /**
     * 删除电视墙
     * @param delTvWallWithWindowDto
     * @return
     */
    boolean delTvWallWithWindow(DelTvWallWithWindowDto delTvWallWithWindowDto);
}
