package com.teamway.base.service;

import com.teamway.base.entity.WarningBoard;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.common.entity.BatchIds;

import java.util.List;

/**
 * <p>
 * 首页警告榜单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20
 */
public interface WarningBoardService extends IService<WarningBoard> {
    /**
     * 添加警告记录
     * @param warningBoard 警告记录信息
     * @return Boolean 添加结果
     */
    Boolean addWarningBoard(WarningBoard warningBoard);

    /**
     * 更新警告记录
     * @param warningBoard 警告记录信息
     * @return Boolean 更新结果
     */
    Boolean updateWarningBoard(WarningBoard warningBoard);

    /**
     * 批量删除警告记录
     * @param request 删除请求参数
     * @return Boolean 删除结果
     */
    Boolean batchDeleteWarningBoard(BatchIds request);

    /**
     * 获取警告记录详情
     * @param id 记录ID
     * @return WarningBoard 警告记录详情
     */
    WarningBoard getWarningBoardById(Long id);

    /**
     * 获取警告记录列表
     * @param warningType 警告类型，可为空
     * @return List<WarningBoard> 警告记录列表
     */
    List<WarningBoard> getWarningBoardList(Integer warningType);
}
