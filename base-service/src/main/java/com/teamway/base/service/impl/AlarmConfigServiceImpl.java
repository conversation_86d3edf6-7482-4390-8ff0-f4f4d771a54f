package com.teamway.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.common.constant.AlarmVoiceTypeConstant;
import com.teamway.base.common.constant.Constant;
import com.teamway.base.common.constant.EnableConstant;
import com.teamway.base.common.constant.RelationTypeConstant;
import com.teamway.base.common.transfer.AlarmConfigTransfer;
import com.teamway.base.dto.config.AlarmConditionQueryDto;
import com.teamway.base.dto.config.AlarmConfigDto;
import com.teamway.base.dto.config.AlarmConfigQueryDto;
import com.teamway.base.dto.voice.AlarmKeepDto;
import com.teamway.base.entity.AlarmConfig;
import com.teamway.base.entity.AlarmVoice;
import com.teamway.base.entity.Algorithm;
import com.teamway.base.mapper.AlarmConfigMapper;
import com.teamway.base.mapper.AlgorithmMapper;
import com.teamway.base.service.AlarmConfigService;
import com.teamway.base.service.AlarmVoiceService;
import com.teamway.base.vo.AlarmAllConfigVo;
import com.teamway.base.vo.AlarmConditionVo;
import com.teamway.common.entity.BaseEntity;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import com.teamway.model.entity.PatrolAlgorithmConfig;
import com.teamway.model.entity.PatrolAlgorithmTemplate;
import com.teamway.model.entity.PatrolMate;
import com.teamway.model.request.patrol.PatrolAlgorithmTemplateQueryRequest;
import com.teamway.service.PatrolAlgorithmConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <p>
 * 告警配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-08
 */
@Service
public class AlarmConfigServiceImpl extends ServiceImpl<AlarmConfigMapper, AlarmConfig> implements AlarmConfigService {

    @Autowired
    private AlarmConfigTransfer alarmConfigTransfer;

    @Autowired
    private AlgorithmMapper algorithmMapper;

    @Autowired
    private PatrolAlgorithmConfigService patrolAlgorithmConfigService;

    @Autowired
    private AlarmVoiceService alarmVoiceService;

    /**
     * 添加或修改
     *
     * @param alarmKeepDto@return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAlarmConfig(AlarmKeepDto alarmKeepDto) {
        if (CollectionUtil.isNotEmpty(alarmKeepDto.getAlarmConfig())){
            //告警配置
            List<AlarmConfigDto> configDtoList = alarmKeepDto.getAlarmConfig();
            //告警列表
            List<AlarmConfig> configList = configDtoList.stream().map(i -> alarmConfigTransfer.dto2entity(i)).toList();
            //告警等级最高的
            AlarmConfig highestAlarmConfig = configList.stream().max(Comparator.comparingInt(config -> Integer.parseInt(config.getAlarmLevel()))).orElse(null);
            //如果是算法就修改告警等级
            if (RelationTypeConstant.ALGORITHM.equals(highestAlarmConfig.getRelationType())) {
                //修改算法告警等级
                if (ObjectUtil.isNotEmpty(highestAlarmConfig)){
                    algorithmMapper.update(new LambdaUpdateWrapper<Algorithm>().eq(BaseEntity::getId,highestAlarmConfig.getAlgorithmId()).set(Algorithm::getLevel,highestAlarmConfig.getAlarmLevel()));
                }
            }
            //修改告警配置
            this.updateBatchById(configList);
        }
        //告警语音设置
        return alarmVoiceUpdate(alarmKeepDto.getAlarmVoice());
    }

    /**
     * 新增或修改语音告警配置
     * @param alarmConfigDto
     * @return
     */
    private boolean alarmVoiceUpdate(AlarmVoice alarmConfigDto) {
        LambdaQueryWrapper<AlarmVoice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlarmVoice::getAlarmType, alarmConfigDto.getRelationType());
        queryWrapper.eq(AlarmVoice::getRelevanceId, alarmConfigDto.getRelevanceId());
        //查询告警语音列表信息
        AlarmVoice alarmVoice = alarmVoiceService.list(queryWrapper).stream().findFirst().orElse(null);
        if (ObjectUtil.isEmpty(alarmVoice)){
            throw new ServiceException(ResultCode.NOT_NULL,"语音告警配置");
        }
        alarmVoice.setVoiceSwitch(alarmConfigDto.getVoiceSwitch());
        alarmVoice.setAlarmVoiceType(alarmConfigDto.getAlarmVoiceType());
        if (ObjectUtil.equals(alarmConfigDto.getAlarmVoiceType(), AlarmVoiceTypeConstant.CUSTOM_UPLOAD)){
            alarmVoice.setVoiceFile(alarmConfigDto.getVoiceFile());
        }
        alarmVoice.setAlgSwitch(alarmConfigDto.getAlgSwitch());
        alarmVoice.setVoiceFilename(alarmConfigDto.getVoiceFilename());
        return alarmVoiceService.updateById(alarmVoice);
    }

    /**
     * 根据巡检点查询告警配置
     *
     * @param alarmConfigQueryDto
     * @return
     */
    @Override
    public AlarmConfig selectDetailByPoint(AlarmConfigQueryDto alarmConfigQueryDto) {
        if (RelationTypeConstant.ALGORITHM.equals(alarmConfigQueryDto.getRelationType())) {
            LambdaQueryWrapper<AlarmConfig> queryAlarmConfigWrapper = new LambdaQueryWrapper<>();
            queryAlarmConfigWrapper.eq(AlarmConfig::getAlarmLevel, alarmConfigQueryDto.getAlarmLevel());
            queryAlarmConfigWrapper.eq(AlarmConfig::getAlgorithmId, alarmConfigQueryDto.getAlgorithmId());
            queryAlarmConfigWrapper.eq(AlarmConfig::getRelationType, alarmConfigQueryDto.getRelationType());
            List<AlarmConfig> alarmConfigList = this.list(queryAlarmConfigWrapper);
            if (CollUtil.isNotEmpty(alarmConfigList)) {
                return alarmConfigList.get(0);
            }
        } else if (RelationTypeConstant.INS_POINT.equals(alarmConfigQueryDto.getRelationType())) {
            LambdaQueryWrapper<AlarmConfig> queryAlarmConfigWrapper = new LambdaQueryWrapper<>();
            queryAlarmConfigWrapper.eq(AlarmConfig::getAlarmLevel, alarmConfigQueryDto.getAlarmLevel());
            queryAlarmConfigWrapper.eq(AlarmConfig::getInsPointId, alarmConfigQueryDto.getInsPointId());
            queryAlarmConfigWrapper.eq(AlarmConfig::getRelationType, alarmConfigQueryDto.getRelationType());
            List<AlarmConfig> alarmConfigList = this.list(queryAlarmConfigWrapper);
            if (CollUtil.isNotEmpty(alarmConfigList)) {
                return alarmConfigList.get(0);
            }
        }
        return new AlarmConfig();
    }

    @Override
    public List<AlarmConfig> selectInsAlarmConfigByInsPointAlgId(Long insPointId, String enable) {
        LambdaQueryWrapper<AlarmConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlarmConfig::getInsPointId, insPointId);
        queryWrapper.eq(StrUtil.isNotBlank(enable), AlarmConfig::getEnable, enable);
        queryWrapper.eq(AlarmConfig::getRelationType, RelationTypeConstant.INS_POINT);
        return this.list(queryWrapper);
    }

    /**
     * 根据算法ID查询配置信息
     *
     * @param algId
     * @return
     */
    @Override
    public List<AlarmConfig> selectInsAlarmConfigByAlgId(Long algId) {
        LambdaQueryWrapper<AlarmConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlarmConfig::getAlgorithmId, algId);
        queryWrapper.eq(AlarmConfig::getRelationType, RelationTypeConstant.ALGORITHM);
        return this.list(queryWrapper);
    }

    /**
     * 查询告警配置  条件
     *
     * @param alarmConditionQueryDto
     * @return
     */
    @Override
    public List<AlarmConditionVo> selectCondition(AlarmConditionQueryDto alarmConditionQueryDto) {
        List<AlarmConditionVo> alarmConditionVoList = new ArrayList<>();
        Algorithm algorithm = algorithmMapper.selectById(alarmConditionQueryDto.getAlgorithmId());
        //红外算法  直接返回
        if (algorithm != null && Constant.INFRARED.equals(algorithm.getAlgName())) {
            alarmConditionVoList.add(AlarmConditionVo.builder().condition("最高温度").conditionKey("$.max").build());
            alarmConditionVoList.add(AlarmConditionVo.builder().condition("最低温度").conditionKey("$.min").build());
            alarmConditionVoList.add(AlarmConditionVo.builder().condition("平均温度").conditionKey("$.avg").build());
            return alarmConditionVoList;
        }
        //默认配置
        AlarmConditionVo defaultCondition = AlarmConditionVo.builder().condition("识别结果").conditionKey("$.result1").build();
        //算法关联告警
        if (RelationTypeConstant.ALGORITHM.equals(alarmConditionQueryDto.getRelationType())) {
            alarmConditionVoList.add(defaultCondition);
            return alarmConditionVoList;
        } else if (RelationTypeConstant.INS_POINT.equals(alarmConditionQueryDto.getRelationType())) {
            //点位关联告警
            //查询点位标识 (启用)
            PatrolAlgorithmTemplateQueryRequest algorithmQueryRequest = new PatrolAlgorithmTemplateQueryRequest();
            algorithmQueryRequest.setPoint_id(alarmConditionQueryDto.getInsPointId().toString());
            PatrolAlgorithmConfig patrolAlgorithmConfig = patrolAlgorithmConfigService.getPatrolAlgorithmConfig(algorithmQueryRequest);
            if (patrolAlgorithmConfig != null) {
                PatrolAlgorithmTemplate configInfo = patrolAlgorithmConfig.getConfigInfo();
                if (configInfo != null) {
                    List<PatrolMate> mateList = configInfo.getMate();
                    if (CollUtil.isNotEmpty(mateList)) {
                        mateList.forEach(pointDetail -> {
                            //启用
                            if (EnableConstant.ENABLE.equals(pointDetail.getState())) {
                                alarmConditionVoList.add(AlarmConditionVo.builder().condition(pointDetail.getName()).conditionKey("$.key" + pointDetail.getId()).build());
                            }
                        });
                    }
                }
            }
            //如果没有点位标识，使用默认配置
            if (CollUtil.isEmpty(alarmConditionVoList)) {
                alarmConditionVoList.add(defaultCondition);
            }
        }
        return alarmConditionVoList;
    }

    /**
     * 查询告警配置
     *
     * @param alarmConfigQueryDto
     * @return
     */
    @Override
    public AlarmAllConfigVo selectList(AlarmConfigQueryDto alarmConfigQueryDto) {
        AlarmVoice alarmVoice = new AlarmVoice();
        LambdaQueryWrapper<AlarmConfig> queryAlarmConfigWrapper = new LambdaQueryWrapper<>();
        if (RelationTypeConstant.ALGORITHM.equals(alarmConfigQueryDto.getRelationType())) {
            queryAlarmConfigWrapper.eq(AlarmConfig::getAlgorithmId, alarmConfigQueryDto.getAlgorithmId());
            queryAlarmConfigWrapper.eq(AlarmConfig::getRelationType, alarmConfigQueryDto.getRelationType());
            //查询告警语音配置
            alarmVoice = alarmVoiceService.list(new LambdaQueryWrapper<AlarmVoice>().eq(AlarmVoice::getAlarmType, RelationTypeConstant.ALGORITHM)
                    .eq(ObjectUtil.isNotEmpty(alarmConfigQueryDto.getAlgorithmId()), AlarmVoice::getRelevanceId, alarmConfigQueryDto.getAlgorithmId())).stream().findFirst().orElse(null);
        } else if (RelationTypeConstant.INS_POINT.equals(alarmConfigQueryDto.getRelationType())) {
            //巡检点告警配置
            if (alarmConfigQueryDto.getInsPointId() == null) {
                throw new ServiceException(ResultCode.NOT_NULL, "巡检点id");
            }
            queryAlarmConfigWrapper.eq(AlarmConfig::getInsPointId, alarmConfigQueryDto.getInsPointId());
            queryAlarmConfigWrapper.eq(AlarmConfig::getRelationType, alarmConfigQueryDto.getRelationType());
            //查询告警语音配置
            alarmVoice = alarmVoiceService.list(new LambdaQueryWrapper<AlarmVoice>().eq(AlarmVoice::getAlarmType, RelationTypeConstant.INS_POINT)
                    .eq(ObjectUtil.isNotEmpty(alarmConfigQueryDto.getInsPointId()), AlarmVoice::getRelevanceId, alarmConfigQueryDto.getInsPointId())).stream().findFirst().orElse(null);
        }
        AlarmAllConfigVo configVo = new AlarmAllConfigVo();
        //告警配置
        configVo.setAlarmConfigList(this.list(queryAlarmConfigWrapper));
        //告警语音配置
        configVo.setAlarmVoice(alarmVoice);
        return configVo;
    }
}
