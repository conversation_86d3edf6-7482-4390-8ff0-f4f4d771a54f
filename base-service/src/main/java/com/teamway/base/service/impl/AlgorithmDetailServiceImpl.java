package com.teamway.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.entity.AlgorithmDetail;
import com.teamway.base.mapper.AlgorithmDetailMapper;
import com.teamway.base.service.AlgorithmDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> hlc
 * @date : 2022/4/14
 */
@Service
public class AlgorithmDetailServiceImpl extends ServiceImpl<AlgorithmDetailMapper, AlgorithmDetail> implements AlgorithmDetailService {

    @Override
    public AlgorithmDetail selectByTypeNameAndAlgId(String typeName, Long algId) {

        LambdaQueryWrapper<AlgorithmDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AlgorithmDetail::getTypeName, typeName);
        lambdaQueryWrapper.eq(AlgorithmDetail::getAlgorithmId, algId);
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public List<AlgorithmDetail> selectByAlgorithmId(Long algorithmId) {
        LambdaQueryWrapper<AlgorithmDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AlgorithmDetail::getAlgorithmId, algorithmId);
        return baseMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public AlgorithmDetail selectByAlgIdAndAlgName(Long algId, String algName) {
        LambdaQueryWrapper<AlgorithmDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AlgorithmDetail::getAlgorithmId, algId);
        lambdaQueryWrapper.eq(AlgorithmDetail::getTypeName, algName);
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

}
