package com.teamway.base.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.entity.AlgorithmGroup;
import com.teamway.base.mapper.AlgorithmGroupMapper;
import com.teamway.base.service.AlgorithmGroupService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-05
 */
@Service
public class AlgorithmGroupServiceImpl extends ServiceImpl<AlgorithmGroupMapper, AlgorithmGroup> implements AlgorithmGroupService {

}
