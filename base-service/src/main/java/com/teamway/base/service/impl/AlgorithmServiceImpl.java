package com.teamway.base.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.common.constant.AlarmLevelConstant;
import com.teamway.base.common.constant.AlarmVoiceTypeConstant;
import com.teamway.base.common.constant.AlgorithmTypeConstant;
import com.teamway.base.common.constant.RelationTypeConstant;
import com.teamway.base.common.constant.SubClassType;
import com.teamway.base.common.constant.SwitchConstant;
import com.teamway.base.dto.algorithm.AlgorithmExcelDto;
import com.teamway.base.dto.algorithm.AlgorithmQueryDto;
import com.teamway.base.entity.*;
import com.teamway.base.mapper.AlgorithmDetailMapper;
import com.teamway.base.mapper.AlgorithmGroupMapper;
import com.teamway.base.mapper.AlgorithmMapper;
import com.teamway.base.mapper.CheckPointMapper;
import com.teamway.base.service.AlarmConfigService;
import com.teamway.base.service.AlarmVoiceService;
import com.teamway.base.service.AlgorithmService;
import com.teamway.base.service.CheckPointService;
import com.teamway.base.util.AlgorithmExcelFormat;
import com.teamway.base.util.TimeUtils;
import com.teamway.base.util.ValidateUtil;
import com.teamway.common.entity.BaseEntity;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.util.ExcelUtils;
import com.teamway.exception.ServiceException;
import com.teamway.model.dto.PageQueryDto;
import com.teamway.model.entity.AlgorithmTemplate;
import com.teamway.service.TemplateService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * <AUTHOR> thh
 * @date : 2022/7/22
 */
@Slf4j
@Service
public class AlgorithmServiceImpl extends ServiceImpl<AlgorithmMapper, Algorithm> implements AlgorithmService {
    @Autowired
    private AlgorithmDetailMapper algorithmDetailMapper;
    @Autowired
    private AlgorithmGroupMapper algorithmGroupMapper;
    @Autowired
    private TemplateService templateService;
    @Autowired
    private AlarmConfigService alarmConfigService;
    @Autowired
    private AlarmVoiceService alarmVoiceService;
    @Resource
    private CheckPointMapper checkPointMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int add(Algorithm algorithm) {
        Algorithm byAlgDesc = this.selectAlgorithmByAlgCode(algorithm.getAlgCode());
        if (byAlgDesc != null) {
            throw new ServiceException(ResultCode.EXIST, "算法标识");
        }
        Algorithm byAlgName = this.selectAlgorithmByAlgName(algorithm.getAlgName());
        if (byAlgName != null) {
            throw new ServiceException(ResultCode.EXIST, "算法名称");
        }
        //得到算法配置模板
        AlgorithmTemplate template = getAlgorithmTemplate(algorithm);
        templateService.addTemplate(template);
        algorithm.setTemplateId(template.getId());
        int result = baseMapper.insert(algorithm);
        //初始化这三种等级的告警配置
        List<AlarmConfig> alarmConfigList = new ArrayList<>();
        alarmConfigList.add(AlarmConfig.builder().algorithmId(algorithm.getId()).relationType(RelationTypeConstant.ALGORITHM).alarmLevel(AlarmLevelConstant.GENERAL).enable(SwitchConstant.CLOSE).build());
        if (ObjectUtil.equals(SubClassType.numericalClass, algorithm.getSubclassType())) {
            alarmConfigList.add(AlarmConfig.builder().algorithmId(algorithm.getId()).relationType(RelationTypeConstant.ALGORITHM).alarmLevel(AlarmLevelConstant.SIGNIFICANCE).enable(SwitchConstant.CLOSE).build());
            alarmConfigList.add(AlarmConfig.builder().algorithmId(algorithm.getId()).relationType(RelationTypeConstant.ALGORITHM).alarmLevel(AlarmLevelConstant.URGENCY).enable(SwitchConstant.CLOSE).build());
        }
        alarmConfigService.saveBatch(alarmConfigList);
        //初始化告警语音配置
        alarmVoiceService.save(new AlarmVoice().setRelevanceId(algorithm.getId()).setAlarmType(RelationTypeConstant.ALGORITHM).setVoiceSwitch(SwitchConstant.CLOSE).setAlarmVoiceType(AlarmVoiceTypeConstant.NO_BROADCAST_CONTENT));
        addAlgTypeList(algorithm.getAlgorithmDetailList(), algorithm.getId());
        return result;
    }

    /**
     * 算法配置模板
     *
     * @param algorithm
     * @return
     */
    private static AlgorithmTemplate getAlgorithmTemplate(Algorithm algorithm) {
        AlgorithmTemplate template = new AlgorithmTemplate();
        template.setReport_conf_thres(algorithm.getReportConfThres());
        template.setInterval(algorithm.getCaptureInterval());
        template.setImage_count(algorithm.getImageCount());
        template.setCount_each_second(algorithm.getCountEachSecond());
        template.setReport_object_size(algorithm.getReportObjectSize());
        template.setReport_time(algorithm.getReportTime());
        //时间格式转换
        template.setReport_effect_work_time(TimeUtils.timeToFloat(algorithm.getReportEffectWorkTime()));
        template.setEnable_deduplication(algorithm.getRemoval());
        template.setNote(algorithm.getAlgName());
        template.setReport_alg_name(algorithm.getAlgCode());
        template.setTarget_alg_name(algorithm.getAlgCode());
        template.setFilter_type(algorithm.getFilterType());
        if (CollectionUtil.isNotEmpty(algorithm.getAlgorithmDetailList())) {
            JSONArray jsonArray = new JSONArray();
            algorithm.getAlgorithmDetailList().stream().forEach(i -> {
                jsonArray.add(i.getTypeName());
            });
            template.setReport_label(jsonArray);
        }
        return template;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDel(List<Long> ids) {
        List<Algorithm> algorithmList = baseMapper.selectBatchIds(ids);
        if (ids.size() != algorithmList.size()) {
            throw new ServiceException(ResultCode.NOT_EXIST, "算法ID");
        }
        //查询该算法是否还绑定检测点
        List<CheckPoint> checkPointList = checkPointMapper.selectList(new LambdaQueryWrapper<CheckPoint>().in(CheckPoint::getAlgId, ids));
        if (CollUtil.isNotEmpty(checkPointList)) {
            throw new ServiceException(ResultCode.DELETE_FAILURE, "请先删除算法所关联检测点");
        }
        int result = baseMapper.deleteByIds(ids);
        if (CollUtil.isNotEmpty(algorithmList)) {
            templateService.removeBatchByIds(algorithmList.stream().map(Algorithm::getTemplateId).collect(Collectors.toList()));
        }
        if (result > 0) {
            LambdaQueryWrapper<AlgorithmDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(AlgorithmDetail::getAlgorithmId, ids);
            algorithmDetailMapper.delete(lambdaQueryWrapper);
            //同时删除算法告警配置
            alarmConfigService.remove(new LambdaQueryWrapper<AlarmConfig>().eq(AlarmConfig::getRelationType, RelationTypeConstant.ALGORITHM).in(AlarmConfig::getAlgorithmId, ids));
            //同时删除算法语音告警配置
            alarmVoiceService.remove(new LambdaQueryWrapper<AlarmVoice>().eq(AlarmVoice::getAlarmType, RelationTypeConstant.ALGORITHM).in(AlarmVoice::getRelevanceId, ids));
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(Algorithm algorithm) {
        Algorithm byId = baseMapper.selectById(algorithm.getId());
        if (byId == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "算法");
        }
        LambdaQueryWrapper<Algorithm> queryWrapperAlgName = new LambdaQueryWrapper<>();
        queryWrapperAlgName.ne(Algorithm::getId, algorithm.getId());
        queryWrapperAlgName.eq(Algorithm::getAlgName, algorithm.getAlgName());
        Algorithm byAlgName = baseMapper.selectOne(queryWrapperAlgName);
        if (byAlgName != null) {
            throw new ServiceException(ResultCode.EXIST, "算法名称");
        }
        LambdaQueryWrapper<Algorithm> queryWrapperAlgDesc = new LambdaQueryWrapper<>();
        queryWrapperAlgDesc.ne(Algorithm::getId, algorithm.getId());
        queryWrapperAlgDesc.eq(Algorithm::getAlgCode, algorithm.getAlgCode());
        Algorithm byAlgDesc = baseMapper.selectOne(queryWrapperAlgDesc);
        if (byAlgDesc != null) {
            throw new ServiceException(ResultCode.EXIST, "算法标识");
        }
        //非主动上报的算法  没有告警等级
        if (!AlgorithmTypeConstant.DETECTION.equals(algorithm.getType())) {
            algorithm.setLevel("");
        }
        int result = baseMapper.updateById(algorithm);
        if (ObjectUtil.isNotEmpty(byId)) {
            AlgorithmTemplate template = new AlgorithmTemplate();
            template.setReport_conf_thres(algorithm.getReportConfThres());
            template.setInterval(algorithm.getCaptureInterval());
            template.setImage_count(algorithm.getImageCount());
            template.setCount_each_second(algorithm.getCountEachSecond());
            template.setReport_object_size(algorithm.getReportObjectSize());
            template.setReport_time(algorithm.getReportTime());
            template.setFilter_type(algorithm.getFilterType());
            template.setReport_effect_work_time(TimeUtils.timeToFloat(algorithm.getReportEffectWorkTime()));
            template.setNote(algorithm.getAlgName());
            template.setReport_alg_name(algorithm.getAlgCode());
            template.setTarget_alg_name(algorithm.getAlgCode());
            template.setFilter_type(algorithm.getFilterType());
            if (CollectionUtil.isNotEmpty(algorithm.getAlgorithmDetailList())) {
                JSONArray jsonArray = new JSONArray();
                algorithm.getAlgorithmDetailList().stream().forEach(i -> {
                    jsonArray.add(i.getTypeName());
                });
                template.setReport_label(jsonArray);
            }
            template.setEnable_deduplication(algorithm.getRemoval());
            template.setId(byId.getTemplateId());
            templateService.updateById(template);
        }
        LambdaQueryWrapper<AlgorithmDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AlgorithmDetail::getAlgorithmId, algorithm.getId());
        algorithmDetailMapper.delete(lambdaQueryWrapper);
        addAlgTypeList(algorithm.getAlgorithmDetailList(), algorithm.getId());
        return result;
    }

    @Override
    public IPage<Algorithm> selectAlgorithm(AlgorithmQueryDto algorithmQueryDto) {
        QueryWrapper<Algorithm> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(StrUtil.isNotBlank(algorithmQueryDto.getAlgName()), Algorithm::getAlgName, algorithmQueryDto.getAlgName());
        queryWrapper.lambda().like(StrUtil.isNotBlank(algorithmQueryDto.getAlgCode()), Algorithm::getAlgCode, algorithmQueryDto.getAlgCode());
        queryWrapper.lambda().eq(StrUtil.isNotBlank(algorithmQueryDto.getType()), Algorithm::getType, algorithmQueryDto.getType());
        queryWrapper.lambda().eq(StrUtil.isNotBlank(algorithmQueryDto.getLevel()), Algorithm::getLevel, algorithmQueryDto.getLevel());
        queryWrapper.lambda().orderByDesc(BaseEntity::getCreateTime);
        IPage<Algorithm> page = new Page<>(algorithmQueryDto.getPageIndex(), algorithmQueryDto.getPageSize());
        baseMapper.selectPage(page, queryWrapper);
        for (Algorithm algorithm : page.getRecords()) {
            if (ObjectUtil.isNotEmpty(algorithm.getTemplateId())) {
                IPage<AlgorithmTemplate> templateList = templateService.getTemplateList(new PageQueryDto(1, 1, algorithm.getAlgCode()));
                AlgorithmTemplate template = templateList.getRecords().stream().findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(template)) {
                    algorithm.setReportConfThres(template.getReport_conf_thres());
                    algorithm.setCaptureInterval(template.getInterval());
                    algorithm.setImageCount(template.getImage_count());
                    algorithm.setCountEachSecond(template.getCount_each_second());
                    algorithm.setReportObjectSize(template.getReport_object_size());
                    algorithm.setReportTime(template.getReport_time());
                    algorithm.setFilterType(template.getFilter_type());
                    algorithm.setReportEffectWorkTime(TimeUtils.floatToTime(template.getReport_effect_work_time()));
                    algorithm.setRemoval(template.getEnable_deduplication());
                }
            }
            QueryWrapper<AlgorithmDetail> detailQueryWrapper = new QueryWrapper<>();
            detailQueryWrapper.lambda().eq(AlgorithmDetail::getAlgorithmId, algorithm.getId());
            List<AlgorithmDetail> algorithmTypeRelations = algorithmDetailMapper.selectList(detailQueryWrapper);
            algorithm.setAlgorithmDetailList(algorithmTypeRelations);
            AlgorithmGroup algorithmGroup = algorithmGroupMapper.selectById(algorithm.getAlgorithmGroupId());
            if (algorithmGroup != null) {
                algorithm.setAlgorithmGroupName(algorithmGroup.getName());
            }
        }
        return page;
    }

    /**
     * 查询所有算法配置信息
     *
     * @param flag 算法类型  1-巡检类 2-人员类 3-输煤类 4-红外测温 5-巡视+红外
     * @return
     */
    @Override
    public List<Algorithm> selectAll(Integer flag) {
        QueryWrapper<Algorithm> queryWrapper = new QueryWrapper<>();
        if (AlgorithmTypeConstant.INSPECTION_INFRARED.equals(flag.toString())) {
            queryWrapper.lambda().in(Algorithm::getType, AlgorithmTypeConstant.INSPECTION, AlgorithmTypeConstant.INSPECTION_INFRARED);
        } else {
            queryWrapper.lambda().eq(Algorithm::getType, flag);
        }
        queryWrapper.lambda().orderByAsc(Algorithm::getAlgName);
        List<Algorithm> algorithmList = baseMapper.selectList(queryWrapper);
        for (Algorithm algorithm : algorithmList) {
            QueryWrapper<AlgorithmDetail> detailQueryWrapper = new QueryWrapper<>();
            detailQueryWrapper.lambda().eq(AlgorithmDetail::getAlgorithmId, algorithm.getId());
            List<AlgorithmDetail> algorithmTypeRelations = algorithmDetailMapper.selectList(detailQueryWrapper);
            algorithm.setAlgorithmDetailList(algorithmTypeRelations);
        }
        return algorithmList;
    }

    @Override
    public Algorithm getAlgorithmByCode(String algCode) {
        Algorithm algorithm = list(new LambdaQueryWrapper<Algorithm>().eq(StrUtil.isNotBlank(algCode), Algorithm::getAlgCode, algCode)).stream().findFirst().orElse(null);
        if (ObjectUtil.isEmpty(algorithm)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "算法");
        }
        IPage<AlgorithmTemplate> templateList = templateService.getTemplateList(new PageQueryDto(1, 1, algCode));
        //在算法配置服务中查询
        AlgorithmTemplate template = templateList.getRecords().stream().findFirst().orElse(null);
        if (ObjectUtil.isNotEmpty(template)) {
            algorithm.setReportConfThres(template.getReport_conf_thres());
            algorithm.setCaptureInterval(template.getInterval());
            algorithm.setImageCount(template.getImage_count());
            algorithm.setCountEachSecond(template.getCount_each_second());
            algorithm.setReportObjectSize(template.getReport_object_size());
            algorithm.setReportTime(template.getReport_time());
            algorithm.setReportEffectWorkTime(template.getReport_effect_work_time());
            algorithm.setRemoval(template.getEnable_deduplication());
        }
        //查询算法详情信息
        QueryWrapper<AlgorithmDetail> detailQueryWrapper = new QueryWrapper<>();
        detailQueryWrapper.lambda().eq(AlgorithmDetail::getAlgorithmId, algorithm.getId());
        List<AlgorithmDetail> algorithmTypeRelations = algorithmDetailMapper.selectList(detailQueryWrapper);
        algorithm.setAlgorithmDetailList(algorithmTypeRelations);
        return algorithm;
    }

    /**
     * 算法id查询算法信息
     *
     * @param id
     * @return
     */
    @Override
    public Algorithm getAlgorithmById(Long id) {
        Algorithm algorithm = baseMapper.selectById(id);
        if (ObjectUtil.isNotEmpty(algorithm)) {
            algorithm.setAlgorithmDetailList(algorithmDetailMapper.selectList(new LambdaQueryWrapper<AlgorithmDetail>().eq(AlgorithmDetail::getAlgorithmId, id)));
        }
        return algorithm;
    }

    @Override
    public void exportExcel(AlgorithmQueryDto algorithmQuery, HttpServletResponse response) {
        algorithmQuery.setPageIndex(1);
        algorithmQuery.setPageSize(10000);
        List<Algorithm> records = selectAlgorithm(algorithmQuery).getRecords();
        if (CollectionUtil.isEmpty(records)) {
            throw new ServiceException(ResultCode.NOT_NULL, "导出的数据");
        }
        List<AlgorithmExcelDto> excelList = records.stream().map(AlgorithmExcelFormat::getExcelDtoByAlgorithm).collect(Collectors.toList());
        try {
            ExcelUtils.exportExcel(excelList, AlgorithmExcelFormat.generateSelectors(), "算法配置信息", "算法配置信息", AlgorithmExcelDto.class, "算法配置信息", response);
        } catch (IOException e) {
            throw new ServiceException(ResultCode.EXPORT_FAIL, e);
        }
    }

    @Override
    public void importExcel(MultipartFile file, HttpServletResponse response) {
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        params.setStartRows(0);
        //easypoi当前版本4.5不支持hibernate-validator8.0.1版本，改为手动校验
        //params.setNeedVerify(true);
        ExcelImportResult<AlgorithmExcelDto> importExcelMore;
        try {
            importExcelMore = ExcelImportUtil.importExcelMore(file.getInputStream(), AlgorithmExcelDto.class, params);
        } catch (Exception e) {
            throw new ServiceException(ResultCode.IMPORT_FAIL, e);
        }
        //判断是否是用模板导入
        Sheet sheet = importExcelMore.getWorkbook().getSheetAt(0);
        String title = sheet.getRow(0).getCell(0).getStringCellValue();
        String excelTitle = "算法配置信息";
        if (!excelTitle.equals(title)) {
            throw new ServiceException(ResultCode.IMPORT_FORMAT_FAIL, "请用模板导入算法信息");
        }
        List<AlgorithmExcelDto> failList = importExcelMore.getFailList();
        List<AlgorithmExcelDto> list = importExcelMore.getList();

        //按不同类型进行校验
        nullCheckByType(list, failList);

        for (AlgorithmExcelDto excelDto : list) {
            try {
                add(AlgorithmExcelFormat.getAlgorithmByExcelDto(excelDto));
            } catch (ServiceException se) {
                excelDto.setErrorMsg(se.getErrorMessage());
                failList.add(excelDto);
            }
        }
        //导出导入失败的记录
        if (!failList.isEmpty()) {
            try {
                ExcelUtils.exportExcel(failList, AlgorithmExcelFormat.generateSelectors(), "算法配置信息", "算法配置信息", AlgorithmExcelDto.class, "算法配置信息", response);
            } catch (IOException e) {
                throw new ServiceException(ResultCode.EXPORT_FAIL, e);
            }
        }
    }

    @Override
    public Algorithm selectAlgorithmByAlgName(String algName) {
        LambdaQueryWrapper<Algorithm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Algorithm::getAlgName, algName);
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public Algorithm selectAlgorithmByAlgCode(String algCode) {
        LambdaQueryWrapper<Algorithm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Algorithm::getAlgCode, algCode);
        return baseMapper.selectOne(lambdaQueryWrapper);
    }


    private void addAlgTypeList(List<AlgorithmDetail> detailList, Long algorithmId) {
        if (!CollectionUtils.isEmpty(detailList)) {
            List<String> distinctAlgName = detailList.stream().map(AlgorithmDetail::getTypeName).distinct().collect(Collectors.toList());
            if (detailList.size() != distinctAlgName.size()) {
                throw new ServiceException(ResultCode.EXIST, "算法子类别名称");
            }
            List<String> distinctAlgDesc = detailList.stream().map(AlgorithmDetail::getTypeDesc).distinct().collect(Collectors.toList());
            if (detailList.size() != distinctAlgDesc.size()) {
                throw new ServiceException(ResultCode.EXIST, "算法子类别描述");
            }
            for (AlgorithmDetail algorithmDetail : detailList) {
                algorithmDetail.setAlgorithmId(algorithmId);
                algorithmDetailMapper.insert(algorithmDetail);
            }
        }
    }

    /**
     * 按不同算法类型进行字段空校验
     *
     * @param list
     * @param failList
     */
    private void nullCheckByType(List<AlgorithmExcelDto> list, List<AlgorithmExcelDto> failList) {
        Iterator<AlgorithmExcelDto> iterator = list.iterator();
        while (iterator.hasNext()) {
            AlgorithmExcelDto next = iterator.next();
            StringJoiner errMsg = new StringJoiner(",");
            switch (next.getType()) {
                case AlgorithmTypeConstant.DETECTION:
                    //智能检测
                case AlgorithmTypeConstant.COAL_TRANSPORTATION:
                    //智能输煤
                    if (StrUtil.isBlank(next.getLevel())) {
                        errMsg.add("告警等级不能为空");
                    }
                    if (StrUtil.isBlank(next.getImageCount())) {
                        errMsg.add("抓图总数不能为空");
                    }
                    if (StrUtil.isBlank(next.getCountEachSecond())) {
                        errMsg.add("每秒抓图数不能为空");
                    }
                    if (StrUtil.isBlank(next.getReportTime())) {
                        errMsg.add("上报时间间隔不能为空");
                    }
                    if (StrUtil.isBlank(next.getRemoval())) {
                        errMsg.add("算法去重不能为空");
                    }
                case AlgorithmTypeConstant.INSPECTION:
                    //智能巡视
                    if (StrUtil.isBlank(next.getAnnotationType())) {
                        errMsg.add("标注类型不能为空");
                    }
                    if (StrUtil.isBlank(next.getReportConfThres())) {
                        errMsg.add("置信度不能为空");
                    }
                    if (StrUtil.isBlank(next.getCaptureInterval())) {
                        errMsg.add("抓图间隔不能为空");
                    }
                    if (StrUtil.isBlank(next.getImageCount())) {
                        errMsg.add("抓图总数不能为空");
                    }
                    if (StrUtil.isBlank(next.getReportObjectSize())) {
                        errMsg.add("识别图像大小不能为空");
                    }
                    if (StrUtil.isBlank(next.getFilterType())) {
                        errMsg.add("过滤类型不能为空");
                    }
                case AlgorithmTypeConstant.INFRARED:
                    //红外测温
                    //手动校验下注解的内容
                    Optional.ofNullable(ValidateUtil.validateParams(next)).ifPresent(errMsg::add);
                    break;
                default:
                    errMsg.add("算法类型错误");
                    break;
            }
            if (errMsg.length() > 0) {
                next.setErrorMsg(errMsg.toString());
                failList.add(next);
                iterator.remove();
            }
        }
    }

}
