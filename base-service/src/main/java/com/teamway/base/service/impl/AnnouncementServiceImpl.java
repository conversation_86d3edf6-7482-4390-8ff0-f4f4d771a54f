package com.teamway.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.teamway.base.entity.Announcement;
import com.teamway.base.mapper.AnnouncementMapper;
import com.teamway.base.service.AnnouncementService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.common.entity.BatchIds;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 首页公告表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20
 */
@Service
@Slf4j
public class AnnouncementServiceImpl extends ServiceImpl<AnnouncementMapper, Announcement>
        implements AnnouncementService {

    @Override
    public Boolean addAnnouncement(Announcement announcement) {
        return this.save(announcement);
    }

    @Override
    public Boolean updateAnnouncement(Announcement announcement) {
        return this.updateById(announcement);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteAnnouncement(BatchIds request) {
        List<Long> ids = request.getIds();
        return this.removeByIds(ids);
    }

    @Override
    public Announcement getAnnouncementById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<Announcement> getAnnouncementList() {
        return this.list(new LambdaQueryWrapper<Announcement>()
                .eq(Announcement::getStatus, 1)
                .orderByAsc(Announcement::getDisplayOrder));
    }

    @Override
    public Boolean updateScrollStatus(Long id, Integer scrollStatus) {
        return this.update(new LambdaUpdateWrapper<Announcement>()
                .eq(Announcement::getId, id)
                .set(Announcement::getScrollStatus, scrollStatus));
    }
}
