package com.teamway.base.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.common.constant.DirSortConstant;
import com.teamway.base.dto.dictionary.AddOrUpdateDto;
import com.teamway.base.dto.dir.DirDto;
import com.teamway.base.entity.BaseSysDir;
import com.teamway.base.entity.BaseSysSubDir;
import com.teamway.base.mapper.BaseSysDirMapper;
import com.teamway.base.mapper.BaseSysSubDirMapper;
import com.teamway.base.service.BaseSysDirService;
import com.teamway.base.service.BaseSysSubDirService;
import com.teamway.base.vo.DirExportVo;
import com.teamway.base.vo.SubDirExportVo;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.util.RedisUtil;
import com.teamway.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.checkerframework.checker.units.qual.A;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Year;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yiju
 * @date 2025-03-03
 * @description 系统字典类服务实现类
 */
@Slf4j
@Service
public class BaseSysDirServiceImpl extends ServiceImpl<BaseSysDirMapper, BaseSysDir> implements BaseSysDirService {

    @Autowired
    private BaseSysSubDirService baseSysSubDirService;
    @Autowired
    private BaseSysDirMapper baseSysDirMapper;
    @Autowired
    private BaseSysSubDirMapper baseSysSubDirMapper;
    @Autowired
    private RedisUtil redisUtil;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveDir(BaseSysDir baseSysDir) {
        //校验新增字典是否与库中数据重复
        checkDictionary(baseSysDir);

        if (baseSysDir.getId() != null) {
            //更新主字典
            boolean mainDir = this.updateById(baseSysDir);
            //先判断子级字典是否有删除 查询库中已有的子字典列表id
            List<Long> existSubIds = baseSysSubDirService.list(new LambdaQueryWrapper<BaseSysSubDir>().eq(BaseSysSubDir::getDirId, baseSysDir.getId())).stream().map(BaseSysSubDir::getId).toList();
            //对比判断当前更新操作时的子级id
            List<Long> updateSubIds = baseSysDir.getSubDirs().stream().map(BaseSysSubDir::getId).toList();
            //判断是否有删除的子级字典
            List<Long> deleteSubIds = existSubIds.stream().filter(id -> !updateSubIds.contains(id)).toList();
            //删除子级字典数据
            if (CollectionUtil.isNotEmpty(deleteSubIds)) {
                baseSysSubDirService.removeBatchByIds(deleteSubIds);
            }
            //更新子级字典
            baseSysDir.getSubDirs().forEach(subDir -> subDir.setDirId(baseSysDir.getId()));
            boolean subDirFlag = baseSysSubDirService.saveOrUpdateBatch(baseSysDir.getSubDirs());
            //更新完成后 同步更新redis中的数据
            if (mainDir && subDirFlag) {
                List<BaseSysDir> sysDictionary = getObjFromRedis("sys_dictionary");
                //先获取具体的更新数据
                sysDictionary.stream().filter(dir -> dir.getId().equals(baseSysDir.getId())).findFirst().ifPresent(dir -> {
                    //更新主字典
                    BeanUtil.copyProperties(baseSysDir, dir);
                });
                //更新redis
                String jsonStr = JSONUtil.toJsonStr(sysDictionary);
                redisUtil.setCacheObject("sys_dictionary", jsonStr);
                return true;
            }
            return false;
        } else {
            //新增主字典
            boolean mainDirFlag = this.save(baseSysDir);
            List<BaseSysSubDir> subDirs = baseSysDir.getSubDirs();
            boolean subDirFlag = false;
            if (CollectionUtil.isNotEmpty(subDirs)) {
                //新增子级字典
                subDirs.forEach(subDir -> subDir.setDirId(baseSysDir.getId()));
                subDirFlag = baseSysSubDirService.saveBatch(subDirs);
            }
            //新增完成时 存入redis缓存
            if (mainDirFlag && subDirFlag) {
                //先获取redis中的数据
                List<BaseSysDir> baseSysDirList = getObjFromRedis("sys_dictionary");
                //添加新字典
                baseSysDirList.add(baseSysDir);
                String jsonStr = JSONUtil.toJsonStr(baseSysDirList);
                //存入redis
                redisUtil.setCacheObject("sys_dictionary", jsonStr);
                return true;
            }
            return false;
        }
    }

    /**
     * 获取redis中数据并转为对象list
     *
     * @param key
     * @return
     */
    public List<BaseSysDir> getObjFromRedis(String key) {
        String sysDictionary = redisUtil.getCacheObject("sys_dictionary");
        List<BaseSysDir> baseSysDirList = new ArrayList<>();
        if (StrUtil.isNotBlank(sysDictionary)) {
            baseSysDirList = JSONUtil.toList(sysDictionary, BaseSysDir.class);
        }else {
            //如果redis中为空 则查询数据库
            baseSysDirList = getDataFromMysql();
            log.info("查询redis数据为空 数据来源于数据库");
        }
        return baseSysDirList;
    }

    /**
     * 从数据库获取字典列表数据
     */
    private List<BaseSysDir> getDataFromMysql() {
        List<BaseSysDir> allDataList = new ArrayList<>();
        List<BaseSysDir> mainDirList = this.list();
        Map<Long, List<BaseSysSubDir>> subDirMap = getSubDirList(mainDirList.stream().map(BaseSysDir::getId).collect(Collectors.toList()));
        mainDirList.forEach(baseSysDir -> {
            List<BaseSysSubDir> subDirList = subDirMap.get(baseSysDir.getId());
            if (CollectionUtil.isNotEmpty(subDirList)) {
                baseSysDir.setSubDirs(subDirList);
            }
            allDataList.add(baseSysDir);
        });
        return allDataList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteDir(BatchIds<Long> ids) {
        // 删除主字典
        boolean mainDirFlag = this.removeBatchByIds(ids.getIds());
        // 删除关联子级字典
        boolean subDirFlag = baseSysSubDirService.remove(new LambdaQueryWrapper<BaseSysSubDir>().in(BaseSysSubDir::getDirId, ids.getIds()));
        if (subDirFlag && mainDirFlag) {
            //删除redis中的数据
            List<BaseSysDir> sysDictionary = getObjFromRedis("sys_dictionary");
            //删除主字典及子字典
            sysDictionary.removeIf(dir -> ids.getIds().contains(dir.getId()));
            String jsonStr = JSONUtil.toJsonStr(sysDictionary);
            redisUtil.setCacheObject("sys_dictionary", jsonStr);
            return true;
        }
        return false;
    }

    @Override
    public IPage<BaseSysDir> listDir(DirDto dirDto) {

        //构建分页查询条件
        Page<BaseSysDir> page = new Page<>(dirDto.getPageIndex(), dirDto.getPageSize());
        LambdaQueryWrapper<BaseSysDir> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(dirDto.getQueryName()), BaseSysDir::getDirName, dirDto.getQueryName())
                .eq(StrUtil.isNotBlank(dirDto.getQueryType()), BaseSysDir::getDirType, dirDto.getQueryType())
                .orderByDesc(BaseSysDir::getUpdateTime);
        //查询主字典
        page = baseSysDirMapper.selectPage(page, queryWrapper);
        List<BaseSysDir> baseSysDirs = page.getRecords();
        //查询子级字典 排序
        if (CollectionUtil.isNotEmpty(baseSysDirs)) {
        Map<Long, List<BaseSysSubDir>> subDirMap = getSubDirList(baseSysDirs.stream().map(BaseSysDir::getId).collect(Collectors.toList()));
            baseSysDirs.forEach(baseSysDir -> {
                List<BaseSysSubDir> subDirList = subDirMap.get(baseSysDir.getId());
                if (CollectionUtil.isNotEmpty(subDirList)) {
                    //排序
                    sortByDirSortRule(baseSysDir.getDirSort(), subDirList);
                    baseSysDir.setSubDirs(subDirList);
                }
            });
        }

        return page;
    }


    @Override
    public Map<Long, List<BaseSysSubDir>> getSubDirList(List<Long> ids) {
        List<BaseSysSubDir> subDirList = baseSysSubDirService.list(new LambdaQueryWrapper<BaseSysSubDir>().in(BaseSysSubDir::getDirId, ids));
        return subDirList.stream()
                .collect(Collectors.groupingBy(BaseSysSubDir::getDirId));
    }

    @Override
    public void exportDir(BatchIds<Long> exportIds, HttpServletResponse response) {
        try {
            List<BaseSysDir> baseSysDirList = this.listByIds(exportIds.getIds());
            List<DirExportVo> dirExportVoList = new ArrayList<>();
            //查询子级字典
            Map<Long, List<BaseSysSubDir>> subDirMap = getSubDirList(exportIds.getIds());

            baseSysDirList.forEach(mainDir -> {
                //主字典属性拷贝
                DirExportVo dirExportVo = BeanUtil.copyProperties(mainDir, DirExportVo.class);
                dirExportVoList.add(dirExportVo);
                List<BaseSysSubDir> subDirList = subDirMap.get(mainDir.getId());
                List<SubDirExportVo> subDirExportVoList = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(subDirList)) {
                    subDirList.forEach(subDir -> {
                        //子字典属性拷贝
                        SubDirExportVo subDirExportVo = BeanUtil.copyProperties(subDir, SubDirExportVo.class);
                        subDirExportVoList.add(subDirExportVo);
                    });
                }
                dirExportVo.setSubDirs(subDirExportVoList);
            });

            String mainDirJson = JSONUtil.toJsonPrettyStr(dirExportVoList);
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            // 获取当前时间
            Date currentDate = new Date();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            String currentTime = dateFormat.format(currentDate);
            //文件名
            String fileName = "dir_" + currentTime + ".json";
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            // 将 JSON 数据写入响应体
            response.getWriter().write(mainDirJson);
            log.info("-------导出字典数据成功---------");
        } catch (IOException e) {
            log.info("-------导出字典数据失败---------");
            throw new ServiceException(ResultCode.EXPORT_FAIL);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean importDir(String type, MultipartFile file) {
        if (ObjectUtil.isEmpty(file)) {
            throw new ServiceException(ResultCode.NOT_NULL, "导入文件");
        }
        if (!Objects.requireNonNull(file.getOriginalFilename()).endsWith(".json")) {
            throw new ServiceException(ResultCode.SO_FAIL, "导入json文件");
        }
        return switch (type) {
            case "1" ->
                //增量导入
                    importDirIncrement(file);
            case "2" ->
                //追加或更新
                    importDirByAddOrUpdate(file);
            case "3" ->
                //全量导入
                    importDirFull(file);
            default -> false;
        };
    }

    /**
     * 增量导入字典
     * @param file
     * @return
     */
    private boolean importDirIncrement(MultipartFile file) {
        //检查导入的文件中是否有重复数据
        List<BaseSysDir> dirList = checkImportData(file);

        //检查导入的文件是否与库中文件重复
        if (CollectionUtil.isNotEmpty(dirList)) {
            // 检查名称重复
            checkDuplicates(dirList, BaseSysDir::getDirName, "主字典名称");
            // 检查编码重复
            checkDuplicates(dirList, BaseSysDir::getDirCode, "主字典编码");
        }

        //保存主字典及子字典数据
        boolean mainFlag = saveDir(dirList);

        //更新redis
        if (mainFlag) {
            //先获取redis中的数据
            List<BaseSysDir> baseSysDirList = getObjFromRedis("sys_dictionary");
            //添加新字典
            baseSysDirList.addAll(dirList);
            String jsonStr = JSONUtil.toJsonStr(baseSysDirList);
            //存入redis
            redisUtil.setCacheObject("sys_dictionary", jsonStr);
            return true;
        }
        return  false;
    }

    /**
     * 全量导入
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean importDirFull(MultipartFile file) {
        //判断导入文件是否有重复数据
        List<BaseSysDir> dirList = checkImportData(file);

        //清除原有所有字典数据 主字典和子字典
        this.remove(new QueryWrapper<>());
        baseSysSubDirService.remove(new QueryWrapper<>());

        //保存字典数据
        boolean flag = saveDir(dirList);

        //更新redis中所有的字典数据
        if (flag && CollectionUtil.isNotEmpty(dirList)) {
            redisUtil.setCacheObject("sys_dictionary", JSON.toJSONString(dirList));
        }
        return flag;
    }


    /**
     * 追加或更新
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean importDirByAddOrUpdate(MultipartFile file) {
            //判断导入的文件中code是否重复
            List<BaseSysDir> dirList = checkImportData(file);

            //从库中获取数据根据code分组
            List<BaseSysDir> baseDirList = this.list();
            Map<String, BaseSysDir> dirCodeMap = baseDirList.stream().collect(Collectors.toMap(
                        BaseSysDir::getDirCode,
                        //值映射
                        dir -> dir,
                        // 键冲突时的合并函数
                        (existing, replacement) -> existing,
                        HashMap::new
                ));

            // 区分新增和更新的数据
            AddOrUpdateDto addOrUpdateDto = partitionDirs(dirList, dirCodeMap);
            List<BaseSysDir> addMainDirList = addOrUpdateDto.getAddList();
            List<BaseSysDir> updateMainDirList = addOrUpdateDto.getUpdateList();

            // 保存新增数据
            boolean addResult = saveDir(addMainDirList);

            // 更新已有数据
            boolean updateResult = updateExistingDirs(updateMainDirList, dirCodeMap);

            //更新redis数据
            if (addResult || updateResult) {
                List<BaseSysDir> dataFromMysql = getDataFromMysql();
                //存入redis
                redisUtil.setCacheObject("sys_dictionary", JSON.toJSONString(dataFromMysql));
                return true;
            }
            return false ;
    }

    /**
     * 区分是更新还是新增数据
     * @param dirList
     * @param dirCodeMap
     * @return
     */
    private AddOrUpdateDto partitionDirs(List<BaseSysDir> dirList, Map<String, BaseSysDir> dirCodeMap) {

        AddOrUpdateDto addOrUpdateDto = new AddOrUpdateDto();

        //导入文件存在code值与库中code值一样 则更新 否者新增
        List<BaseSysDir> addList = new ArrayList<>();
        List<BaseSysDir> updateList = new ArrayList<>();
        for (BaseSysDir dir : dirList) {
            if (dirCodeMap.containsKey(dir.getDirCode())) {
                updateList.add(dir);
            } else {
               addList.add(dir);
            }
        }
        addOrUpdateDto.setAddList(addList);
        addOrUpdateDto.setUpdateList(updateList);
        
        return addOrUpdateDto;
    }

    /**
     *更新已存在的字典对象  适用于追加或更新
     * @param updateMainDirList
     * @param dirCodeMap
     * @return
     */
    private boolean updateExistingDirs(List<BaseSysDir> updateMainDirList,  Map<String, BaseSysDir> dirCodeMap) {
        if (CollectionUtil.isNotEmpty(updateMainDirList)) {
            List<BaseSysDir> needUpdateDirList = new ArrayList<>();
            //收集需要更新的子字典对象
            List<Long> subDirList = new ArrayList<>();
            for (BaseSysDir dir : updateMainDirList) {
                BaseSysDir baseSysDir1 = dirCodeMap.get(dir.getDirCode());
                dir.setId(baseSysDir1.getId());
              //添加到要更新的集合中
              needUpdateDirList.add(dir);
            }
            // 批量更新主字典
            boolean mainUpdateResult = this.updateBatchById(needUpdateDirList);

            // 批量更新子字典 更新前先删除收集到的id
            needUpdateDirList.forEach(mainDir ->{
                subDirList.add(mainDir.getId());
            });
            baseSysSubDirService.remove(new LambdaQueryWrapper<BaseSysSubDir>().in(BaseSysSubDir::getDirId, subDirList));
            boolean subUpdateResult = baseSysSubDirService.saveBatch(
                    //遍历每个主字典 同时给当前主字典下子字典设置dirId
                    needUpdateDirList.stream()
                            .flatMap(mainDir -> mainDir.getSubDirs().stream().peek(subDir -> subDir.setDirId(mainDir.getId())))
                            .collect(Collectors.toList())
            );
            return mainUpdateResult && subUpdateResult;
        }
        return false;
    }
    /**
     *检查导入文件列表与库中数据是否重复
     * @param dirList
     * @param extractor
     * @param fieldName
     * @param <T>
     */
    private <T> void checkDuplicates(List<BaseSysDir> dirList, java.util.function.Function<BaseSysDir, T> extractor, String fieldName) {
        List<T> savedList = this.list().stream().map(extractor).toList();
        Set<T> savedSet = new HashSet<>(savedList);
        List<T> duplicateList = dirList.stream().map(extractor).filter(savedSet::contains).toList();
        if (CollectionUtil.isNotEmpty(duplicateList)) {
            for (T item : duplicateList) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, fieldName + ", " + item + "已在库中");
            }
        }
    }

    /**
     * 保存字典数据
     *
     * @param dirList
     * @return
     */
    private boolean saveDir(List<BaseSysDir> dirList) {
        //保存主字典数据
        boolean mainFlag = false;
        List<BaseSysSubDir> subDirList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dirList)) {
            mainFlag = this.saveBatch(dirList);
            //收集子字典
            subDirList = dirList.stream().filter(mainDir -> CollectionUtil.isNotEmpty(mainDir.getSubDirs())).flatMap(mainDir -> mainDir.getSubDirs().stream().peek(subDir -> subDir.setDirId(mainDir.getId()))).toList();
        }
        //保存子字典
        boolean subFlag = false;
        if (CollectionUtil.isNotEmpty(subDirList)) {
            subFlag = baseSysSubDirService.saveBatch(subDirList);
        }
        return subFlag && mainFlag;
    }

    /**
     * 读取文件并检查导入的文件内是否有重复数据
     *
     * @param file
     */
    private List<BaseSysDir> checkImportData(MultipartFile file) {

        try {
            //读取json文件
            String disJson = IoUtil.readUtf8(file.getInputStream());
            JSONArray array = JSONUtil.parseArray(disJson);
            List<BaseSysDir> dirList = JSONUtil.toList(array, BaseSysDir.class);

            //对主字典按名称分组
            Map<String, List<BaseSysDir>> groupByNameMap = dirList.stream().collect(Collectors.groupingBy(BaseSysDir::getDirName));
            groupByNameMap.forEach((key, value) -> {
                //如果每个分组下数量大于1表示有重复数据
                if (value.size() > 1) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "导入文件数据重复,主字典重复名称:" + key);
                }
            });
            //对主字典按编码分组
            Map<String, List<BaseSysDir>> groupByCodeMap = dirList.stream().collect(Collectors.groupingBy(BaseSysDir::getDirCode));
            groupByCodeMap.forEach((key, value) -> {
                //如果每个分组下数量大于1表示有重复数据
                if (value.size() > 1) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "导入文件数据重复,主字典重复编码:" + key);
                }
            });

            //判断每个主字典下子字典列表内是否有重复
            dirList.forEach(mainDir ->{
                List<BaseSysSubDir> subDirList = mainDir.getSubDirs();
                //校验子导入的子字典的名称是否重复
                checkSubDuplicateField(subDirList, null, BaseSysSubDir::getSubDirName, "子字典名称");
                //校验子导入的子字典的编码是否重复
                checkSubDuplicateField(subDirList, null, BaseSysSubDir::getSubDirCode, "子字典编码");
            });

            return dirList;
        } catch (IOException e) {
            log.error("导入字典数据失败,异常信息", e);
        }
        return null;
    }


    @Override
    public List<BaseSysDir> getDirList() {
        //从redis获取所有字典数据
        List<BaseSysDir> sysDicList = getObjFromRedis("sys_dictionary");
        //对字典进行排序
        if (CollectionUtil.isNotEmpty(sysDicList)) {
            //收集子字典
            sysDicList.forEach(mainDir -> {
                List<BaseSysSubDir> subDirList = new ArrayList<>(mainDir.getSubDirs());
                //排序
                sortByDirSortRule(mainDir.getDirSort(), subDirList);
                mainDir.setSubDirs(subDirList);
            });
        }
        return sysDicList ;
    }

    @Override
    public boolean refreshDirCache() {
        log.info("刷新字典缓存");
        //从数据库查询所有字典数据
        List<BaseSysDir> mainDirList = this.list();
        List<BaseSysSubDir> subDirList = baseSysSubDirService.list();
        //构建存储对象
        List<BaseSysDir> baseSysDirList = new ArrayList<>();
        mainDirList.forEach(mainDir -> {
            List<BaseSysSubDir> subDirs = subDirList.stream().filter(subDir -> subDir.getDirId().equals(mainDir.getId())).toList();
            mainDir.setSubDirs(subDirs);
            baseSysDirList.add(mainDir);
        });
            redisUtil.setCacheObject("sys_dictionary", JSONUtil.toJsonStr(baseSysDirList));
            log.info("刷新字典数据成功");
        return redisUtil.hasKey("sys_dictionary");
    }

    /**
     * 排序
     */
    private void sortByDirSortRule(String disSort, List<BaseSysSubDir> subDirList) {
        if (CollectionUtil.isEmpty(subDirList)) {
            return;
        }

        if (StrUtil.isNotEmpty(disSort)) {
            switch (disSort) {
                // 自然排序升序
                case DirSortConstant.NATURAL_SORT_ASC:
                    subDirList.sort(Comparator.comparing(sub -> sub.getSubDirCode().toLowerCase()));
                    break;
                // 自然排序降序
                case DirSortConstant.NATURAL_SORT_DESC:
                    subDirList.sort(Comparator.comparing((BaseSysSubDir sub) -> sub.getSubDirCode().toLowerCase()).reversed());
                    break;
                // 使用排序数字升序
                case DirSortConstant.NUMBER_SORT_ASC:
                    subDirList.sort(Comparator.comparingInt(BaseSysSubDir::getSubDirNo));
                    break;
                // 使用排序数字降序
                case DirSortConstant.NUMBER_SORT_DESC:
                    subDirList.sort(Comparator.comparingInt(BaseSysSubDir::getSubDirNo).reversed());
                    break;
                // 按照修改时间升序
                case DirSortConstant.MODIFY_TIME_SORT_ASC:
                    subDirList.sort(Comparator.comparing(BaseSysSubDir::getUpdateTime));
                    break;
                // 按照修改时间降序
                case DirSortConstant.MODIFY_TIME_SORT_DESC:
                    subDirList.sort(Comparator.comparing(BaseSysSubDir::getUpdateTime).reversed());
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 校验新增 更新字典数据
     * @param baseSysDir 字典数据
     */
    private void checkDictionary(BaseSysDir baseSysDir) {
        //更新校验
        if (ObjectUtil.isNotEmpty(baseSysDir.getId())) {
            //库中存在的主字典字内容
            BaseSysDir savedMainDir = this.getById(baseSysDir.getId());
            //检验主字典名称及编码 以及判断子字典条数
            List<BaseSysSubDir> subDirs = checkMainAndSubDirsNumber(baseSysDir, savedMainDir);
            //库中存在同主字典下的子字典数据
            List<BaseSysSubDir> savedSubDirList = baseSysSubDirService.list(new LambdaQueryWrapper<BaseSysSubDir>().eq(BaseSysSubDir::getDirId, baseSysDir.getId()));
            checkSubDuplicateField(subDirs, savedSubDirList, BaseSysSubDir::getSubDirName, "子字典名称");
            checkSubDuplicateField(subDirs, savedSubDirList, BaseSysSubDir::getSubDirCode, "子字典编码");

        } else {
            //新增校验
            BaseSysDir savedMainDir = this.getOne(new LambdaQueryWrapper<BaseSysDir>().eq(StrUtil.isNotEmpty(baseSysDir.getDirName()), BaseSysDir::getDirName, baseSysDir.getDirName()));
            if (ObjectUtil.isEmpty(savedMainDir)) {
                savedMainDir = this.getOne(new LambdaQueryWrapper<BaseSysDir>().eq(StrUtil.isNotEmpty(baseSysDir.getDirCode()), BaseSysDir::getDirCode, baseSysDir.getDirCode()));
            }
            //校验主字典自身是否重复以及校验是否和库中数据重复
            List<BaseSysSubDir> subDirs = checkMainAndSubDirsNumber(baseSysDir, savedMainDir);
            //检验子字典名称自身是否重复以及与库中数据是否重复
            checkSubDuplicateField(subDirs, null, BaseSysSubDir::getSubDirName, "子字典名称");
            //检验子字典编码自身是否重复以及与库中数据是否重复
            checkSubDuplicateField(subDirs, null, BaseSysSubDir::getSubDirCode, "子字典编码");
        }

    }

    /**
     * 检查主字典的特定字段是否重复
     *
     * @param newDir         新增或更新字典
     * @param savedDir       库中已存在字典
     * @param fieldExtractor 需要检查的字段
     * @param fieldName      字段名称
     * @param <T>            数据类型
     * @throws ServiceException 如果存在重复数据则抛出异常
     */
    private <T> void checkMainDirDuplicateField(BaseSysDir newDir, BaseSysDir savedDir, Function<BaseSysDir, T> fieldExtractor, String fieldName) throws ServiceException {
       //使用fieldExtractor函数提取要校验的值
        T newValue = fieldExtractor.apply(newDir);
        LambdaQueryWrapper<BaseSysDir> queryWrapper = new LambdaQueryWrapper<>();
            if (ObjectUtil.isNotEmpty(savedDir)) {
                if (fieldName.equals("主字典名称")) {
                    queryWrapper.eq(BaseSysDir::getDirName, newValue);
                } else if (fieldName.equals("主字典编码")) {
                    queryWrapper.eq(BaseSysDir::getDirCode, newValue);
                }
                if (ObjectUtil.isNotEmpty(newDir.getId())) {
                    queryWrapper.ne(BaseSysDir::getId, newDir.getId());
                }
                BaseSysDir existingDir = baseSysDirMapper.selectOne(queryWrapper);
                if (existingDir != null) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, fieldName + " 已存在,不能重复：" + newValue);
                }
            }
    }

    /**
     * 校验子字典导入或新增、更新时是否重复 以及校验子字典是否和库里数据重复
     *
     * @param importList     导入的子字典列表
     * @param existingList   库中已有的子字典列表
     * @param fieldExtractor 字段提取器
     * @param fieldName      字段名称
     * @param <T>            字段类型
     * @throws ServiceException 异常
     * @description 遍历 existingList，对于每个 existingSubDir，使用 stream 和 anyMatch 方法检查它是否是正在更新的记录（即 importList 中是否存在具有相同 id 的记录）。
     * 如果不是正在更新的记录，则使用 fieldExtractor 提取该子字典的特定字段值，并将其添加到 existingFieldValues 集合中。
     */
    private static <T> void checkSubDuplicateField(List<BaseSysSubDir> importList, List<BaseSysSubDir> existingList, Function<BaseSysSubDir, T> fieldExtractor, String fieldName) throws ServiceException {

        //校验新增或者导入内部子字典是否重复
        checkInternalDuplicates(importList,fieldExtractor, fieldName);

        // 获取库中已有子字典的特定字段集合，同时排除正在更新的记录
        Set<T> existingFieldValues = new HashSet<>();
        if (CollectionUtil.isNotEmpty(existingList)) {
            for (BaseSysSubDir existingSubDir : existingList) {
                boolean isUpdateRecord = importList.stream()
                        .anyMatch(importSubDir -> importSubDir.getId() != null && importSubDir.getId().equals(existingSubDir.getId()));
                if (!isUpdateRecord) {
                    existingFieldValues.add(fieldExtractor.apply(existingSubDir));
                }
            }
        }
        // 找出导入数据中重复的特定字段值
        List<T> duplicateFieldValues = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(importList)) {
            duplicateFieldValues = importList.stream()
                    .map(fieldExtractor)
                    .filter(existingFieldValues::contains)
                    .toList();
        }

        if (CollectionUtil.isNotEmpty(duplicateFieldValues)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, fieldName + ",重复: " + duplicateFieldValues);
        }
    }


    /**
     * 检验主字典名称及编码 以及判断子字典条数
     *
     * @param baseSysDir
     * @param savedMainDir
     * @return
     */
    private @NotNull List<BaseSysSubDir> checkMainAndSubDirsNumber(BaseSysDir baseSysDir, BaseSysDir savedMainDir) {
        //校验主字典名称
        checkMainDirDuplicateField(baseSysDir, savedMainDir, BaseSysDir::getDirName, "主字典名称");
        //校验主字典编码
        checkMainDirDuplicateField(baseSysDir, savedMainDir, BaseSysDir::getDirCode, "主字典编码");

        List<BaseSysSubDir> subDirs = baseSysDir.getSubDirs();
        if (CollectionUtil.isEmpty(subDirs)) {
            throw new ServiceException(ResultCode.NOT_NULL, "子字典数据");
        }
        return subDirs;
    }

    /**
     * 判断新增或者是导入数据内部子字典中数据是否重复
     *
     * @param importList     导入或新增子之字典数据
     * @param fieldExtractor 检验字段
     * @param fieldName      字段名称
     * @param <T>            数据类型
     * @throws ServiceException 异常
     */
    private static <T> void checkInternalDuplicates(List<BaseSysSubDir> importList, Function<BaseSysSubDir, T> fieldExtractor, String fieldName) throws ServiceException {
        Set<T> seenValues = new HashSet<>();
        List<T> internalDuplicates = new ArrayList<>();
        for (BaseSysSubDir subDir : importList) {
            //提取需要校验的值
            T value = fieldExtractor.apply(subDir);
            //如果添加操作返回 false，这表明该字段值已经存在于 seenValues 中，也就意味着出现了重复
            if (!seenValues.add(value)) {
                internalDuplicates.add(value);
            }
        }
        if (CollectionUtil.isNotEmpty(internalDuplicates)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE,fieldName + ",重复: " + internalDuplicates);
        }
    }
}
