package com.teamway.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.common.constant.CameraStateConstant;
import com.teamway.base.common.constant.CameraTypeConstant;
import com.teamway.base.common.properties.CameraPresetProperties;
import com.teamway.base.dto.preset.CameraPresetQueryDto;
import com.teamway.base.dto.preset.CameraPresetRefreshDto;
import com.teamway.base.dto.stream.StreamPresetDto;
import com.teamway.base.dto.stream.StreamResultDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CameraPreset;
import com.teamway.base.entity.PollCamera;
import com.teamway.base.mapper.CameraMapper;
import com.teamway.base.mapper.CameraPresetMapper;
import com.teamway.base.mapper.PollCameraMapper;
import com.teamway.base.service.CameraPresetService;
import com.teamway.base.util.ScreenshotUtils;
import com.teamway.base.vo.CameraPresetVo;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.util.ImageUtils;
import com.teamway.common.util.SnowflakeUtils;
import com.teamway.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-25
 */
@Service
@SuppressWarnings("all")
@Slf4j
public class CameraPresetServiceImpl extends ServiceImpl<CameraPresetMapper, CameraPreset> implements CameraPresetService {

    @Autowired
    private CameraPresetProperties cameraPresetProperties;
    @Autowired
    private CameraMapper cameraMapper;
    @Autowired
    private PollCameraMapper pollCameraMapper;

    /**
     * 摄像机预置位图片存储桶
     */
    private final String destBucket = "camera-preset";

    @Override
    public List<CameraPreset> findList(CameraPresetQueryDto cameraPresetQueryDto) {
        //每台摄像机都有默认预置位
        List<CameraPreset> list = new ArrayList<>();
        CameraPreset cameraPreset = new CameraPreset();
        cameraPreset.setId(-1L);
        cameraPreset.setNo("0");
        cameraPreset.setName("默认预置位");
        cameraPreset.setCameraId(cameraPresetQueryDto.getCameraId());
        list.add(cameraPreset);
        //查询摄像机预置位
        LambdaQueryWrapper<CameraPreset> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CameraPreset::getCameraId, cameraPresetQueryDto.getCameraId());
        queryWrapper.like(StrUtil.isNotBlank(cameraPresetQueryDto.getName()), CameraPreset::getName, cameraPresetQueryDto.getName());
        //排序   将编号转成整型   为空、字母排在前面
        queryWrapper.last("ORDER BY CONVERT(no,UNSIGNED)");
        list.addAll(this.list(queryWrapper));
        return list;
    }

    @Override
    public List<CameraPresetVo> selectByCameraIds(BatchIds<Long> ids) {
        List<Camera> cameraList = cameraMapper.selectBatchIds(ids.getIds());
        List<CameraPresetVo> cameraPresetList = new ArrayList<>();
        cameraList.forEach(camera -> {
            CameraPresetVo cameraPresetVo = new CameraPresetVo();
            cameraPresetVo.setCameraId(camera.getId());
            cameraPresetVo.setCameraName(camera.getName());
            CameraPresetQueryDto cameraPresetQueryDto = new CameraPresetQueryDto();
            cameraPresetQueryDto.setCameraId(camera.getId());
            cameraPresetVo.setCameraPresetList(this.findList(cameraPresetQueryDto));
            cameraPresetList.add(cameraPresetVo);
        });
        return cameraPresetList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized boolean addCameraPreset(CameraPreset cameraPreset) {
        LambdaQueryWrapper<CameraPreset> query = new LambdaQueryWrapper<>();
        query.eq(CameraPreset::getCameraId, cameraPreset.getCameraId());
        query.eq(CameraPreset::getName, cameraPreset.getName());
        CameraPreset preset = baseMapper.selectOne(query);
        if (preset != null) {
            throw new ServiceException(ResultCode.EXIST, "摄像机预置位名称");
        }
        //校验摄像机存在、不在线、类型
        Camera camera = cameraMapper.selectById(cameraPreset.getCameraId());
        if (camera == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
        }
        if (CameraStateConstant.OFF_LINE.equals(camera.getState())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "摄像机不在线");
        }
        if (CameraTypeConstant.TYPE_GUN.equals(camera.getType())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "枪机不能添加预置位");
        } else if (CameraTypeConstant.TYPE_DEMO.equals(camera.getType())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "半球不能添加预置位");
        }
        //查询摄像机预置位
        Map<String, Object> requestMap = new HashMap(2);
        requestMap.put("mainId", camera.getMainId());
        requestMap.put("subId", camera.getSubId());
        String jsonResult = HttpUtil.get(cameraPresetProperties.getGet(), requestMap, cameraPresetProperties.getTimeOut());
        StreamResultDto streamResultDto = JSONObject.parseObject(jsonResult, StreamResultDto.class);
        if (streamResultDto.getCode() != ResultCode.SUCCESS.getCode()) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "调用流媒体添加预置位失败");
        }

        List<StreamPresetDto> streamPresetDtoList = JSON.parseArray(streamResultDto.getData().toJSONString(), StreamPresetDto.class);
        List<Integer> noList = streamPresetDtoList.stream().map(StreamPresetDto::getPresetId).collect(Collectors.toList());
        Integer[] nos = noList.toArray(new Integer[noList.size()]);
        //获取摄像机最大预置位编号
        Integer presetId = solve(nos);
        if (presetId > 255) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "摄像机最多添加255个预置位");
        }
        //判断预置位编号是否存在    存在删除
        LambdaQueryWrapper<CameraPreset> cameraQueryWrapper = new LambdaQueryWrapper<>();
        cameraQueryWrapper.eq(CameraPreset::getCameraId, cameraPreset.getCameraId());
        cameraQueryWrapper.eq(CameraPreset::getNo, presetId);
        CameraPreset repetitionNo = this.baseMapper.selectOne(cameraQueryWrapper);
        if (repetitionNo != null) {
            this.baseMapper.deleteById(repetitionNo.getId());
        }
        cameraPreset.setNo(presetId.toString());
        cameraPreset.setImageUrl(uploadPicture(camera));
        this.save(cameraPreset);
        //查找相同ip摄像机  并添加预置位
        List<Camera> cameraList = getCameraByIP(camera);
        if (CollUtil.isNotEmpty(cameraList)) {
            for (Camera correlationCamera : cameraList) {
                cameraPreset.setId(SnowflakeUtils.getSnowflakeId());
                cameraPreset.setCameraId(correlationCamera.getId());
                cameraPreset.setImageUrl(uploadPicture(correlationCamera));
                boolean save = this.save(cameraPreset);
                log.info("添加相同ip摄像机预置位成功:{}", save);
            }
        }

        Map<String, Object> map = new HashMap(3);
        map.put("mainId", camera.getMainId());
        map.put("subId", camera.getSubId());
        map.put("presetId", presetId);
        String setResult = HttpUtil.get(cameraPresetProperties.getSet(), map, cameraPresetProperties.getTimeOut());
        StreamResultDto setStreamResultDto = JSONObject.parseObject(setResult, StreamResultDto.class);
        if (setStreamResultDto.getCode() != ResultCode.SUCCESS.getCode()) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "调用流媒体添加预置位失败");
        }
        return true;
    }

    /**
     * 查找某个摄像机的红外或可见光摄像机
     *
     * @param camera
     * @return
     */
    private List<Camera> getCameraByIP(Camera camera) {
        //根据ip查询所有摄像机  不包含本身这台
        LambdaQueryWrapper<Camera> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(Camera::getId, camera.getId());
        queryWrapper.eq(Camera::getIp, camera.getIp());
        return cameraMapper.selectList(queryWrapper);
    }

    /**
     * 找缺失数字
     *
     * @param a 整型一维数组 给定的数字串
     * @return Long
     */
    private Integer solve(Integer[] a) {
        if (a == null || a.length == 0) {
            return 1;
        }
        Integer start = a[0];
        for (int i = 1; i < a.length; i++) {
            if (a[i] - a[i - 1] == 1) {
                start = a[i];
            } else {
                return start + 1;
            }
        }
        return start + 1;
    }

    /**
     * 保存图片
     *
     * @param camera
     * @return
     */
    private String uploadPicture(Camera camera) {
        try {
            //抓图
            ScreenshotUtils.ScreenshotDto screenshotDto = ScreenshotUtils.ScreenshotDto.builder().ip(camera.getIp()).port(camera.getPort())
                    .username(camera.getUsername()).password(camera.getPassword()).channel(Integer.valueOf(camera.getChannel()))
                    .factory(camera.getFactory()).type(camera.getType()).build();
            String screenshot = ScreenshotUtils.screenshot(screenshotDto);
            //上传图片
            return ImageUtils.saveImage(screenshot, destBucket);
        } catch (Exception e) {
            log.error("保存图片错误:", e);
            return "";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCameraPreset(CameraPreset cameraPreset) {
        if (cameraPreset.getId() == null) {
            throw new ServiceException(ResultCode.NOT_NULL, "预置位ID");
        }
        //校验预置位存在
        LambdaQueryWrapper<CameraPreset> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CameraPreset::getId, cameraPreset.getId());
        CameraPreset cameraPresetById = baseMapper.selectOne(queryWrapper);
        if (cameraPresetById == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "预置位");
        }
        LambdaQueryWrapper<CameraPreset> query = new LambdaQueryWrapper<>();
        query.eq(CameraPreset::getCameraId, cameraPresetById.getCameraId());
        query.eq(CameraPreset::getName, cameraPreset.getName());
        query.ne(CameraPreset::getId, cameraPreset.getId());
        List<CameraPreset> cameraPresets = baseMapper.selectList(query);
        if (CollUtil.isNotEmpty(cameraPresets)) {
            throw new ServiceException(ResultCode.EXIST, "预置位名称");
        }
        //校验摄像机存在、不在线
        Camera camera = cameraMapper.selectById(cameraPresetById.getCameraId());
        if (camera == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
        }
        if (CameraStateConstant.OFF_LINE.equals(camera.getState())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "摄像机不在线");
        }
        cameraPreset.setImageUrl(uploadPicture(camera));
        boolean updateById = this.updateById(cameraPreset);

        //查找相同ip摄像机  并修改预置位
        List<Camera> cameraList = getCameraByIP(camera);
        if (CollUtil.isNotEmpty(cameraList)) {
            for (Camera correlationCamera : cameraList) {
                LambdaUpdateWrapper<CameraPreset> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(CameraPreset::getCameraId, correlationCamera.getId());
                updateWrapper.eq(CameraPreset::getNo, cameraPresetById.getNo());
                updateWrapper.set(CameraPreset::getName, cameraPreset.getName());
                updateWrapper.set(CameraPreset::getImageUrl, uploadPicture(correlationCamera));
                boolean update = this.update(updateWrapper);
                log.info("修改相同ip摄像机预置位成功:{}", update);
            }
        }

        //调用流媒体修改预置位
        Map<String, Object> map = new HashMap(3);
        map.put("mainId", camera.getMainId());
        map.put("subId", camera.getSubId());
        map.put("presetId", cameraPresetById.getNo());
        String result = HttpUtil.get(cameraPresetProperties.getSet(), map, cameraPresetProperties.getTimeOut());
        StreamResultDto setStreamResultDto = JSONObject.parseObject(result, StreamResultDto.class);
        if (setStreamResultDto.getCode() != ResultCode.SUCCESS.getCode()) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "调用流媒体修改预置位失败");
        }
        return updateById;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delBatchCameraPreset(BatchIds<Long> batchIds) {
        //校验是否关联轮巡组
        LambdaQueryWrapper<PollCamera> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PollCamera::getCameraPresetId, batchIds.getIds());
        List<PollCamera> pollCameraList = pollCameraMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(pollCameraList)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "预置位关联视频轮巡组，不能删除");
        }
        for (Long id : batchIds.getIds()) {
            CameraPreset cameraPreset = baseMapper.selectById(id);
            if (cameraPreset == null) {
                throw new ServiceException(ResultCode.NOT_EXIST, "预置位");
            }
            Camera camera = cameraMapper.selectById(cameraPreset.getCameraId());
            if (camera == null) {
                throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
            }
            if (CameraStateConstant.OFF_LINE.equals(camera.getState())) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "摄像机不在线");
            }
            //查找相同IP摄像机  并修改预置位
            List<Camera> cameraList = getCameraByIP(camera);
            if (CollUtil.isNotEmpty(cameraList)) {
                List<Long> cameraIds = cameraList.stream().map(Camera::getId).toList();
                LambdaQueryWrapper<CameraPreset> presetQueryWrapper = new LambdaQueryWrapper<>();
                presetQueryWrapper.in(CameraPreset::getCameraId, cameraIds);
                presetQueryWrapper.eq(CameraPreset::getNo, cameraPreset.getNo());
                boolean remove = this.remove(presetQueryWrapper);
                log.info("删除相同ip摄像机预置位成功:{}", remove);
            }
            Map<String, Object> map = new HashMap(3);
            map.put("mainId", camera.getMainId());
            map.put("subId", camera.getSubId());
            map.put("presetId", cameraPreset.getNo());
            String result = HttpUtil.get(cameraPresetProperties.getDelete(), map, cameraPresetProperties.getTimeOut());
            StreamResultDto setStreamResultDto = JSONObject.parseObject(result, StreamResultDto.class);
            if (setStreamResultDto.getCode() != ResultCode.SUCCESS.getCode()) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "调用流媒体删除预置位失败");
            }
            this.removeById(id);
        }
        return true;
    }


    @Override
    public boolean refresh(CameraPresetRefreshDto cameraPresetRefreshDto) {
        Camera camera = cameraMapper.selectById(cameraPresetRefreshDto.getCameraId());
        if (camera == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
        }
        if (CameraStateConstant.OFF_LINE.equals(camera.getState())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "摄像机不在线");
        }
        return refresh(camera);
    }

    @Override
    public boolean refresh(Camera camera) {
        Long cameraId = camera.getId();
        if (CameraTypeConstant.TYPE_GUN.equals(camera.getType())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "枪机刷新预置位无效");
        }
        Map<String, Object> map = new HashMap(2);
        map.put("mainId", camera.getMainId());
        map.put("subId", camera.getSubId());
        String jsonResult = HttpUtil.get(cameraPresetProperties.getGet(), map, cameraPresetProperties.getTimeOut());
        StreamResultDto streamResultDto = JSONObject.parseObject(jsonResult, StreamResultDto.class);
        if (streamResultDto.getCode() == ResultCode.SUCCESS.getCode()) {
            List<StreamPresetDto> streamPresetDtoList = JSONArray.parseArray(streamResultDto.getData().toJSONString(), StreamPresetDto.class);
            log.info("{}获取预置位数量:{}", camera.getId(), streamPresetDtoList.size());
            List<String> nos = new ArrayList<>();
            List<CameraPreset> newCameraPresetList = new ArrayList<>();
            List<CameraPreset> updateCameraPresetList = new ArrayList<>();
            for (StreamPresetDto streamPresetDto : streamPresetDtoList) {
                nos.add(String.valueOf(streamPresetDto.getPresetId()));
                LambdaQueryWrapper<CameraPreset> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(CameraPreset::getNo, streamPresetDto.getPresetId());
                queryWrapper.eq(CameraPreset::getCameraId, cameraId);
                CameraPreset cameraPresetByName = baseMapper.selectOne(queryWrapper);
                String streamPresetName = streamPresetDto.getPresetName();
                if (cameraPresetByName == null) {
                    //沒有就新增
                    CameraPreset newCameraPreset = new CameraPreset();
                    newCameraPreset.setCameraId(cameraId);
                    //未返回预置位名称   使用预置点+编号 命名
                    if ("true".equals(streamPresetName)) {
                        newCameraPreset.setName("预置点" + streamPresetDto.getPresetId());
                    } else {
                        newCameraPreset.setName(streamPresetName);
                    }
                    newCameraPreset.setNo(streamPresetDto.getPresetId().toString());
                    newCameraPresetList.add(newCameraPreset);
                } else if (!"true".equals(streamPresetName) && !cameraPresetByName.getName().equals(streamPresetName)) {
                    //如果数据库的预置位名称跟摄像机的预置位名称不一致，以摄像机的预置位名称为准
                    cameraPresetByName.setName(streamPresetName);
                    updateCameraPresetList.add(cameraPresetByName);
                }
            }
            if (CollUtil.isNotEmpty(newCameraPresetList)) {
                this.saveBatch(newCameraPresetList);
            }
            if (CollUtil.isNotEmpty(updateCameraPresetList)) {
                this.updateBatchById(updateCameraPresetList);
            }
            //多余的数据需删除
            LambdaQueryWrapper<CameraPreset> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CameraPreset::getCameraId, cameraId);
            queryWrapper.notIn(CollUtil.isNotEmpty(nos), CameraPreset::getNo, nos);
            List<CameraPreset> cameraPresets = baseMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(cameraPresets)) {
                List<Long> ids = cameraPresets.stream().map(CameraPreset::getId).collect(Collectors.toList());
                baseMapper.deleteBatchIds(ids);
            }
            return true;
        } else {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "调用流媒体刷新预置位失败");
        }
    }

    @Override
    public void timingRefresh() {
        //查询在线的球机或云台
        LambdaQueryWrapper<Camera> cameraLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cameraLambdaQueryWrapper.in(Camera::getType, List.of(CameraTypeConstant.TYPE_BALL, CameraTypeConstant.TYPE_PLATFORM));
        cameraLambdaQueryWrapper.eq(Camera::getState, CameraStateConstant.ON_LINE);
        List<Camera> cameraList = cameraMapper.selectList(cameraLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(cameraList)) {
            for (Camera camera : cameraList) {
                Map<String, Object> map = new HashMap(2);
                map.put("mainId", camera.getMainId());
                map.put("subId", camera.getSubId());
                String jsonResult = null;
                try {
                    jsonResult = HttpUtil.get(cameraPresetProperties.getGet(), map, cameraPresetProperties.getTimeOut());
                } catch (Exception e) {
                    log.info("获取摄像机预置位异常：摄像机：{}", camera, e);
                    continue;
                }
                StreamResultDto streamResultDto = JSONObject.parseObject(jsonResult, StreamResultDto.class);
                if (streamResultDto.getCode() == ResultCode.SUCCESS.getCode()) {
                    List<StreamPresetDto> streamPresetDtoList = JSON.parseArray(streamResultDto.getData().toJSONString(), StreamPresetDto.class);
                    List<String> nos = new ArrayList<>();
                    List<CameraPreset> newCameraPresetList = new ArrayList<>();
                    List<CameraPreset> updateCameraPresetList = new ArrayList<>();
                    for (StreamPresetDto streamPresetDto : streamPresetDtoList) {
                        nos.add(String.valueOf(streamPresetDto.getPresetId()));
                        LambdaQueryWrapper<CameraPreset> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(CameraPreset::getNo, streamPresetDto.getPresetId());
                        queryWrapper.eq(CameraPreset::getCameraId, camera.getId());
                        CameraPreset cameraPresetByName = null;
                        List<CameraPreset> cameraPresetList = baseMapper.selectList(queryWrapper);
                        if (CollUtil.isNotEmpty(cameraPresetList)) {
                            cameraPresetByName = cameraPresetList.getFirst();
                        }
                        if (CollUtil.isNotEmpty(cameraPresetList) && cameraPresetList.size() > 1) {
                            log.info("====================同步摄像机预置位重复摄像机：{},数据：{}", camera.getId(), JSON.toJSONString(cameraPresetList));
                        }
                        String streamPresetName = streamPresetDto.getPresetName();
                        if (cameraPresetByName == null) {
                            //沒有就新增
                            CameraPreset newCameraPreset = new CameraPreset();
                            newCameraPreset.setCameraId(camera.getId());
                            //未返回预置位名称   使用预置点+编号 命名
                            if ("true".equals(streamPresetName)) {
                                newCameraPreset.setName("预置点" + streamPresetDto.getPresetId());
                            } else {
                                newCameraPreset.setName(streamPresetName);
                            }
                            newCameraPreset.setNo(streamPresetDto.getPresetId().toString());
                            newCameraPresetList.add(newCameraPreset);
                        } else if (!"true".equals(streamPresetName) && !cameraPresetByName.getName().equals(streamPresetName)) {
                            //如果数据库的预置位名称跟摄像机的预置位名称不一致，以摄像机的预置位名称为准
                            cameraPresetByName.setName(streamPresetName);
                            updateCameraPresetList.add(cameraPresetByName);
                        }
                    }
                    if (CollUtil.isNotEmpty(newCameraPresetList)) {
                        this.saveBatch(newCameraPresetList);
                    }
                    if (CollUtil.isNotEmpty(updateCameraPresetList)) {
                        this.updateBatchById(updateCameraPresetList);
                    }
                    //多余的数据需删除
                    LambdaQueryWrapper<CameraPreset> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(CameraPreset::getCameraId, camera.getId());
                    queryWrapper.notIn(CollUtil.isNotEmpty(nos), CameraPreset::getNo, nos);
                    List<CameraPreset> cameraPresets = baseMapper.selectList(queryWrapper);
                    if (CollUtil.isNotEmpty(cameraPresets)) {
                        List<Long> ids = cameraPresets.stream().map(CameraPreset::getId).collect(Collectors.toList());
                        baseMapper.deleteBatchIds(ids);
                    }
                }
            }
        } else {
            log.debug("没有要更新的摄像机");
        }
    }
}
