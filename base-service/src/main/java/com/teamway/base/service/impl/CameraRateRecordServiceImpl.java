package com.teamway.base.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.common.constant.CameraOnLineRateConstant;
import com.teamway.base.common.constant.CameraStateConstant;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CameraRateRecord;
import com.teamway.base.mapper.CameraRateRecordMapper;
import com.teamway.base.service.CameraRateRecordService;
import com.teamway.base.service.CameraService;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.util.DateUtils;
import com.teamway.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 摄像机在线率记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-13
 */
@Service
public class CameraRateRecordServiceImpl extends ServiceImpl<CameraRateRecordMapper, CameraRateRecord> implements CameraRateRecordService {

    @Autowired
    private CameraService cameraService;

    /**
     * 保存摄像机在线率
     *
     * @return
     */
    @Override
    public boolean save() {
        CameraRateRecord cameraRateRecord = new CameraRateRecord();
        //在线数
        long online = cameraService.count(new LambdaQueryWrapper<Camera>().eq(Camera::getState, CameraStateConstant.ON_LINE));
        cameraRateRecord.setOnline((int) online);
        //离线数
        long offline = cameraService.count(new LambdaQueryWrapper<Camera>().eq(Camera::getState, CameraStateConstant.OFF_LINE));
        cameraRateRecord.setOffline((int) offline);
        //总数
        cameraRateRecord.setTotal(cameraRateRecord.getOnline() + cameraRateRecord.getOffline());
        //在线率
        cameraRateRecord.setOnLineRate((double) online / cameraRateRecord.getTotal() * 100);
        //时间
        cameraRateRecord.setTime(DateUtils.getLocalDateTimeString());
        return this.save(cameraRateRecord);
    }

    /**
     * 根据时间类型  查询摄像机在线率记录
     *
     * @param type 1-当天、2-当周（近7天）、3-当月（近30天）、4当年（近365天）
     * @return
     */
    @Override
    public List<CameraRateRecord> getCameraOnLineRate(Integer type) {
        QueryWrapper<CameraRateRecord> queryWrapper = new QueryWrapper<>();
        LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        Date date = DateUtils.localDateTime2Date(localDateTime);
        DateTime dateTime;
        if (CameraOnLineRateConstant.DAY.equals(type)) {
            //当天
            queryWrapper.gt("time", DateUtils.getStartLocalDateTimeString());
            queryWrapper.lt("time", DateUtils.getEndLocalDateTimeString());
            return this.list(queryWrapper);
        } else if (CameraOnLineRateConstant.WEEK.equals(type)) {
            //近7天
            dateTime = DateUtil.offsetDay(date, -6);
        } else if (CameraOnLineRateConstant.MONTH.equals(type)) {
            //近30天
            dateTime = DateUtil.offsetDay(date, -29);
        } else if (CameraOnLineRateConstant.YEAR.equals(type)) {
            //近365天
            dateTime = DateUtil.offsetDay(date, -364);
        } else {
            throw new ServiceException(ResultCode.WRONG, "类型");
        }
        queryWrapper.select("DATE(time) AS time, AVG(on_line_rate) AS on_line_rate");
        queryWrapper.between("time", dateTime.toString(), DateUtils.getEndLocalDateTimeString());
        //按日期分组   只拿第一条
        queryWrapper.last("GROUP BY DATE(time)");
        return this.list(queryWrapper);
    }

}
