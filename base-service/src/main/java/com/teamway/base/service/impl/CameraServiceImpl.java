package com.teamway.base.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shenzhen.teamway.NewOnvifCommon;
import com.shenzhen.teamway.model.dto.GetPicUrlDto;
import com.shenzhen.teamway.util.ResultMessage;
import com.teamway.base.common.constant.*;
import com.teamway.base.common.properties.CameraPresetProperties;
import com.teamway.base.common.properties.GetCameraInfoProperties;
import com.teamway.base.common.transfer.CameraTransfer;
import com.teamway.base.core.event.CameraEvent;
import com.teamway.base.dto.camera.*;
import com.teamway.base.dto.stream.StreamResultDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CameraPreset;
import com.teamway.base.entity.ElementInfo;
import com.teamway.base.entity.Region;
import com.teamway.base.mapper.CameraMapper;
import com.teamway.base.mapper.ElementInfoMapper;
import com.teamway.base.service.*;
import com.teamway.base.util.ScreenshotUtils;
import com.teamway.base.util.ValidateUtil;
import com.teamway.base.vo.RegionTreeVo;
import com.teamway.common.constant.AlarmCenterAlarmType;
import com.teamway.common.constant.DeviceEnum;
import com.teamway.common.dto.excel.ExcelSelectorDTO;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.event.AlarmCenterEvent;
import com.teamway.common.util.DateUtils;
import com.teamway.common.util.ExcelUtils;
import com.teamway.common.util.ImageUtils;
import com.teamway.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 针对表【camera(摄像机配置表)】的数据库操作Service实现
 *
 * <AUTHOR> liucheng
 * @date : 2022/4/14
 */
@Service
@SuppressWarnings("all")
@Slf4j
public class CameraServiceImpl extends ServiceImpl<CameraMapper, Camera> implements CameraService {

    @Autowired
    private RegionService regionService;
    @Autowired
    private CameraTransfer cameraTransfer;
    @Autowired
    private GetCameraInfoProperties getCameraInfoProperties;
    @Autowired
    private ElementInfoMapper elementInfoMapper;
    @Autowired
    private PollCameraService pollCameraService;
    @Autowired
    private CameraPresetService cameraPresetService;
    @Autowired
    private CameraPresetProperties presetProperties;
    @Autowired
    private CameraMapper cameraMapper;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private CameraStateRecordService cameraStateRecordService;
    @Value("${deviceOfflineTime.camera:60}")
    private int cameraOfflineTime;


    @Override
    public Page<Camera> findPageList(CameraQueryDto cameraQueryDto) {
        LambdaQueryWrapper<Camera> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(cameraQueryDto.getId() != null, Camera::getId, cameraQueryDto.getId());
        queryWrapper.like(StrUtil.isNotBlank(cameraQueryDto.getName()), Camera::getName, cameraQueryDto.getName());
        queryWrapper.like(StrUtil.isNotBlank(cameraQueryDto.getIp()), Camera::getIp, cameraQueryDto.getIp());
        queryWrapper.in(cameraQueryDto.getRegionId() != null, Camera::getRegionId, regionService.getChildId(cameraQueryDto.getRegionId()));
        queryWrapper.eq(cameraQueryDto.getFeature() != null, Camera::getFeature, cameraQueryDto.getFeature());
        queryWrapper.eq(cameraQueryDto.getType() != null, Camera::getType, cameraQueryDto.getType());
        queryWrapper.eq(cameraQueryDto.getState() != null, Camera::getState, cameraQueryDto.getState());
        queryWrapper.orderByAsc(Camera::getName);
        Page<Camera> page = new Page<>(cameraQueryDto.getPageIndex(), cameraQueryDto.getPageSize());
        baseMapper.selectPage(page, queryWrapper);
        List<Camera> cameraList = page.getRecords();
        //查询摄像机不为空  查询区域路径
        if (CollUtil.isNotEmpty(cameraList)) {
            Map<Long, Region> regionMap = regionService.getRegionMap(cameraList.stream().map(Camera::getRegionId).collect(Collectors.toList()));
            cameraList.forEach(camera -> Optional.ofNullable(regionMap.get(camera.getRegionId()))
                    .ifPresent(region -> camera.setRegionName(region.getAbsolutePath())));
        }
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Camera addCamera(Camera camera) {
        checkPlayModel(camera);
        LambdaQueryWrapper<Camera> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Camera::getName, camera.getName());
        Camera cameraByName = baseMapper.selectOne(queryWrapper);
        if (cameraByName != null) {
            throw new ServiceException(ResultCode.EXIST, "摄像机名称");
        }
        Region existsRegion = regionService.getById(camera.getRegionId());
        if (existsRegion == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "区域");
        }
        //判断主ID+子ID是否重复
        if (StrUtil.isNotBlank(camera.getMainId()) && StrUtil.isNotBlank(camera.getSubId())) {
            LambdaQueryWrapper<Camera> queryWrapperMainSubId = new LambdaQueryWrapper<>();
            queryWrapperMainSubId.eq(Camera::getMainId, camera.getMainId());
            queryWrapperMainSubId.eq(Camera::getSubId, camera.getSubId());
            List<Camera> cameras = baseMapper.selectList(queryWrapperMainSubId);
            if (CollUtil.isNotEmpty(cameras)) {
                throw new ServiceException(ResultCode.EXIST, "主ID以及子ID");
            }
        } else if (StrUtil.isBlank(camera.getMainId()) && StrUtil.isNotBlank(camera.getSubId())
                || StrUtil.isNotBlank(camera.getMainId()) && StrUtil.isBlank(camera.getSubId())) {
            //主id子id必须同时存在
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "主ID子ID必须同时存在");
        }
        camera.setState(CameraStateConstant.OFF_LINE);
        camera.setOffLineTime(DateUtils.getLocalDateTimeString());
        boolean save = this.save(camera);
        //发布事件
        applicationContext.publishEvent(new CameraEvent(this, Arrays.asList(camera), CameraOperationConstant.ADD));
        return save ? camera : null;
    }

    /**
     * 批量添加摄像机
     *
     * @param saveCameraListDto
     * @return : boolean
     * @date : 2023/3/15
     * <AUTHOR> zhangsai
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAdd(SaveCameraListDto saveCameraListDto) {
        List<Camera> list = this.list();
        //数据库名称  记录
        Map<String, List<Camera>> collect = list.stream().collect(Collectors.groupingBy(Camera::getName));
        Map<String, Integer> cameraNameMap = new HashMap<>();
        collect.forEach((key, value) -> cameraNameMap.put(key, value.size()));

        List<Camera> cameraList = saveCameraListDto.getCameraList();
        //记录摄像机名称  出现次数

        cameraList.forEach(camera -> {
            //重名
            if (cameraNameMap.containsKey(camera.getName())) {
                Integer num = cameraNameMap.get(camera.getName());
                cameraNameMap.put(camera.getName(), num + 1);
            } else {
                cameraNameMap.put(camera.getName(), 1);
            }
            //重复摄像机名称重命名   名称-序号
            if (cameraNameMap.containsKey(camera.getName())) {
                Integer num = cameraNameMap.get(camera.getName());
                if (num > 1) {
                    //新名称确认重名
                    String cameraName = camera.getName() + "-" + num;
                    if (!cameraNameMap.containsKey(cameraName)) {
                        camera.setName(cameraName);
                    } else {
                        //如果新名称重名   直到找到名称为止
                        String name = getCameraName(cameraNameMap, camera.getName());
                        camera.setName(name);
                    }
                }
            }
            Region existsRegion = regionService.getById(camera.getRegionId());
            if (existsRegion == null) {
                throw new ServiceException(ResultCode.NOT_EXIST, "区域");
            }
            //判断主ID+子ID是否重复
            if (StrUtil.isNotBlank(camera.getMainId()) && StrUtil.isNotBlank(camera.getSubId())) {
                LambdaQueryWrapper<Camera> queryWrapperMainSubId = new LambdaQueryWrapper<>();
                queryWrapperMainSubId.eq(Camera::getMainId, camera.getMainId());
                queryWrapperMainSubId.eq(Camera::getSubId, camera.getSubId());
                List<Camera> cameras = baseMapper.selectList(queryWrapperMainSubId);
                if (CollUtil.isNotEmpty(cameras)) {
                    throw new ServiceException(ResultCode.EXIST, "主ID以及子ID");
                }
            } else if (StrUtil.isBlank(camera.getMainId()) && StrUtil.isNotBlank(camera.getSubId())
                    || StrUtil.isNotBlank(camera.getMainId()) && StrUtil.isBlank(camera.getSubId())) {
                //主id子id必须同时存在
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "主ID子ID必须同时存在");
            }
            camera.setState(CameraStateConstant.OFF_LINE);
            camera.setOffLineTime(DateUtil.format(LocalDateTimeUtil.now(), "yyyy-MM-dd HH:mm:ss"));
            checkPlayModel(camera);
        });
        //批量添加
        boolean result = this.saveBatch(cameraList);

        //发布事件
        applicationContext.publishEvent(new CameraEvent(this, cameraList, CameraOperationConstant.ADD));
        return result;
    }

    private String getCameraName(Map<String, Integer> cameraNameMap, String name) {
        int i = 2;
        //重复摄像机名称重命名   名称-序号
        while (true) {
            String cameraName = name + "-" + i;
            if (!cameraNameMap.containsKey(cameraName)) {
                cameraNameMap.put(cameraName, 1);
                return cameraName;
            }
            i++;
        }
    }

    /**
     * 批量修改摄像机
     *
     * @param updateCameraDto
     * @return : boolean
     * @date : 2023/3/15
     * <AUTHOR> zhangsai
     */
    @Override
    public boolean batchUpdate(UpdateCameraDto updateCameraDto) {
        List<Camera> cameraList = this.baseMapper.selectBatchIds(updateCameraDto.getIds());
        if (CollUtil.isEmpty(cameraList)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
        }
        Camera camera = new Camera();
        BeanUtil.copyProperties(updateCameraDto, camera);
        checkPlayModel(camera);
        LambdaUpdateWrapper<Camera> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(Camera::getId, updateCameraDto.getIds());
        //发送事件
        applicationContext.publishEvent(new CameraEvent(this, cameraList, CameraOperationConstant.UPDATE));
        return this.baseMapper.update(camera, updateWrapper) > 0;
    }

    /**
     * 校验电视墙播放模式参数完整性
     *
     * @param camera
     */
    private void checkPlayModel(Camera camera) {
        if ("1".equals(camera.getPlayModel())) {
            if (camera.getPlayPort() == null || camera.getPlayPort().trim().length() == 0) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "海康私有协议播放时播放端口必填");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delBatchCamera(List<Long> ids) {
        List<Camera> cameras = this.listByIds(ids);
        // 摄像机不存在
        if (CollUtil.isEmpty(cameras) || CollUtil.size(cameras) != CollUtil.size(ids)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
        }
        //删除摄像机
        boolean result = this.removeByIds(ids);
        //发布事件
        applicationContext.publishEvent(new CameraEvent(this, cameras, CameraOperationConstant.DELETE));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCamera(Camera camera) {
        checkPlayModel(camera);
        Camera oldCamera = baseMapper.selectById(camera.getId());
        if (oldCamera == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
        }
        LambdaQueryWrapper<Camera> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Camera::getName, camera.getName());
        queryWrapper.ne(Camera::getId, camera.getId());
        Camera update = baseMapper.selectOne(queryWrapper);
        if (update != null) {
            throw new ServiceException(ResultCode.EXIST, "摄像机名称");
        }
        Region existsRegion = regionService.getById(camera.getRegionId());
        if (existsRegion == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "区域");
        }
        LambdaQueryWrapper<Camera> queryWrapperMainSubId = new LambdaQueryWrapper<>();
        queryWrapperMainSubId.eq(Camera::getMainId, camera.getMainId());
        queryWrapperMainSubId.eq(Camera::getSubId, camera.getSubId());
        queryWrapperMainSubId.ne(Camera::getId, camera.getId());
        List<Camera> cameras = baseMapper.selectList(queryWrapperMainSubId);
        if (CollUtil.isNotEmpty(cameras)) {
            throw new ServiceException(ResultCode.EXIST, "主ID以及子ID");
        }
        //摄像机修改区域   删除地图元素
        if (!camera.getRegionId().equals(oldCamera.getRegionId())) {
            LambdaQueryWrapper<ElementInfo> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(ElementInfo::getObjectId, camera.getId());
            elementInfoMapper.delete(deleteWrapper);
        }
        //发布事件
        applicationContext.publishEvent(new CameraEvent(this, List.of(camera), CameraOperationConstant.UPDATE));

        return this.updateById(camera);
    }

    /**
     * 导入摄像机
     *
     * @param file
     * @return CameraImportDto
     * @date : 2022/7/12
     * <AUTHOR> zhangsai
     */
    @Override
    public CameraImportDto importExcel(MultipartFile file) {
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        params.setStartRows(0);
        //版本问题，easypoi不支持hibernate-validator8.0.1
        //params.setNeedVerify(true);
        ExcelImportResult<ExcelCameraDto> importExcelMore = null;
        try {
            importExcelMore = ExcelImportUtil.importExcelMore(file.getInputStream(), ExcelCameraDto.class, params);
        } catch (Exception e) {
            throw new ServiceException(ResultCode.IMPORT_FAIL, e);
        }
        //判断是否是用模板导入
        Sheet sheet = importExcelMore.getWorkbook().getSheetAt(0);
        String title = sheet.getRow(0).getCell(0).getStringCellValue();
        String excelTitle = "摄像机信息";
        if (!excelTitle.equals(title)) {
            throw new ServiceException(ResultCode.IMPORT_FORMAT_FAIL, "请用模板导入摄像机信息");
        }
        //获取违反注解校验的错误信息
        List<ExcelCameraDto> failList = importExcelMore.getFailList();
        //获取正常数据
        List<ExcelCameraDto> list = importExcelMore.getList();
        //整合一起
        list.addAll(failList);
        //校验参数合法
        CheckCameraDto checkCameraDto = this.checkImportParam(list);
        List<Camera> normalCameraList = checkCameraDto.getNormalCameraList();
        List<ExcelCameraDto> failCameraList = checkCameraDto.getFailCameraList();
        //记录成功、失败的数量
        Integer normalNum = normalCameraList.size();
        Integer failNum = failCameraList.size();
        String errorUrl = "";
        //导出校验失败的数据
        if (CollUtil.isNotEmpty(failCameraList)) {
            List<ExcelSelectorDTO> selectorDTOList = this.generateSelectors();
            try (Workbook workbook = ExcelUtils.exportExcel(failCameraList, selectorDTOList, "摄像机信息", "摄像机信息001", ExcelCameraDto.class, "摄像机错误信息");
                 ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                workbook.write(byteArrayOutputStream);
                byte[] workbookBytes = byteArrayOutputStream.toByteArray();
                //将错误信息上传对象存储
                errorUrl = ImageUtils.uploadBytes(workbookBytes, "摄像机错误信息.xlsx", "");
            } catch (Exception e) {
                throw new ServiceException(ResultCode.EXPORT_FAIL, e);
            }
        }
        boolean result = this.saveBatch(normalCameraList);

        //发布事件
        Thread.ofVirtual().start(() -> {
            applicationContext.publishEvent(new CameraEvent(this, normalCameraList, CameraOperationConstant.ADD));
        });
        //构建返回信息
        CameraImportDto cameraImportDto = new CameraImportDto();
        cameraImportDto.setSuccessNum(normalNum);
        cameraImportDto.setFailNum(failNum);
        cameraImportDto.setErrorMessage(errorUrl);
        return cameraImportDto;
    }

    /**
     * 校验参数合法   &  返回成功和失败的数据
     *
     * @param excelCameraList
     * @return
     */
    private CheckCameraDto checkImportParam(List<ExcelCameraDto> excelCameraList) {
        //正常数据
        List<Camera> normalCameraList = new ArrayList<>();
        //错误数据
        List<ExcelCameraDto> failCameraList = new ArrayList<>();
        //区域列表
        List<Region> regionList = regionService.list();
        Map<String, Long> regionMap = getRegionMap(regionList);

        Map<String, Integer> map = new HashMap<>();
        for (ExcelCameraDto excelCameraDto : excelCameraList) {
            StringJoiner joiner = new StringJoiner(",");
            //注解校验
            Optional.ofNullable(ValidateUtil.validateParams(excelCameraDto)).ifPresent(joiner::add);
            //判断数据库中摄像机名称是否已存在
            LambdaQueryWrapper<Camera> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Camera::getName, excelCameraDto.getName());
            Camera repetitionCamera = this.getOne(queryWrapper);
            if (repetitionCamera != null) {
                joiner.add("摄像机名称已存在");
            }
            //判断数据库中主ID+子ID是否重复
            if (StrUtil.isNotBlank(excelCameraDto.getMainId()) && StrUtil.isNotBlank(excelCameraDto.getSubId())) {
                LambdaQueryWrapper<Camera> queryWrapperMain = new LambdaQueryWrapper<>();
                queryWrapperMain.eq(Camera::getMainId, excelCameraDto.getMainId());
                queryWrapperMain.eq(Camera::getSubId, excelCameraDto.getSubId());
                Camera cameraMain = this.getOne(queryWrapperMain);
                if (cameraMain != null) {
                    joiner.add("主ID以及子ID已存在");
                }
                //判断列表中是否有重复主ID子ID
                String MainKey = excelCameraDto.getMainId() + excelCameraDto.getSubId();
                if (map.containsKey(MainKey)) {
                    joiner.add("主ID子ID已重复");
                    Integer num = map.get(MainKey);
                    map.put(MainKey, num + 1);
                } else {
                    map.put(MainKey, 1);
                }
            } else if (StrUtil.isBlank(excelCameraDto.getMainId()) && StrUtil.isNotBlank(excelCameraDto.getSubId())
                    || StrUtil.isNotBlank(excelCameraDto.getMainId()) && StrUtil.isBlank(excelCameraDto.getSubId())) {
                //主id子id必须同时存在
                joiner.add("主ID子ID必须同时存在");
            }
            //判断列表中是否有重复摄像机名称
            if (map.containsKey(excelCameraDto.getName())) {
                joiner.add("摄像机名称已重复");
                Integer num = map.get(excelCameraDto.getName());
                map.put(excelCameraDto.getName(), num + 1);
            } else {
                map.put(excelCameraDto.getName(), 1);
            }
            if (joiner.length() > 0) {
                String errorMsg = excelCameraDto.getErrorMsg();
                if (StrUtil.isNotBlank(errorMsg)) {
                    excelCameraDto.setErrorMsg(errorMsg + "," + joiner.toString());
                } else {
                    excelCameraDto.setErrorMsg(joiner.toString());
                }
            }
            //添加错误的数据
            if (StrUtil.isNotBlank(excelCameraDto.getErrorMsg())) {
                failCameraList.add(excelCameraDto);
            } else {
                //正常数据
                Camera camera = cameraTransfer.dto2entity(excelCameraDto);
                Long regionId = regionMap.get(camera.getRegionName());
                if (regionId != null) {
                    camera.setRegionId(regionId);
                }
                //默认状态和离线时间
                camera.setState(CameraStateConstant.OFF_LINE);
                camera.setOffLineTime(DateUtil.format(LocalDateTimeUtil.now(), "yyyy-MM-dd HH:mm:ss"));
                normalCameraList.add(camera);
            }
        }
        return new CheckCameraDto(normalCameraList, failCameraList);
    }

    /**
     * 导出摄像机
     *
     * @param cameraQueryDto
     * @param response
     * @return
     * @date : 2022/7/12
     * <AUTHOR> zhangsai
     */
    @Override
    public void exportExcel(CameraQueryDto cameraQueryDto, HttpServletResponse response) {
        //默认导出1w条数据
        cameraQueryDto.setPageIndex(1);
        cameraQueryDto.setPageSize(10000);
        Page<Camera> pageList = this.findPageList(cameraQueryDto);
        List<Camera> cameraList = pageList.getRecords();
        if (CollUtil.isEmpty(cameraList)) {
            throw new ServiceException(ResultCode.NOT_NULL, "导出的数据");
        }
        List<ExcelCameraDto> cameraDtoList = cameraTransfer.entity2dtoList(cameraList);
        List<ExcelSelectorDTO> selectorDTOList = this.generateSelectors();
        try {
            ExcelUtils.exportExcel(cameraDtoList, selectorDTOList, "摄像机信息", "摄像机信息001", ExcelCameraDto.class, "摄像机信息", response);
        } catch (IOException e) {
            throw new ServiceException(ResultCode.EXPORT_FAIL, e);
        }
    }

    private String getRegion(Region region, List<Region> regionList) {
        if (region == null) {
            return "";
        }
        List<String> parentList = new ArrayList<>();
        List<String> list = this.buildTree(region.getPid(), regionList, parentList);
        StringBuffer sb = new StringBuffer();
        for (int i = list.size() - 1; i >= 0; i--) {
            sb.append(list.get(i)).append("/");
        }
        sb.append(region.getName());
        return sb.toString();
    }

    private List<String> buildTree(Long id, List<Region> list, List<String> parentList) {
        for (Region region : list) {
            if (region.getId().equals(id)) {
                parentList.add(region.getName());
                buildTree(region.getPid(), list, parentList);
            }
        }
        return parentList;
    }

    /**
     * 设置下拉框内容
     *
     * @return
     */
    private List<ExcelSelectorDTO> generateSelectors() {
        List<ExcelSelectorDTO> selectorList = new ArrayList<>();

        // 摄像机类型
        ExcelSelectorDTO typeSelector = new ExcelSelectorDTO();
        typeSelector.setSheetIndex(0);
        typeSelector.setFirstRow(0);
        typeSelector.setLastRow(65535);
        typeSelector.setFirstCol(5);
        typeSelector.setLastCol(5);
        typeSelector.setData(new String[]{"枪机", "球机", "云台", "半球", "卡片机"});
        selectorList.add(typeSelector);

        // 摄像机功能
        ExcelSelectorDTO featureSelector = new ExcelSelectorDTO();
        featureSelector.setSheetIndex(0);
        featureSelector.setFirstRow(0);
        featureSelector.setLastRow(65535);
        featureSelector.setFirstCol(6);
        featureSelector.setLastCol(6);
        featureSelector.setData(new String[]{"普通摄像机", "红外摄像机", "水位摄像机", "人脸摄像机", "全景摄像机"});
        selectorList.add(featureSelector);

        // 区域
        ExcelSelectorDTO regionSelector = new ExcelSelectorDTO();
        regionSelector.setSheetIndex(0);
        regionSelector.setFirstRow(0);
        regionSelector.setLastRow(65535);
        regionSelector.setFirstCol(7);
        regionSelector.setLastCol(7);
        List<Region> regionList = regionService.page(new Page<>(1, 20)).getRecords();
        List<String> regionPath = regionList.stream().map(Region::getAbsolutePath).collect(Collectors.toList());
        regionSelector.setData(regionPath.toArray(new String[regionPath.size()]));
        selectorList.add(regionSelector);

        // 视频协议
        ExcelSelectorDTO protocolSelector = new ExcelSelectorDTO();
        protocolSelector.setSheetIndex(0);
        protocolSelector.setFirstRow(0);
        protocolSelector.setLastRow(65535);
        protocolSelector.setFirstCol(11);
        protocolSelector.setLastCol(11);
        protocolSelector.setData(new String[]{"tcp", "udp"});
        selectorList.add(protocolSelector);

        // 厂家
        ExcelSelectorDTO factorySelector = new ExcelSelectorDTO();
        factorySelector.setSheetIndex(0);
        factorySelector.setFirstRow(0);
        factorySelector.setLastRow(65535);
        factorySelector.setFirstCol(12);
        factorySelector.setLastCol(12);
        factorySelector.setData(new String[]{"海康", "大华", "华为", "高德", "金三立"});
        selectorList.add(factorySelector);

        return selectorList;
    }


    /**
     * 将区域转成map
     * key区域绝对路径，value区域id
     *
     * @param regionList
     * @return
     */
    private Map<String, Long> getRegionMap(List<Region> regionList) {
        if (CollUtil.isEmpty(regionList)) {
            return new HashMap<>();
        }
        return regionList.stream().collect(Collectors.toMap(
                Region::getAbsolutePath,
                Region::getId
        ));
    }

    /**
     * 深度优先遍历,查找树的全部叶子路径（非递归方式）
     *
     * @param root 根节点
     * @return 叶子路径的集合
     */
    private static List<List<RegionTreeVo>> dfsTree(RegionTreeVo root) {
        Stack<RegionTreeVo> nodeStack = new Stack<>();
        Stack<List<RegionTreeVo>> pathStack = new Stack<>();
        List<List<RegionTreeVo>> result = new ArrayList<>();
        nodeStack.push(root);
        ArrayList<RegionTreeVo> arrayList = new ArrayList<>();
        arrayList.add(root);
        pathStack.push(arrayList);
        while (!nodeStack.isEmpty()) {
            RegionTreeVo curNode = nodeStack.pop();
            List<RegionTreeVo> curPath = pathStack.pop();
            if (curNode.getChildren() == null || curNode.getChildren().size() <= 0) {
                result.add(curPath);
            } else {
                result.add(curPath);
                int childSize = curNode.getChildren().size();
                for (int i = childSize - 1; i >= 0; i--) {
                    RegionTreeVo node = (RegionTreeVo) curNode.getChildren().get(i);
                    nodeStack.push(node);
                    List<RegionTreeVo> list = new ArrayList<>(curPath);
                    list.add(node);
                    pathStack.push(list);
                }
            }
        }
        return result;
    }

    /**
     * 下载模板
     *
     * @param response
     * @return
     * @date : 2022/7/12
     * <AUTHOR> zhangsai
     */
    @Override
    public void downloadTemplate(HttpServletResponse response) {
        InputStream inputStream = null;
        try {
            ClassPathResource classPathResource = new ClassPathResource("excelTemplate/摄像机导入模板.xlsx");
            inputStream = classPathResource.getInputStream();
            Workbook workbook = new XSSFWorkbook(inputStream);
            List<ExcelSelectorDTO> selectorDTOList = this.generateSelectors();
            ExcelUtils.defaultExport(workbook, selectorDTOList, "摄像机导入模板", response);
        } catch (Exception e) {
            throw new ServiceException(ResultCode.DOWNLOAD_FAIL, e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("关闭输入流异常", e);
            }
        }
    }

    @Override
    public String snapshot(CameraSnapshotDto cameraSnapshotDto) {
        //转动预置位
        if (cameraSnapshotDto.getPresetId() != null && cameraSnapshotDto.getId() != null && cameraSnapshotDto.getPresetId() != -1) {
            Camera camera = this.getById(cameraSnapshotDto.getId());
            if (null == camera) {
                throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
            }
            CameraPreset cameraPreset = cameraPresetService.getById(cameraSnapshotDto.getPresetId());
            if (null == cameraPreset) {
                throw new ServiceException(ResultCode.NOT_EXIST, "预置位");
            }
            ScreenshotUtils.gotoPreset(camera.getMainId(), camera.getSubId(), cameraPreset.getNo());
        }
        ScreenshotUtils.ScreenshotDto screenshotDto = ScreenshotUtils.ScreenshotDto.builder().ip(cameraSnapshotDto.getIp())
                .port(cameraSnapshotDto.getPort()).username(cameraSnapshotDto.getUsername()).password(cameraSnapshotDto.getPassword())
                .channel(Integer.valueOf(cameraSnapshotDto.getChannel())).factory(cameraSnapshotDto.getFactory()).type(cameraSnapshotDto.getType()).build();
        return ScreenshotUtils.screenshot(screenshotDto);
    }

    /**
     * 获取摄像机抓图URL
     *
     * @param id
     * @return : java.lang.String
     * @date : 2023/6/8
     * <AUTHOR> zhangsai
     */
    @Override
    public String snapshotUrl(Long id) {
        Camera camera = this.getById(id);
        if (null == camera) {
            throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
        }
        ResultMessage snapshotOps = new ResultMessage();
        try {
            snapshotOps = NewOnvifCommon.getPicUrl(new GetPicUrlDto(camera.getIp(), camera.getPort(), camera.getUsername(), camera.getPassword(), Integer.valueOf(camera.getChannel()), 3000));
        } catch (Exception e) {
            throw new ServiceException(ResultCode.DOWNLOAD_FAIL);
        }
        String url = (String) snapshotOps.getData();
        log.info("抓图URL：{}", url);
        String username = camera.getUsername();
        String password = camera.getPassword();
        if (StringUtils.isEmpty(url)) {
            return "";
        } else if (url.contains(username) && url.contains(password)) {
            return url;
        }
        if (CameraFactoryConstant.HIKVISION.equals(camera.getFactory())
                || CameraFactoryConstant.DAHUA.equals(camera.getFactory())) {
            //海康摄像机获取url拼接账号密码   ************************@*********/onvif-http/snapshot?Profile_101
            String auth = camera.getUsername() + ":" + camera.getPassword() + "@";
            String http = "http://";
            String[] split = url.split(http);
            if (split.length < 2) {
                throw new ServiceException(ResultCode.WRONG, "获取抓图URL");
            }
            String result = http + auth + split[1];
            return result;
        }
        return url;
    }

    @Override
    public String getCameraUrl(CameraUrlDto cameraUrlDto) {
        Long cameraId = cameraUrlDto.getCameraId();
        String urlType = cameraUrlDto.getUrlType();
        if (CameraUrlTypeConstant.ONVIF.equals(urlType)) {
            return this.snapshotUrl(cameraId);
        } else if (CameraUrlTypeConstant.RTSP.equals(urlType)) {
            //rtsp://admin:Bxgs135790@***************/Stream/Channels/101
            Camera camera = this.getById(cameraId);
            if (null == camera) {
                throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
            }
            String rtsp = "rtsp://";
            String auth = camera.getUsername() + ":" + camera.getPassword() + "@";
            String ip = camera.getIp();
            return rtsp + auth + ip + "/Stream/Channels/101";
        }
        throw new ServiceException(ResultCode.WRONG, "地址类型");
    }

    /**
     * 刷新摄像机状态
     *
     * @return : com.teamway.base.common.ResultModel
     * @date : 2022/7/12
     * <AUTHOR> Gaven
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reflushStatus() {
        log.info("开始刷新摄像机状态");
        try {
            List<Camera> cameraList = this.list();
            if (CollUtil.isEmpty(cameraList)) {
                throw new ServiceException(ResultCode.REFRESH_CAMERA_FAIL);
            }
            String url = getCameraInfoProperties.getAllStatus();
            String jsonResult = HttpUtil.get(url, getCameraInfoProperties.getTimeOut());
            StreamResultDto cameraResultDto = JSONObject.parseObject(jsonResult, StreamResultDto.class);
            if (cameraResultDto.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("调用流媒体获取预置位失败:{}", jsonResult);
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "调用流媒体获取预置位失败");
            }
            List<SyncCameraDto> syncCameraDtoList = JSON.parseArray(cameraResultDto.getData().toJSONString(), SyncCameraDto.class);
            if (CollUtil.isEmpty(syncCameraDtoList)) {
                throw new ServiceException(ResultCode.STREAM_CAMERA_NULL);
            }

            List<Long> openIds = new ArrayList<>();
            List<Long> closeIds = new ArrayList<>();
            List<String> openList = syncCameraDtoList.stream()
                    .filter(camera -> camera.getStatus().equals(CameraStateConstant.ON_LINE))
                    .map(item -> item.getMainId() + "_" + item.getSubId()).collect(Collectors.toList());
            for (Camera camera : cameraList) {
                String key = camera.getMainId() + "_" + camera.getSubId();
                boolean openFlag = openList.contains(key);
                if (openFlag && camera.getState().equals(CameraStateConstant.OFF_LINE)) {
                    openIds.add(camera.getId());
                } else if (!openFlag && camera.getState().equals(CameraStateConstant.ON_LINE)) {
                    closeIds.add(camera.getId());
                }
            }
            //流媒体在线   数据库不在线   改在线
            if (CollUtil.isNotEmpty(openIds)) {
                LambdaUpdateWrapper<Camera> offUpdateWrapper = new LambdaUpdateWrapper<>();
                offUpdateWrapper.set(Camera::getState, CameraStateConstant.ON_LINE);
                offUpdateWrapper.in(Camera::getId, openIds);
                this.update(offUpdateWrapper);
                //保存摄像机在线记录
                cameraStateRecordService.batchSave(openIds, CameraStateConstant.ON_LINE.toString());
            }
            //流媒体不在线   数据库在线   改不在线
            if (CollUtil.isNotEmpty(closeIds)) {
                //摄像机离线推送消息到告警中心
                List<Camera> cameras = cameraList.stream().filter(camera -> closeIds.contains(camera.getId())).toList();
                if (CollUtil.isNotEmpty(cameras)) {
                    Map<Long, Region> regionMap = regionService.getRegionMap(cameras.stream().map(Camera::getRegionId).collect(Collectors.toList()));
                    for (Camera camera : cameras) {
                        Thread.ofVirtual().start(() -> {
                            //获取区域名称
                            Region region = regionMap.get(camera.getRegionId());
                            if (region != null) {
                                camera.setRegionName(region.getAbsolutePath());
                            }
                            //发送告警中心
                            sendMessageToCenter(camera);
                        });
                    }
                }
                LambdaUpdateWrapper<Camera> onUpdateWrapper = new LambdaUpdateWrapper<>();
                onUpdateWrapper.set(Camera::getState, CameraStateConstant.OFF_LINE);
                onUpdateWrapper.set(Camera::getOffLineTime, LocalDateTime.now().format(DateUtils.DATETIME_FORMATTER));
                onUpdateWrapper.in(Camera::getId, closeIds);
                this.update(onUpdateWrapper);
                //保存摄像机离线记录
                cameraStateRecordService.batchSave(closeIds, CameraStateConstant.OFF_LINE.toString());
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.info("--------更新摄像机状态失败--------", e);
            throw new ServiceException(ResultCode.CAMERA_STATUS_FAIL, e);
        }
        log.info("刷新摄像机状态结束");
    }

    /**
     * 摄像机离线推送消息到告警中心
     *
     * @param camera
     * @return
     */
    private void sendMessageToCenter(Camera camera) {
        //记录缓存当中一小时内相同ip的摄像机状态变化将不会推送告警中心
        if (StrUtil.isNotEmpty(camera.getOffLineTime())) {
            String offLineTime = camera.getOffLineTime();
            LocalDateTime parse = LocalDateTime.parse(offLineTime, DateUtils.DATETIME_FORMATTER);
            Duration between = Duration.between(parse, LocalDateTime.now());
            long minutes = between.toMinutes();
            if (minutes >= 0 && minutes < cameraOfflineTime) {
                log.info("ip为{}的摄像机{}分钟内已存在告警推送", camera.getIp(), cameraOfflineTime);
                return;
            }
        }
        AlarmCenterEvent event = new AlarmCenterEvent(this);
        event.setType(AlarmCenterAlarmType.DEVICE_OFFLINE_ALARM);
        event.setLevel(Integer.valueOf(AlarmLevelConstant.GENERAL));
        event.setCameraId(camera.getId());
        event.setRegionName(camera.getRegionName());
        event.setDeviceName(camera.getName());
        event.setValue(DeviceEnum.CAMERA.getStatusDesc());
        event.setAlarmDesc(DeviceEnum.CAMERA.getDesc());
        event.setAlarmTime(LocalDateTime.now().format(DateUtils.DATETIME_FORMATTER));
        event.setState(0);
        //发送告警中心
        log.info("摄像机{}离线，推送消息到告警中心", camera.getIp());
        applicationContext.publishEvent(event);
    }

    @Override
    public void reflushVideoUrl() {
        List<Camera> list = this.list();
        if (list != null && list.size() > 0) {
            for (Camera camera : list) {
                try {
                    if (camera.getVideoUrl() == null || camera.getVideoUrl().trim().length() == 0) {
                        ResultMessage videoUrl = NewOnvifCommon.getVideoUrl(camera.getIp(), camera.getUsername(), camera.getPassword());
                        if ("0".equals(videoUrl.getStatus())) {
                            camera.setVideoUrl((String) videoUrl.getData());
                            this.updateById(camera);
                        }
                    } else {
                        if (camera.getVideoUrl().contains(camera.getIp())
                                && camera.getVideoUrl().contains(camera.getUsername())
                                && camera.getVideoUrl().contains(camera.getPassword())) {
                        } else {
                            ResultMessage videoUrl = NewOnvifCommon.getVideoUrl(camera.getIp(), camera.getUsername(), camera.getPassword());
                            if ("0".equals(videoUrl.getStatus())) {
                                camera.setVideoUrl((String) videoUrl.getData());
                                this.updateById(camera);
                            }
                        }
                    }
                } catch (Exception e) {
                }
            }
        }
    }

    @Override
    public Camera getCameraById(Long id) {
        Camera camera = this.getById(id);
        if (ObjectUtil.isEmpty(camera)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "摄像机不存在");
        }
        Region region = regionService.getById(camera.getRegionId());
        camera.setRegionName(region.getName());
        return camera;
    }

    @Override
    public Long selectCameraNumByState(Integer state) {
        LambdaQueryWrapper<Camera> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Camera::getState, state);
        return this.baseMapper.selectCount(queryWrapper);
    }

    @Override
    public CameraDto selectCameraOnAndOffNum() {
        CameraDto cameraDto = new CameraDto();
        cameraDto.setOnNum(this.selectCameraNumByState(1));
        cameraDto.setOffNum(this.selectCameraNumByState(0));
        return cameraDto;
    }

    @Override
    public List<Long> getCameraIdByRegionId(List<Long> regionIdList) {
        return cameraMapper.getCameraIdByRegionId(regionIdList);
    }

    /**
     * 根据摄像机id返回摄像机信息
     *
     * @param cameraId
     * @return
     */
    @Override
    public List<Camera> getCameraAndTypeById(Long cameraId) {
        Camera camera = getById(cameraId);
        if (ObjectUtil.isEmpty(camera)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "摄像机信息");
        }
        List<Camera> cameraList = list(
                new LambdaQueryWrapper<Camera>()
                        .eq(StrUtil.isNotBlank(camera.getIp()), Camera::getIp, camera.getIp())
                        .groupBy(Camera::getFeature)
        );
        return cameraList;
    }
}




