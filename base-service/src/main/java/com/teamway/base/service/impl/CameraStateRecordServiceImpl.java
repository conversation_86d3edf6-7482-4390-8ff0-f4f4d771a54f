package com.teamway.base.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CameraStateRecord;
import com.teamway.base.mapper.CameraMapper;
import com.teamway.base.mapper.CameraStateRecordMapper;
import com.teamway.base.service.CameraStateRecordService;
import com.teamway.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 摄像机状态记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Service
public class CameraStateRecordServiceImpl extends ServiceImpl<CameraStateRecordMapper, CameraStateRecord> implements CameraStateRecordService {

    @Autowired
    private CameraMapper cameraMapper;

    /**
     * 根据摄像机id集合  批量保存记录
     *
     * @param cameraIds
     * @param state     1-在线  0-离线
     * @return
     */
    @Override
    public boolean batchSave(List<Long> cameraIds, String state) {
        String localDateTimeString = DateUtils.getLocalDateTimeString();
        List<CameraStateRecord> cameraStateRecordList = new ArrayList<>();
        cameraIds.forEach(cameraId -> {
            CameraStateRecord cameraStateRecord = new CameraStateRecord();
            cameraStateRecord.setCameraId(cameraId);
            Camera camera = cameraMapper.selectById(cameraId);
            cameraStateRecord.setCameraName(camera != null ? camera.getName() : "未知摄像机");
            cameraStateRecord.setState(state);
            cameraStateRecord.setTime(localDateTimeString);
            cameraStateRecordList.add(cameraStateRecord);
        });
        return this.saveBatch(cameraStateRecordList);
    }

}
