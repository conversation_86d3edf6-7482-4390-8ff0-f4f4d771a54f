package com.teamway.base.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.common.constant.ViewGroupConstant;
import com.teamway.base.dto.CameraView.CameraViewDto;
import com.teamway.base.dto.CameraView.CameraViewIdsDto;
import com.teamway.base.dto.CameraView.CameraViewIndex;
import com.teamway.base.dto.CameraView.CameraViewListDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CameraView;
import com.teamway.base.entity.Region;
import com.teamway.base.mapper.CameraViewMapper;
import com.teamway.base.service.CameraService;
import com.teamway.base.service.CameraViewService;
import com.teamway.base.service.RegionService;
import com.teamway.base.util.UserUtils;
import com.teamway.base.vo.CameraViewListVo;
import com.teamway.common.entity.BaseEntity;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class CameraViewServiceImpl extends ServiceImpl<CameraViewMapper, CameraView> implements CameraViewService {

    @Autowired
    private CameraService cameraService;
    @Autowired
    private RegionService regionService;

    @Override
    public Boolean viewStorage(CameraViewDto dto) {
        String userAccount = UserUtils.getUserAccount();
        if (ObjectUtil.equals(ViewGroupConstant.PUBLIC_VIEW, dto.getViewGroup())) {
            long count = count(new LambdaQueryWrapper<CameraView>().eq(StringUtils.isNotEmpty(dto.getViewName()), CameraView::getViewName, dto.getViewName()).isNull(CameraView::getUserAccount));
            if (count > 0) {
                throw new ServiceException(ResultCode.EXIST, "公共视图名");
            }
        } else if (ObjectUtil.equals(ViewGroupConstant.PERSONAL_VIEW, dto.getViewGroup())) {
            long count = count(new LambdaQueryWrapper<CameraView>().eq(StringUtils.isNotEmpty(dto.getViewName()), CameraView::getViewName, dto.getViewName())
                    .eq(StringUtils.isNotEmpty(userAccount), CameraView::getUserAccount, userAccount));
            if (count > 0) {
                throw new ServiceException(ResultCode.EXIST, "个人视图名");
            }
        }
        CameraView cameraView = new CameraView();
        cameraView.setViewName(dto.getViewName());
        cameraView.setViewNum(dto.getViewNum());
        if (ObjectUtil.equals(dto.getViewGroup(), ViewGroupConstant.PERSONAL_VIEW)) {
            cameraView.setUserAccount(userAccount);
        }
        cameraView.setViewGroup(dto.getViewGroup());
        List<CameraViewIndex> cameraIdList = dto.getCameraIdList();
        if (CollectionUtil.isNotEmpty(cameraIdList)) {
            //用json存储摄像机id和viewIndex
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < cameraIdList.size(); i++) {
                if (ObjectUtil.isNotEmpty(cameraIdList.get(i))) {
                    builder.append(JSON.toJSONString(cameraIdList.get(i)));
                    if (i < cameraIdList.size() - 1) {
                        builder.append(",");
                    }
                }
            }
            cameraView.setCameraIds(builder.toString());
        }
        return save(cameraView);
    }

    @Override
    public Boolean updateView(CameraViewDto dto) {
        if (ObjectUtil.isEmpty(dto.getId())) {
            throw new ServiceException(ResultCode.NOT_EXIST, "视图ID");
        }
        String userAccount = UserUtils.getUserAccount();
        if (ObjectUtil.equals(ViewGroupConstant.PUBLIC_VIEW, dto.getViewGroup())) {
            long count = count(new LambdaQueryWrapper<CameraView>().eq(StringUtils.isNotEmpty(dto.getViewName()), CameraView::getViewName, dto.getViewName())
                    .isNull(CameraView::getUserAccount).ne(CameraView::getId, dto.getId()));
            if (count > 0) {
                throw new ServiceException(ResultCode.EXIST, "公共视图名");
            }
        } else if (ObjectUtil.equals(ViewGroupConstant.PERSONAL_VIEW, dto.getViewGroup())) {
            long count = count(new LambdaQueryWrapper<CameraView>().eq(StringUtils.isNotEmpty(dto.getViewName()), CameraView::getViewName, dto.getViewName())
                    .eq(StringUtils.isNotEmpty(userAccount), CameraView::getUserAccount, userAccount).ne(CameraView::getId, dto.getId()));
            if (count > 0) {
                throw new ServiceException(ResultCode.EXIST, "个人视图名");
            }
        }
        LambdaUpdateWrapper<CameraView> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CameraView::getId, dto.getId());
        wrapper.set(CameraView::getViewName, dto.getViewName());
        wrapper.set(ObjectUtil.isNotEmpty(dto.getViewNum()), CameraView::getViewNum, dto.getViewNum());
        List<CameraViewIndex> cameraIdList = dto.getCameraIdList();
        //公有视图切换个人视图 个人切换公有
        switch (dto.getViewGroup()) {
            case 0:
                //设置用户id为空
                wrapper.set(CameraView::getUserAccount, null);
                break;
            case 1:
                if (StringUtils.isEmpty(userAccount)) {
                    throw new ServiceException(ResultCode.NOT_NULL, "用户ID");
                }
                wrapper.set(CameraView::getUserAccount, userAccount);
                break;
            default:
                break;
        }
        wrapper.set(ObjectUtil.isNotEmpty(dto.getViewGroup()), CameraView::getViewGroup, dto.getViewGroup());
        if (ObjectUtil.equals(dto.getViewGroup(), ViewGroupConstant.PUBLIC_VIEW)) {
            wrapper.set(ObjectUtil.isNotEmpty(dto.getViewGroup()), CameraView::getViewGroup, dto.getViewGroup());
        } else if (ObjectUtil.equals(dto.getViewGroup(), ViewGroupConstant.PERSONAL_VIEW)) {

        }
        if (CollectionUtil.isNotEmpty(cameraIdList)) {
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < cameraIdList.size(); i++) {
                builder.append(JSON.toJSONString(cameraIdList.get(i)));
                if (i < cameraIdList.size() - 1) {
                    builder.append(",");
                }
            }
            wrapper.set(CameraView::getCameraIds, builder.toString());
        }
        return update(wrapper);
    }

    @Override
    public Boolean deleteView(CameraViewIdsDto dto) {
        List<CameraView> views = listByIds(dto.getIds());
        if (ObjectUtil.isEmpty(views)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "视图");
        }
        return this.removeBatchByIds(dto.getIds());
    }

    @Override
    public CameraViewListVo selectCameraViewList(CameraViewListDto dto) {
        Page<CameraView> page = new Page<>(dto.getPageIndex(), dto.getPageSize());
        CameraViewListVo vo = new CameraViewListVo();

        if (Objects.nonNull(dto.getViewGroup())) {
            if (Objects.equals(ViewGroupConstant.PERSONAL_VIEW, dto.getViewGroup())) {
                List<CameraView> personal = listView(ViewGroupConstant.PERSONAL_VIEW, page);
                vo.setPersonalView(personal);
                vo.setCommonView(new ArrayList<>());
            } else if (Objects.equals(ViewGroupConstant.PUBLIC_VIEW, dto.getViewGroup())) {
                //查询公共视图
                List<CameraView> publicView = listView(ViewGroupConstant.PUBLIC_VIEW, page);
                vo.setCommonView(publicView);
                vo.setPersonalView(new ArrayList<>());
            } else {
                throw new ServiceException(ResultCode.WRONG, String.format("视图类型%s参数", dto.getViewGroup()));
            }
        } else {
            //查询个人视图
            List<CameraView> personal = listView(ViewGroupConstant.PERSONAL_VIEW, page);
            //查询公共视图
            List<CameraView> publicView = listView(ViewGroupConstant.PUBLIC_VIEW, page);
            vo.setCommonView(publicView);
            vo.setPersonalView(personal);
        }
        return vo;
    }

    private List<CameraView> listView(Integer viewGroup, Page<CameraView> page) {
        Page<CameraView> viewPage = new Page<>();
        LambdaQueryWrapper<CameraView> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CameraView::getViewGroup, viewGroup);
        queryWrapper.orderByDesc(BaseEntity::getCreateTime);
        if (ObjectUtil.equals(viewGroup, 1)) {
            String userAccount = UserUtils.getUserAccount();
            queryWrapper.eq(CameraView::getUserAccount, userAccount);
            viewPage = baseMapper.selectPage(page, queryWrapper);
        } else if (ObjectUtil.equals(viewGroup, 0)) {
            queryWrapper.isNull(CameraView::getUserAccount);
            viewPage = baseMapper.selectPage(page, queryWrapper);
        }
        if (ObjectUtil.isEmpty(viewPage)) {
            return null;
        }
        List<CameraView> records = viewPage.getRecords();
        //存入摄像机信息
        for (CameraView i : records) {
            List<CameraViewIndex> list = new ArrayList<>();
            String cameraIds = i.getCameraIds();
            if (StringUtils.isNotEmpty(cameraIds)) {
                String[] splits = cameraIds.split("(?<=[\\}]),(?=[\\{])");
                for (String split : splits) {
                    String trim = split.trim();
                    CameraViewIndex viewIndex = JSON.parseObject(trim, CameraViewIndex.class);
                    list.add(viewIndex);
                }
                List<Camera> cameraList = new ArrayList<>();
                list.stream().forEach(camera -> {
                    Camera byId = cameraService.getById(camera.getId());
                    if (ObjectUtil.isNotEmpty(byId)) {
                        byId.setViewIndex(camera.getViewIndex());
                        cameraList.add(byId);
                    }
                });
                if (CollectionUtil.isNotEmpty(cameraList)){
                    for (Camera camera : cameraList) {
                        Region region = regionService.getById(camera.getRegionId());
                        if (ObjectUtil.isNotEmpty(region)){
                            camera.setCameraPath(region.getAbsolutePath());
                        }
                    }

                }
                i.setCameraList(cameraList);
            }
        }
        ;
        return records;
    }


}
