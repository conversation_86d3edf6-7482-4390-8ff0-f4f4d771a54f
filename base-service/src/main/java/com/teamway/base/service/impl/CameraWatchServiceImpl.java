package com.teamway.base.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.common.properties.CameraPresetProperties;
import com.teamway.base.dto.stream.StreamResultDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CameraPreset;
import com.teamway.base.entity.CameraWatch;
import com.teamway.base.mapper.CameraWatchMapper;
import com.teamway.base.service.CameraPresetService;
import com.teamway.base.service.CameraService;
import com.teamway.base.service.CameraWatchService;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * 摄像机监控配置服务实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public class CameraWatchServiceImpl extends ServiceImpl<CameraWatchMapper, CameraWatch> implements CameraWatchService {

    @Autowired
    private CameraService cameraService;

    @Autowired
    private CameraPresetProperties cameraPresetProperties;
    
    @Autowired
    private CameraPresetService cameraPresetService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addWatch(CameraWatch cameraWatch) {
        CameraWatch watch = getOne(new LambdaQueryWrapper<CameraWatch>().eq(ObjectUtil.isNotEmpty(cameraWatch.getCameraId()), CameraWatch::getCameraId, cameraWatch.getCameraId()));
        if (ObjectUtil.isNotEmpty(watch)){
            cameraWatch.setId(watch.getId());
            //新增或编辑守望位
            setWatch(cameraWatch);
            return updateById(cameraWatch);
        }
        //新增或编辑守望位
        setWatch(cameraWatch);
        return save(cameraWatch);
    }

    /**
     * 新增或编辑守望位
     * @param watch
     */
    private void setWatch(CameraWatch watch) {
        Camera camera = cameraService.getById(watch.getCameraId());
        if (ObjectUtil.isEmpty(camera)){
            throw new ServiceException(ResultCode.NOT_EXIST,"摄像机信息");
        }
        CameraPreset preset = cameraPresetService.getById(watch.getPresetId());
        if (ObjectUtil.isEmpty(preset)){
            throw new ServiceException(ResultCode.NOT_EXIST,"预置位信息");
        }
        //新增或编辑守望位
        Map<String, Object> map = new HashMap(5);
        map.put("mainId", camera.getMainId());
        map.put("subId", camera.getSubId());
        map.put("presetId", preset.getNo());
        map.put("enable",Integer.valueOf(watch.getStatus()));
        map.put("seconds",watch.getWaitTime());
        log.info("调用设置守望位接口参数{}",map);
        String result = HttpUtil.get(cameraPresetProperties.getSetWatch(), map, cameraPresetProperties.getTimeOut());
        log.info("调用设置守望位接口，参数返回{}",result);
        StreamResultDto setStreamResultDto = JSONObject.parseObject(result, StreamResultDto.class);
        if (setStreamResultDto.getCode() != ResultCode.SUCCESS.getCode()) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "设置预置位失败");
        }
    }

    @Override
    public CameraWatch getWatchById(Long id) {
        return list(new LambdaQueryWrapper<CameraWatch>().eq(ObjectUtil.isNotEmpty(id), CameraWatch::getCameraId,id)).stream().findFirst().orElse(new CameraWatch());
    }
}