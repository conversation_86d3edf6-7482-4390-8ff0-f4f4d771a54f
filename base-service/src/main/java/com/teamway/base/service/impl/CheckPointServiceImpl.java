package com.teamway.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shenzhen.teamway.util.ImgBase64Util;
import com.teamway.base.dto.check.CheckPointBatchUpdateDto;
import com.teamway.base.dto.check.CheckPointDto;
import com.teamway.base.dto.check.CheckPointListDto;
import com.teamway.base.dto.check.CheckSaveDto;
import com.teamway.base.dto.check.DeleteCheckDto;
import com.teamway.base.dto.check.RequestCameraAlgDto;
import com.teamway.base.dto.check.UrlChangeBaseDto;
import com.teamway.base.entity.Algorithm;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CheckPoint;
import com.teamway.base.entity.Region;
import com.teamway.base.mapper.CheckPointMapper;
import com.teamway.base.mapper.RegionMapper;
import com.teamway.base.service.AlgorithmService;
import com.teamway.base.service.CameraService;
import com.teamway.base.service.CheckPointService;
import com.teamway.base.util.TimeUtils;
import com.teamway.base.vo.AlgListVo;
import com.teamway.base.vo.CheckPointVo;
import com.teamway.common.entity.BaseEntity;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import com.teamway.model.dto.AlgorithmConfigDto;
import com.teamway.model.entity.AlgorithmConfig;
import com.teamway.model.request.AlgorithmDeleteRequest;
import com.teamway.model.request.AlgorithmQueryRequest;
import com.teamway.model.request.AlgorithmTemplateRequest;
import com.teamway.service.AiConfigService;
import com.teamway.service.TemplateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.extension.parser.JsqlParserGlobal.executorService;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CheckPointServiceImpl extends ServiceImpl<CheckPointMapper, CheckPoint> implements CheckPointService {

    private static final String CAMERA_URL = "camera_snapshot_url";

    @Resource
    private AiConfigService aiConfigService;

    @Resource
    @Lazy
    private CameraService cameraService;

    @Resource
    private RegionMapper regionMapper;

    @Autowired
    private TemplateService templateService;

    @Autowired
    private AlgorithmService algorithmService;


    /**
     * 查询检测点列表
     *
     * @param pointDto
     * @return
     */
    @Override
    public Page<CheckPointVo> listCheckPoint(CheckPointListDto pointDto) {
        //区域id集合
        List<Long> regionIdList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(pointDto.getRegionId())){
            //当根据区域id查询时，查询出该区域下所有的子区域 也包括子区域的子区域
            regionIdList = findAllSubRegionIds(pointDto.getRegionId());
            regionIdList.add(pointDto.getRegionId());
        }
        LambdaQueryWrapper<CheckPoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(pointDto.getAlgDeviceName()),CheckPoint::getAlgDeviceName,pointDto.getAlgDeviceName());
        queryWrapper.eq(ObjectUtil.isNotEmpty(pointDto.getCameraId()),CheckPoint::getCameraId,pointDto.getCameraId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(pointDto.getAlgId()),CheckPoint::getAlgId,pointDto.getAlgId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(pointDto.getEnable()),CheckPoint::getEnable,pointDto.getEnable());
        queryWrapper.in(CollectionUtil.isNotEmpty(regionIdList),CheckPoint::getRegionId,regionIdList);
        queryWrapper.orderByDesc(BaseEntity::getCreateTime);
        //当摄像机id为空时，查出所有检测点摄像机绑定的算法
        Page<CheckPoint> page = page(new Page<>(pointDto.getPageIndex(), pointDto.getPageSize()), queryWrapper);
        List<CheckPointVo> pageList = new ArrayList<>();
        //遍历数据库查询检测点查询结果
        if (CollectionUtil.isNotEmpty(page.getRecords())){
            //用分页数据去比对算法信息列表
            for (CheckPoint record : page.getRecords()) {
                //比对成功返回CheckPointVo 失败返回null
                CheckPointVo pointVo = traversalData(record);
                if (ObjectUtil.isNotEmpty(pointVo)){
                    pageList.add(pointVo);
                }
            }
        }
        Page<CheckPointVo> voPage = new Page<>();
        voPage.setTotal(page.getTotal());
        voPage.setRecords(pageList);
        return voPage;
    }

    /**
     * 递归方法来查找所有子区域
     * @param parentId
     * @return
     */
    public List<Long> findAllSubRegionIds(Long parentId) {
        List<Region> regionData = regionMapper.selectList(new LambdaQueryWrapper<>());
        List<Long> subRegionIds = new ArrayList<>();
        for (Region region : regionData) {
            if (parentId.equals(region.getPid())) {
                subRegionIds.add(region.getId());
                // 递归查找更深层的子区域
                subRegionIds.addAll(findAllSubRegionIds(region.getId()));
            }
        }
        return subRegionIds;
    }


    /**
     * 匹配数据库中数据是否在算法服务中
     * @param record
     * @return
     */
    private CheckPointVo traversalData(CheckPoint record) {
        List<AlgorithmConfig> algDto = analyticStructure(record.getCameraId());
        //dto转换成vo
        if (ObjectUtil.isNotEmpty(algDto)){
            AlgorithmConfig configInfo = algDto.get(0);
            JSONArray info = configInfo.getConfigInfo();
            // 将JSONArray转换为List<JSONObject>以便使用stream()
            List<RequestCameraAlgDto> algDtos = info.toJavaList(RequestCameraAlgDto.class);
            RequestCameraAlgDto cameraAlgDto = algDtos.stream().filter(i -> ObjectUtil.equals(i.getNote(), record.getNote())).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(configInfo)){
                CheckPointVo pointVo = new CheckPointVo();
                pointVo.setAlgDeviceId(record.getAlgDeviceId());
                pointVo.setId(record.getId());
                pointVo.setConfigId(configInfo.getId());
                pointVo.setCameraId(Long.valueOf(configInfo.getCameraId()));
                pointVo.setDeviceId(configInfo.getDeviceId());
                pointVo.setCameraName(configInfo.getCameraName());
                pointVo.setUpdateBy(record.getUpdateBy());
                pointVo.setUpdateTime(record.getUpdateTime());
                //查询设备信息
                pointVo.setAlgDeviceName(record.getAlgDeviceName());
                pointVo.setPointName(record.getPointName());
                //查询摄像机信息是否在数据库当中
                CheckPoint checkPoint = list(new LambdaQueryWrapper<CheckPoint>().eq(ObjectUtil.isNotEmpty(record.getCameraId()), CheckPoint::getCameraId, record.getCameraId())).stream().findFirst().orElse(null);
                if(ObjectUtil.isNotEmpty(checkPoint)){
                    //查询摄像机信息
                    Camera camera = cameraService.getById(checkPoint.getCameraId());
                    if (ObjectUtil.isNotEmpty(camera)){
                        pointVo.setCameraName(camera.getName());
                        //查询区域信息
                        Region region = regionMapper.selectById(camera.getRegionId());
                        if (ObjectUtil.isNotEmpty(region)){
                            pointVo.setRegionName(region.getAbsolutePath());
                        }
                    }
                }
                //从算法配置服务中拿到修改时所需参数传给前端
                if (ObjectUtil.isNotEmpty(cameraAlgDto)){
                    return getCheckPointVo(cameraAlgDto, pointVo,record.getCameraId());
                }
            }
        }
        return null;
    }

    /**
     * dto转vo
     * @param cameraAlgDto
     * @param pointVo
     * @return
     */
    private CheckPointVo getCheckPointVo(RequestCameraAlgDto cameraAlgDto, CheckPointVo pointVo,Long cameraId) {
        CheckPointVo convert = Convert.convert(CheckPointVo.class, pointVo);
        convert.setNote(cameraAlgDto.getNote());
        convert.setEnable(ObjectUtil.isEmpty(cameraAlgDto.getEnable())?0:cameraAlgDto.getEnable());
        convert.setReportConfThres(cameraAlgDto.getReport_conf_thres());
        convert.setInterval(cameraAlgDto.getInterval());
        convert.setImageCount(cameraAlgDto.getImage_count());
        convert.setCountEachSecond(cameraAlgDto.getCount_each_second());
        convert.setReportObjectSize(cameraAlgDto.getReport_object_size());
        convert.setReportTime(cameraAlgDto.getReport_time());
        convert.setReportEffectWorkTime(TimeUtils.floatToTime(cameraAlgDto.getReport_effect_work_time()));
        //获取抓图地址
        convert.setCameraSnapshotUrl(cameraAlgDto.getCamera_snapshot_url());
        convert.setReportAlgName(cameraAlgDto.getReport_alg_name());
        convert.setTargetAlgName(cameraAlgDto.getTarget_alg_name());
        convert.setReportNextAlg(cameraAlgDto.getReport_next_alg());
        convert.setReportLabel(cameraAlgDto.getReport_label());
        convert.setReportMark(cameraAlgDto.getReport_mark());
        convert.setAlgInOut(cameraAlgDto.getAlg_in_out());
        convert.setFilterType(cameraAlgDto.getFilter_type());
        convert.setReportRepeat(cameraAlgDto.getReport_repeat());
        convert.setReportArea(cameraAlgDto.getReport_area());
        convert.setEnableDeduplication(ObjectUtil.isEmpty(cameraAlgDto.getEnable_deduplication())?0:cameraAlgDto.getEnable_deduplication());
        List<String> subAlgList = cameraAlgDto.getSub_alg();
        if (CollUtil.isNotEmpty(subAlgList)){
            List<Algorithm> algorithmList = algorithmService.list(new LambdaQueryWrapper<Algorithm>().in(Algorithm::getAlgCode, subAlgList));
            if (ObjectUtil.isNotEmpty(algorithmList)){
                List<String> algIdList = algorithmList.stream().map(alg -> String.valueOf(alg.getId())).toList();
                convert.setSubAlg(algIdList);
            }
        }
        convert.setReportAreaParam(cameraAlgDto.getReport_area_param());
        return convert;
    }

    /**
     * 从请求返回结果中解析出算法配置信息
     * @param cameraId
     * @return
     */
    @Override
    public List<AlgorithmConfig> analyticStructure(Long cameraId) {
        AlgorithmQueryRequest queryRequest = new AlgorithmQueryRequest();
        queryRequest.setCameraId(String.valueOf(cameraId));
        //调用算法服务接口
        Map<String, List<AlgorithmConfig>> config = aiConfigService.getAiConfigByDeviceIdAndCameraId(queryRequest);
        List<AlgorithmConfig> configs = config.get(String.valueOf(cameraId));
        return configs;
    }

    /**
     * 更新检测点配置
     * @param pointDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCheckPoint(CheckPointDto pointDto) {
        //修改时，先根据摄像机id查询到该摄像机检测点相关信息
        AlgorithmConfigDto configDto = new AlgorithmConfigDto();
        configDto.setCamera_id(String.valueOf(pointDto.getCameraId()));
        configDto.setDevice_id(String.valueOf(pointDto.getDeviceId()));
        Camera camera = cameraService.getById(pointDto.getCameraId());
        if (ObjectUtil.isNotEmpty(camera)){
            configDto.setCamera_user(camera.getUsername());
            configDto.setCamera_password(camera.getPassword());
        }
        JSONObject algCheckDtoJson = JSONObject.parseObject(JSONObject.toJSONString(getRequestCameraAlgDto(pointDto)));
        // 创建一个JSONArray来存储JSONObject
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(algCheckDtoJson);
        configDto.setConfig_info(jsonArray);
        //修改相关检测点配置
        CheckPoint point = new CheckPoint();
        point.setId(pointDto.getId());
        point.setEnable(pointDto.getEnable());
        point.setAlgDeviceId(pointDto.getAlgDeviceId());
        point.setAlgDeviceName(pointDto.getAlgDeviceName());
        point.setRegionId(pointDto.getRegionId());
        updateById(point);
        return aiConfigService.updateConfigById(configDto);
    }

    /**
     * 赋对应的值给dto，以便调用算法服务接口
     * @param pointDto
     * @return
     */
    @NotNull
    private RequestCameraAlgDto getRequestCameraAlgDto(CheckPointDto pointDto) {
        RequestCameraAlgDto algDto = new RequestCameraAlgDto();
        algDto.setNote(pointDto.getNote());
        algDto.setEnable(pointDto.getEnable());
        algDto.setEnable_deduplication(pointDto.getEnableDeduplication());
        algDto.setReport_conf_thres(pointDto.getReportConfThres());
        algDto.setInterval(pointDto.getInterval());
        algDto.setImage_count(pointDto.getImageCount());
        algDto.setCount_each_second(pointDto.getCountEachSecond());
        algDto.setReport_object_size(pointDto.getReportObjectSize());
        algDto.setReport_time(pointDto.getReportTime());
        // 设置新的时间
        algDto.setReport_effect_work_time(TimeUtils.timeToFloat(pointDto.getReportEffectWorkTime()));
        algDto.setCamera_snapshot_url(pointDto.getCameraSnapshotUrl());
        algDto.setReport_alg_name(pointDto.getReportAlgName());
        algDto.setTarget_alg_name(pointDto.getTargetAlgName());
        algDto.setReport_next_alg(pointDto.getReportNextAlg());
        algDto.setReport_label(pointDto.getReportLabel());
        algDto.setReport_mark(pointDto.getReportMark());
        algDto.setAlg_in_out(pointDto.getAlgInOut());
        algDto.setFilter_type(pointDto.getFilterType());
        algDto.setReport_repeat(pointDto.getReportRepeat());
        if (CollectionUtil.isNotEmpty(pointDto.getSubAlg())){
            List<Algorithm> algorithms = algorithmService.listByIds(pointDto.getSubAlg());
            if (CollectionUtil.isNotEmpty(algorithms)){
                List<String> algNameList = algorithms.stream().map(Algorithm::getAlgCode).toList();
                algDto.setSub_alg(algNameList);
            }
        }
        algDto.setReport_area(pointDto.getReportArea());
        //转换区域参数中参数的类型
        List<JSONObject> reportAreaParam = pointDto.getReportAreaParam();
        for (JSONObject json : reportAreaParam) {
            // 获取threshold值并转换为浮点
            json.put("threshold", json.getFloat("threshold"));
            //获取state值并转换为整数
            json.put("state", json.getInteger("state"));
        }
        algDto.setReport_area_param(pointDto.getReportAreaParam());
        return algDto;
    }

    /**
     * 删除检测点配置
     *
     * @param checkDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteCheckPoint(DeleteCheckDto checkDto) {
        List<CheckPoint> pointList = listByIds(checkDto.getConfigId());
        if (CollectionUtil.isEmpty(pointList)){
            throw new ServiceException(ResultCode.NOT_NULL,"检测点信息");
        }
        //删除算法配置信息
        for (CheckPoint point : pointList) {
            AlgorithmDeleteRequest request = new AlgorithmDeleteRequest();
            request.setId(point.getTemplateId());
            request.setAlgNameList(new ArrayList<>(Collections.singleton(point.getAlgDesc())));
            aiConfigService.removeAlgorithmConfig(request);
        }
        //删除检测点信息
        return removeBatchByIds(checkDto.getConfigId());
    }

    @Override
    public AlgListVo queryAlgNameByCameraId(Long cameraId) {
        if (ObjectUtil.isEmpty(cameraId)){
            throw new ServiceException(ResultCode.NOT_NULL,"摄像机id");
        }
        AlgListVo vo = new AlgListVo();
        List<CheckPoint> pointList = list(new LambdaQueryWrapper<CheckPoint>().eq(CheckPoint::getCameraId, cameraId));
        if (CollectionUtil.isNotEmpty(pointList)){
            List<String> algNameList = pointList.stream().map(CheckPoint::getNote).toList();
            vo.setAlgNameList(algNameList);
        }
        return vo;
    }

    /**
     * 保存检测点信息
     * @param saveDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCheck(CheckSaveDto saveDto) {
        //摄像机id
        List<Long> cameraIdList = saveDto.getCameraId();
        //算法id
        List<Integer> algIdList = saveDto.getAlgIdList();
        //存储查询出的算法配置信息
        JSONArray algList = new JSONArray();
        //存储算法名称
        List<RequestCameraAlgDto> algRequestList = new ArrayList<>();
        for (Integer aLong : algIdList) {
            JSONArray template = templateService.getConfigByTemplateId(aLong);
            if (ObjectUtil.isNotEmpty(template)){
                RequestCameraAlgDto algDtos = template.toJavaList(RequestCameraAlgDto.class).getFirst();
                algRequestList.add(algDtos);
                algList.add(template.getFirst());
            }
        }
        if (CollectionUtil.isEmpty(algList)){
            throw new ServiceException(ResultCode.NOT_NULL,"算法模板信息");
        }
        //保存检测点信息到算法服务
        return saveCheckPointInformation(cameraIdList, algList, algRequestList);
    }

    /**
     * 保存检测点信息到算法服务
     * @param cameraIdList
     * @param algList
     * @param algRequestList
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCheckPointInformation( List<Long> cameraIdList, JSONArray algList, List<RequestCameraAlgDto> algRequestList) {
        for (Long cameraId : cameraIdList) {
            //查询摄像机信息
            Camera camera = cameraService.getById(cameraId);
            if (ObjectUtil.isEmpty(camera)){
                throw new ServiceException(ResultCode.NOT_NULL,"摄像机信息");
            }
            //保存算法配置信息
            AlgorithmTemplateRequest request = new AlgorithmTemplateRequest();
            request.setCamera_id(String.valueOf(cameraId));
            //保存时设置抓图地址
            // 使用虚拟线程池，每个任务会在一个虚拟线程上执行
            ExecutorService executorService = Executors.newVirtualThreadPerTaskExecutor();
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (Object algInfo : algList) {
                JSONObject info = (JSONObject) algInfo;
                // 异步执行每个请求，使用虚拟线程池
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        // 获取抓图地址
                        String url = cameraService.snapshotUrl(cameraId);
                        info.put(CAMERA_URL, url);
                    } catch (Exception e) {
                        // 如果发生异常，设置空字符串
                        info.put(CAMERA_URL, "");
                    }
                }, executorService);
                futures.add(future);
            }
            try {
                // 等待最多 3 秒，超时后停止所有任务
                CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                allOf.get(3, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.info("新增检测点获取抓图地址超时");
            } finally {
                // 关闭虚拟线程池并中断所有任务
                executorService.shutdownNow();
            }
            request.setConfig_info(algList);
            request.setCamera_ip(camera.getIp());
            request.setCamera_name(camera.getName());
            aiConfigService.addConfigInfo(request);
            //当对应摄像机对应算法比原来少 或者 一样时
            List<CheckPoint> checkPointList = list(new LambdaQueryWrapper<CheckPoint>().eq(CheckPoint::getCameraId, cameraId));
            //对比新增检测点 和 数据库中检测点信息
            Boolean compared = compareNotes(checkPointList, algRequestList, camera);
            if (!compared){
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 保存过滤后的检测点信息
     * @param algRequestList
     * @param camera
     */
    private Boolean saveFilterCheckPoint(List<RequestCameraAlgDto> algRequestList, Camera camera) {
        List<CheckPoint> pointList = new ArrayList<>();
        //保存检测点
        for (RequestCameraAlgDto alg : algRequestList) {
            //查询出对应算法id
            CheckPoint point = new CheckPoint().setNote(alg.getNote()).setCameraId(camera.getId()).setAlgDesc(alg.getReport_alg_name()).setEnable(alg.getEnable());
            Algorithm algorithm = algorithmService.list(new LambdaQueryWrapper<Algorithm>().eq(Algorithm::getAlgName, alg.getNote())).stream().findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(algorithm)){
                point.setAlgId(algorithm.getId());
            }
            //查询出算法模板绑定id
            List<AlgorithmConfig> algorithmConfigs = analyticStructure(camera.getId());
            if (CollectionUtil.isNotEmpty(algorithmConfigs)){
                AlgorithmConfig algorithmConfig = algorithmConfigs.getFirst();
                if (ObjectUtil.isNotEmpty(algorithmConfig)){
                    point.setTemplateId(algorithmConfig.getId());
                }
            }
            point.setPointName(camera.getIp());
            pointList.add(point);
        }
        //保存检测点集合
        return saveBatch(pointList);
    }

    /**
     * 比较摄像机对应新增算法 和 数据库中已存算法的差别
     * @param checkPointList
     * @param algRequestList
     * @param camera
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean compareNotes(List<CheckPoint> checkPointList, List<RequestCameraAlgDto> algRequestList,Camera camera) {
        if (CollectionUtil.isEmpty(checkPointList)){
            // 只有 algRequestList 不为空，保存 algRequestList 中的元素
            return saveFilterCheckPoint(algRequestList, camera);
        }
        // 提取 checkPointList 中的 note 集合
        Set<String> checkPointNotes = checkPointList.stream().map(CheckPoint::getNote).collect(Collectors.toSet());
        // 提取 algRequestList 中的 note 集合
        Set<String> algRequestNotes = algRequestList.stream().map(RequestCameraAlgDto::getNote).collect(Collectors.toSet());
        // 检查两个集合是否完全相同
        if (checkPointNotes.equals(algRequestNotes)) {
            //两个集合元素完全一样
            return Boolean.TRUE;
        } else {
            Boolean removeBatchByIds = Boolean.TRUE;
            // 找出 checkPointList 有的 algRequestList 没有的
            Set<String> onlyInCheckPoint = new HashSet<>(checkPointNotes);
            //只在 checkPointList 中的元素
            onlyInCheckPoint.removeAll(algRequestNotes);
            if (CollectionUtil.isNotEmpty(onlyInCheckPoint)){
                List<CheckPoint> pointList = checkPointList.stream().filter(point -> onlyInCheckPoint.contains(point.getNote())).toList();
                if (CollectionUtil.isNotEmpty(pointList)){
                    DeleteCheckDto checkDto = new DeleteCheckDto();
                    checkDto.setConfigId(pointList.stream().map(CheckPoint::getId).collect(Collectors.toList()));
                    removeBatchByIds = deleteCheckPoint(checkDto);
                }
            }
            // 找出 algRequestList 有的 checkPointList 没有的
            Set<String> onlyInAlgRequest = new HashSet<>(algRequestNotes);
            //只在 algRequestList 中的元素
            onlyInAlgRequest.removeAll(checkPointNotes);
            if (CollectionUtil.isEmpty(onlyInAlgRequest)){
                return removeBatchByIds;
            }
            return saveFilterCheckPoint(algRequestList.stream().filter(point -> onlyInAlgRequest.contains(point.getNote())).collect(Collectors.toList()), camera);
        }
    }

    /**
     * 将 URL 转换为 Base64 编码
     *
     * @param @return
     */
    @Override
    public String urlChangeBase(UrlChangeBaseDto urlChangeBaseDto) throws IOException {
        String urlImage = urlChangeBaseDto.getUrl();
        // 确保 URL 以 "http://" 开头
        if (!urlImage.startsWith("http://")) {
            throw new IllegalArgumentException("URL 必须以 'http://' 开头");
        }
        // 找到第一个 ':' 符号的位置（用户名和密码分隔符）
        int colonIndex = urlImage.indexOf(':', 7); // 从 'http://' 后开始找
        // 找到第二个 '@' 符号的位置（密码和地址分隔符）
        int secondAtIndex = urlImage.lastIndexOf("@");
        // 如果没有找到分隔符或格式错误，抛出异常
        if (colonIndex == -1 || secondAtIndex == -1) {
            // 自动关闭 InputStream
            try (InputStream is = new URL(urlImage).openStream()) {
                // 读取图片数据到字节数组中
                byte[] imageBytes = is.readAllBytes();
                // 使用 Base64 编码图片数据
                return Base64.getEncoder().encodeToString(imageBytes);
            }catch (IOException e){
                throw new ServiceException(ResultCode.OPERATION_FAILURE,"无法读取图片地址");
            }
        }
        // 提取用户名
        String username = urlImage.substring(7, colonIndex);  // 'http://' 的长度为 7
        // 提取密码
        String password = urlImage.substring(colonIndex + 1, secondAtIndex);
        // 提取地址
        String address = urlImage.substring(secondAtIndex + 1);
        return ImgBase64Util.NetImageToBase64(username, password, "http://" + address);
    }

    @Override
    public void batchUpdate(CheckPointBatchUpdateDto dto) {
        List<CheckPoint> checkPoints = listByIds(dto.getIds());
        if (CollectionUtil.isEmpty(checkPoints)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "检测点数据");
        }

        List<String> checkPointNotes = checkPoints.stream().map(CheckPoint::getNote).collect(Collectors.toList());

        //根据相机ID找到对应的算法配置信息
        List<AlgorithmConfig> algorithmConfigs = checkPoints.stream().map(CheckPoint::getCameraId).distinct().map(cameraId -> {
            AlgorithmQueryRequest queryRequest = new AlgorithmQueryRequest();
            queryRequest.setCameraId(String.valueOf(cameraId));
            Map<String, List<AlgorithmConfig>> configMap = aiConfigService.getAiConfigByDeviceIdAndCameraId(queryRequest);
            List<AlgorithmConfig> configs = configMap.get(String.valueOf(cameraId));
            return CollectionUtil.isEmpty(configs) ? null : configs.getFirst();
        }).filter(Objects::nonNull).toList();

        Map<Long, String> algorithmIdToCode = algorithmService.list().stream().collect(Collectors.toMap(Algorithm::getId, Algorithm::getAlgCode));

        int fail = 0;
        //对每个要被修改的算法配置进行赋值
        for (AlgorithmConfig config : algorithmConfigs) {
            JSONArray configInfo = config.getConfigInfo();
            //该算法配置下的算法参数列表
            List<RequestCameraAlgDto> reqCameraList = configInfo.toJavaList(RequestCameraAlgDto.class);
            //过滤出需要被批量修改的算法检测点的算法参数列表
            reqCameraList = reqCameraList.stream().filter(r -> checkPointNotes.contains(r.getNote())).collect(Collectors.toList());
            //赋值
            reqCameraList.forEach(req -> {
                Optional.ofNullable(dto.getEnable()).ifPresent(req::setEnable);
                Optional.ofNullable(dto.getReportConfThres()).ifPresent(req::setReport_conf_thres);
                Optional.ofNullable(dto.getInterval()).ifPresent(req::setInterval);
                Optional.ofNullable(dto.getImageCount()).ifPresent(req::setImage_count);
                Optional.ofNullable(dto.getCountEachSecond()).ifPresent(req::setCount_each_second);
                Optional.ofNullable(dto.getReportObjectSize()).ifPresent(req::setReport_object_size);
                Optional.ofNullable(dto.getReportTime()).ifPresent(req::setReport_time);
                Optional.ofNullable(dto.getReportEffectWorkTime()).ifPresent(time -> req.setReport_effect_work_time(TimeUtils.timeToFloat(time)));
                Optional.ofNullable(dto.getDeviceId()).ifPresent(req::setDeviceId);
                Optional.ofNullable(dto.getFilterType()).ifPresent(req::setFilter_type);
                Optional.ofNullable(dto.getEnableDeduplication()).ifPresent(req::setEnable_deduplication);
                Optional.ofNullable(dto.getSubAlg()).ifPresent(subAlg -> {
                    //子算法ID转为算法标识
                    List<String> subAlgCodes = subAlg.stream().map(s -> algorithmIdToCode.get(Long.valueOf(s))).collect(Collectors.toList());
                    req.setSub_alg(subAlgCodes);
                });
            });
            //修改算法配置
            AlgorithmConfigDto configDto = new AlgorithmConfigDto();
            configDto.setCamera_id(config.getCameraId());
            configDto.setDevice_id(StringUtils.isBlank(dto.getDeviceId()) ? config.getDeviceId() : dto.getDeviceId());
            configDto.setCamera_user(config.getCameraUser());
            configDto.setCamera_password(config.getCameraPassword());
            configDto.setConfig_info(JSONArray.parseArray(JSON.toJSONString(reqCameraList)));
            try {
                aiConfigService.updateConfigById(configDto);
            } catch (Exception e) {
                log.error("算法配置修改失败", e);
                fail++;
            }
        }

        if (fail > 0) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, String.format("该批数据共关联%d条算法配置，%d条修改成功,%d条修改失败", algorithmConfigs.size(), algorithmConfigs.size()-fail, fail));
        }
    }

    @Override
    public List<Camera> getCameraCheckPoint() {;
        List<Camera> cameraList = cameraService.list();
        CheckPointListDto pointDto = new CheckPointListDto();
        pointDto.setPageSize(1000);
        Page<CheckPointVo> checkPointVoPage = this.listCheckPoint(pointDto);
        List<CheckPointVo> checkPointList = checkPointVoPage.getRecords();
        if (CollUtil.isNotEmpty(cameraList)) {
            for (Camera camera : cameraList) {
                List<CheckPointVo> checkPointVoList = checkPointList.stream().filter(checkPointVo -> camera.getId().equals(checkPointVo.getCameraId())).toList();
                camera.setPointList(checkPointVoList);
            }
        }
        return cameraList;
    }

}