package com.teamway.base.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.dto.other.CommonTypeDto;
import com.teamway.base.entity.CommonType;
import com.teamway.base.mapper.CommonTypeMapper;
import com.teamway.base.service.CommonTypeService;
import com.teamway.common.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用类型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Slf4j
@Service
public class CommonTypeServiceImpl extends ServiceImpl<CommonTypeMapper, CommonType> implements CommonTypeService {

    // 配置定时任务间隔时间，默认为300000毫秒（5分钟）
    @Value("${commontype.cache.refresh.interval:300000}")
    private long cacheRefreshInterval;

    // 内存缓存，按category分组存储
    private Map<String, List<CommonType>> commonTypeCache = new ConcurrentHashMap<>();

    // 应用启动时初始化缓存
    @PostConstruct
    public void initCache() {
        refreshCache();
        log.info("CommonType缓存将每{}毫秒刷新一次", cacheRefreshInterval);
    }

    // 定时任务，使用配置的时间间隔刷新缓存
    @Scheduled(fixedRateString = "${commontype.cache.refresh.interval:60000}")
    public void scheduledRefreshCache() {
        refreshCache();
    }

    // 刷新缓存方法
    private synchronized void refreshCache() {
        try {
            log.info("开始刷新CommonType缓存...");
            List<CommonType> allTypes = this.list();

            // 清空现有缓存
            commonTypeCache.clear();

            // 按category分组重新填充缓存
            Map<String, List<CommonType>> groupedTypes = allTypes.stream()
                    .collect(Collectors.groupingBy(CommonType::getCategory));

            commonTypeCache.putAll(groupedTypes);
            log.info("CommonType缓存刷新完成，共加载{}个分类，{}条记录", commonTypeCache.size(), allTypes.size());
        } catch (Exception e) {
            log.error("刷新CommonType缓存失败", e);
        }
    }

    // 强制刷新缓存的方法，可在数据变更后调用
    public void forceRefreshCache() {
        refreshCache();
    }

    // 从缓存获取数据的私有方法
    private List<CommonType> getFromCache(String category) {
        return commonTypeCache.getOrDefault(category, new ArrayList<>());
    }

    @Override
    public List<CommonType> getPersonJobList() {
        List<CommonType> result = getFromCache(CommonConstant.PERSON_JOB_TYPE);
        if (result.isEmpty()) {
            // 缓存未命中，从数据库读取并加入缓存
            result = this.lambdaQuery().eq(CommonType::getCategory, CommonConstant.PERSON_JOB_TYPE).list();
            if (!result.isEmpty()) {
                commonTypeCache.put(CommonConstant.PERSON_JOB_TYPE, result);
            }
        }
        return result;
    }

    @Override
    public List<CommonType> getPersonDepartmentList() {
        List<CommonType> result = getFromCache(CommonConstant.PERSON_DEPARTMENT);
        if (result.isEmpty()) {
            result = this.lambdaQuery().eq(CommonType::getCategory, CommonConstant.PERSON_DEPARTMENT).list();
            if (!result.isEmpty()) {
                commonTypeCache.put(CommonConstant.PERSON_DEPARTMENT, result);
            }
        }
        return result;
    }

    @Override
    public List<CommonType> getPersonPostList() {
        List<CommonType> result = getFromCache(CommonConstant.PERSON_POST);
        if (result.isEmpty()) {
            result = this.lambdaQuery().eq(CommonType::getCategory, CommonConstant.PERSON_POST).list();
            if (!result.isEmpty()) {
                commonTypeCache.put(CommonConstant.PERSON_POST, result);
            }
        }
        return result;
    }

    @Override
    public List<CommonType> getPersonUnitList() {
        List<CommonType> result = getFromCache(CommonConstant.PERSON_UNIT);
        if (result.isEmpty()) {
            result = this.lambdaQuery().eq(CommonType::getCategory, CommonConstant.PERSON_UNIT).list();
            if (!result.isEmpty()) {
                commonTypeCache.put(CommonConstant.PERSON_UNIT, result);
            }
        }
        return result;
    }

    @Override
    public List<CommonType> getCarColorList() {
        List<CommonType> result = getFromCache(CommonConstant.CAR_COLOR);
        if (result.isEmpty()) {
            result = this.lambdaQuery().eq(CommonType::getCategory, CommonConstant.CAR_COLOR).list();
            if (!result.isEmpty()) {
                commonTypeCache.put(CommonConstant.CAR_COLOR, result);
            }
        }
        return result;
    }

    @Override
    public List<CommonType> getTypeListByCategory(CommonTypeDto commonTypeDto) {
        String category = commonTypeDto.getCategory();
        List<CommonType> result = getFromCache(category);

        if (result.isEmpty()) {
            // 缓存未命中，从数据库读取
            result = this.lambdaQuery()
                    .eq(CommonType::getCategory, category)
                    .orderByAsc(CommonType::getTypeCode)
                    .list();

            // 将结果加入缓存
            if (!result.isEmpty()) {
                commonTypeCache.put(category, result);
            }
        } else {
            // 缓存命中，按typeCode排序返回
            result = result.stream()
                    .sorted((a, b) -> a.getTypeCode().compareTo(b.getTypeCode()))
                    .collect(Collectors.toList());
        }

        return result;
    }

    // 根据类别和编码获取名称的便捷方法
    public String getNameByTypeCodeAndCategory(String typeCode, String category) {
        if (typeCode == null || category == null) {
            return null;
        }

        List<CommonType> types = getFromCache(category);
        return types.stream()
                .filter(type -> typeCode.equals(type.getTypeCode()))
                .findFirst()
                .map(CommonType::getName)
                .orElse(null);
    }
}