package com.teamway.base.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.dto.tv.decoder.DecoderBaseResDto;
import com.teamway.base.dto.tv.decoder.DecoderDelDto;
import com.teamway.base.dto.tv.decoder.DecoderLayoutDto;
import com.teamway.base.entity.Decoder;
import com.teamway.base.entity.TvScene;
import com.teamway.base.mapper.DecoderMapper;
import com.teamway.base.service.DecoderService;
import com.teamway.base.service.TvSceneService;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.entity.ResultModel;
import com.teamway.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class DecoderServiceImpl extends ServiceImpl<DecoderMapper, Decoder> implements DecoderService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${decoderUrl}")
    private String decoderUrl;

    @Autowired
    private TvSceneService tvSceneService;

    @Override
    public boolean add(Decoder decoder) {
        LambdaQueryWrapper<Decoder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Decoder::getName, decoder.getName());
        List<Decoder> list = this.list(lambdaQueryWrapper);
        if (list != null && list.size() > 0) {
            throw new ServiceException(ResultCode.EXIST, "解码器名称");
        }
        String addUrl = decoderUrl + "/hkSdk/addDevice";
        String body = null;
        try {
            body = restTemplate.getForEntity(
                    addUrl + "?ip=" + decoder.getIp() + "&port=" + decoder.getPort() + "&user=" + decoder.getUsername() + "&password=" + decoder.getPassword(),
                    String.class).getBody();
        } catch (RestClientException ignored) {
        }
        if (body == null) {
            throw new ServiceException(ResultCode.WRONG, "请求解码器服务");
        }
        DecoderBaseResDto decoderBaseResDto = JSON.parseObject(body, DecoderBaseResDto.class);
        if (decoderBaseResDto != null && decoderBaseResDto.getCode() == 0) {
            decoder.setTableLayout(decoder.getLayout().getLength() + "," + decoder.getLayout().getWidth());
            if (!this.save(decoder)) {
                throw new ServiceException(ResultCode.WRONG, "保存至数据库");
            }
            return true;
        } else {
            assert decoderBaseResDto != null;
            log.error("请求解码器服务错误：{}", decoderBaseResDto.getMsg());
            throw new ServiceException(ResultCode.WRONG, "请求解码器服务");
        }
    }

    @Override
    public boolean updateDecoder(Decoder decoder) {
        Decoder old = this.getById(decoder.getId());
        if (old == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "解码器ID");
        }
        LambdaQueryWrapper<Decoder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Decoder::getName, decoder.getName());
        lambdaQueryWrapper.ne(Decoder::getId, decoder.getId());
        List<Decoder> list = this.list(lambdaQueryWrapper);
        if (list != null && list.size() > 0) {
            throw new ServiceException(ResultCode.EXIST,"解码器名称");
        }
        decoder.setTableLayout(decoder.getLayout().getLength() + "," + decoder.getLayout().getWidth());
        boolean isNeed = old.getIp().equals(decoder.getIp()) && old.getPort().equals(decoder.getPort()) && old.getUsername().equals(decoder.getUsername()) && old.getPassword().equals(decoder.getPassword());
        if (isNeed) {
            return this.updateById(decoder);
        }
        String updateUrl = decoderUrl + "/hkSdk/updateDevice";
        String body = null;
        try {
            body = restTemplate.getForEntity(
                    updateUrl + "?oldIp=" + old.getIp() + "&newIp=" + decoder.getIp() + "&port=" + decoder.getPort() + "&user=" + decoder.getUsername() + "&password=" + decoder.getPassword(),
                    String.class).getBody();
        } catch (RestClientException ignored) {
        }
        if (body == null) {
            throw new ServiceException(ResultCode.WRONG, "请求解码器服务");
        }
        DecoderBaseResDto decoderBaseResDto = JSON.parseObject(body, DecoderBaseResDto.class);
        if (decoderBaseResDto != null && decoderBaseResDto.getCode() == 0) {
            if (!this.updateById(decoder)) {
                throw new ServiceException(ResultCode.WRONG, "数据库修改");
            }
            return true;
        } else {
            assert decoderBaseResDto != null;
            log.error("请求解码器服务错误：{}", decoderBaseResDto.getMsg());
            throw new ServiceException(ResultCode.WRONG, "请求解码器服务");
        }
    }

    @Override
    public ResultModel<String> del(DecoderDelDto decoderDelDto) {
        int s = 0;
        int f = 0;
        for (String decoderId : decoderDelDto.getDecoderIds()) {
            Decoder decoder = this.getById(decoderId);
            if (decoder == null) {
                f++;
                continue;
            }
            String delUrl = decoderUrl + "/hkSdk/delDevice";
            String body = null;
            try {
                body = restTemplate.getForEntity(
                        delUrl + "?ip=" + decoder.getIp(),
                        String.class).getBody();
            } catch (RestClientException ignored) {
            }
            if (body == null) {
                f++;
                continue;
            }
            DecoderBaseResDto decoderBaseResDto = JSON.parseObject(body, DecoderBaseResDto.class);
            if (decoderBaseResDto != null && decoderBaseResDto.getCode() == 0) {
                if (this.removeById(decoderId)) {
                    LambdaQueryWrapper<TvScene> tvSceneLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    tvSceneLambdaQueryWrapper.eq(TvScene::getDecoderDeviceId, decoderId);
                    tvSceneService.remove(tvSceneLambdaQueryWrapper);
                    s++;
                } else {
                    f++;
                }
            } else {
                f++;
            }
        }
        if (f > 0) {
            return new ResultModel<>(9999, "成功：" + s + "个，失败：" + f + "个。");
        } else {
            return ResultModel.success();
        }
    }

    @Override
    public List<Decoder> listAndSynthesisLayout() {
        List<Decoder> list = this.list();
        if (list != null && list.size() > 0) {
            list.forEach(decoder -> {
                String[] split = decoder.getTableLayout().split(",");
                DecoderLayoutDto decoderLayoutDto = new DecoderLayoutDto();
                decoderLayoutDto.setLength(Integer.valueOf(split[0]));
                decoderLayoutDto.setWidth(Integer.valueOf(split[1]));
                decoder.setLayout(decoderLayoutDto);
            });
        }
        return list;
    }
}
