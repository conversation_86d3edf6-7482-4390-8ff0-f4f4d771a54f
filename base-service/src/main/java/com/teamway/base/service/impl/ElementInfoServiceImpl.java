package com.teamway.base.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.common.constant.CameraStateConstant;
import com.teamway.base.dto.element.QueryElementDto;
import com.teamway.base.dto.element.UpdatePositionDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.ElementInfo;
import com.teamway.base.entity.IconInfo;
import com.teamway.base.enums.ElementTypeEnum;
import com.teamway.base.factory.ElementProcessorFactory;
import com.teamway.base.mapper.CameraMapper;
import com.teamway.base.mapper.ElementInfoMapper;
import com.teamway.base.mapper.IconInfoMapper;
import com.teamway.base.service.ElementInfoService;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <p>
 * 地图元素表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-15
 */
@Service
@Slf4j
public class ElementInfoServiceImpl extends ServiceImpl<ElementInfoMapper, ElementInfo> implements ElementInfoService {

    @Autowired
    private ElementInfoMapper elementInfoMapper;

    @Autowired
    private CameraMapper cameraMapper;

    @Autowired
    private IconInfoMapper iconInfoMapper;

    /**
     * 元素处理器工厂
     */
    @Autowired
    private ElementProcessorFactory processorFactory;

    /**
     * 批量添加地图元素
     * 根据元素类型执行不同的业务逻辑并保存到数据库
     *
     * @param elementInfoList 元素列表
     * @return 添加是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(List<ElementInfo> elementInfoList) {
        // 按元素类型分组处理
        Map<String, List<ElementInfo>> typeGroupedElements = elementInfoList.stream()
                .collect(Collectors.groupingBy(ElementInfo::getType));

        // 处理每种类型的元素
        typeGroupedElements.forEach(this::processElementsByType);

        // 检查所有元素是否已存在
        elementInfoList.forEach(this::checkElementExistence);

        // 使用MyBatisPlus批量保存
        return this.saveBatch(elementInfoList);
    }

    /**
     * 根据类型处理元素集合
     *
     * @param type 元素类型
     * @param elements 元素列表
     */
    private void processElementsByType(String type, List<ElementInfo> elements) {
        // 将字符串类型转换为枚举
        ElementTypeEnum elementType = ElementTypeEnum.getByCode(type)
                .orElseThrow(() -> new ServiceException(ResultCode.OPERATION_FAILURE,
                        StrUtil.format("不支持的元素类型: {}", type)));

        // 根据元素类型获取相应的处理器
        Consumer<ElementInfo> processor = processorFactory.getProcessor(elementType);

        // 处理每个元素
        elements.forEach(processor);
    }

    /**
     * 检查元素是否已存在
     *
     * @param elementInfo 元素信息
     */
    private void checkElementExistence(ElementInfo elementInfo) {
        LambdaQueryWrapper<ElementInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ElementInfo::getObjectId, elementInfo.getObjectId())
                .eq(ElementInfo::getType, elementInfo.getType());

        long count = elementInfoMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "此元素不能重复添加");
        }
    }

    /**
     * 更新元素位置
     *
     * @param positionDto 位置更新DTO
     * @return 更新的记录数
     */
    @Override
    public int update(UpdatePositionDto positionDto) {
        ElementInfo elementInfo = new ElementInfo();
        elementInfo.setId(positionDto.getId());
        elementInfo.setPosX(positionDto.getPosX());
        elementInfo.setPosY(positionDto.getPosY());
        return elementInfoMapper.updateById(elementInfo);
    }

    /**
     * 批量删除元素
     *
     * @param ids 元素ID列表
     * @return 删除的记录数
     */
    @Override
    public int delete(List<Long> ids) {
        return elementInfoMapper.deleteByIds(ids);
    }

    /**
     * 根据元素ID查找
     *
     * @param elementId 元素ID
     * @return 元素列表
     */
    @Override
    public List<ElementInfo> findByParam(Long elementId) {
        return elementInfoMapper.findByParam(elementId);
    }

    /**
     * 根据条件查询元素
     *
     * @param queryElementDto 查询条件DTO
     * @return 元素列表
     */
    @Override
    public List<ElementInfo> selectList(QueryElementDto queryElementDto) {
        LambdaQueryWrapper<ElementInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(queryElementDto.getType()), ElementInfo::getType, queryElementDto.getType());
        queryWrapper.eq(StrUtil.isNotBlank(queryElementDto.getRegionId()), ElementInfo::getRegionId, queryElementDto.getRegionId());
        queryWrapper.eq(StrUtil.isNotBlank(queryElementDto.getObjectId()), ElementInfo::getObjectId, queryElementDto.getObjectId());
        List<ElementInfo> list = this.list(queryWrapper);
        List<IconInfo> iconInfoList = iconInfoMapper.selectList(null);
        list.forEach(elementInfo -> {
            Camera camera = cameraMapper.selectById(elementInfo.getObjectId());
            if (camera != null) {
                elementInfo.setName(camera.getName());
                if (CameraStateConstant.OFF_LINE.equals(camera.getState())) {
                    elementInfo.setState(CameraStateConstant.OFF_LINE.toString());
                } else {
                    elementInfo.setState(CameraStateConstant.ON_LINE.toString());
                }
            }

            // 使用Stream API查找对应图标
            String iconUrl = iconInfoList.stream()
                    .filter(iconInfo -> iconInfo.getType().equals(elementInfo.getIconType()))
                    .map(IconInfo::getIconUrl)
                    .findFirst()
                    .orElse("");
            elementInfo.setIconUrl(iconUrl);
        });

        return list;
    }
}