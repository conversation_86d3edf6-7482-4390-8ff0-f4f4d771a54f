package com.teamway.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.entity.HScreenContent;
import com.teamway.base.mapper.HScreenContentMapper;
import com.teamway.base.service.HScreenContentService;
import com.teamway.common.entity.BatchIds;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 横屏显示内容配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Slf4j
@Service
public class HScreenContentServiceImpl extends ServiceImpl<HScreenContentMapper, HScreenContent> implements HScreenContentService {

    @Override
    public Boolean addHScreenContent(HScreenContent hScreenContent) {
        return this.save(hScreenContent);
    }

    @Override
    public Boolean updateHScreenContent(HScreenContent hScreenContent) {
        return this.updateById(hScreenContent);
    }

    @Override
    public Boolean batchDeleteHScreenContent(BatchIds request) {
        return this.removeByIds(request.getIds());
    }

    @Override
    public HScreenContent getHScreenContentById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<HScreenContent> getHScreenContentList(Long screenId) {
        LambdaQueryWrapper<HScreenContent> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(HScreenContent::getScreenId, screenId)
                .orderByAsc(HScreenContent::getSerialNumber);
        return this.list(wrapper);
    }
}