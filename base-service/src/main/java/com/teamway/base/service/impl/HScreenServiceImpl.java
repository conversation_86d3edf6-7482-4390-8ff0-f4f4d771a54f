package com.teamway.base.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.dto.screen.HScreenQueryDto;
import com.teamway.base.entity.HScreen;
import com.teamway.base.entity.HScreenContent;
import com.teamway.base.mapper.HScreenMapper;
import com.teamway.base.service.HScreenContentService;
import com.teamway.base.service.HScreenService;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 横屏配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Slf4j
@Service
public class HScreenServiceImpl extends ServiceImpl<HScreenMapper, HScreen> implements HScreenService {

    @Autowired
    private HScreenContentService hScreenContentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addHScreen(HScreen hScreen) {
        hScreen.setId(IdWorker.getId());
        for (HScreenContent hScreenContent : hScreen.getHScreenContentList()) {
            hScreenContent.setScreenId(hScreen.getId());
        }
        hScreenContentService.saveBatch(hScreen.getHScreenContentList());
        return this.save(hScreen);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateHScreen(HScreen hScreen) {
        hScreenContentService.saveOrUpdateBatch(hScreen.getHScreenContentList());
        return this.updateById(hScreen);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteHScreen(BatchIds request) {
        hScreenContentService.remove(new LambdaQueryWrapper<HScreenContent>()
                .in(CollectionUtil.isNotEmpty(request.getIds()),
                        HScreenContent::getScreenId,
                        request.getIds()));
        return this.removeByIds(request.getIds());
    }

    @Override
    public HScreen getHScreenById(Long id) {
        if (id == null) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "ID不能为空");
        }
        // 先查询主表数据
        HScreen hScreen = this.getById(id);
        if (hScreen == null) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "未找到对应的横屏配置");
        }
        // 查询关联的内容列表
        List<HScreenContent> contentList = hScreenContentService.lambdaQuery()
                .eq(HScreenContent::getScreenId, id)
                .orderByAsc(HScreenContent::getSerialNumber)
                .list();
        hScreen.setHScreenContentList(contentList != null ? contentList : new ArrayList<>());
        return hScreen;
    }

    @Override
    public IPage<HScreen> findPageList(HScreenQueryDto queryDto) {
        LambdaQueryWrapper<HScreen> wrapper = new LambdaQueryWrapper<HScreen>()
                .eq(queryDto.getId() != null, HScreen::getId, queryDto.getId())
                .like(StrUtil.isNotBlank(queryDto.getScreenName()), HScreen::getScreenName, queryDto.getScreenName())
                .eq(StrUtil.isNotBlank(queryDto.getDisplayMode()), HScreen::getDisplayMode, queryDto.getDisplayMode())
                .eq(StrUtil.isNotBlank(queryDto.getStatus()), HScreen::getStatus, queryDto.getStatus())
                .orderBy(StrUtil.isNotBlank(queryDto.getSortBy()), queryDto.getIsAsc(), HScreen::getId)
                .orderByDesc(HScreen::getCreateTime);

        return this.page(new Page<>(queryDto.getPageIndex(), queryDto.getPageSize()), wrapper);
    }
}