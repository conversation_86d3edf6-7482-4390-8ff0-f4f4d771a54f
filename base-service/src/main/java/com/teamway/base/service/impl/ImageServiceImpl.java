package com.teamway.base.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.dto.screenshot.ImageQueryDto;
import com.teamway.base.dto.screenshot.ScreenshotExportDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.Image;
import com.teamway.base.entity.Region;
import com.teamway.base.mapper.CameraMapper;
import com.teamway.base.mapper.ImageMapper;
import com.teamway.base.service.ImageService;
import com.teamway.base.service.RegionService;
import com.teamway.base.util.ScreenshotUtils;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.util.ExcelUtils;
import com.teamway.common.util.ImageUtils;
import com.teamway.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.net.ssl.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-10-20
 * @Description: 摄像机截图管理
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class ImageServiceImpl extends ServiceImpl<ImageMapper, Image> implements ImageService {

    @Autowired
    private ImageMapper imageMapper;

    @Autowired
    private RegionService regionService;

    @Autowired
    private CameraMapper cameraMapper;

    /**
     * 截图管理图片存储桶
     */
    private final String destBucket = "screenshot-management";

    @Override
    public Image takePotos(Long cameraId) {
        Camera camera = cameraMapper.selectById(cameraId);
        if (null == camera) {
            throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
        }
        ScreenshotUtils.ScreenshotDto screenshotDto = ScreenshotUtils.ScreenshotDto.builder().ip(camera.getIp()).port(camera.getPort())
                .username(camera.getUsername()).password(camera.getPassword()).channel(Integer.valueOf(camera.getChannel()))
                .factory(camera.getFactory()).type(camera.getType()).build();
        String screenshot = ScreenshotUtils.screenshot(screenshotDto);
        //上传图片
        String url = ImageUtils.saveImage(screenshot, destBucket);
        Image image = new Image();
        image.setImageUrl(url);
        image.setCameraId(cameraId);
        image.setCameraName(camera.getName());
        image.setIp(camera.getIp());
        this.save(image);
        return image;
    }

    @Override
    public int batchDel(List<Long> ids) {
        return this.baseMapper.deleteBatchIds(ids);
    }

    @Override
    public IPage<Image> selectPageList(ImageQueryDto imageQueryDto) {
        List<Camera> cameraList = cameraMapper.selectCameraById();
        List<Long> cameraIds = getCameraIds(imageQueryDto.getObjectType(), imageQueryDto.getObjectId());
        IPage<Image> page = new Page(imageQueryDto.getPageIndex(), imageQueryDto.getPageSize());
        LambdaQueryWrapper<Image> queryWrapper = new LambdaQueryWrapper<>();
        //如果传了摄像机id或区域id，没查到摄像机id  就返回空
        if (CollUtil.isEmpty(cameraIds) && StrUtil.isNotBlank(imageQueryDto.getObjectType()) && imageQueryDto.getObjectId() != null) {
            return page;
        }
        queryWrapper.in(CollUtil.isNotEmpty(cameraIds), Image::getCameraId, cameraIds)
                .in(CollUtil.isNotEmpty(imageQueryDto.getIds()), Image::getId, imageQueryDto.getIds())
                .ge(StrUtil.isNotBlank(imageQueryDto.getStartTime()), Image::getCreateTime, imageQueryDto.getStartTime())
                .le(StrUtil.isNotBlank(imageQueryDto.getEndTime()), Image::getCreateTime, imageQueryDto.getEndTime())
                .orderByDesc(Image::getCreateTime);
        imageMapper.selectPage(page, queryWrapper);
        List<Image> imageList = page.getRecords();
        if (CollUtil.isNotEmpty(imageList)) {
            Map<Long, Camera> cameraMap = Optional.ofNullable(cameraList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.toMap(Camera::getId, Function.identity()));
            imageList.forEach(image -> {
                Camera camera = cameraMap.get(image.getCameraId());
                if (camera != null) {
                    image.setCameraPath(camera.getCameraPath());
                }
            });
        }
        return page;
    }

    /**
     * 获取摄像机id集合
     *
     * @param objectType 类型  1-摄像机  2-区域
     * @param objectId   摄像机Id 或 区域Id
     * @return
     */
    private List<Long> getCameraIds(String objectType, Long objectId) {
        List<Long> cameraIdList = new ArrayList<>();
        if (StrUtil.isNotBlank(objectType) && objectId != null) {
            if ("1".equals(objectType)) {
                cameraIdList.add(objectId);
            } else {
                //查询所有子区域
                List<Long> regionIds = new ArrayList<>();
                this.buildMap(objectId, regionService.list(), regionIds);
                List<Camera> cameraList = cameraMapper.selectList(new LambdaQueryWrapper<Camera>().in(Camera::getRegionId, regionIds));
                cameraList.forEach(camera -> cameraIdList.add(camera.getId()));
            }
        }
        return cameraIdList;
    }

    /**
     * 查询父区域下子区域
     *
     * @param pId
     * @param list
     * @return :
     * @date : 2022/8/5
     * <AUTHOR> zhangsai
     */
    private List<Long> buildMap(Long pId, List<Region> list, List<Long> regionIds) {
        for (Region region : list) {
            if (region.getId().equals(pId)) {
                regionIds.add(region.getId());
            }
            if (region.getPid().equals(pId)) {
                regionIds.add(region.getId());
                //递归
                buildMap(region.getId(), list, regionIds);
            }
        }
        return regionIds;
    }

    @Override
    public void export(BatchIds<Long> batchIds, HttpServletResponse response) {
        ImageQueryDto imageQueryDto = new ImageQueryDto();
        imageQueryDto.setIds(batchIds.getIds());
        IPage<Image> infoDtoIPage = this.selectPageList(imageQueryDto);
        List<Image> list = infoDtoIPage.getRecords();
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "无需要导出数据");
        }
        List exportList = new ArrayList();
        for (Image image : list) {
            String imageUrl = ImageUtils.getOssVisitUrl(image.getImageUrl());
            byte[] file = downFile(imageUrl);
            ScreenshotExportDto screenshotExportDto = new ScreenshotExportDto();
            screenshotExportDto.setImg(file);
            screenshotExportDto.setCameraName(image.getCameraName());
            screenshotExportDto.setRegionName(image.getCameraPath());
            screenshotExportDto.setCreateTime(DateUtil.format(image.getCreateTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            screenshotExportDto.setRemark(image.getRemark());
            exportList.add(screenshotExportDto);
        }

        ExportParams exportParams = new ExportParams("截图列表", "截图列表", ExcelType.HSSF);
        try {
            ExcelUtils.exportExcel(exportList, ScreenshotExportDto.class, "截图列表", exportParams, response);
        } catch (IOException e) {
            throw new ServiceException(ResultCode.EXPORT_FAIL, e);
        }
    }

    @Override
    public byte[] downFile(String imaUrl) {
        imaUrl = ImageUtils.getOssVisitUrl(imaUrl);
        InputStream inputStream = null;
        try {
            ignoreSsl();
            URL value = new URL(imaUrl);
            URLConnection connection = value.openConnection();
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            inputStream = connection.getInputStream();
            byte[] var2;
            try {
                copy(inputStream, output);
                var2 = output.toByteArray();
            } finally {
                output.toByteArray();
            }
            return var2;
        } catch (Exception e) {
            log.info("图片下载失败", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.info("关闭流异常", e);
                }
            }
        }
        return null;
    }

    @Override
    public Boolean saveNote(Image image) {
        return updateById(image);
    }

    private int copy(InputStream input, OutputStream output) throws IOException {
        long count = 0L;
        int n;
        for (byte[] buffer = new byte[4096]; -1 != (n = input.read(buffer)); count += (long) n) {
            output.write(buffer, 0, n);
        }
        return count > 2147483647L ? -1 : (int) count;
    }


    private static void trustAllHttpsCertificates() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[1];
        TrustManager tm = new miTM();
        trustAllCerts[0] = tm;
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, null);
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
    }

    static class miTM implements TrustManager, X509TrustManager {
        public X509Certificate[] getAcceptedIssuers() {
            return null;
        }

        public boolean isServerTrusted(X509Certificate[] certs) {
            return true;
        }

        public boolean isClientTrusted(X509Certificate[] certs) {
            return true;
        }

        public void checkServerTrusted(X509Certificate[] certs, String authType) throws CertificateException {
            return;
        }

        public void checkClientTrusted(X509Certificate[] certs, String authType) throws CertificateException {
            return;
        }
    }

    /**
     * 忽略HTTPS请求的SSL证书，必须在openConnection之前调用	 * @throws Exception
     */
    public static void ignoreSsl() throws Exception {
        HostnameVerifier hv = new HostnameVerifier() {
            @Override
            public boolean verify(String urlHostName, SSLSession session) {
                System.out.println("Warning: URL Host: " + urlHostName + " vs. " + session.getPeerHost());
                return true;
            }
        };
        trustAllHttpsCertificates();
        HttpsURLConnection.setDefaultHostnameVerifier(hv);
    }

}
