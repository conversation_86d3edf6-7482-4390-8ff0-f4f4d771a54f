package com.teamway.base.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.teamway.base.dto.model.GbInfoVo;
import com.teamway.base.dto.model.ModelConfigVo;
import com.teamway.base.dto.model.ModelParamQueryDto;
import com.teamway.base.dto.model.ModelParamVo;
import com.teamway.base.dto.model.ModelQueryDto;
import com.teamway.base.entity.Algorithm;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.Model;
import com.teamway.base.entity.StreamMedia;
import com.teamway.base.mapper.ModelMapper;
import com.teamway.base.service.AlgorithmService;
import com.teamway.base.service.CameraService;
import com.teamway.base.service.ModelService;
import com.teamway.base.service.StreamMediaService;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> thh
 * @date : 2022/7/22
 */
@Slf4j
@Service
public class ModerlServiceImpl extends ServiceImpl<ModelMapper, Model> implements ModelService {

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private StreamMediaService streamMediaService;

    @Autowired
    private CameraService cameraService;

    @Value("${streamUrl}")
    private String streamUrl;

    @Override
    public boolean addModel(Model model) {
        Model exist = getByAlgId(model.getAlgId());
        if (Objects.nonNull(exist)) {
            throw new ServiceException(ResultCode.EXIST, "识别内容");
        }
        return save(model);
    }

    @Override
    public boolean updateModel(Model model) {
        Model exist = getByAlgId(model.getAlgId());
        if (Objects.nonNull(exist) && !Objects.equals(exist.getId(), model.getId())) {
            throw new ServiceException(ResultCode.EXIST, "识别内容");
        }
        return updateById(model);
    }

    @Override
    public IPage<Model> selectPageList(ModelQueryDto queryDto) {
        IPage<Model> page = new Page<>(queryDto.getPageIndex(), queryDto.getPageSize());
        LambdaQueryWrapper<Model> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(queryDto.getModelUrl()), Model::getModelUrl, queryDto.getModelUrl());
        queryWrapper.eq(Objects.nonNull(queryDto.getAlgFrameType()), Model::getAlgFrameType, queryDto.getAlgFrameType());
        queryWrapper.like(StrUtil.isNotBlank(queryDto.getTypes()), Model::getTypes, queryDto.getTypes());
        queryWrapper.like(Objects.nonNull(queryDto.getGraphCardId()), Model::getGraphCardId, queryDto.getGraphCardId());
        page = page(page, queryWrapper);
        Map<Long, String> algMap = getAlgMap();
        page.getRecords().forEach(i -> i.setAlgName(algMap.get(i.getAlgId())));
        return page;
    }

    @Override
    public List<Model> getAll() {
        List<Model> list = list();
        Map<Long, String> algMap = getAlgMap();
        list.forEach(i -> i.setAlgName(algMap.get(i.getAlgId())));
        return list;
    }

    @Override
    public ModelParamVo getParameter(ModelParamQueryDto query) {
        //固定拿ID等于2的流媒体数据
        StreamMedia streamMedia = streamMediaService.getById(2);
        if (Objects.isNull(streamMedia)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "ID为2的流媒体数据");
        }
        //算法做非空判断
        Algorithm algorithm = algorithmService.getById(query.getAlgId());
        if (Objects.isNull(algorithm)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "算法");
        }
        //根据算法ID拿到模型配置数据
        Model model = getByAlgId(query.getAlgId());
        if (Objects.isNull(model)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "算法对应的模型");
        }

        //组装数据
        ModelConfigVo modelBuild = ModelConfigVo.builder()
                .modelUrl(model.getModelUrl())
                .modelWidth(model.getImageWidth())
                .modelHeight(model.getImageHeight())
                .confConfidence(model.getConfThres())
                .iouConfidence(model.getDupRemovalThres())
                .modelType(model.getAlgFrameType())
                .classes(Lists.newArrayList(model.getTypes().split(",")))
                .algName(algorithm.getAlgCode())
                .build();
        Camera camera = cameraService.getCameraById(query.getCameraId());
        if (Objects.isNull(camera)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "相机");
        }

        //配置文件中流媒体的IP和端口
        String[] split = streamUrl.split(":");
        GbInfoVo gbInfo = GbInfoVo.builder()
                .gbServerIp(split[0])
                .gbServerPort(Integer.parseInt(split[1]))
                .mainId(camera.getMainId())
                .subId(camera.getSubId())
                .build();

        return ModelParamVo.builder()
                .streamIp(streamMedia.getIp())
                .rtspPort(streamMedia.getRtspPort())
                .rtmpPort(streamMedia.getRtmpPort())
                .httpPort(streamMedia.getHttpPort())
                .sslPort(streamMedia.getSslPort())
                .gpuId(model.getGraphCardId())
                .modelConfig(modelBuild)
                .gbInfo(gbInfo)
                .build();
    }

    private Model getByAlgId(Long algId) {
        LambdaQueryWrapper<Model> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Model::getAlgId, algId);
        List<Model> list = list(wrapper);
        return list.isEmpty() ? null : list.getFirst();
    }

    private Map<Long, String> getAlgMap() {
        List<Algorithm> algorithms = algorithmService.list();
        return algorithms.stream().collect(Collectors.toMap(Algorithm::getId, Algorithm::getAlgName));
    }

}
