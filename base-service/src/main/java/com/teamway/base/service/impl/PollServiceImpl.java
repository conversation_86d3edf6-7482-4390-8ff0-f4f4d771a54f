package com.teamway.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.dto.poll.PollDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CameraPreset;
import com.teamway.base.entity.Poll;
import com.teamway.base.entity.PollCamera;
import com.teamway.base.mapper.CameraMapper;
import com.teamway.base.mapper.CameraPresetMapper;
import com.teamway.base.mapper.PollCameraMapper;
import com.teamway.base.mapper.PollMapper;
import com.teamway.base.service.PollCameraService;
import com.teamway.base.service.PollService;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 视频轮巡表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-25
 */
@Service
public class PollServiceImpl extends ServiceImpl<PollMapper, Poll> implements PollService {

    @Autowired
    private PollMapper pollMapper;

    @Autowired
    private PollCameraMapper pollCameraMapper;

    @Autowired
    private CameraMapper cameraMapper;

    @Autowired
    private PollCameraService pollCameraService;

    @Autowired
    private CameraPresetMapper cameraPresetMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int add(Poll poll) {
        LambdaQueryWrapper<Poll> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Poll::getName, poll.getName());
        List<Poll> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            throw new ServiceException(ResultCode.EXIST, "轮巡组名");
        }
        int insert = pollMapper.insert(poll);
        if (insert > 0) {
            insertGroupCamera(poll.getId(), poll.getPollCameraList());
        }
        return insert;
    }

    @Override
    public int delete(Long id) {
        Poll poll = pollMapper.selectById(id);
        if (poll == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "视频轮巡组id");
        }
        int result = pollMapper.deleteById(id);
        if (result > 0) {
            LambdaQueryWrapper<PollCamera> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(PollCamera::getPollId, id);
            pollCameraMapper.delete(lambdaQueryWrapper);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(Poll poll) {
        Poll id = pollMapper.selectById(poll.getId());
        if (id == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "轮巡组");
        }
        LambdaQueryWrapper<Poll> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Poll::getName, poll.getName());
        queryWrapper.ne(Poll::getId, poll.getId());
        List<Poll> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            throw new ServiceException(ResultCode.EXIST, "轮巡组名");
        }
        int result = pollMapper.updateById(poll);
        LambdaQueryWrapper<PollCamera> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PollCamera::getPollId, poll.getId());
        pollCameraMapper.delete(lambdaQueryWrapper);
        insertGroupCamera(poll.getId(), poll.getPollCameraList());
        return result;
    }

    @Override
    public List<Poll> selectList(PollDto pollDto) {
        QueryWrapper<Poll> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(StrUtil.isNotBlank(pollDto.getName()), Poll::getName, pollDto.getName());
        List<Poll> polls = pollMapper.selectList(queryWrapper);
        for (Poll poll : polls) {
            List<PollCamera> cameras = pollCameraMapper.findPollCamera(poll.getId());
            poll.setPollCameraList(cameras);
        }
        return polls;
    }

    private void insertGroupCamera(Long pollId, List<PollCamera> list) {
        list.forEach(data -> {
            Camera camera = cameraMapper.selectById(data.getCameraId());
            if (camera == null) {
                throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
            }
            //排除默认预置位
            if (data.getCameraPresetId() != -1L) {
                CameraPreset cameraPreset = cameraPresetMapper.selectById(data.getCameraPresetId());
                if (cameraPreset == null) {
                    throw new ServiceException(ResultCode.NOT_EXIST, "摄像机预置位");
                }
            }
            data.setPollId(pollId);
        });
        pollCameraService.saveBatch(list);
    }
}
