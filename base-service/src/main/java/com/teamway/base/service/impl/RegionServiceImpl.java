package com.teamway.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.common.constant.CameraStateConstant;
import com.teamway.base.common.constant.CameraTreeIden;
import com.teamway.base.common.constant.SwitchConstant;
import com.teamway.base.dto.camera.CameraTreeDto;
import com.teamway.base.entity.Algorithm;
import com.teamway.base.entity.AlgorithmDetail;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CheckPoint;
import com.teamway.base.entity.Region;
import com.teamway.base.mapper.CameraMapper;
import com.teamway.base.mapper.RegionMapper;
import com.teamway.base.service.AlgorithmDetailService;
import com.teamway.base.service.AlgorithmService;
import com.teamway.base.service.CheckPointService;
import com.teamway.base.service.RegionService;
import com.teamway.base.vo.AlgListVo;
import com.teamway.base.vo.RegionTreeVo;
import com.teamway.common.entity.BaseEntity;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.util.SnowflakeUtils;
import com.teamway.exception.ServiceException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;


/**
 * 针对表【region(区域表)】的数据库操作Service实现
 *
 * <AUTHOR> liucheng
 * @date : 2022/4/14
 */
@Service
public class RegionServiceImpl extends ServiceImpl<RegionMapper, Region> implements RegionService {

    private static final String PATH_SEPARATOR = "/";


    @Autowired
    private CameraMapper cameraMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private CheckPointService checkPointService;
    @Autowired
    private AlgorithmService algorithmService;
    @Autowired
    private AlgorithmDetailService algorithmDetailService;

    @Override
    public List<RegionTreeVo> getRegionTree() {
        return buildTree(Region.ROOT_REGION_ID, this.list());
    }

    @Override
    public List<RegionTreeVo> findTreeByCamera(CameraTreeDto cameraTreeDto) {
        List<RegionTreeVo> regionTreeVos = buildTreeByCamera(Region.ROOT_REGION_ID, this.list(), cameraTreeDto);
        if (StringUtils.isNotBlank(cameraTreeDto.getFuzzy())) {
            filterRegionTree(regionTreeVos, cameraTreeDto.getFuzzy());
        }
        return regionTreeVos;
    }

    /**
     * 过滤出匹配的节点
     *
     * @param tree  完整数据
     * @param fuzzy 过滤的名称
     */
    private boolean filterRegionTree(List tree, String fuzzy) {
        Iterator iterator = tree.iterator();
        //列表中是否存在符合过滤条件的区域或摄像机
        boolean listMatch = false;
        while (iterator.hasNext()) {
            Object object = iterator.next();
            //该节点是否是符合过滤条件的区域或摄像机
            boolean nodeMatch = false;
            if (object instanceof RegionTreeVo region) {
                if (region.getName().contains(fuzzy)) {
                    nodeMatch = true;
                } else {
                    if (CollectionUtils.isNotEmpty(region.getChildren())) {
                        nodeMatch = filterRegionTree(region.getChildren(), fuzzy);
                    }
                }
            } else if (object instanceof Camera camera) {
                if (camera.getName().contains(fuzzy) || camera.getIp().contains(fuzzy)) {
                    nodeMatch = true;
                }
            } else {
                throw new ServiceException(ResultCode.WRONG, "系统");
            }
            if (nodeMatch) {
                //如果该节点是符合过滤条件的区域或摄像机，则表明列表中存在符合条件的区域或摄像机
                listMatch = true;
            } else {
                //否则将此不符合条件的节点移除
                iterator.remove();
            }
        }
        return listMatch;
    }

    @Override
    public boolean addRegion(Region region) {
        Long pid = region.getPid();
        if (pid < Region.ROOT_REGION_ID) {
            throw new ServiceException(ResultCode.ILLEGAL, "上级区域节点的id");
        }
        if (!Region.ROOT_REGION_ID.equals(pid)) {
            Region existsPid = this.getById(pid);
            if (existsPid == null && !Region.ROOT_REGION_ID.equals(pid)) {
                throw new ServiceException(ResultCode.NOT_EXIST, "上级区域节点");
            }
        }
        //相同节点下不能有重名的
        if (existsSameName(pid, null, region.getName())) {
            throw new ServiceException(ResultCode.EXIST, "上级区域下该区域名称");
        }
        // 设置绝对路径
        setAbsolutePathRecursively(region);
        boolean save = this.save(region);
        // 将区域节点保存早redis缓存中
        saveRegionToRedis();
        return save;
    }

    private void saveRegionToRedis() {
        List<Region> regions = this.list();
        try {
            redisTemplate.delete("region");
            redisTemplate.opsForList().rightPushAll("region", regions);
        } catch (Exception e) {
            log.error("redis缓存区域节点失败");
            e.printStackTrace();
        }
    }

    private void setAbsolutePathRecursively(Region region) {
        if (region == null) {
            return;
        }
        // 查找父级Region并递归设置其绝对路径
        Region parent = getOne(new LambdaQueryWrapper<Region>().eq(Region::getId, region.getPid()));
        if (parent != null) {
            setAbsolutePathRecursively(parent);
        }
        // 拼接父级绝对路径和当前节点名称
        StringBuilder absolutePath = new StringBuilder();
        if (parent != null) {
            absolutePath.append(parent.getAbsolutePath());
            absolutePath.append("/");
        }
        absolutePath.append(region.getName());
        // 设置当前节点的绝对路径
        region.setAbsolutePath(absolutePath.toString());
        // 无需再递归设置子节点的绝对路径，因为它们的绝对路径已经通过父级间接设置好了
    }

    /**
     * 删除区域
     * 以下情况删除不能
     * 1 不存在该区域
     * 2 该区域下存在子区域
     * 3 根区域不能删除
     * 4 区域下存在设备不能删除
     * 5 区域下存在摄像机不能删除
     *
     * @param ids
     * @return : boolean
     * @date : 2022/4/14
     * <AUTHOR> liucheng
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delBatchRegion(List<Long> ids) {
        List<Region> regions = this.listByIds(ids);
        //不存在该区域
        if (CollUtil.isEmpty(regions) || CollUtil.size(regions) != CollUtil.size(ids)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "区域");
        }
        for (Long id : ids) {
            // 该区域下存在子区域
            if (existsChildRegion(id)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "当前区域有子区域，不能删除");
            }
            //区域下存在摄像机不能删除
            if (existsBindCamera(id)) {
                throw new ServiceException(ResultCode.NOT_DELETE, "当前区域下存在摄像机");
            }
        }
        boolean b = this.removeByIds(ids);
        saveRegionToRedis();
        return b;
    }


    @Override
    public boolean updateRegion(Region region) {
        Long id = region.getId();
        //校验上级区域为自己
        if (id.equals(region.getPid())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "当前区域的上级区域不能是自己");
        }
        //校验上级区域为自己的子级
        Map<Long, Region> childrenMap = new HashMap<>();
        this.buildMap(id, this.list(), childrenMap);
        if (childrenMap.containsKey(region.getPid())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "当前区域的上级区域不能是自己的子级");
        }
        Region existsRegion = this.getById(id);
        if (existsRegion == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "区域");
        }
        //同级节点不能有相同的区域名称
        if (existsSameName(region.getPid(), id, region.getName())) {
            throw new ServiceException(ResultCode.EXIST, "同级节点下该区域名称");
        }
        if (!Region.ROOT_REGION_ID.equals(region.getPid())) {
            //判断上级节点是否存在
            Region existsPid = this.getById(region.getPid());
            if (null == existsPid) {
                throw new ServiceException(ResultCode.NOT_EXIST, "父节点");
            }
        }
        region.setUpdateTime(LocalDateTime.now());
        //当修改节点名称时 该节点的子节点绝对路径也要变
        // 更新节点的绝对路径
        String parentPathFromRoot = getParentPathFromRoot(region);
        if (StringUtils.isNotBlank(parentPathFromRoot)) {
            int last = parentPathFromRoot.lastIndexOf('/');
            if (last != -1) {
                parentPathFromRoot = parentPathFromRoot.substring(0, last);
            }
        }
        updateAbsolutePathRecursively(region, parentPathFromRoot);
        boolean b = this.updateById(region);
        saveRegionToRedis();
        return b;
    }

    public String getParentPathFromRoot(Region region) {
        if (region == null) {
            // 如果节点为空，返回空路径
            return "";
        }

        // 假设有一个方法可以根据父节点ID获取父节点对象
        Region parent = getById(region.getPid());
        if (parent == null) {
            // 如果没有父节点，说明当前节点是根节点
            return "/" + region.getName();
        }

        // 递归调用，获取父节点的父路径，并在前面加上当前节点的名称
        String parentPath = getParentPathFromRoot(parent);
        return parentPath + "/" + region.getName();
    }

    private void updateAbsolutePathRecursively(Region region, String parentPath) {
        if (region == null) {
            return; // 如果region为空，直接返回
        }

        // 构建当前节点的绝对路径
        StringBuilder absolutePath = new StringBuilder();
        if (parentPath != null && !parentPath.isEmpty()) {
            absolutePath.append(parentPath);
            // 确保父路径以'/'结尾
            if (!parentPath.endsWith(PATH_SEPARATOR)) {
                absolutePath.append(PATH_SEPARATOR);
            }
        }
        absolutePath.append(region.getName());

        // 设置当前节点的绝对路径
        region.setAbsolutePath(absolutePath.toString());

        // 更新当前Region到数据库
        this.updateById(region);

        // 递归更新子节点的绝对路径
        List<Region> children = getChildren(region.getId());
        for (Region child : children) {
            // 在父节点的绝对路径后添加一个'/'，然后传递给子节点
            updateAbsolutePathRecursively(child, absolutePath.toString());
        }
    }

    private List<Region> getChildren(Long parentId) {
        return this.list(new LambdaQueryWrapper<Region>().eq(Region::getPid, parentId));
    }

    /**
     * 构建区域ID树
     *
     * @return
     */
    @Override
    public List<RegionTreeVo> getRegionIdTree(String regionId) {
        if (StrUtil.isEmpty(regionId)) {
            return this.getRegionTree();
        } else {
            return this.buildTree(Long.valueOf(regionId), this.list());
        }
    }

    @Override
    public void getRegionIdInOneTree(RegionTreeVo regionIdTreeDto, List<Long> regionIdList) {
        List<RegionTreeVo> children = regionIdTreeDto.getChildren();
        regionIdList.add(regionIdTreeDto.getId());
        if (CollUtil.isNotEmpty(children)) {
            for (RegionTreeVo child : children) {
                getRegionIdInOneTree(child, regionIdList);
            }
        }
    }

    @Override
    public List<Region> getParentTree() {
        return this.list(new LambdaQueryWrapper<Region>().eq(Region::getPid, "0").orderByAsc(Region::getSort));
    }

    @Override
    public List<Long> getChildId(Long id) {
        List<Long> result = new ArrayList();
        buildIdList(id, result);
        result.add(id);
        return result;

    }

    private void buildIdList(Long id, List<Long> list) {
        List<Region> regions = lambdaQuery().eq(Region::getPid, id).list();
        for (Region region : regions) {
            buildIdList(region.getId(), list);
            list.add(region.getId());
        }
    }

    /**
     * 查询父区域下子区域
     *
     * @param pId
     * @param list
     * @return :
     * @date : 2022/8/5
     * <AUTHOR> zhangsai
     */
    private Map<Long, Region> buildMap(Long pId, List<Region> list, Map<Long, Region> childrenMap) {
        for (Region region : list) {
            if (region.getPid().equals(pId)) {
                childrenMap.put(region.getId(), region);
                //递归
                buildMap(region.getId(), list, childrenMap);
            }
        }
        return childrenMap;
    }

    /**
     * 判断是否有摄像机被指定区域绑定
     *
     * @param regionId
     * @return : boolean
     * @date : 2022/4/15
     * <AUTHOR> liucheng
     */
    private boolean existsBindCamera(Long regionId) {
        QueryWrapper<Camera> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Camera::getRegionId, regionId);
        List<Camera> collection = cameraMapper.selectList(queryWrapper);
        return CollUtil.isNotEmpty(collection);
    }

    /**
     * 指定区域id下是否有子区域
     *
     * @param id
     * @return : boolean
     * @date : 2022/4/14
     * <AUTHOR> liucheng
     */
    private boolean existsChildRegion(Long id) {
        QueryWrapper<Region> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Region::getPid, id);
        return CollUtil.isNotEmpty(this.list(queryWrapper));
    }

    /**
     * 在指定pid节点下是否已经有该名称的区域
     *
     * @param pid
     * @param regionName
     * @return : boolean
     * @date : 2022/4/14
     * <AUTHOR> liucheng
     */
    private boolean existsSameName(Long pid, Long id, String regionName) {
        QueryWrapper<Region> sameQuery = new QueryWrapper<>();
        //pid相同 名字相同 表示已经存在
        sameQuery.lambda().eq(Region::getPid, pid)
                .ne(id != null, Region::getId, id)
                .eq(Region::getName, regionName);
        return CollUtil.isNotEmpty(this.list(sameQuery));
    }

    /**
     * 构建区域树
     *
     * @param pId
     * @param list
     * @return : java.util.List<com.teamway.base.vo.RegionTreeVo>
     * @date : 2022/4/15
     * <AUTHOR> liucheng
     */
    private List<RegionTreeVo> buildTree(Long pId, List<Region> list) {
        List<RegionTreeVo> regionTreeVoList = new LinkedList<>();
        if (CollUtil.isNotEmpty(list)) {
            for (Region region : list) {
                if (region.getPid().equals(pId)) {
                    RegionTreeVo regionTreeVo = RegionTreeVo.convertRegion(region);
                    //递归
                    List<RegionTreeVo> childRegionList = buildTree(region.getId(), list);
                    regionTreeVo.setChildren(childRegionList);
                    regionTreeVoList.add(regionTreeVo);
                }
            }
        }
        if (CollUtil.isNotEmpty(list)) {
            //按序号排序
            regionTreeVoList.sort(Comparator.comparing(RegionTreeVo::getSort));
        }
        return regionTreeVoList;
    }

    /**
     * 构建区域树下摄像机
     *
     * @param pId
     * @param list
     * @return : java.util.List<com.teamway.base.vo.RegionTreeVo>
     * @date : 2022/7/17
     * <AUTHOR> liucheng
     */
    private List<RegionTreeVo> buildTreeByCamera(Long pId, List<Region> list, CameraTreeDto cameraTreeDto) {
        //当前面条件筛选的条件为空时，重新查询区域列表
        if (CollectionUtils.isEmpty(list)) {
            list = this.list();
        }
        List<RegionTreeVo> regionTreeVoList = new LinkedList<>();
        if (CollUtil.isNotEmpty(list)) {
            for (Region region : list) {
                if (region.getPid().equals(pId)) {
                    QueryWrapper<Camera> queryWrapper = new QueryWrapper<>();
                    queryWrapper.in(ObjectUtil.isNotEmpty(cameraTreeDto.getFactory()), "bc.factory", cameraTreeDto.getFactory());
                    queryWrapper.in(ObjectUtil.isNotEmpty(cameraTreeDto.getType()), "bc.type", cameraTreeDto.getType());
                    queryWrapper.in(ObjectUtil.isNotEmpty(cameraTreeDto.getFeature()), "bc.feature", cameraTreeDto.getFeature());
                    //queryWrapper.and(StringUtils.isNotEmpty(cameraTreeDto.getFuzzy()),i -> i.like("bc.name", cameraTreeDto.getFuzzy()).or().like("bc.ip", cameraTreeDto.getFuzzy()));
                    queryWrapper.orderByDesc("bc.create_time");
                    queryWrapper.eq("bc.region_id", region.getId());
                    queryWrapper.eq(cameraTreeDto.getState() != null, "bc.state", cameraTreeDto.getState());
                    List<Camera> cameraList = cameraMapper.selectListAndRegionPath(queryWrapper);
                    RegionTreeVo regionTreeVo = RegionTreeVo.convertRegion(region);
                    cameraList.forEach(camera -> {
                        //增加摄像机标识
                        camera.setIden(CameraTreeIden.CAMERA);
                        camera.setRegionName(region.getName());
                        //查询摄像机下 绑定算法
                        List<AlgListVo> algListVo = new ArrayList<>();
                        if (ObjectUtil.equals(cameraTreeDto.getQueryAlg(), SwitchConstant.OPEN)) {
                            List<CheckPoint> pointList = checkPointService.list(new LambdaQueryWrapper<CheckPoint>().eq(CheckPoint::getCameraId, camera.getId()));
                            if (CollectionUtils.isNotEmpty(pointList)) {
                                pointList.forEach(cameraPoint -> {
                                    //id为随机生成id algid为算法id 为了适配前端区域树
                                    AlgListVo idenAlg = new AlgListVo().setId(SnowflakeUtils.getSnowflakeId()).setAlgId(cameraPoint.getAlgId()).setName(cameraPoint.getNote()).setIden(CameraTreeIden.ALG);
                                    //查询算法识别类型
                                    Algorithm algorithm = algorithmService.getOne(new LambdaQueryWrapper<Algorithm>().eq(BaseEntity::getId, cameraPoint.getAlgId()));
                                    if (ObjectUtil.isNotEmpty(algorithm)) {
                                        //查询算法子类别
                                        List<AlgorithmDetail> detailList = algorithmDetailService.list(new LambdaQueryWrapper<AlgorithmDetail>().eq(AlgorithmDetail::getAlgorithmId, algorithm.getId()));
                                        if (CollectionUtils.isNotEmpty(detailList)) {
                                            idenAlg.setRecognitionType(detailList.stream().map(AlgorithmDetail::getTypeName).toList());
                                        }
                                    }
                                    algListVo.add(idenAlg);
                                });
                            }
                        }
                        camera.setChildren(algListVo);
                    });
                    regionTreeVo.setChildren(cameraList);
                    // 计算当前区域的在线数量和总数量
                    int onlineCount = 0;
                    int totalCount = 0;
                    if (CollectionUtils.isNotEmpty(cameraList)) {
                        onlineCount = (int) cameraList.stream().filter(i -> ObjectUtil.equals(i.getState(), CameraStateConstant.ON_LINE)).count();
                        totalCount = cameraList.size();
                    }
                    // 递归处理子区域
                    List<RegionTreeVo> childRegionList = buildTreeByCamera(region.getId(), list, cameraTreeDto);
                    regionTreeVo.getChildren().addAll(childRegionList);
                    // 累加子区域的在线数量和总数量
                    for (RegionTreeVo childRegion : childRegionList) {
                        onlineCount += childRegion.getOnline();
                        totalCount += childRegion.getTotalNum();
                    }
                    // 设置父区域的在线数量和总数量
                    regionTreeVo.setOnline(onlineCount);
                    regionTreeVo.setTotalNum(totalCount);
                    //增加区域标识
                    regionTreeVo.setIden(CameraTreeIden.REGION);
                    regionTreeVoList.add(regionTreeVo);
                }
            }
        }
        if (CollUtil.isNotEmpty(list)) {
            // 按序号排序
            regionTreeVoList.sort(Comparator.comparing(RegionTreeVo::getSort));
        }
        return regionTreeVoList;
    }

    @Override
    public Map<Long, Region> getRegionMap(List<Long> ids) {
        List<Region> regions = this.baseMapper.selectBatchIds(ids);
        Map<Long, Region> regionMap = new HashMap<>();
        for (Region region : regions) {
            regionMap.put(region.getId(), region);
        }
        return regionMap;
    }

}




