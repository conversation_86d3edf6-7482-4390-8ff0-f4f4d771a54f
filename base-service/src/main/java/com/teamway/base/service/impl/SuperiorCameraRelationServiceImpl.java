package com.teamway.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.dto.superior.CascadeEquipmentDto;
import com.teamway.base.dto.superior.QueryCameraRelationDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.SuperiorCameraRelation;
import com.teamway.base.mapper.SuperiorCameraRelationMapper;
import com.teamway.base.service.CameraService;
import com.teamway.base.service.SuperiorCameraRelationService;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 摄像机国标平台关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Slf4j
@Service
public class SuperiorCameraRelationServiceImpl extends ServiceImpl<SuperiorCameraRelationMapper, SuperiorCameraRelation> implements SuperiorCameraRelationService {

    @Value("${streamUrl}")
    private String streamUrl;
    @Autowired
    private CameraService cameraService;

    /**
     * 批量修改摄像机关联国标平台
     *
     * @param relationList
     * @return
     */
    @Override
    public Boolean updateBatch(List<SuperiorCameraRelation> relationList) {
        // 获取所有需要校验的videoCode，并检查是否存在重复
        Set<String> videoCodesToCheck = new HashSet<>();
        for (SuperiorCameraRelation relation : relationList) {
            if (StrUtil.isNotBlank(relation.getVideoCode()) && !videoCodesToCheck.add(relation.getVideoCode())) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "视频通道编码ID存在重复");
            }
        }

        List<SuperiorCameraRelation> existingRelations;
        // 批量查询所有相关的SuperiorCameraRelation
        if (CollUtil.isNotEmpty(videoCodesToCheck)) {
            existingRelations = this.baseMapper.selectList(
                    new LambdaQueryWrapper<SuperiorCameraRelation>()
                            .in(SuperiorCameraRelation::getVideoCode, videoCodesToCheck)
            );
        } else {
            existingRelations = Collections.emptyList();
        }

        // 校验
        relationList.forEach(relation -> {
            if (StrUtil.isNotBlank(relation.getVideoCode())) {
                //当前配置跟数据库的数据判断是否存在
                boolean exists = existingRelations.stream().anyMatch(existingRelation ->
                        existingRelation.getVideoCode().equals(relation.getVideoCode()) &&
                                !existingRelation.getId().equals(relation.getId()) &&
                                existingRelation.getSuperiorPlatformId().equals(relation.getSuperiorPlatformId())
                );
                if (exists) {
                    throw new ServiceException(ResultCode.EXIST, "视频通道编码ID: " + relation.getVideoCode());
                }
                sendUpdate(streamUrl, relation);
            } else {
                sendDel(streamUrl, relation);
            }
        });
        return this.updateBatchById(relationList);
    }

    /**
     * 获取摄像机关联国标平台
     *
     * @return
     */
    @Override
    public List<CascadeEquipmentDto> getCascadeInfo() {
        List<SuperiorCameraRelation> nationalRelationList = this.baseMapper.selectAll(null);
        List<CascadeEquipmentDto> cascadeEquipmentDtoList = new ArrayList<>();
        nationalRelationList.forEach(relation -> {
            CascadeEquipmentDto cascadeEquipmentDto = new CascadeEquipmentDto();
            cascadeEquipmentDto.setMainId(relation.getMainId());
            cascadeEquipmentDto.setSubId(relation.getSubId());
            cascadeEquipmentDto.setPlatformId(relation.getSuperiorPlatformId().intValue());
            cascadeEquipmentDto.setCascadeId(relation.getVideoCode());
            cascadeEquipmentDto.setId(relation.getId());
            cascadeEquipmentDtoList.add(cascadeEquipmentDto);
        });
        return cascadeEquipmentDtoList;
    }

    @Override
    public Page<SuperiorCameraRelation> selectPageList(QueryCameraRelationDto queryCameraRelationDto) {
        Page<SuperiorCameraRelation> page = new Page<>(queryCameraRelationDto.getPageIndex(), queryCameraRelationDto.getPageSize());
        QueryWrapper<SuperiorCameraRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SuperiorCameraRelation::getSuperiorPlatformId, queryCameraRelationDto.getSuperiorPlatformId());
        queryWrapper.lambda().eq(StrUtil.isNotBlank(queryCameraRelationDto.getVideoCode()),SuperiorCameraRelation::getVideoCode, queryCameraRelationDto.getVideoCode());
        queryWrapper.likeRight(StrUtil.isNotBlank(queryCameraRelationDto.getIp()), "bc.ip", queryCameraRelationDto.getIp());
        queryWrapper.likeRight(StrUtil.isNotBlank(queryCameraRelationDto.getName()), "bc.name", queryCameraRelationDto.getName());
        //默认排序
        queryWrapper.lambda().orderByAsc(StrUtil.isBlank(queryCameraRelationDto.getSortBy()), SuperiorCameraRelation::getId);
        //指定排序
        queryWrapper.orderBy(StrUtil.isNotBlank(queryCameraRelationDto.getSortBy()), queryCameraRelationDto.getIsAsc(), queryCameraRelationDto.getSortBy());
        this.baseMapper.selectPageList(page, queryWrapper);
        return page;
    }

    /**
     * 修改平台摄像机关联通知流媒体
     *
     * @param streamUrl
     * @param superiorCameraRelation
     */
    private void sendUpdate(String streamUrl, SuperiorCameraRelation superiorCameraRelation) {
        Camera camera = cameraService.getById(superiorCameraRelation.getCameraId());
        Map<String, Object> requestMap = new HashMap(4);
        requestMap.put("platformId", superiorCameraRelation.getSuperiorPlatformId());
        requestMap.put("mainId", camera.getMainId());
        requestMap.put("subId", camera.getSubId());
        requestMap.put("cascadeId", superiorCameraRelation.getVideoCode());
        try {
            String jsonResult = HttpUtil.get("http://" + streamUrl + "/cascade/setCascadeInfo", requestMap, 6000);
            log.info("修改平台摄像机关联通知流媒体：{}", jsonResult);
        } catch (Exception e) {
            log.error("修改平台摄像机关联通知流媒体失败:", e);
            throw new ServiceException(ResultCode.OPERATION_FAILURE, e.getMessage());
        }
    }

    /**
     * 删除平台关联摄像机通知流媒体
     *
     * @param streamUrl
     * @param superiorCameraRelation
     */
    private void sendDel(String streamUrl, SuperiorCameraRelation superiorCameraRelation) {
        SuperiorCameraRelation oldNationalRelation = this.baseMapper.selectById(superiorCameraRelation.getId());
        if (oldNationalRelation == null) {
            log.info("未找到平台关联摄像机的配置");
            return;
        }
        Map<String, Object> requestMap = new HashMap<>(2);
        requestMap.put("platformId", oldNationalRelation.getSuperiorPlatformId());
        requestMap.put("cascadeId", oldNationalRelation.getVideoCode());
        try {
            String jsonResult = HttpUtil.get("http://" + streamUrl + "/cascade/delCascadeInfo", requestMap, 6000);
            log.info("删除平台关联摄像机通知流媒体：{}", jsonResult);
        } catch (Exception e) {
            log.error("删除平台关联摄像机通知流媒体失败:", e);
            throw new ServiceException(ResultCode.OPERATION_FAILURE, e.getMessage());
        }
    }

}
