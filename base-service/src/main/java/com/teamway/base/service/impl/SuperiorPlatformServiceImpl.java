package com.teamway.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.dto.stream.StreamResultDto;
import com.teamway.base.dto.superior.SuperiorPlatformDto;
import com.teamway.base.dto.superior.SuperiorStatusDto;
import com.teamway.base.entity.SuperiorPlatform;
import com.teamway.base.mapper.SuperiorPlatformMapper;
import com.teamway.base.service.SuperiorPlatformService;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 上级平台配置
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@Slf4j
@Service
public class SuperiorPlatformServiceImpl extends ServiceImpl<SuperiorPlatformMapper, SuperiorPlatform> implements SuperiorPlatformService {

    @Value("${streamUrl}")
    private String streamUrl;

    /**
     * 修改
     *
     * @param standard
     * @return
     */
    @Override
    public Boolean update(SuperiorPlatform standard) {
        //校验接入配置端口是否冲突
        LambdaQueryWrapper<SuperiorPlatform> queryWrapper = new LambdaQueryWrapper<SuperiorPlatform>()
                .eq(SuperiorPlatform::getLocalSipPort, standard.getLocalSipPort())
                .ne(SuperiorPlatform::getId, standard.getId());
        List<SuperiorPlatform> superiorPlatformList = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(superiorPlatformList)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "本地SIP服务端口与其他平台冲突");
        }
        boolean update = this.baseMapper.updateById(standard) > 0;
        //异步调用
        ThreadUtil.execAsync(() -> send(streamUrl, standard));
        return update;
    }

    /**
     * 获取国标平台配置
     *
     * @return
     */
    @Override
    public List<SuperiorPlatformDto> getNationalStandardList() {
        List<SuperiorPlatformDto> platformList = new ArrayList<>();
        List<SuperiorPlatform> standardList = this.baseMapper.selectList(null);
        standardList.forEach(standard -> {
            SuperiorPlatformDto superiorPlatform = new SuperiorPlatformDto();
            superiorPlatform.setSipServerId(standard.getSipServerId());
            superiorPlatform.setSipServerRegionId(standard.getSipServerDomain());
            superiorPlatform.setSipServerIp(standard.getSipServerIp());
            superiorPlatform.setSipServerPort(Integer.valueOf(standard.getSipServerPort()));
            superiorPlatform.setPassword(standard.getPassword());
            superiorPlatform.setSipUserId(standard.getSipUserId());
            superiorPlatform.setSipLocalPort(Integer.valueOf(standard.getLocalSipPort()));
            superiorPlatform.setTransProto("TCP".equals(standard.getProtocol()) ? 1 : 0);
            superiorPlatform.setRegisterRange(Integer.valueOf(standard.getValidityDate()));
            superiorPlatform.setHeartbeatRange(Integer.valueOf(standard.getHeartbeat()));
            superiorPlatform.setHeartbeatNumber(Integer.valueOf(standard.getHeartbeatOvertime()));
            superiorPlatform.setRegister(Integer.valueOf(standard.getUseStart()));
            superiorPlatform.setPlatformId(standard.getId().intValue());
            superiorPlatform.setId(standard.getId());
            platformList.add(superiorPlatform);
        });
        return platformList;
    }

    @Override
    public SuperiorStatusDto getStatus(Long id) {
        Map<String, Object> requestMap = new HashMap<>(1);
        requestMap.put("platformId", id);
        try {
            String jsonResult = HttpUtil.get("http://" + streamUrl + "/cascade/getStatus", requestMap, 6000);
            log.info("获取上级平台状态：{}", jsonResult);
            StreamResultDto streamResultDto = JSONObject.parseObject(jsonResult, StreamResultDto.class);
            if (streamResultDto.getCode() != ResultCode.SUCCESS.getCode()) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "调用流媒体获取上级状态失败");
            }
            return JSON.toJavaObject(streamResultDto.getData(), SuperiorStatusDto.class);
        } catch (Exception e) {
            log.error("调用流媒体获取上级状态失败", e);
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "调用流媒体获取上级状态失败");
        }
    }

    @Override
    public Boolean sendDeviceInfo(Long id) {
        Map<String, Object> requestMap = new HashMap<>(1);
        requestMap.put("platformId", id);
        try {
            String jsonResult = HttpUtil.get("http://" + streamUrl + "/cascade/sendDeviceInfo", requestMap, 6000);
            log.info("向上级平台推送设备信息：{}", jsonResult);
            StreamResultDto streamResultDto = JSONObject.parseObject(jsonResult, StreamResultDto.class);
            return streamResultDto.getCode() == ResultCode.SUCCESS.getCode();
        } catch (Exception e) {
            log.error("向上级平台推送设备信息失败", e);
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "向上级平台推送设备信息失败");
        }
    }

    /**
     * 通知流媒体
     *
     * @param streamUrl
     * @param standard
     */
    public void send(String streamUrl, SuperiorPlatform standard) {
        Map<String, Object> requestMap = new HashMap<>(13);
        requestMap.put("sipServerId", standard.getSipServerId());
        requestMap.put("sipServerRegionId", standard.getSipServerDomain());
        requestMap.put("sipServerIp", standard.getSipServerIp());
        requestMap.put("sipServerPort", standard.getSipServerPort());
        requestMap.put("passwd", standard.getPassword());
        requestMap.put("sipUserId", standard.getSipUserId());
        requestMap.put("localPort", standard.getLocalSipPort());
        requestMap.put("transProto", "TCP".equals(standard.getProtocol()) ? "1" : "0");
        requestMap.put("registerRange", standard.getValidityDate());
        requestMap.put("heartbeatRange", standard.getHeartbeat());
        requestMap.put("heartbeatNumber", standard.getHeartbeatOvertime());
        requestMap.put("register", standard.getUseStart());
        requestMap.put("platformId", standard.getId());
        try {
            String jsonResult = HttpUtil.get("http://" + streamUrl + "/cascade/setUpPlatformInfo", requestMap, 6000);
            log.info("通知流媒体：{}", jsonResult);
        } catch (Exception e) {
            log.error("通知流媒体失败", e);
        }
    }
}
