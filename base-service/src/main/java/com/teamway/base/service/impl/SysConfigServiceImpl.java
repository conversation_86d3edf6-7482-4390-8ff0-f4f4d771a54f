package com.teamway.base.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.entity.SysConfig;
import com.teamway.base.mapper.SysConfigMapper;
import com.teamway.base.service.SysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022-10-21
 * @Description: 系统配置
 */
@Service
@Slf4j
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements SysConfigService {


    @Override
    public void saveSysConfig(SysConfig sysConfig) {
        baseMapper.updateById(sysConfig);
    }

    @Override
    public SysConfig selectSysConfig() {
        return baseMapper.selectOne(null);
    }

}
