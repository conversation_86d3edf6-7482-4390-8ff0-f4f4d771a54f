package com.teamway.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.common.constant.CameraFactoryConstant;
import com.teamway.base.common.constant.CameraFeatureConstant;
import com.teamway.base.dto.temperature.Coordinate;
import com.teamway.base.dto.temperature.CoordinateDto;
import com.teamway.base.dto.temperature.GetByCameraIdDto;
import com.teamway.base.dto.temperature.GetIrRuleVo;
import com.teamway.base.dto.temperature.GetTemperatureDrawLineForInsDto;
import com.teamway.base.dto.temperature.PointDto;
import com.teamway.base.dto.temperature.RuleDto;
import com.teamway.base.dto.temperature.SetIrRuleDto;
import com.teamway.base.dto.temperature.TemperatureDrawLineUpdateDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CameraPreset;
import com.teamway.base.entity.TemperatureDrawLine;
import com.teamway.base.mapper.TemperatureDrawLineMapper;
import com.teamway.base.service.CameraPresetService;
import com.teamway.base.service.CameraService;
import com.teamway.base.service.TemperatureDrawLineService;
import com.teamway.base.util.InfraredUtils;
import com.teamway.base.vo.DataListVo;
import com.teamway.base.vo.GetTemperatureDrawLineForInsVo;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.util.SnowflakeUtils;
import com.teamway.exception.ServiceException;
import com.teamway.exception.VerifyException;
import com.teamway.thermal.common.enums.CameraManufacturer;
import com.teamway.thermal.common.enums.CameraType;
import com.teamway.thermal.common.enums.MeterType;
import com.teamway.thermal.entity.TemperatureRule;
import com.teamway.thermal.entity.dahua.TemperatureCoordinate;
import com.teamway.thermal.exception.RuleConfigException;
import com.teamway.thermal.request.LoginRequest;
import com.teamway.thermal.service.TemperatureRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.rmi.ServerException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 针对表【temperature_draw_line(红外热成像摄像机测温框)】的数据库操作Service实现
 *
 * <AUTHOR> liucheng
 * @date : 2022/6/30
 */

@Service
@Slf4j
@SuppressWarnings("all")
public class TemperatureDrawLineServiceImpl extends ServiceImpl<TemperatureDrawLineMapper, TemperatureDrawLine> implements TemperatureDrawLineService {
    @Autowired
    private CameraService cameraService;
    @Autowired(required = false)
    private TemperatureRuleService temperatureRuleService;
    @Autowired
    private CameraPresetService cameraPresetService;

    private static final int FEATURE = 2;

    private static final float PG = 250;

    private static final float POSITION = 8191;

    private static final float GAODE_HALF_X_POSITION = 512;

    private static final float GAODE_HALF_Y_POSITION = 384;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public boolean addBatchOrUpdate(TemperatureDrawLineUpdateDto drawLineUpdateDto) {
        validBuildMeterType(drawLineUpdateDto);
        int xMaxPosition = Integer.valueOf(drawLineUpdateDto.getXxMaxPosition());
        int yMaxPosition = Integer.valueOf(drawLineUpdateDto.getYyMaxPosition());
        String cameraId = drawLineUpdateDto.getCameraId();
        //判断摄像机是否存在
        Camera infraredCamera = getInfraredCamera(cameraId);
        if (infraredCamera == null) {
            throw new VerifyException(ResultCode.NOT_EXIST, "摄像机");
        }
        CameraManufacturer cameraFactory = InfraredUtils.getCameraFactory(infraredCamera.getFactory(), infraredCamera.getType());
        CameraType cameraType = InfraredUtils.getCameraType(infraredCamera.getFactory(), infraredCamera.getType());
        List<CoordinateDto> coordinateDtoList = drawLineUpdateDto.getCoordinateDtoList();
        LoginRequest loginRequest = buildLoginRequest(infraredCamera);
        String presetNo = drawLineUpdateDto.getPresetNo();
        if (presetNo == null) {
            presetNo = "0";
        }
        List<TemperatureDrawLine> existsPlanList = listByCameraIds(Collections.singletonList(cameraId), presetNo);
        if (CollUtil.size(coordinateDtoList) == 0) {
            //传入的坐标集合为空 直接删除该摄像机下所有的测温项
            for (TemperatureDrawLine temperatureDrawLine : existsPlanList) {
                //先删除数据库的数据，就算后面删除测温项sdk抛出异常还能回滚数据
                //如果是先删除测温项配置，再插入到数据库，当数据库数据保存失败时，测温项已经删除了，会导致数据不一致的问题
                this.removeById(temperatureDrawLine.getId());
                try {
                    temperatureRuleService.deleteByPresetIdAndRuleId(loginRequest, String.valueOf(temperatureDrawLine.getPresetId()), String.valueOf(temperatureDrawLine.getRuleId()));
                } catch (Exception e) {
                    log.info("删除测温项1：" + temperatureDrawLine.getRuleName() + "失败");
                }

            }
            return true;
        }
        List<TemperatureDrawLine> updateList = new ArrayList<>();
        List<TemperatureDrawLine> insertList = new ArrayList<>();
        List<Long> allUpdateIdsTemp = new ArrayList<>();
        if (CollUtil.isNotEmpty(coordinateDtoList)) {
            Map<String, Long> sameRuleNameCountMap = coordinateDtoList.stream().collect(Collectors.groupingBy(CoordinateDto::getRuleName, Collectors.counting()));
            for (Map.Entry<String, Long> entry : sameRuleNameCountMap.entrySet()) {
                if (entry.getValue() > 1) {
                    throw new ServiceException(ResultCode.EXIST, "测温项规则" + entry.getKey());
                }
            }
            LocalDateTime now = LocalDateTime.now();
            String imageUrl = drawLineUpdateDto.getImageUrl();
            for (CoordinateDto coordinateDto : coordinateDtoList) {
                TemperatureDrawLine temperatureDrawLine = new TemperatureDrawLine();
                temperatureDrawLine.setPresetId(Integer.valueOf(presetNo));
                temperatureDrawLine.setImageUrl(imageUrl);
                temperatureDrawLine.setCameraId(cameraId);
                temperatureDrawLine.setType(coordinateDto.getType());
                temperatureDrawLine.setPurpose(coordinateDto.getPurpose());
                temperatureDrawLine.setRuleName(coordinateDto.getRuleName());
                temperatureDrawLine.setThresholdValue(coordinateDto.getThresholdValue());
                //转换坐标为json数组字符串[{x:1,y:2}]
                List<Coordinate> coordinate = coordinateDto.getCoordinate();
                String coordinateStr = JSONUtil.toJsonStr(coordinate);
                temperatureDrawLine.setCoordinate(coordinateStr);
                temperatureDrawLine.setWarnReportEnable(coordinateDto.getWarnReportEnable());
                Optional<TemperatureDrawLine> first = existsPlanList.stream().filter(l -> l.getId().equals(coordinateDto.getId())).findFirst();
                if (coordinateDto.getId() != null && first.isPresent()) {
                    //传入的数据id不为空 且在数据库中已经存在 则开始判断值是否相同
                    TemperatureDrawLine exists = first.get();
                    allUpdateIdsTemp.add(exists.getId());
                    //判断坐标相同
                    boolean sameCoordinate = Objects.equals(exists.getCoordinate(), coordinateStr);
                    //判断名称相同
                    boolean sameRuleName = Objects.equals(exists.getRuleName(), coordinateDto.getRuleName());
                    //告警开关相同
                    boolean sameWarnEnable = Objects.equals(exists.getWarnReportEnable(), coordinateDto.getWarnReportEnable());
                    //阈值相同
                    boolean sameThresholdValue = Objects.equals(exists.getThresholdValue(), coordinateDto.getThresholdValue());
                    if (sameCoordinate && sameRuleName && sameWarnEnable & sameThresholdValue) {
                        continue;
                    }
                    temperatureDrawLine.setId(coordinateDto.getId());
                    temperatureDrawLine.setRuleId(exists.getRuleId());
                    temperatureDrawLine.setPresetId(exists.getPresetId());
                    updateList.add(temperatureDrawLine);
                } else {
                    //加入新增集合
                    temperatureDrawLine.setCameraId(cameraId);
                    temperatureDrawLine.setCreateTime(now);
                    temperatureDrawLine.setId(SnowflakeUtils.getSnowflakeId());
                    insertList.add(temperatureDrawLine);
                }
            }
        }
        //计算集合的单差集，即只返回【集合1】中有，但是【集合2】中没有的元素，例如：
        //subtractToList([1,2,3,4],[2,3,4,5]) -》 [1]
        //数据库有，但传入的数据没有，则表示删除
        List<Long> deleteIds = CollUtil.subtractToList(existsPlanList.stream().map(TemperatureDrawLine::getId).collect(Collectors.toList()), allUpdateIdsTemp);
        //若请求的数据中在数据库中，但不在更新集合和新增集合中就代表删除
        if ((CollUtil.size(existsPlanList) + CollUtil.size(insertList) - CollUtil.size(deleteIds)) > 12) {
            //摄像机多支持12个,但是要考虑后面算法上报还要绘制,以及测温项过多会并无意义和识别结果不准确的问题
            throw new VerifyException(ResultCode.OPERATION_FAILURE, "每台热成像摄像机预置位最多添加12个自定义测温项");
        }
        if (CollUtil.isNotEmpty(deleteIds)) {
            //删除测温项 值删除指定id的数据
            for (TemperatureDrawLine temperatureDrawLine : existsPlanList.stream().filter(l -> deleteIds.contains(l.getId())).collect(Collectors.toList())) {
                this.removeById(temperatureDrawLine.getId());
                try {
                    log.info("删除测温项参数为：PresetId=" + temperatureDrawLine.getPresetId() + "，RuleId=" + temperatureDrawLine.getRuleId());
                    temperatureRuleService.deleteByPresetIdAndRuleId(loginRequest, String.valueOf(temperatureDrawLine.getPresetId()), String.valueOf(temperatureDrawLine.getRuleId()));
                } catch (RuntimeException runtimeException) {
                    log.info("删除测温项2：" + temperatureDrawLine.getRuleName() + "失败");
                    //throw new ServiceException("删除测温项：" + temperatureDrawLine.getRuleName() + "失败");
                }
            }
        }
        if (CollUtil.isNotEmpty(insertList)) {
            for (TemperatureDrawLine temperatureDrawLine : insertList) {
                //先新增数据到数据库，就算后面测温项添加失败，这条新增的数据还能回滚
                //如果是先新增测温项配置，再插入到数据库，当数据库数据保存失败时，测温项已经添加了，会导致数据不一致的问题
                this.save(temperatureDrawLine);
                String coordinateStr = temperatureDrawLine.getCoordinate();
                List<Coordinate> coordinateList = JSONUtil.toList(coordinateStr, Coordinate.class);
                //新增测温项
                try {
                    String ruleId = "";
                    TemperatureRule ruleAddInfo = new TemperatureRule();
                    ruleAddInfo.setAreaType(validBuildMeterType(coordinateList, temperatureDrawLine.getType()));
                    ruleAddInfo.setPresetId(String.valueOf(temperatureDrawLine.getPresetId()));
                    ruleAddInfo.setName(temperatureDrawLine.getRuleName());
                    ruleAddInfo.setCoordinates(parseCoordinate(coordinateList, drawLineUpdateDto.getYyMaxPosition(), infraredCamera.getFactory()));
                    setTemperatureRulePositionDeviationProportion(ruleAddInfo, cameraFactory, cameraType, xMaxPosition, yMaxPosition);
                    log.info("新增摄像机测温项配置参数为：{}，登录信息为：{}" + ruleAddInfo, loginRequest);
                    ruleId = temperatureRuleService.add(loginRequest, ruleAddInfo);
                    log.info("新增摄像机测温项配置结果为：" + ruleId);
                    temperatureDrawLine.setRuleId(ruleId);
                } catch (RuleConfigException ruleConfigException) {
                    ruleConfigException.printStackTrace();
                    throw new ServiceException(ResultCode.WRONG, "登录");
                } catch (RuntimeException exception) {
                    exception.printStackTrace();
                    throw new ServiceException(ResultCode.WRONG, "登录");
                }
                //添加成功后更新测温项id
                this.updateById(temperatureDrawLine);
            }
        }
        if (CollUtil.isNotEmpty(updateList)) {
            //更新测温项
            for (TemperatureDrawLine temperatureDrawLine : updateList) {
                //先更新数据库的数据  就算后面更新测温项失败了 数据还能回滚
                //如果是先更新测温项配置，再插入到数据库，当数据库数据保存失败时，测温项已经更新了，会导致数据不一致的问题
                TemperatureDrawLine old = this.getById(temperatureDrawLine.getId());
                if (old.getCoordinate().equals(temperatureDrawLine.getCoordinate()) && old.getRuleName().equals(temperatureDrawLine.getRuleName())) {
                    //先查询一次原来的数据 判断坐标是否发生了改变 当坐标和名称发生改变时 才去调用sdk进行更新 只修改阈值时不进行sdk调用
                    this.updateById(temperatureDrawLine);
                } else {
                    this.updateById(temperatureDrawLine);
                    //更新测温项
                    try {
                        TemperatureRule updateInfo = new TemperatureRule();
                        updateInfo.setId(temperatureDrawLine.getRuleId());
                        updateInfo.setName(temperatureDrawLine.getRuleName());
                        updateInfo.setPresetId(String.valueOf(temperatureDrawLine.getPresetId()));
                        //配置坐标
                        String coordinateStr = temperatureDrawLine.getCoordinate();
                        List<Coordinate> coordinateList = JSONUtil.toList(coordinateStr, Coordinate.class);
                        updateInfo.setAreaType(validBuildMeterType(coordinateList, temperatureDrawLine.getType()));
                        updateInfo.setCoordinates(parseCoordinate(coordinateList, drawLineUpdateDto.getYyMaxPosition(), infraredCamera.getFactory()));
                        setTemperatureRulePositionDeviationProportion(updateInfo, cameraFactory, cameraType, xMaxPosition, yMaxPosition);
                        temperatureRuleService.update(loginRequest, updateInfo);
                    } catch (RuleConfigException ruleConfigException) {
                        throw new ServiceException(ResultCode.OPERATION_FAILURE, "更新摄像机测温项配置失败");
                    } catch (RuntimeException exception) {
                        throw new ServiceException(ResultCode.OPERATION_FAILURE, "更新测温项配置数据失败");
                    }
                }
            }
        }
        return true;
    }

    private void setTemperatureRulePositionDeviationProportion(TemperatureRule temperatureRule, CameraManufacturer cameraFactory, CameraType cameraType, int xMaxPosition, int yMaxPosition) {
        //cameraFactory.getCode()==5&&cameraType.getCode()==4 为高德半球
        if (cameraFactory.getCode() == 5 && cameraType.getCode() == 4) {
            temperatureRule.setXPositionDeviationProportion((float) GAODE_HALF_X_POSITION / xMaxPosition);
            temperatureRule.setYPositionDeviationProportion((float) GAODE_HALF_Y_POSITION / yMaxPosition);
        } else {
            temperatureRule.setXPositionDeviationProportion((float) POSITION / xMaxPosition);
            temperatureRule.setYPositionDeviationProportion((float) POSITION / yMaxPosition);
        }
    }

    /**
     * 转换前端坐标为摄像机sdk所需要的坐标
     *
     * @param coordinateList 前端坐标
     * @param yyMaxPosition  前端最大y轴坐标
     * @return : java.util.LinkedList<com.teamway.thermal.entity.TemperatureCoordinate>
     * @date : 2022/8/17
     * <AUTHOR> liucheng
     */
    private LinkedList<TemperatureCoordinate> parseCoordinate(List<Coordinate> coordinateList, String yyMaxPosition, int factory) {
        LinkedList<TemperatureCoordinate> coordinateLinkedList = new LinkedList<>();
        for (Coordinate coordinate : coordinateList) {
            log.info("转换前坐标:{}", coordinate.toString());
            int y = coordinate.getY();
            int x = coordinate.getX();
            //海康
            if (CameraFactoryConstant.HIKVISION.equals(factory)) {
                x = (int) (x * 1.11);
                //起始位置  前端左上角  摄像机左下角
                y = (Integer.valueOf(yyMaxPosition) - y) * 2;
            }
            TemperatureCoordinate c = new TemperatureCoordinate(x, y);
            coordinateLinkedList.add(c);
            log.info("转换后坐标:{}", c.toString());
        }
        return coordinateLinkedList;
    }

    /**
     * 校验测温框类型和对应的边限制并返回sdk所需类型
     *
     * @param coordinateList 坐标集合
     * @param type           测温框类型
     * @return : com.teamway.thermal.common.enums.RADIOMETRY_METERTYPE
     * @date : 2022/8/17
     * <AUTHOR> liucheng
     */
    private MeterType validBuildMeterType(List<Coordinate> coordinateList, String type) {
        if (!"RECT".equals(type) && !"POLYGON".equals(type) && !"POINT".equals(type) && !"LINE".equals(type)) {
            throw new VerifyException(ResultCode.NOT_EXIST, "测温框类型");
        }
        if ("POINT".equals(type)) {
            //点
            if (CollUtil.size(coordinateList) != 1) {
                throw new VerifyException(ResultCode.OPERATION_FAILURE, "点测温框只支持1个点");
            }
            return MeterType.POINT;
        }
        if ("LINE".equals(type)) {
            //点
            if (CollUtil.size(coordinateList) != 2) {
                throw new VerifyException(ResultCode.OPERATION_FAILURE, "线测温框只支持2条边");
            }
            return MeterType.LINE;
        }
        if ("RECT".equals(type)) {
            //矩形
            if (CollUtil.size(coordinateList) != 4) {
                throw new VerifyException(ResultCode.OPERATION_FAILURE, "矩形测温框只支持4条边");
            }
            return MeterType.RECTANGLE;
        }
        if ("POLYGON".equals(type)) {
            //多边形  热成像摄像机最多支持6条边 也就是6组点
            if (CollUtil.size(coordinateList) > 6) {
                throw new VerifyException(ResultCode.OPERATION_FAILURE, "多边形测温框最多支持6条边");
            }
            if (CollUtil.size(coordinateList) < 3) {
                throw new VerifyException(ResultCode.OPERATION_FAILURE, "多边形测温框最少需要3条边");
            }
            return MeterType.POLYGON;
        }
        throw new VerifyException(ResultCode.NOT_EXIST, "测温框类型");
    }

    private void validBuildMeterType(TemperatureDrawLineUpdateDto drawLineUpdateDto) {
        List<CoordinateDto> coordinateDtoList = drawLineUpdateDto.getCoordinateDtoList();
        for (CoordinateDto coordinateDto : coordinateDtoList) {
            String type = coordinateDto.getType();
            List<Coordinate> coordinate = coordinateDto.getCoordinate();
            if ("POINT".equals(type)) {
                //点
                if (CollUtil.size(coordinate) != 1) {
                    throw new VerifyException(ResultCode.OPERATION_FAILURE, "点测温框只支持1个点");
                }
                return;
            } else if ("LINE".equals(type)) {
                //点
                if (CollUtil.size(coordinate) != 2) {
                    throw new VerifyException(ResultCode.OPERATION_FAILURE, "线测温框只支持2条边");
                }
                return;
            } else if ("RECT".equals(type)) {
                //矩形
                if (CollUtil.size(coordinate) != 4) {
                    throw new VerifyException(ResultCode.OPERATION_FAILURE, "矩形测温框只支持4条边");
                }
                return;
            } else if ("POLYGON".equals(type)) {
                //多边形  热成像摄像机最多支持6条边 也就是6组点
                if (CollUtil.size(coordinate) > 6) {
                    throw new VerifyException(ResultCode.OPERATION_FAILURE, "多边形测温框最多支持6条边");
                }
                if (CollUtil.size(coordinate) < 3) {
                    throw new VerifyException(ResultCode.OPERATION_FAILURE, "多边形测温框最少需要3条边");
                }
                return;
            } else {
                throw new VerifyException(ResultCode.OPERATION_FAILURE, "不存在这种测温框类型");
            }
        }
    }

    private Camera getInfraredCamera(String cameraId) {
        LambdaQueryWrapper<Camera> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(Camera::getId, cameraId);
        queryWrapper.eq(Camera::getFeature, FEATURE);
        return cameraService.getOne(queryWrapper);
    }

    private List<TemperatureDrawLine> listByCameraIds(List<String> cameraIds, String presetNo) {
        LambdaQueryWrapper<TemperatureDrawLine> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(TemperatureDrawLine::getCameraId, cameraIds);
        lambdaQueryWrapper.eq(TemperatureDrawLine::getPresetId, presetNo);
        return this.list(lambdaQueryWrapper);
    }

    private LoginRequest buildLoginRequest(Camera camera) {
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setCameraIp(camera.getIp());
        loginRequest.setUserName(camera.getUsername());
        loginRequest.setPassword(camera.getPassword());
        loginRequest.setPort(camera.getPort());
        loginRequest.setCameraType(InfraredUtils.getCameraType(camera.getFactory(), camera.getType()));
        loginRequest.setCameraManufacturer(InfraredUtils.getCameraFactory(camera.getFactory(), camera.getType()));
        loginRequest.setTimeout(3000);
        return loginRequest;
    }

    @Override
    public TemperatureDrawLineUpdateDto getByCameraId(GetByCameraIdDto getByCameraIdDto) throws ServerException {
        Camera camera = cameraService.getById(getByCameraIdDto.getCameraId());
        if (camera == null) {
            throw new VerifyException(ResultCode.NOT_EXIST, "摄像机");
        }
        if (CameraFeatureConstant.INFRARED != camera.getFeature()) {
            throw new VerifyException(ResultCode.OPERATION_FAILURE, "摄像机非热成像摄像机");
        }
        //转换成前端需要的对象
        TemperatureDrawLineUpdateDto temperatureDrawLineUpdateDto = new TemperatureDrawLineUpdateDto();
        String presetNo = getByCameraIdDto.getPresetNo();
        if (StrUtil.isBlank(presetNo)) {
            presetNo = "0";
        }
        List<TemperatureDrawLine> temperatureDrawLines = this.listByCameraIds(Collections.singletonList(getByCameraIdDto.getCameraId()), presetNo);
        boolean needInit = true;
        List<CoordinateDto> coordinateDtoList = new ArrayList<>(CollUtil.size(temperatureDrawLines));
        for (TemperatureDrawLine temperatureDrawLine : temperatureDrawLines) {
            if (needInit) {
                temperatureDrawLineUpdateDto.setCameraId(temperatureDrawLine.getCameraId());
                temperatureDrawLineUpdateDto.setImageUrl(temperatureDrawLine.getImageUrl());
                needInit = false;
            }
            CoordinateDto coordinateDto = new CoordinateDto();
            coordinateDto.setId(temperatureDrawLine.getId());
            coordinateDto.setType(temperatureDrawLine.getType());
            coordinateDto.setPurpose(temperatureDrawLine.getPurpose());
            coordinateDto.setRuleId(temperatureDrawLine.getRuleId());
            coordinateDto.setRuleName(temperatureDrawLine.getRuleName());
            coordinateDto.setThresholdValue(temperatureDrawLine.getThresholdValue());
            coordinateDto.setWarnReportEnable(temperatureDrawLine.getWarnReportEnable());
            //转换坐标
            String coordinateJsonStr = temperatureDrawLine.getCoordinate();
            if (StrUtil.isNotBlank(coordinateJsonStr)) {
                try {
                    coordinateDto.setCoordinate((List<Coordinate>) JSONUtils.parse(coordinateJsonStr));
                } catch (JSONException e) {
                    throw new ServerException("JSON格式转换异常");
                }
            } else {
                coordinateDto.setCoordinate(Collections.emptyList());
            }
            coordinateDtoList.add(coordinateDto);
        }
        temperatureDrawLineUpdateDto.setCoordinateDtoList(coordinateDtoList);
        //如果没有绘制过框,则默认抓取一张提供
        return temperatureDrawLineUpdateDto;
    }

    @Override
    public void setIrRule(SetIrRuleDto setIrRuleDto) {
        String deviceID = setIrRuleDto.getDeviceID();
        String channelID = setIrRuleDto.getChannelID();
        Camera infraredCamera = getInfraredCameraByMainSubId(deviceID, channelID);
        if (infraredCamera == null) {
            throw new VerifyException(ResultCode.NOT_EXIST, "摄像机");
        }
        CameraManufacturer cameraFactory = InfraredUtils.getCameraFactory(infraredCamera.getFactory(), infraredCamera.getType());
        CameraType cameraType = InfraredUtils.getCameraType(infraredCamera.getFactory(), infraredCamera.getType());
        //cameraFactory.getCode()==5&&cameraType.getCode()==4 为高德半球
        float xProportion = 1f;
        float yProportion = 1f;
        if (cameraFactory.getCode() == 5 && cameraType.getCode() == 4) {
            xProportion = (float) GAODE_HALF_X_POSITION / PG;
            yProportion = (float) GAODE_HALF_Y_POSITION / PG;
        } else {
            xProportion = (float) POSITION / PG;
            yProportion = (float) POSITION / PG;
        }
        GetIrRuleVo getIrRuleVo = new GetIrRuleVo();
        List<RuleDto> rules = new ArrayList<>();
        LoginRequest loginRequest = buildLoginRequest(infraredCamera);
        //1、新增 3、删除
        int ctrlType = setIrRuleDto.getCtrlType();
        if (1 == ctrlType) {
            for (RuleDto rule : setIrRuleDto.getRules()) {
                TemperatureRule ruleInfo = new TemperatureRule();
                ruleInfo.setXPositionDeviationProportion(xProportion);
                ruleInfo.setYPositionDeviationProportion(yProportion);
                ruleInfo.setAreaType(setAreaType(rule.getRuleType()));
                ruleInfo.setPresetId(String.valueOf(setIrRuleDto.getPresetID()));
                ruleInfo.setName(rule.getRuleName());
                LinkedList<TemperatureCoordinate> coordinates = new LinkedList<>();
                for (PointDto point : rule.getPoints()) {
                    TemperatureCoordinate temperatureCoordinate = new TemperatureCoordinate();
                    temperatureCoordinate.setX((int) point.getX());
                    temperatureCoordinate.setY((int) point.getY());
                    coordinates.add(temperatureCoordinate);
                }
                ruleInfo.setCoordinates(coordinates);
                try {
                    temperatureRuleService.add(loginRequest, ruleInfo);
                } catch (Exception e) {
                    log.info("新增测温项失败,失败的测温规则为：" + ruleInfo);
                    throw new ServiceException(ResultCode.SO_FAIL, "新增测温项");
                }
            }
        } else if (3 == ctrlType) {
            for (RuleDto rule : setIrRuleDto.getRules()) {
                try {
                    temperatureRuleService.deleteByPresetIdAndRuleId(loginRequest, String.valueOf(setIrRuleDto.getPresetID()), String.valueOf(rule.getRuleID()));
                } catch (Exception e) {
                    log.info("删除测温项失败,失败的测温规则为：" + rule);
                    throw new ServiceException(ResultCode.SO_FAIL, "删除测温项");
                }
            }
        }

    }

    private MeterType setAreaType(String ruleType) {
        if ("0".equals(ruleType)) {
            return MeterType.POINT;
        } else if ("1".equals(ruleType)) {
            return MeterType.POLYGON;
        } else if ("2".equals(ruleType)) {
            return MeterType.LINE;
        }
        return null;
    }

    @Override
    public GetIrRuleVo getIrRule(String deviceId, String channelId, int presetId) {
        Camera infraredCamera = getInfraredCameraByMainSubId(deviceId, channelId);
        if (infraredCamera == null) {
            throw new VerifyException(ResultCode.NOT_EXIST, "摄像机");
        }
        CameraManufacturer cameraFactory = InfraredUtils.getCameraFactory(infraredCamera.getFactory(), infraredCamera.getType());
        CameraType cameraType = InfraredUtils.getCameraType(infraredCamera.getFactory(), infraredCamera.getType());
        //cameraFactory.getCode()==5&&cameraType.getCode()==4 为高德半球
        float xProportion = 1f;
        float yProportion = 1f;
        if (cameraFactory.getCode() == 5 && cameraType.getCode() == 4) {
            xProportion = (float) GAODE_HALF_X_POSITION / PG;
            yProportion = (float) GAODE_HALF_Y_POSITION / PG;
        } else {
            xProportion = (float) POSITION / PG;
            yProportion = (float) POSITION / PG;
        }
        GetIrRuleVo getIrRuleVo = new GetIrRuleVo();
        List<RuleDto> rules = new ArrayList<>();
        LoginRequest loginRequest = buildLoginRequest(infraredCamera);
        List<TemperatureRule> temperatureRules = temperatureRuleService.listByPresetId(loginRequest, String.valueOf(presetId));
        for (TemperatureRule temperatureRule : temperatureRules) {
            RuleDto ruleDto = new RuleDto();
            ruleDto.setRuleID(Integer.valueOf(temperatureRule.getId()));
            ruleDto.setRuleName(temperatureRule.getName());
            ruleDto.setRuleType(setRuleType(temperatureRule.getAreaType()));
            List<PointDto> points = new ArrayList<>();
            for (TemperatureCoordinate coordinate : temperatureRule.getCoordinates()) {
                PointDto pointDto = new PointDto();
                pointDto.setX((float) coordinate.getX() / xProportion);
                pointDto.setY((float) coordinate.getY() / yProportion);
                points.add(pointDto);
            }
            ruleDto.setPoints(points);
            rules.add(ruleDto);
        }
        getIrRuleVo.setRules(rules);
        return getIrRuleVo;
    }

    @Override
    public GetTemperatureDrawLineForInsVo getTemperatureDrawLineForIns(GetTemperatureDrawLineForInsDto getTemperatureDrawLineForInsDto) {
        LambdaQueryWrapper<TemperatureDrawLine> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(StrUtil.isNotBlank(getTemperatureDrawLineForInsDto.getId()), TemperatureDrawLine::getCameraId, getTemperatureDrawLineForInsDto.getId());
        if (StrUtil.isNotBlank(getTemperatureDrawLineForInsDto.getPresetId())) {
            CameraPreset cameraPreset = cameraPresetService.getById(getTemperatureDrawLineForInsDto.getPresetId());
            if (cameraPreset == null) {
                throw new ServiceException(ResultCode.NOT_EXIST, "预置位");
            }
            queryWrapper.eq(TemperatureDrawLine::getPresetId, cameraPreset.getNo());
        }
        List<TemperatureDrawLine> temperatureDrawLines = this.baseMapper.selectList(queryWrapper);
        GetTemperatureDrawLineForInsVo vo = new GetTemperatureDrawLineForInsVo();
        List<DataListVo> list = new ArrayList<>();
        for (TemperatureDrawLine temperatureDrawLine : temperatureDrawLines) {
            DataListVo dataListVo = new DataListVo();
            dataListVo.setRuleId(temperatureDrawLine.getRuleId());
            dataListVo.setRuleName(temperatureDrawLine.getRuleName());
            list.add(dataListVo);
        }
        vo.setList(list);
        return vo;
    }

    /**
     * 获取测温项
     *
     * @param getTemperatureDrawLineForInsDto
     * @return
     */
    @Override
    public List<TemperatureRule> getTemperature(GetTemperatureDrawLineForInsDto getTemperatureDrawLineForInsDto) {
        Camera camera = cameraService.getById(getTemperatureDrawLineForInsDto.getId());
        if (camera == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "摄像机");
        }
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setCameraIp(camera.getIp());
        loginRequest.setPort(camera.getPort());
        loginRequest.setUserName(camera.getUsername());
        loginRequest.setPassword(camera.getPassword());
        loginRequest.setCameraType(InfraredUtils.getCameraType(camera.getFactory(), camera.getType()));
        loginRequest.setCameraManufacturer(InfraredUtils.getCameraFactory(camera.getFactory(), camera.getType()));
        loginRequest.setTimeout(6000);
        log.info("loginRequest:{}", JSON.toJSONString(loginRequest));
        return temperatureRuleService.listByPresetId(loginRequest, getTemperatureDrawLineForInsDto.getPresetId().toString());
    }

    private String setRuleType(MeterType areaType) {
        if (1 == areaType.getMeterType()) {
            return "0";
        } else if (2 == areaType.getMeterType()) {
            return "2";
        } else if (3 == areaType.getMeterType()) {
            return "1";
        }
        return "";
    }

    private Camera getInfraredCameraByMainSubId(String deviceID, String channelID) {
        LambdaQueryWrapper<Camera> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(Camera::getMainId, deviceID);
        queryWrapper.eq(Camera::getSubId, channelID);
        queryWrapper.eq(Camera::getFeature, FEATURE);
        return cameraService.getOne(queryWrapper);
    }
}
