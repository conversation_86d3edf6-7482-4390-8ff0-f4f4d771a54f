package com.teamway.base.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.teamway.base.dto.tv.tv.AddSceneDto;
import com.teamway.base.dto.tv.tv.AddSceneScreenListDto;
import com.teamway.base.dto.tv.tv.AddSceneScreenListWindowsListDto;
import com.teamway.base.dto.tv.tv.ChangeTvWallWithWindowDto;
import com.teamway.base.dto.tv.tv.DelTvWallWithWindowDto;
import com.teamway.base.dto.tv.tv.EditSceneNameDto;
import com.teamway.base.dto.tv.tv.GetSceneByIdScreenListVo;
import com.teamway.base.dto.tv.tv.GetSceneByIdScreenListWindowsListVo;
import com.teamway.base.dto.tv.tv.GetSceneByIdVo;
import com.teamway.base.dto.tv.tv.SelectSceneVo;
import com.teamway.base.dto.tv.tv.SetWallWinResDataDto;
import com.teamway.base.dto.tv.tv.SetWallWinResDto;
import com.teamway.base.dto.tv.tv.UpdateSceneDto;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.Decoder;
import com.teamway.base.entity.TvScene;
import com.teamway.base.entity.TvScreen;
import com.teamway.base.entity.TvWindow;
import com.teamway.base.service.CameraService;
import com.teamway.base.service.DecoderService;
import com.teamway.base.service.TvSceneService;
import com.teamway.base.service.TvScreenService;
import com.teamway.base.service.TvWallService;
import com.teamway.base.service.TvWindowService;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class TvWallServiceImpl implements TvWallService {

    @Autowired
    private TvSceneService tvSceneService;
    @Autowired
    private TvScreenService tvScreenService;
    @Autowired
    private TvWindowService tvWindowService;
    @Autowired
    private DecoderService decoderService;
    @Autowired
    private CameraService cameraService;
    @Autowired
    private RestTemplate restTemplate;
    @Value("${decoderUrl}")
    private String decoderUrl;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addScene(AddSceneDto addSceneDto) {
        Decoder decoder = decoderService.getById(addSceneDto.getDecoderDeviceId());
        if (decoder == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "解码器ID");
        }
        LambdaQueryWrapper<TvScene> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TvScene::getSceneName, addSceneDto.getSceneName());
        List<TvScene> list = tvSceneService.list(lambdaQueryWrapper);
        if (list != null && list.size() > 0) {
            throw new ServiceException(ResultCode.EXIST, "场景名称");
        }
        //保存场景
        TvScene tvScene = new TvScene();
        tvScene.setSceneName(addSceneDto.getSceneName());
        tvScene.setDecoderDeviceId(Long.valueOf(addSceneDto.getDecoderDeviceId()));
        if (tvSceneService.save(tvScene)) {
            //保存屏幕布局
            for (AddSceneScreenListDto addSceneScreenListDto : addSceneDto.getScreenList()) {
                List<Integer> screenCoordinateList = addSceneScreenListDto.getScreenCoordinateList();
                if (screenCoordinateList == null || screenCoordinateList.size() != 4) {
                    throw new ServiceException(ResultCode.ILLEGAL, "screenCoordinateList传参为空或者长度不为4...");
                }
                StringBuilder stringBuffer = new StringBuilder();
                for (Integer s : screenCoordinateList) {
                    stringBuffer.append(s).append(",");
                }
                String screenCoordinate = stringBuffer.toString();
                if (addSceneScreenListDto.getScreenType() == null) {
                    TvScreen tvScreen = new TvScreen();
                    tvScreen.setTvSceneId(tvScene.getId());
                    tvScreen.setScreenCoordinate(screenCoordinate.substring(0, screenCoordinate.length() - 1));
                    tvScreenService.save(tvScreen);
                } else {
                    checkScreenTypeAndWindowsList(addSceneScreenListDto.getScreenType(), addSceneScreenListDto.getWindowsList());
                    TvScreen tvScreen = new TvScreen();
                    tvScreen.setScreenType(String.valueOf(addSceneScreenListDto.getScreenType()));
                    tvScreen.setTvSceneId(tvScene.getId());
                    tvScreen.setScreenCoordinate(screenCoordinate.substring(0, screenCoordinate.length() - 1));
                    if (tvScreenService.save(tvScreen)) {
                        for (int i = 0; i < addSceneScreenListDto.getWindowsList().size(); i++) {
                            AddSceneScreenListWindowsListDto addSceneScreenListWindowsListDto = addSceneScreenListDto.getWindowsList().get(i);
                            TvWindow tvWindow = new TvWindow();
                            if (addSceneScreenListWindowsListDto.getCameraId() != null && addSceneScreenListWindowsListDto.getCameraId().trim().length() != 0) {
                                if (cameraService.getById(addSceneScreenListWindowsListDto.getCameraId()) == null) {
                                    throw new ServiceException(ResultCode.NOT_EXIST, "cameraId");
                                }
                                tvWindow.setCameraId(Long.valueOf(addSceneScreenListWindowsListDto.getCameraId()));
                            }
                            tvWindow.setWindowIndex(addSceneScreenListWindowsListDto.getWindowIndex());
                            tvWindow.setTvScreenId(tvScreen.getId());
                            tvWindowService.save(tvWindow);
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateScene(UpdateSceneDto updateSceneDto) {
        TvScene tvScene = tvSceneService.getById(updateSceneDto.getSceneId());
        if (tvScene == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "场景ID");
        }
        Decoder decoder = decoderService.getById(updateSceneDto.getDecoderDeviceId());
        if (decoder == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "解码器ID");
        }
        LambdaQueryWrapper<TvScene> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TvScene::getSceneName, updateSceneDto.getSceneName());
        lambdaQueryWrapper.ne(TvScene::getId, updateSceneDto.getSceneId());
        List<TvScene> list = tvSceneService.list(lambdaQueryWrapper);
        if (list != null && list.size() > 0) {
            throw new ServiceException(ResultCode.EXIST, "场景名称");
        }
        tvScene.setSceneName(updateSceneDto.getSceneName());
        tvScene.setDecoderDeviceId(Long.valueOf(updateSceneDto.getDecoderDeviceId()));
        if (tvSceneService.updateById(tvScene)) {
            //删除之前的
            LambdaQueryWrapper<TvScreen> tvScreenLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tvScreenLambdaQueryWrapper.eq(tvScene.getId() != null, TvScreen::getTvSceneId, tvScene.getId());
            List<TvScreen> tvScreens = tvScreenService.list(tvScreenLambdaQueryWrapper);
            List<Long> tvScreenIds = new ArrayList<>();
            if (tvScreens != null && tvScreens.size() > 0) {
                for (TvScreen tvScreen : tvScreens) {
                    tvScreenIds.add(tvScreen.getId());
                }
            }
            LambdaQueryWrapper<TvWindow> tvWindowLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tvWindowLambdaQueryWrapper.in(TvWindow::getTvScreenId, tvScreenIds);
            List<TvWindow> tvWindows = tvWindowService.list(tvWindowLambdaQueryWrapper);
            List<Long> tvWindowIds = new ArrayList<>();
            List<Integer> wins = new ArrayList<>();
            if (tvWindows != null && tvWindows.size() > 0) {
                for (TvWindow tvWindow : tvWindows) {
                    tvWindowIds.add(tvWindow.getId());
                    wins.add(tvWindow.getWindowNo());
                }
            }
            //如果该场景处于上墙状态中，进行清窗
            if ("1".equals(tvScene.getState())) {
                //清除开窗
                String cleanWallWinUrl = decoderUrl + "/hkSdk/cleanWallWin";
                for (Integer win : wins) {
                    Map<String, Object> params = new HashMap<>();
                    List<Integer> oneWin = new ArrayList<>();
                    oneWin.add(win);
                    params.put("ip", decoder.getIp());
                    params.put("wins", oneWin);
                    try {
                        restTemplate.postForEntity(cleanWallWinUrl, params, String.class);
                    } catch (RestClientException e) {
                    }
                }
                //状态设为0
                tvScene.setState("0");
                tvSceneService.updateById(tvScene);
            }
            tvWindowService.removeBatchByIds(tvWindowIds);
            tvScreenService.removeBatchByIds(tvScreenIds);
            //保存新增的
            for (AddSceneScreenListDto addSceneScreenListDto : updateSceneDto.getScreenList()) {
                List<Integer> screenCoordinateList = addSceneScreenListDto.getScreenCoordinateList();
                if (screenCoordinateList == null || screenCoordinateList.size() != 4) {
                    throw new ServiceException(ResultCode.ILLEGAL, "screenCoordinateList传参为空或者长度不为4...");
                }
                StringBuilder stringBuffer = new StringBuilder();
                for (Integer s : screenCoordinateList) {
                    stringBuffer.append(s).append(",");
                }
                String screenCoordinate = stringBuffer.toString();
                if (addSceneScreenListDto.getScreenType() == null) {
                    TvScreen tvScreen = new TvScreen();
                    tvScreen.setTvSceneId(tvScene.getId());
                    tvScreen.setScreenCoordinate(screenCoordinate.substring(0, screenCoordinate.length() - 1));
                    tvScreenService.save(tvScreen);
                } else {
                    checkScreenTypeAndWindowsList(addSceneScreenListDto.getScreenType(), addSceneScreenListDto.getWindowsList());
                    TvScreen tvScreen = new TvScreen();
                    tvScreen.setScreenType(String.valueOf(addSceneScreenListDto.getScreenType()));
                    tvScreen.setTvSceneId(tvScene.getId());
                    tvScreen.setScreenCoordinate(screenCoordinate.substring(0, screenCoordinate.length() - 1));
                    if (tvScreenService.save(tvScreen)) {
                        for (int i = 0; i < addSceneScreenListDto.getWindowsList().size(); i++) {
                            AddSceneScreenListWindowsListDto addSceneScreenListWindowsListDto = addSceneScreenListDto.getWindowsList().get(i);
                            TvWindow tvWindow = new TvWindow();
                            if (addSceneScreenListWindowsListDto.getCameraId() != null && addSceneScreenListWindowsListDto.getCameraId().trim().length() != 0) {
                                if (cameraService.getById(addSceneScreenListWindowsListDto.getCameraId()) == null) {
                                    throw new ServiceException(ResultCode.NOT_EXIST, "cameraId");
                                }
                                tvWindow.setCameraId(Long.valueOf(addSceneScreenListWindowsListDto.getCameraId()));
                            }
                            tvWindow.setWindowIndex(addSceneScreenListWindowsListDto.getWindowIndex());
                            tvWindow.setTvScreenId(tvScreen.getId());
                            tvWindowService.save(tvWindow);
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    public boolean editSceneName(EditSceneNameDto editSceneNameDto) {
        TvScene tvScene = tvSceneService.getById(editSceneNameDto.getSceneId());
        if (tvScene == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "场景ID");
        }
        LambdaQueryWrapper<TvScene> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TvScene::getSceneName, editSceneNameDto.getSceneName());
        lambdaQueryWrapper.ne(TvScene::getId, tvScene.getId());
        List<TvScene> list = tvSceneService.list(lambdaQueryWrapper);
        if (list != null && list.size() > 0) {
            throw new ServiceException(ResultCode.EXIST, "场景名称");
        }
        tvScene.setSceneName(editSceneNameDto.getSceneName());
        return tvSceneService.updateById(tvScene);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteScene(String sceneId) {
        TvScene tvScene = tvSceneService.getById(sceneId);
        if (tvScene == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "场景ID");
        }
        LambdaQueryWrapper<TvScreen> tvScreenLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tvScreenLambdaQueryWrapper.eq(tvScene.getId() != null, TvScreen::getTvSceneId, tvScene.getId());
        List<TvScreen> tvScreens = tvScreenService.list(tvScreenLambdaQueryWrapper);
        List<Long> tvScreenIds = new ArrayList<>();
        if (tvScreens != null && tvScreens.size() > 0) {
            for (TvScreen tvScreen : tvScreens) {
                tvScreenIds.add(tvScreen.getId());
            }
        }
        List<Long> tvWindowIds = new ArrayList<>();
        List<Integer> wins = new ArrayList<>();
        if (tvScreenIds.size() > 0) {
            LambdaQueryWrapper<TvWindow> tvWindowLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tvWindowLambdaQueryWrapper.in(TvWindow::getTvScreenId, tvScreenIds);
            List<TvWindow> tvWindows = tvWindowService.list(tvWindowLambdaQueryWrapper);
            if (tvWindows != null && tvWindows.size() > 0) {
                for (TvWindow tvWindow : tvWindows) {
                    tvWindowIds.add(tvWindow.getId());
                    wins.add(tvWindow.getWindowNo());
                }
            }
        }

        //如果该场景处于上墙状态中，进行清窗
        if ("1".equals(tvScene.getState())) {
            Decoder decoder = decoderService.getById(tvScene.getDecoderDeviceId());
            if (decoder != null) {
                //清除开窗
                String cleanWallWinUrl = decoderUrl + "/hkSdk/cleanWallWin";
                for (Integer win : wins) {
                    Map<String, Object> params = new HashMap<>();
                    List<Integer> oneWin = new ArrayList<>();
                    oneWin.add(win);
                    params.put("ip", decoder.getIp());
                    params.put("wins", oneWin);
                    try {
                        restTemplate.postForEntity(cleanWallWinUrl, params, String.class);
                    } catch (RestClientException e) {
                    }
                }
            }
        }
        tvWindowService.removeBatchByIds(tvWindowIds);
        tvScreenService.removeBatchByIds(tvScreenIds);
        tvSceneService.removeById(tvScene);
        return true;
    }

    @Override
    public List<SelectSceneVo> selectScene(String decoderDeviceId) {
        if (decoderService.getById(decoderDeviceId) == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "解码器ID");
        }
        List<SelectSceneVo> selectSceneVos = new ArrayList<>();
        LambdaQueryWrapper<TvScene> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TvScene::getDecoderDeviceId, decoderDeviceId);
        List<TvScene> list = tvSceneService.list(lambdaQueryWrapper);
        if (list != null && list.size() > 0) {
            for (TvScene tvScene : list) {
                SelectSceneVo selectSceneVo = new SelectSceneVo();
                selectSceneVo.setSceneId(String.valueOf(tvScene.getId()));
                selectSceneVo.setSceneName(tvScene.getSceneName());
                selectSceneVo.setSceneState(tvScene.getState());
                selectSceneVos.add(selectSceneVo);
            }
        }
        return selectSceneVos;
    }

    @Override
    public GetSceneByIdVo getSceneById(String sceneId) {
        GetSceneByIdVo getSceneByIdVo = new GetSceneByIdVo();
        TvScene tvScene = tvSceneService.getById(sceneId);
        if (tvScene == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "场景ID");
        }
        getSceneByIdVo.setSceneId(sceneId);
        getSceneByIdVo.setSceneName(tvScene.getSceneName());
        getSceneByIdVo.setSceneState(tvScene.getState());
        getSceneByIdVo.setDecoderDeviceId(String.valueOf(tvScene.getDecoderDeviceId()));
        List<GetSceneByIdScreenListVo> screenList = new ArrayList<>();
        LambdaQueryWrapper<TvScreen> tvScreenLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tvScreenLambdaQueryWrapper.eq(tvScene.getId() != null, TvScreen::getTvSceneId, tvScene.getId());
        List<TvScreen> tvScreens = tvScreenService.list(tvScreenLambdaQueryWrapper);
        if (tvScreens != null && tvScreens.size() > 0) {
            for (TvScreen tvScreen : tvScreens) {
                GetSceneByIdScreenListVo getSceneByIdScreenListVo = new GetSceneByIdScreenListVo();
                getSceneByIdScreenListVo.setScreenId(String.valueOf(tvScreen.getId()));
                List<Integer> screenCoordinateList = new ArrayList<>();
                for (String s : tvScreen.getScreenCoordinate().split(",")) {
                    screenCoordinateList.add(Integer.valueOf(s));
                }
                getSceneByIdScreenListVo.setScreenCoordinateList(screenCoordinateList);
                if (tvScreen.getScreenType() == null) {
                    getSceneByIdScreenListVo.setWindowsList(new ArrayList<>());
                } else {
                    getSceneByIdScreenListVo.setScreenType(Integer.valueOf(tvScreen.getScreenType()));
                    List<GetSceneByIdScreenListWindowsListVo> windowsList = new ArrayList<>();
                    LambdaQueryWrapper<TvWindow> tvWindowLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    tvWindowLambdaQueryWrapper.eq(TvWindow::getTvScreenId, tvScreen.getId());
                    List<TvWindow> tvWindows = tvWindowService.list(tvWindowLambdaQueryWrapper);
                    for (TvWindow tvWindow : tvWindows) {
                        GetSceneByIdScreenListWindowsListVo vo = new GetSceneByIdScreenListWindowsListVo();
                        if (tvWindow.getCameraId() != null) {
                            Camera camera = cameraService.getById(tvWindow.getCameraId());
                            if (camera != null) {
                                vo.setCameraName(camera.getName());
                                vo.setCameraId(String.valueOf(tvWindow.getCameraId()));
                                vo.setWindowIndex(tvWindow.getWindowIndex());
                            }
                        }
                        windowsList.add(vo);
                    }
                    getSceneByIdScreenListVo.setWindowsList(windowsList);
                }
                screenList.add(getSceneByIdScreenListVo);
            }
        }
        getSceneByIdVo.setScreenList(screenList);
        return getSceneByIdVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeTv(String sceneId) {
        TvScene tvScene = tvSceneService.getById(sceneId);
        if (tvScene == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "场景ID");
        }
        //第一步查询state=1的场景，清除开窗
        LambdaQueryWrapper<TvScene> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TvScene::getState, "1");
        List<TvScene> list = tvSceneService.list(lambdaQueryWrapper);
        if (list != null && list.size() > 0) {
            if (list.size() == 1) {
                TvScene old = list.get(0);
                Decoder decoder = decoderService.getById(old.getDecoderDeviceId());
                String stopUrl = decoderUrl + "/hkSdk/stop";
                String cleanWallWinUrl = decoderUrl + "/hkSdk/cleanWallWin";
                LambdaQueryWrapper<TvScreen> tvScreenLambdaQueryWrapper = new LambdaQueryWrapper<>();
                tvScreenLambdaQueryWrapper.eq(old.getId() != null, TvScreen::getTvSceneId, old.getId());
                tvScreenLambdaQueryWrapper.isNotNull(TvScreen::getScreenType);
                List<TvScreen> tvScreens = tvScreenService.list(tvScreenLambdaQueryWrapper);
                List<Integer> wins = new ArrayList<>();
                for (TvScreen tvScreen : tvScreens) {
                    LambdaQueryWrapper<TvWindow> tvWindowLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    tvWindowLambdaQueryWrapper.eq(TvWindow::getTvScreenId, tvScreen.getId());
                    List<TvWindow> tvWindows = tvWindowService.list(tvWindowLambdaQueryWrapper);
                    for (TvWindow tvWindow : tvWindows) {
                        wins.add(tvWindow.getWindowNo());
                    }
                }
                //2、清除开窗
                for (Integer win : wins) {
                    Map<String, Object> params = new HashMap<>();
                    List<Integer> oneWin = new ArrayList<>();
                    oneWin.add(win);
                    params.put("ip", decoder.getIp());
                    params.put("wins", oneWin);
                    try {
                        restTemplate.postForEntity(cleanWallWinUrl, params, String.class);
                    } catch (RestClientException e) {
                    }
                }
                //3、状态设为0
                old.setState("0");
                tvSceneService.updateById(old);
            } else {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "数据库数据异常，存在多个在墙上的场景...");
            }
        }
        //第二步开窗，播放视频，保存winNo
        Decoder decoder = decoderService.getById(tvScene.getDecoderDeviceId());
        LambdaQueryWrapper<TvScreen> tvScreenLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tvScreenLambdaQueryWrapper.eq(tvScene.getId() != null, TvScreen::getTvSceneId, tvScene.getId());
        tvScreenLambdaQueryWrapper.isNotNull(TvScreen::getScreenType);
        List<TvScreen> tvScreens = tvScreenService.list(tvScreenLambdaQueryWrapper);
        String setWallWinUrl = decoderUrl + "/hkSdk/setWallWin";
        String playUrl = decoderUrl + "/hkSdk/playUrl";
        String play = decoderUrl + "/hkSdk/play";
        for (TvScreen tvScreen : tvScreens) {
            String[] split = tvScreen.getScreenCoordinate().split(",");
            List<SetWallWinResDataDto> data = null;
            //1、开窗
            try {
                String body = restTemplate.getForEntity(
                        setWallWinUrl + "?ip=" + decoder.getIp()
                                + "&winType=" + getWinType(tvScreen.getScreenType())
                                + "&startX=" + split[0]
                                + "&startY=" + split[1]
                                + "&widthCount=" + split[2]
                                + "&heightCount=" + split[3],
                        String.class).getBody();
                SetWallWinResDto setWallWinResDto = JSON.parseObject(body, SetWallWinResDto.class);
                data = setWallWinResDto.getData();
                if (data == null) {
                    throw new RuntimeException();
                }
            } catch (Exception e) {
                log.info("开窗失败，解码器ip={},tvScreenId={}", decoder.getIp(), tvScreen.getId());
                throw new ServiceException(ResultCode.WRONG, "开窗");
            }
            LambdaQueryWrapper<TvWindow> tvWindowLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tvWindowLambdaQueryWrapper.eq(TvWindow::getTvScreenId, tvScreen.getId());
            List<TvWindow> tvWindows = tvWindowService.list(tvWindowLambdaQueryWrapper);
            for (int i = 0; i < tvWindows.size(); i++) {
                TvWindow tvWindow = tvWindows.get(i);
                Camera camera = cameraService.getById(tvWindow.getCameraId());
                if (camera != null) {
                    //2、播放视频
                    try {
                        playVideo(decoder, data.get(i).getWinNo(), camera, restTemplate, play, playUrl);
                    } catch (RestClientException e) {
                        log.info("播放视频失败，解码器ip={},tvWindowId={}", decoder.getIp(), tvWindow.getId());
                        throw new ServiceException(ResultCode.WRONG, "播放视频");
                    }
                }
                //3、保存winNo
                tvWindow.setWindowNo(data.get(i).getWinNo());
                tvWindowService.updateById(tvWindow);
            }
        }
        //第三步设置该场景state=1
        tvScene.setState("1");
        tvSceneService.updateById(tvScene);
        return true;
    }

    private void playVideo(Decoder decoder, Integer winNo, Camera camera, RestTemplate restTemplate, String play, String playUrl) {
        String playModel = camera.getPlayModel();
        if ("1".equals(playModel)) {
            restTemplate.getForEntity(
                    play + "?ip=" + decoder.getIp()
                            + "&winNo=" + winNo
                            + "&cameraIp=" + camera.getIp()
                            + "&port=" + camera.getPlayPort()
                            + "&user=" + camera.getUsername()
                            + "&password=" + camera.getPassword()
                            + "&channel=" + camera.getChannel(),
                    String.class);
        }
        if ("2".equals(playModel)) {
            Map<String, Object> params = new HashMap<>();
            params.put("ip", decoder.getIp());
            params.put("winNo", winNo);
            params.put("url", camera.getVideoUrl());
            restTemplate.postForEntity(playUrl, params, String.class);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeTvWallWithWindow(ChangeTvWallWithWindowDto changeTvWallWithWindowDto) {
        TvScene tvScene = tvSceneService.getById(changeTvWallWithWindowDto.getSceneId());
        if (tvScene == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "场景ID");
        }
        if (!"1".equals(tvScene.getState())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "该场景未在墙上...");
        }
        TvScreen tvScreen = tvScreenService.getById(changeTvWallWithWindowDto.getScreenId());
        if (tvScene == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "屏幕ID");
        }
        Camera camera = cameraService.getById(changeTvWallWithWindowDto.getCameraId());
        if (camera == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "摄像机ID");
        }
        Decoder decoder = decoderService.getById(tvScene.getDecoderDeviceId());
        String playUrl = decoderUrl + "/hkSdk/playUrl";
        String play = decoderUrl + "/hkSdk/play";
        String setWallWinUrl = decoderUrl + "/hkSdk/setWallWin";
        if (tvScreen.getScreenType() == null) {
            //开窗播放视频，并添加window数据
            String[] split = tvScreen.getScreenCoordinate().split(",");
            List<SetWallWinResDataDto> data = null;
            try {
                String body = restTemplate.getForEntity(
                        setWallWinUrl + "?ip=" + decoder.getIp()
                                + "&winType=" + getWinType(tvScreen.getScreenType())
                                + "&startX=" + split[0]
                                + "&startY=" + split[1]
                                + "&widthCount=" + split[2]
                                + "&heightCount=" + split[3],
                        String.class).getBody();
                SetWallWinResDto setWallWinResDto = JSON.parseObject(body, SetWallWinResDto.class);
                data = setWallWinResDto.getData();
                if (data == null) {
                    throw new RuntimeException();
                }
            } catch (Exception e) {
                log.info("开窗失败，解码器ip={},tvScreenId={}", decoder.getIp(), tvScreen.getId());
                throw new ServiceException(ResultCode.WRONG, "开窗");
            }

            for (int i = 0; i < changeTvWallWithWindowDto.getScreenType(); i++) {
                TvWindow tvWindow = new TvWindow();
                tvWindow.setTvScreenId(tvScreen.getId());
                tvWindow.setWindowNo(data.get(i).getWinNo());
                if (i == Integer.valueOf(changeTvWallWithWindowDto.getWinIndex()) - 1) {
                    tvWindow.setCameraId(Long.valueOf(changeTvWallWithWindowDto.getCameraId()));
                    tvWindow.setWindowIndex(Integer.valueOf(changeTvWallWithWindowDto.getWinIndex()));
                    //播放视频
                    try {
                        playVideo(decoder, tvWindow.getWindowNo(), camera, restTemplate, play, playUrl);
                    } catch (RestClientException e) {
                        log.info("播放视频失败，解码器ip={},tvWindowId={}", decoder.getIp(), tvWindow.getId());
                        throw new ServiceException(ResultCode.WRONG, "播放视频");
                    }
                }
                tvWindowService.save(tvWindow);
            }
        } else {
            if (!tvScreen.getScreenType().equals(String.valueOf(changeTvWallWithWindowDto.getScreenType()))) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "screenType和原先的不一致...");
            }
            //修改window数据，并播放视频
            LambdaQueryWrapper<TvWindow> tvWindowLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tvWindowLambdaQueryWrapper.eq(TvWindow::getTvScreenId, tvScreen.getId());
            List<TvWindow> tvWindows = tvWindowService.list(tvWindowLambdaQueryWrapper);
            TvWindow tvWindow = tvWindows.get(Integer.valueOf(changeTvWallWithWindowDto.getWinIndex()) - 1);
            tvWindow.setWindowIndex(Integer.valueOf(changeTvWallWithWindowDto.getWinIndex()));
            tvWindow.setCameraId(Long.valueOf(changeTvWallWithWindowDto.getCameraId()));
            tvWindowService.updateById(tvWindow);
            //播放视频
            try {
                playVideo(decoder, tvWindow.getWindowNo(), camera, restTemplate, play, playUrl);
            } catch (RestClientException e) {
                log.info("播放视频失败，解码器ip={},tvWindowId={}", decoder.getIp(), tvWindow.getId());
                throw new ServiceException(ResultCode.WRONG, "播放视频");
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delTvWallWithWindow(DelTvWallWithWindowDto delTvWallWithWindowDto) {
        TvScene tvScene = tvSceneService.getById(delTvWallWithWindowDto.getSceneId());

        if (tvScene == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "场景ID");
        }
        if (!"1".equals(tvScene.getState())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "该场景未在墙上...");
        }
        TvScreen tvScreen = tvScreenService.getById(delTvWallWithWindowDto.getScreenId());
        if (tvScene == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "屏幕ID");
        }
        LambdaQueryWrapper<TvWindow> tvWindowLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tvWindowLambdaQueryWrapper.eq(TvWindow::getTvScreenId, delTvWallWithWindowDto.getScreenId());
        tvWindowLambdaQueryWrapper.eq(TvWindow::getWindowIndex, Integer.valueOf(delTvWallWithWindowDto.getWinIndex()));
        TvWindow tvWindow = tvWindowService.getOne(tvWindowLambdaQueryWrapper);
        if (tvWindow == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "与屏幕id相对应的winIndex");
        }
        //停止播放视频
        Decoder decoder = decoderService.getById(tvScene.getDecoderDeviceId());
        String stopUrl = decoderUrl + "/hkSdk/stop";
        try {
            restTemplate.getForEntity(
                    stopUrl + "?ip=" + decoder.getIp()
                            + "&winNo=" + tvWindow.getWindowNo(),
                    String.class);
        } catch (RestClientException e) {
        }

        return tvWindowService.update(Wrappers.<TvWindow>update().lambda()
                .eq(TvWindow::getId, tvWindow.getId())
                .set(TvWindow::getCameraId, null));
    }

    private Integer getWinType(String screenType) {
        int type = 0;
        switch (screenType) {
            case "1":
                type = 1;
                break;
            case "2":
                type = 2;
                break;
            case "4":
                type = 3;
                break;
            case "6":
                type = 4;
                break;
            case "8":
                type = 5;
                break;
            case "9":
                type = 6;
                break;
            case "12":
                type = 7;
                break;
            case "16":
                type = 8;
                break;
            case "25":
                type = 9;
                break;
            case "36":
                type = 10;
                break;
            default:
                break;
        }
        if (type == 0) {
            throw new ServiceException(ResultCode.ILLEGAL, "screenType=" + screenType);
        }
        return type;
    }

    private void checkScreenTypeAndWindowsList(Integer screenType, List<AddSceneScreenListWindowsListDto> windowsList) {
        if (!screenType.equals(windowsList.size())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "screenType传参和windowsList数量不匹配...");
        }
        if (screenType != 1 && screenType != 2 && screenType != 4 && screenType != 6 && screenType != 8
                && screenType != 9 && screenType != 12 && screenType != 16 && screenType != 25 && screenType != 36) {
            throw new ServiceException(ResultCode.NOT_EXIST, "screenType传参需为1,2,4,6,8,9,12,16,25,36...");
        }
    }
}
