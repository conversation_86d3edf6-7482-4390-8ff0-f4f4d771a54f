package com.teamway.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.teamway.base.entity.WarningBoard;
import com.teamway.base.mapper.WarningBoardMapper;
import com.teamway.base.service.WarningBoardService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.common.entity.BatchIds;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 首页警告榜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20
 */
@Service
@Slf4j
public class WarningBoardServiceImpl extends ServiceImpl<WarningBoardMapper, WarningBoard>
        implements WarningBoardService {

    /**
     * 添加警告记录
     * @param warningBoard 警告记录信息
     * @return Boolean 添加结果
     */
    @Override
    public Boolean addWarningBoard(WarningBoard warningBoard) {
        return this.save(warningBoard);
    }

    /**
     * 更新警告记录
     * @param warningBoard 警告记录信息
     * @return Boolean 更新结果
     */
    @Override
    public Boolean updateWarningBoard(WarningBoard warningBoard) {
        return this.updateById(warningBoard);
    }

    /**
     * 批量删除警告记录
     * @param request 删除请求参数
     * @return Boolean 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteWarningBoard(BatchIds request) {
        List<Long> ids = request.getIds();
        return this.removeByIds(ids);
    }

    /**
     * 获取警告记录详情
     * @param id 记录ID
     * @return WarningBoard 警告记录详情
     */
    @Override
    public WarningBoard getWarningBoardById(Long id) {
        return this.getById(id);
    }

    /**
     * 获取警告记录列表
     * @param warningType 警告类型，可为空
     * @return List<WarningBoard> 警告记录列表
     */
    @Override
    public List<WarningBoard> getWarningBoardList(Integer warningType) {
        return this.list(new LambdaQueryWrapper<WarningBoard>()
                .eq(WarningBoard::getStatus, 1)
                .eq(warningType != null, WarningBoard::getWarningType, warningType)
                .orderByAsc(WarningBoard::getDisplayOrder));
    }
}
