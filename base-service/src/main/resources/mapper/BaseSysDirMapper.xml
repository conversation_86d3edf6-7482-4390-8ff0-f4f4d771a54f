<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.base.mapper.BaseSysDirMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teamway.base.entity.BaseSysDir">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="dir_name" property="dirName" />
        <result column="dir_code" property="dirCode" />
        <result column="dir_sort" property="dirSort" />
        <result column="dir_type" property="dirType" />
        <result column="dir_remark" property="dirRemark" />
        <collection property="subDirs" ofType="com.teamway.base.entity.BaseSysSubDir">
            <id property="id" column="sub_id"/>
            <result property="dirId" column="dir_id"/>
            <result property="subDirName" column="sub_dir_name"/>
            <result property="subDirCode" column="sub_dir_code"/>
            <result property="subDirNo" column="sub_dir_no"/>
            <result property="subDirColor" column="sub_dir_color"/>
            <result property="subDirSimple" column="sub_dir_simple"/>
            <result property="subDirRemark" column="sub_dir_remark"/>
            <result column="create_time" property="createTime" />
            <result column="update_time" property="updateTime" />
            <result column="create_by" property="createBy" />
            <result column="update_by" property="updateBy" />
        </collection>
    </resultMap>



</mapper>
