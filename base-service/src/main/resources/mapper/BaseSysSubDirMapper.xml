<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.base.mapper.BaseSysSubDirMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teamway.base.entity.BaseSysSubDir">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="dir_id" property="dirId" />
        <result column="sub_dir_no" property="subDirNo" />
        <result column="sub_dir_name" property="subDirName" />
        <result column="sub_dir_code" property="subDirCode" />
        <result column="sub_dir_simple" property="subDirSimple" />
        <result column="sub_dir_color" property="subDirColor" />
        <result column="sub_dir_remark" property="subDirRemark" />
    </resultMap>

</mapper>
