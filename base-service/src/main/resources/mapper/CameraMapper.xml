<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.base.mapper.CameraMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teamway.base.entity.Camera">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="region_id" property="regionId" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="ip" property="ip" />
        <result column="port" property="port" />
        <result column="channel" property="channel" />
        <result column="feature" property="feature" />
        <result column="video_protocol" property="videoProtocol" />
        <result column="factory" property="factory" />
        <result column="main_id" property="mainId" />
        <result column="sub_id" property="subId" />
        <result column="state" property="state" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="absolute_path" property="cameraPath" />
    </resultMap>

    <update id="reflushStatus" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE base_camera
            SET state = #{item.status}
            WHERE
            main_id = #{item.mainId}
            AND sub_id = #{item.subId}
        </foreach>
    </update>

    <select id="getCameraIdByRegionId" resultType="long">
        select id from base_camera where region_id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectListAndRegionPath" resultMap="BaseResultMap">
        SELECT
            bc.id,
            bc.name,
            bc.type,
            bc.region_id,
            bc.username,
            bc.password,
            bc.ip,
            bc.port,
            bc.channel,
            bc.feature,
            bc.video_protocol,
            bc.factory,
            bc.main_id,
            bc.sub_id,
            bc.state,
            bc.create_time,
            bc.update_time,
            bc.create_by,
            bc.update_by,
            br.absolute_path
        FROM
            base_camera bc left join base_region br on bc.region_id = br.id
        <where>
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                ${ew.sqlSegment}
            </if>
        </where>
    </select>
    <select id="selectCameraById" resultMap="BaseResultMap">
        SELECT
        bc.id,
        bc.name,
        bc.type,
        bc.region_id,
        bc.username,
        bc.password,
        bc.ip,
        bc.port,
        bc.channel,
        bc.feature,
        bc.video_protocol,
        bc.factory,
        bc.main_id,
        bc.sub_id,
        bc.state,
        bc.create_time,
        bc.update_time,
        bc.create_by,
        bc.update_by,
        br.absolute_path
        FROM
        base_camera bc left join base_region br on bc.region_id = br.id
    </select>

    <select id="countGroupByRegion" resultType="com.teamway.base.dto.camera.CameraCountDto">
        SELECT region_id regionId,count(1) num FROM base_camera GROUP BY region_id
    </select>

</mapper>
