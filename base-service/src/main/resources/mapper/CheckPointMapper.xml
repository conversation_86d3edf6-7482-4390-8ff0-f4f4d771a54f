<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.base.mapper.CheckPointMapper">

    <select id="listCheckPoint" resultType="com.teamway.base.vo.CheckPointVo">
        select
            bcp.point_name AS pointName,
            br.name AS name,
            ind.device_name AS deviceName,
            bcp.camera_id AS cameraId,
            bcp.update_time AS updateTime,
            bcp.update_by AS updateBy
        FROM
            base_check_point bcp
                LEFT JOIN base_region br on
                bcp.region_id = br.id
                LEFT JOIN ins_device ind on
                bcp.ins_device_id = ind.id
    </select>
</mapper>