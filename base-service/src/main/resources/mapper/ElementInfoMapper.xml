<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.base.mapper.ElementInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teamway.base.entity.ElementInfo">
        <id column="cameraName" property="name"/>
        <result column="type" property="type"/>
        <result column="object_id" property="objectId"/>
        <result column="position_x" property="posX"/>
        <result column="position_y" property="posY"/>
        <result column="create_time" property="updateTime"/>
        <result column="update_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>


    <select id="findByParam" resultMap="BaseResultMap">
        select m.*,c.name cameraName from map m INNER JOIN base_camera c on m.object_id = c.id
        <where>
            <if test="regionId!= null and regionId != 0">
                and c.region_id = #{regionId}
            </if>
        </where>
    </select>
</mapper>