<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.base.mapper.PollCameraMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teamway.base.entity.PollCamera">
        <id column="camera_preset_id" property="cameraPresetId"/>
        <result column="camera_id" property="cameraId"/>
        <result column="poll_id" property="pollId"/>
        <result column="sort" property="sort"/>
        <result column="name" property="cameraName"/>
        <result column="main_id" property="mainId"/>
        <result column="sub_id" property="subId"/>
        <result column="cp_name" property="cameraPresetName"/>
        <result column="no" property="no"/>
        <result column="state" property="state"/>
        <result column="video_protocol" property="videoProtocol"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="id" property="id"/>
    </resultMap>


    <select id="findPollCamera" resultMap="BaseResultMap">
        SELECT pc.id,
               pc.camera_id,
               pc.camera_preset_id,
               pc.sort,
               pc.poll_id,
               c.NAME,
               c.main_id,
               c.sub_id,
               c.state,
               c.video_protocol,
               CASE
                   WHEN pc.camera_preset_id = -1 THEN '默认预置位'
                   ELSE cp.NAME
                   END AS cp_name,
               cp.no,
               br.absolute_path    regionName,
               pc.create_time,
               pc.create_by,
               pc.update_time,
               pc.update_by
        FROM base_poll p
                 INNER JOIN base_poll_camera pc ON pc.poll_id = p.id
                 INNER JOIN base_camera c ON c.id = pc.camera_id
                 LEFT JOIN base_camera_preset cp ON cp.id = pc.camera_preset_id
                 LEFT JOIN base_region br ON br.id = c.region_id
        WHERE p.id = #{poll_id}
        order by sort asc
    </select>
</mapper>