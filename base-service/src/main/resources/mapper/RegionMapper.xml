<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.base.mapper.RegionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teamway.base.entity.Region">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="pid" property="pid" />
        <result column="image_url" property="imageUrl" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <sql id="Base_Column_List">
        id,region_name,pid,
        create_time,create_by,update_time,
        update_by
    </sql>

</mapper>
