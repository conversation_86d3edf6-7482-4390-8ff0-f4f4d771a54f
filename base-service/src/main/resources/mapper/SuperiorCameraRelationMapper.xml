<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.base.mapper.SuperiorCameraRelationMapper">

    <select id="selectPageList"  resultType="com.teamway.base.entity.SuperiorCameraRelation">
        SELECT
            bsuc.*,
            bc.`name`,
            bc.ip
        FROM
            base_superior_camera_relation bsuc
        LEFT JOIN base_camera bc
            ON bsuc.camera_id = bc.id
        <where>
            <if test="ew.sqlSegment != null">
                ${ew.sqlSegment}
            </if>
        </where>
    </select>

    <select id="selectAll"  resultType="com.teamway.base.entity.SuperiorCameraRelation">
        SELECT
            bsuc.*,
            bc.`name`,
            bc.ip,
            bc.main_id,
            bc.sub_id
        FROM
            base_superior_camera_relation bsuc
        LEFT JOIN base_camera bc
            ON bsuc.camera_id = bc.id
        <where>
            <if test="ew.sqlSegment != null">
                ${ew.sqlSegment}
            </if>
        </where>
    </select>

</mapper>