<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.base.mapper.TemperatureDrawLineMapper">

    <resultMap id="BaseResultMap" type="com.teamway.base.entity.TemperatureDrawLine">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="cameraId" column="camera_id" jdbcType="VARCHAR"/>
            <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="coordinate" column="coordinate" jdbcType="VARCHAR"/>
            <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
            <result property="thresholdValue" column="threshold_value" jdbcType="DOUBLE"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,camera_id,image_url,
        type,coordinate,rule_name,
        threshold_value,create_time,create_user,
        modify_time,modify_user
    </sql>
    <select id="listThresholdValueCamera" resultType="com.teamway.base.dto.temperature.DrawLineCameraDto">
        SELECT
            trl.id,
            trl.purpose,
            trl.rule_name,
            trl.warn_report_enable,
            trl.preset_id,
            trl.rule_id,
            trl.threshold_value,
            c.ip,
            c.username,
            c.password,
            c.port,
            c.id as camera_id,
            c.name as camera_name
        FROM
            base_temperature_draw_line trl,
            base_camera c
        WHERE
            trl.camera_id = c.id and trl.warn_report_enable=1 and c.status=1
        ORDER BY
            camera_id DESC
    </select>
</mapper>
