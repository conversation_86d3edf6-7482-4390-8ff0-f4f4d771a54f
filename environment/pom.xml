<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.teamway</groupId>
        <artifactId>businessServer-hm</artifactId>
        <version>2.0.1.RELEASE</version>
    </parent>
    <artifactId>environment-hm</artifactId>
    <name>environment-hm</name>
    <dependencies>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>common-hm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>mqtt</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>exception</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.teamway.base</groupId>
            <artifactId>baseService-server</artifactId>
            <version>${base-service.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.greenrobot</groupId>
            <artifactId>eventbus</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>
