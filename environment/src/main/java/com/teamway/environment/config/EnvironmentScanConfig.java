package com.teamway.environment.config;

import jakarta.annotation.PostConstruct;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 */
@MapperScan("com.teamway.environment.mapper")
@ComponentScan("com.teamway.environment")
public class EnvironmentScanConfig {
    @PostConstruct
    public void init() {
        System.out.println("EnvironmentScanConfig init");
    }
}
