package com.teamway.environment.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DeviceControlDto {

    private Long id;

    /**
     * 1 开 0 关
     */
    private String state;


    /**
     * 空调命令
     *
     * 关机发射 （送给设备时传中文）
     * 制冷开机发射 （送给设备时传中文）
     * 1-制冷，18，左右扫风，风速大
     * 2-制冷，20，左右扫风，风速大
     * 3-制冷，22，左右扫风，风速大
     * 4-制冷，24，左右扫风，风速大
     * 5-制冷，26，左右扫风，风速大
     * 6-制冷，18，左右扫风，风速中
     * 7-制冷，20，左右扫风，风速中
     * 8-制冷，22，左右扫风，风速中
     * 9-制冷，24，左右扫风，风速中
     * 10-制冷，26，左右扫风，风速中
     * 11-制冷，18，左右扫风，风速小
     * 12-制冷，20，左右扫风，风速小
     * 13-制冷，22，左右扫风，风速小
     * 14-制冷，24，左右扫风，风速小
     * 15-制冷，26，左右扫风，风速小
     * 16-除湿，18，左右扫风
     * 17-除湿，20，左右扫风
     * 18-除湿，22，左右扫风
     * 19-除湿，24，左右扫风
     * 20-除湿，26，左右扫风
     */
    private Integer airconditionConfigOrder;


    private String deviceCode;

    private String szID;


    private Float temp;

    private Float humidity;

    private String type;



}
