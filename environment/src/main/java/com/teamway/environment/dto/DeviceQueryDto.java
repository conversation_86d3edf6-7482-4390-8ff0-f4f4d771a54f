package com.teamway.environment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceQueryDto {


    /**
     * 设备类型
     * 1 动环设备类型  包含类型有 （1 温度传感器 2 水浸传感器 小类）
     * 2 远控设备类型 包含类型有 （3 空调 4 排风扇 5 抽湿 6 灯光控制 7 水泵 小类）
     * 3 消防设备类型 包含类型有 （8 烟感）
     */
    private String type;

    /**
     * 区域ID
     */

    private Long regionId;

}
