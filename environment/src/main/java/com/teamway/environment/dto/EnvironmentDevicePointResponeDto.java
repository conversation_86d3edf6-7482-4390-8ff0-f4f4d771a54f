package com.teamway.environment.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class EnvironmentDevicePointResponeDto {

    /**
     * 点位类型，0-未知、1-所有类型、2-模拟量输入(数值型数据，例如温湿度)、3-开关量输入(开或者关，1表示开或0表示关，例如水浸)、 4-模拟量输出 5-开关量输出
     */
    private String emType;

    /**
     * 监测点位ID
     */
    private String szID;

    /**
     * 值
     */
    private String value;

    /**
     * 数据状态, -1:未知, 0:正常, 1:1级告警, 2:2级告警, 3:3级告警, 4:4级告警, 5:操作事件, 6:无效数据。
     */
    private Integer nAlarmLevel;


    /**
     * 点位名称
     */
    private String szPointName;

    /**
     * 时间
     */
    private String time;


    /**
     * 设备编码
     */
    private String deviceId;

}
