package com.teamway.environment.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MqttBaseResDto {
    private Integer code;
    private String message;

    public static MqttBaseResDto fail() {
        return new MqttBaseResDto(9999, "失败");
    }

    public static MqttBaseResDto fail(String msg) {
        return new MqttBaseResDto(9999, msg);
    }

    public static MqttBaseResDto success() {
        return new MqttBaseResDto(0, "成功");
    }
}
