package com.teamway.environment.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MqttGetDeviceListDto extends MqttBaseResDto {
    private List<GetDeviceListDto> data;
    public static MqttGetDeviceListDto success(List<GetDeviceListDto> data) {
        MqttGetDeviceListDto deviceListDto =new MqttGetDeviceListDto();
        deviceListDto.setCode(0);
        deviceListDto.setMessage("成功");
        deviceListDto.setData(data);
        return deviceListDto;
    }
}
