package com.teamway.environment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("aircondition_config")
public class AirconditionConfig {

    private Long id;
    /**
     * 模式：1 制冷、2 制热、3 除湿、4 送风（空调）
     */
    private String mode;

    /**
     * 左右摆风（开/关）：1 开 0 关（空调）
     */
    private String leftAndRightWind;

    /**
     * 当前设置温度（空调）
     */
    private Float currentTmp;

    /**
     * 当前设置风速（空调）
     */
    private Float currentWindSpeed;


}
