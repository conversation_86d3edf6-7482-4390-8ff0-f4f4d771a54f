package com.teamway.environment.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("device")
public class Device extends BaseEntity<Device> {
    /**
     * 告警名称
     */
    private String name;


    /**
     * 设备类型 1 温度传感器 2 水浸传感器 3 空调 4 排风扇 5 抽湿 6 灯光控制 7 水泵 8 烟感
     */
    private String type;

    /**
     * 温度/室内温度/送风口温度
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Float temp;


    /**
     * 湿度/室内湿度/送风口湿度
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Float humidity;


    /**
     * 区域ID
     */

    private Long regionId;


    /**
     * 设备唯一性编码
     */
    private String deviceCode;



    /**
     * 运行状态：1 告警 0 正常
     */
    private String runState;




    /**
     * 水浸告警： 1 告警 0 正常
     */
    private String waterImmersionAlarm;



    /**
     * 上下扫风（开/关）：1 开 0 关（空调）
     */
    private String upAndDownWind;



    /**
     * 故障状态：1 告警 0 正常
     */

    private String faultState;

    /**
     * 水位高告警：1 告警 0 正常
     */

    private String highWaterLevel;
    /**
     * 水位低告警：1 告警 0 正常
     */

    private String lowWaterLevel;
    /**
     * 漏水告警：1 告警 0 正常
     */

    private String leakage;
    /**
     * 其它告警：1 告警 0 正常
     */

    private String other;



    /**
     * 烟感告警：1 告警 0 正常
     */

    private String smokeDetectorState;

    /**
     * 1 开 0 关
     */
    private String state;


    /**
     * 变电站Id
     */

    private String siteId;

    /**
     * 空调命令
     *
     * 关机发射 （送给设备时传中文）
     * 制冷开机发射 （送给设备时传中文）
     * 1-制冷，18，左右扫风，风速大
     * 2-制冷，20，左右扫风，风速大
     * 3-制冷，22，左右扫风，风速大
     * 4-制冷，24，左右扫风，风速大
     * 5-制冷，26，左右扫风，风速大
     * 6-制冷，18，左右扫风，风速中
     * 7-制冷，20，左右扫风，风速中
     * 8-制冷，22，左右扫风，风速中
     * 9-制冷，24，左右扫风，风速中
     * 10-制冷，26，左右扫风，风速中
     * 11-制冷，18，左右扫风，风速小
     * 12-制冷，20，左右扫风，风速小
     * 13-制冷，22，左右扫风，风速小
     * 14-制冷，24，左右扫风，风速小
     * 15-制冷，26，左右扫风，风速小
     * 16-除湿，18，左右扫风
     * 17-除湿，20，左右扫风
     * 18-除湿，22，左右扫风
     * 19-除湿，24，左右扫风
     * 20-除湿，26，左右扫风
     */
    private Integer airconditionConfigOrder;





    private Long cameraId;



}
