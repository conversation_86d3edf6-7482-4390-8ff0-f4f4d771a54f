package com.teamway.environment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_env_device_alarm")
public class DeviceAlarm extends BaseEntity<DeviceAlarm> {
    /**
     * device表ID
     */
    private Long deviceId;

    /**
     *
     * 告警描述
     *
     */
    private String describe;

}
