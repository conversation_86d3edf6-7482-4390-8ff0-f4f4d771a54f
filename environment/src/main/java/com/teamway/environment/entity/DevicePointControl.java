package com.teamway.environment.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_env_device_point_control")
public class DevicePointControl {

    @TableField(value = "id", fill = FieldFill.INSERT)
    private Long id;

    /**
     * 对应设备表ID
     */
    private Long deviceId;

    /**
     * 监测点位ID
     */
    private String szId;



}
