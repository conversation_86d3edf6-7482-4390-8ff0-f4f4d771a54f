package com.teamway.environment.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 动环关联摄像机
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("b_env_donghuan_camera")
public class DonghuanCamera extends BaseEntity<DonghuanCamera> {

    private static final long serialVersionUID = 1L;

    /**
     * 动环id
     */
    @TableField("donghuan_device_id")
    private Long donghuanDeviceId;

    /**
     * 动环名称
     */
    @TableField(exist = false)
    private String donghuanDeviceName;

    /**
     * 摄像机id
     */
    @TableField("camera_id")
    private Long cameraId;

    /**
     * 摄像机名称
     */
    @TableField(exist = false)
    private String cameraName;

    /**
     * 摄像机预置位id
     */
    @TableField("camera_preset_id")
    private Long cameraPresetId;

    /**
     * 摄像机预置位名称
     */
    @TableField(exist = false)
    private String cameraPresetName;

}
