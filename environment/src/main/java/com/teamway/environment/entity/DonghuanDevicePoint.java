package com.teamway.environment.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 动环设备关联点位表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("donghuan_device_point")
public class DonghuanDevicePoint extends BaseEntity<DonghuanDevicePoint> {

    private static final long serialVersionUID = 1L;

    /**
     * 动环设备id
     */
    @TableField("donghuan_device_id")
    private Long donghuanDeviceId;

    /**
     * 点位编码
     */
    @TableField("point_id")
    private String pointId;

    /**
     * 点位名称
     */
    @TableField("point_name")
    private String pointName;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;
    /**
     * 点位备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 告警等级
     */
    @TableField("alarm_level")
    private Integer alarmLevel;

    /**
     * 点位类型  1-温度 2-湿度 3-风速 4-气压 7-PV 15-水浸距离 16-漏水绳线长 -遥测
     */
    @TableField("type")
    private String type;

    /**
     * 告警下限  -遥测
     */
    @TableField("alarm_low_limit")
    private Float alarmLowLimit;

    /**
     * 告警上限  -遥测
     */
    @TableField("alarm_up_limit")
    private Float alarmUpLimit;

    /**
     * 预警下限   -遥测
     */
    @TableField("warn_low_limit")
    private Float warnLowLimit;

    /**
     * 预警上限   -遥测
     */
    @TableField("warn_up_limit")
    private Float warnUpLimit;

    @TableField("value")
    private String value;

    @TableField("alarm_flag")
    private String alarmFlag;
}
