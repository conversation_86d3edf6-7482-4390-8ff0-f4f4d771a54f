package com.teamway.environment.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import com.teamway.common.valid.annotation.IP;
import com.teamway.common.valid.annotation.SpecialCharacters;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("b_env_donghuan_host")
public class DonghuanHost extends BaseEntity<DonghuanHost> {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @Length(max = 30, message = "名称长度小于30个字符")
    @SpecialCharacters(message = "名称不能包含特殊字符", ignoreEmptyStr = true)
    @TableField("name")
    private String name;

    /**
     * 厂家 1 海康 2 大华 3 华为
     */
    @TableField("factory")
    @NotBlank(message = "厂家不能为空")
    @Pattern(regexp = "1|2|3", message = "厂家类型错误")
    private String factory;

    /**
     * ip地址
     */
    @TableField("ip")
    @NotBlank(message = "ip不能为空")
    @IP(ignoreEmptyStr = true)
    private String ip;

    /**
     * 端口
     */
    @Range(min = 0, max = 65535, message = "端口错误-范围0-65535之间")
    @TableField("port")
    private String port;

    /**
     * 用户名
     */
    @TableField("username")
    @NotBlank(message = "用户名不能为空")
    @Length(max = 30, message = "用户名长度小于30个字符")
    private String username;

    /**
     * 密码
     */
    @TableField("password")
    @NotBlank(message = "密码不能为空")
    @Length(max = 30, message = "密码长度小于30个字符")
    private String password;

    @TableField("region_id")
    private Long regionId;
    
    @TableField(exist = false)
    private String regionName;

    /**
     * 在线状态，1在线，0不在线
     */
    @TableField("state")
    private String state;

}
