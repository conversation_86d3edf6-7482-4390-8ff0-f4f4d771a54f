package com.teamway.environment.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 动环主机配置信息
 * <AUTHOR>
 */
@Data
@TableName("b_env_environment_device_host")
public class EnvironmentDeviceHost {


    @TableField(value = "id", fill = FieldFill.INSERT)
    private Long id;

    /**
     * 登录名称
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 端口
     */
    private Integer port;


    /**
     * IP地址
     */
    private String ip;
}
