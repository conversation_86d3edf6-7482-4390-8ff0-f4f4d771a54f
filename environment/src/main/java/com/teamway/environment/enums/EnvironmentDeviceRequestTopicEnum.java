package com.teamway.environment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum EnvironmentDeviceRequestTopicEnum {
    /**
     * 下行动环注册并获取传感器信息列表
     */
    deviceGetRequest("/v1_0/environment/device/get/request/dh/", "请求获取传感器信息列表"),

    /**
     * 下行根据传感器ID获取传感器点位信息列表
     */
    devicePointGetRequest("/v1_0/environment/point/get/request/dh/", "下行根据传感器ID获取传感器点位信息列表"),

    /**
     * 下行控制点位
     */
    devicePointControlGetRequest("/v1_0/environment/point/control/request/dh/", "下行控制点位")

    ;
    private String topic;
    private String desc;
}
