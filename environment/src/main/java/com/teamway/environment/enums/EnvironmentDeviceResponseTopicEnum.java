package com.teamway.environment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @noinspection ALL
 */

@Getter
@AllArgsConstructor
public enum EnvironmentDeviceResponseTopicEnum {
    response("/v1_0/environment/", "统一主题"),
    /**
     * 上行动环注册并获取传感器信息列表
     */
    deviceGetResponse("/v1_0/environment/device/get/response/dh/", "返回传感器信息列表"),

    devicePointGetResponse("/v1_0/environment/point/get/response/dh/", "上行根据传感器ID获取传感器点位信息列表"),

    devicePointControlGetResponse("/v1_0/environment/point/control/response/dh/", "上行控制点位"),

    devicePointPointAlarm("/v1_0/environment/point/alarm/response/dh/", "上行点位告警上报"),



    ;
    private String topic;
    private String desc;
}
