package com.teamway.environment.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @noinspection ALL
 */

public enum EnvironmentDeviceType {

    temperatureSensor("1", "温度传感器"),
    waterSensor("2", "水浸传感器"),
    aircondition("3", "空调"),
    exhaustFan("4", "排风扇"),
    dehumidify("5", "抽湿"),
    lightControl("6", "灯光控制"),
    waterPump("7", "水泵"),
    smokeDetector("8", "烟感");
    @Getter
    private String value;
    @Getter
    private String desc;

    EnvironmentDeviceType(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
