package com.teamway.environment.properties;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Component
@ConfigurationProperties(prefix = "aircondition")
public class AirconditionProperties {
   private String airconditionOrder;
   private String airconditionClose;
   private String airconditionOpen;
   private String temp;
   private String humidity;
}
