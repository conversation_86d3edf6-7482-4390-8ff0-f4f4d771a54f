package com.teamway.environment.properties;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Component
@ConfigurationProperties(prefix = "dehumidify")
public class DehumidifyProperties {
   private String temp;
   private String humidity;
   private String malfunction;
   private String highWaterLevelAlarm;
   private String lowWaterLevelAlarm;
   private String leakageAlarm;
   private String otherAlarm;
   private String state;
}
