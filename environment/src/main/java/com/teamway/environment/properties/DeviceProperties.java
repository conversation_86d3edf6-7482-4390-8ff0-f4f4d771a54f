package com.teamway.environment.properties;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Component
@ConfigurationProperties(prefix = "device")
public class DeviceProperties {
   private String airconditionName;
   private String waterSensorName;
   private String temperatureSensorName;
}
