package com.teamway.environment.properties;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Component
@ConfigurationProperties(prefix = "temperaturesensor")
public class TemperatureSensorProperties {
   private String state;
   private String temp;
   private String humidity;
}
