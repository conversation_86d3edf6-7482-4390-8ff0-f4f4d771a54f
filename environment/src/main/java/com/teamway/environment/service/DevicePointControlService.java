package com.teamway.environment.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.environment.entity.DevicePointControl;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DevicePointControlService extends IService<DevicePointControl> {

    /**
     * 通过设备id查询列表
     * @param deviceId
     * @return
     */
    List<DevicePointControl> selectByDeviceId(Long deviceId);

    /**
     * 通过设备id和监测id查询
     * @param deviceId
     * @param szId
     * @return
     */
    DevicePointControl selectByDeviceIdAndSzId(Long deviceId,String szId);


}
