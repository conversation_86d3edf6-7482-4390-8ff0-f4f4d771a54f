package com.teamway.environment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.base.entity.Camera;
import com.teamway.environment.entity.DonghuanCamera;

import java.util.List;

/**
 * <p>
 * 动环关联摄像机 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
public interface DonghuanCameraService extends IService<DonghuanCamera> {

    /**
     * 根据动环id查询动环关联摄像机
     *
     * @param id
     * @return
     */
    List<DonghuanCamera> getByDonghuanId(Long id);


    /**
     * 根据动环id查询动环关联摄像机信息
     *
     * @param id
     * @return
     */
    List<Camera> getCameraByDonghuanId(Long id);




}
