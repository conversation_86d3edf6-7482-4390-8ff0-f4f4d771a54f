package com.teamway.environment.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.environment.entity.DonghuanDevicePoint;

import java.util.List;

/**
 * <p>
 * 动环设备关联点位表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface DonghuanDevicePointService extends IService<DonghuanDevicePoint> {
    /**
     * 根据设备id获取设备点位
     *
     * @param id
     * @return
     */
    List<DonghuanDevicePoint> getByDevice(Long id);

}
