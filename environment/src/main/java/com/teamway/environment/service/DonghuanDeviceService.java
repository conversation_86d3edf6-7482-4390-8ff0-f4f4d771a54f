package com.teamway.environment.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.common.entity.PageModel;
import com.teamway.environment.dto.DeviceControlDto;
import com.teamway.environment.dto.DeviceQueryDto;
import com.teamway.environment.dto.GetDonghuanDevicesDto;
import com.teamway.environment.dto.ResponeDto;
import com.teamway.environment.entity.DonghuanDevice;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface DonghuanDeviceService extends IService<DonghuanDevice> {

    /**
     *查询动环设备信息
     * @param getDonghuanDevicesDto
     * @return
     */
    PageModel<List<DonghuanDevice>> getDonghuanDevices(GetDonghuanDevicesDto getDonghuanDevicesDto);

    /**
     * 查询设备信息
     * @param deviceQueryDto
     * @return
     */
    public List selectDevice(DeviceQueryDto deviceQueryDto);

    /**
     * 设置属性
     * @param deviceControlDto
     */
    public void propertySet(DeviceControlDto deviceControlDto);

    /**
     * 同步设备点位
     * @param responeDto
     */
    public void syncDevicePoint(ResponeDto responeDto);

    /**
     * 告警
     * @param deviceCode
     * @param alarm
     * @param pointName
     */
    public void alarm(String deviceCode, String alarm, String pointName);
}
