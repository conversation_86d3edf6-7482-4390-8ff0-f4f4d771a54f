package com.teamway.environment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.common.entity.PageModel;
import com.teamway.environment.dto.GetDonghuanHostsDto;
import com.teamway.environment.entity.DonghuanHost;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface DonghuanHostService extends IService<DonghuanHost> {

    /**
     * 获得东环主机
     * @param getDonghuanHostsDto
     * @return
     */
    PageModel<List<DonghuanHost>> getDonghuanHosts(GetDonghuanHostsDto getDonghuanHostsDto);

}
