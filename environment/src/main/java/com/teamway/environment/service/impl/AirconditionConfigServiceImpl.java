package com.teamway.environment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.environment.entity.AirconditionConfig;
import com.teamway.environment.mapper.AirconditionConfigMapper;
import com.teamway.environment.service.AirconditionConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AirconditionConfigServiceImpl extends ServiceImpl<AirconditionConfigMapper, AirconditionConfig> implements AirconditionConfigService {

    @Override
    public List<AirconditionConfig> selectAirconditionConfig() {
        return this.baseMapper.selectList(null);
    }
}
