package com.teamway.environment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.environment.entity.DeviceAlarm;
import com.teamway.environment.mapper.DeviceAlarmMapper;
import com.teamway.environment.service.DeviceAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeviceAlarmServiceImpl extends ServiceImpl<DeviceAlarmMapper, DeviceAlarm> implements DeviceAlarmService {

    @Override
    public void add(DeviceAlarm deviceAlarm) {
        this.save(deviceAlarm);
    }
}
