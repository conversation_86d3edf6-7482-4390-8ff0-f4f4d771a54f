package com.teamway.environment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.environment.entity.DevicePointControl;
import com.teamway.environment.mapper.DevicePointControlMapper;
import com.teamway.environment.service.DevicePointControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DevicePointControlServiceImpl extends ServiceImpl<DevicePointControlMapper, DevicePointControl> implements DevicePointControlService {

    @Override
    public List<DevicePointControl> selectByDeviceId(Long deviceId) {
        return lambdaQuery().eq(DevicePointControl::getDeviceId,deviceId).list();
    }

    @Override
    public DevicePointControl selectByDeviceIdAndSzId(Long deviceId, String szId) {
        return lambdaQuery().eq(DevicePointControl::getDeviceId,deviceId).eq(DevicePointControl::getSzId,szId).one();
    }

}
