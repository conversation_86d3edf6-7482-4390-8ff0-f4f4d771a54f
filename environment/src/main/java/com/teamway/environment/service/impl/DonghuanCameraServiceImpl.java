package com.teamway.environment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.entity.Camera;
import com.teamway.base.service.CameraPresetService;
import com.teamway.base.service.CameraService;
import com.teamway.environment.entity.DonghuanCamera;
import com.teamway.environment.mapper.********************;
import com.teamway.environment.service.DonghuanCameraService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 动环关联摄像机 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Service
public class DonghuanCameraServiceImpl extends ServiceImpl<********************, DonghuanCamera> implements DonghuanCameraService {


    @Autowired
    @Lazy
    private CameraService cameraService;
    @Autowired
    @Lazy
    private CameraPresetService cameraPresetService;

    /**
     * 根据动环id查询动环关联摄像机
     *
     * @param id
     * @return
     */
    @Override
    public List<DonghuanCamera> getByDonghuanId(Long id) {
        List<DonghuanCamera> donghuanCameraList = this.list(new LambdaQueryWrapper<DonghuanCamera>().eq(DonghuanCamera::getDonghuanDeviceId, id));
        donghuanCameraList.forEach(donghuanCamera -> { });
        return donghuanCameraList;
    }

    /**
     * 根据动环id查询动环关联摄像机信息
     *
     * @param id
     * @return
     */
    @Override
    public List<Camera> getCameraByDonghuanId(Long id) {
        List<Camera> cameraList = new ArrayList<>();
        List<DonghuanCamera> donghuanCameraList = this.list(new LambdaQueryWrapper<DonghuanCamera>().eq(DonghuanCamera::getDonghuanDeviceId, id));
        donghuanCameraList.forEach(donghuanCamera -> {});
        return cameraList;
    }
}
