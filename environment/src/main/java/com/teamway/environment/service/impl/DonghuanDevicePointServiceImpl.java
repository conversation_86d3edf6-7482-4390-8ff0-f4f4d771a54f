package com.teamway.environment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.environment.entity.DonghuanDevicePoint;
import com.teamway.environment.mapper.DonghuanDevicePointMapper;
import com.teamway.environment.service.DonghuanDevicePointService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 动环设备关联点位表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Service
public class DonghuanDevicePointServiceImpl extends ServiceImpl<DonghuanDevicePointMapper, DonghuanDevicePoint> implements DonghuanDevicePointService {
    /**
     * 根据设备id获取设备点位
     *
     * @param id
     * @return
     */
    @Override
    public List<DonghuanDevicePoint> getByDevice(Long id) {
        LambdaQueryWrapper<DonghuanDevicePoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DonghuanDevicePoint::getDonghuanDeviceId, id);
        return this.list(queryWrapper);
    }
}
