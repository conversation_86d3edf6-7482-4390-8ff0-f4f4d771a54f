package com.teamway.environment.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.common.constant.SwitchConstant;
import com.teamway.base.entity.Region;
import com.teamway.base.service.RegionService;
import com.teamway.common.entity.PageModel;
import com.teamway.environment.constant.DeviceType;
import com.teamway.environment.dto.*;
import com.teamway.environment.entity.DeviceAlarm;
import com.teamway.environment.entity.DevicePointControl;
import com.teamway.environment.entity.DonghuanDevice;
import com.teamway.environment.entity.DonghuanHost;
import com.teamway.environment.enums.EnvironmentDeviceRequestTopicEnum;
import com.teamway.environment.enums.EnvironmentDeviceType;
import com.teamway.environment.mapper.DonghuanDeviceMapper;
import com.teamway.environment.properties.*;
import com.teamway.environment.service.*;
import com.teamway.exception.ServiceException;
import com.teamway.mqtt.MqttUtils;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Service
@Slf4j
public class DonghuanDeviceServiceImpl extends ServiceImpl<DonghuanDeviceMapper, DonghuanDevice> implements DonghuanDeviceService {
    @PostConstruct
    public void init() {
        System.out.println("==============com.teamway.environment.mapper.DonghuanDeviceMapper==========");
    }
    @Autowired
    @Lazy
    private RegionService regionService;
    @Autowired
    private DonghuanHostService donghuanHostService;

    @Autowired
    private DevicePointControlService devicePointControlService;

    @Autowired
    private AirconditionProperties airconditionProperties;

    @Autowired
    private DonghuanCameraService donghuanCameraService;
    @Autowired
    private WaterSensorProperties waterSensorProperties;
    @Autowired
    private ExhaustFanProperties exhaustFanProperties;
    @Autowired
    private LightControlProperties lightControlProperties;
    @Autowired
    private WaterPumpProperties waterPumpProperties;
    @Autowired
    private TemperatureSensorProperties temperatureSensorProperties;

    @Autowired
    private DehumidifyProperties dehumidifyProperties;

    @Autowired
    private DeviceAlarmService deviceAlarmService;

    @Override
    public PageModel<List<DonghuanDevice>> getDonghuanDevices(GetDonghuanDevicesDto getDonghuanDevicesDto) {
        LambdaQueryWrapper<DonghuanDevice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(getDonghuanDevicesDto.getName()), DonghuanDevice::getName, getDonghuanDevicesDto.getName());
        queryWrapper.eq(StrUtil.isNotBlank(getDonghuanDevicesDto.getRegionId()), DonghuanDevice::getRegionId, getDonghuanDevicesDto.getRegionId());
        Page<DonghuanDevice> page = new Page<>(getDonghuanDevicesDto.getPageIndex(), getDonghuanDevicesDto.getPageSize());
        baseMapper.selectPage(page, queryWrapper);
        List<DonghuanDevice> donghuanDevices = page.getRecords();
        for (DonghuanDevice donghuanDevice : donghuanDevices) {
            if (donghuanDevice.getRegionId() != null) {
                Region byId = regionService.getById(donghuanDevice.getRegionId());
                if (byId != null) {
                    donghuanDevice.setRegion(byId.getName());
                }
            }
            if (donghuanDevice.getDonghuanHostId() != null) {
                DonghuanHost byId = donghuanHostService.getById(donghuanDevice.getDonghuanHostId());
                if (byId != null) {
                    donghuanDevice.setDonghuanHostName(byId.getName());
                }
            }
        }
        PageModel<List<DonghuanDevice>> pageModel = new PageModel<>();
        pageModel.setCount(page.getTotal());
        pageModel.setData(donghuanDevices);
        return pageModel;
    }

    @Override
    public List selectDevice(DeviceQueryDto deviceQueryDto) {
        Map<Object,List<DonghuanDevice>> map = new HashMap<Object,List<DonghuanDevice>>();
        LambdaQueryWrapper<DonghuanDevice> queryWrapper = new LambdaQueryWrapper<>();

        if(ObjectUtil.isNotNull(deviceQueryDto.getRegionId())){
            List<Long> list = regionService.getChildId(deviceQueryDto.getRegionId());
            queryWrapper.in(DonghuanDevice::getRegionId,list);
        }
        List<String> types = new ArrayList();
        if(DeviceType.TEMPERATURE_SENSOR.equals(deviceQueryDto.getType())){
            types.add(DeviceType.TEMPERATURE_SENSOR);
            types.add(DeviceType.WATER_SENSOR);
        }else if(DeviceType.WATER_SENSOR.equals(deviceQueryDto.getType())){
            types.add(DeviceType.AIR_CONDITIONER);
            types.add(DeviceType.EXHAUST_FAN);
            types.add(DeviceType.DEHUMIDIFICATION);
            types.add(DeviceType.LIGHTING_CONTROL);
            types.add(DeviceType.WATER_PUMP);
        }else if(DeviceType.AIR_CONDITIONER.equals(deviceQueryDto.getType())){
            types.add("8");
        }
        queryWrapper.in(CollectionUtil.isNotEmpty(types),DonghuanDevice::getType,types);

        List<DonghuanDevice> devices = this.list(queryWrapper);
        if(CollectionUtil.isNotEmpty(devices)){
            for(DonghuanDevice device:devices){
                if(map.containsKey(device.getRegionId())){
                    map.get(device.getRegionId()).add(device);
                }else{
                    List list = new ArrayList();
                    list.add(device);
                    map.put(device.getRegionId(),list);
                }
                List cameras = donghuanCameraService.getCameraByDonghuanId(device.getId());
                if(CollUtil.isNotEmpty(cameras)){
                    device.setCameras(cameras);
                }
            }
        }
        List resultList = new ArrayList();
        for(Object key:map.keySet()){
            DeviceResultDto deviceResultDto = new DeviceResultDto();
            Region region = regionService.getById((Long)key);
            if(ObjectUtil.isNull(region)){
                throw new ServiceException((Long)key+"未找到区域信息");
            }
            deviceResultDto.setRegion(region);
            List list = map.get(key);
            deviceResultDto.getDevices().addAll(list);
            resultList.add(deviceResultDto);
        }
        return resultList;
    }



    @Override
    public void propertySet(DeviceControlDto deviceControlDto) {
        if(ObjectUtil.isNull(deviceControlDto.getId())){
            throw new ServiceException("设置设备属性ID不能为空");
        }
        DonghuanDevice temp = this.getById(deviceControlDto.getId());
        if(ObjectUtil.isNull(temp)){
            throw new ServiceException("未找到设备信息");
        }
        deviceControlDto.setDeviceCode(temp.getDeviceCode());
        deviceControlDto.setType(temp.getType());
        List<DevicePointControl> devicePointControl = devicePointControlService.selectByDeviceId(temp.getId());
        if(CollUtil.isEmpty(devicePointControl)){
            throw new ServiceException("该设备没有控制点位，无法控制");
        }
        deviceControlDto.setSzID(devicePointControl.get(0).getSzId());
        String state = deviceControlDto.getState();
        SealHeadControlDto sealHeadControlDto = new SealHeadControlDto();
        sealHeadControlDto.setDeviceId(temp.getDeviceCode());
        sealHeadControlDto.setSzID(deviceControlDto.getSzID());
        if(EnvironmentDeviceType.aircondition.getValue().equals(temp.getType())){
            Integer airconditionConfigOrder = deviceControlDto.getAirconditionConfigOrder();
            String sendState = "";
            if(!deviceControlDto.getState().equals(temp.getState())){
                deviceControlDto.setAirconditionConfigOrder(null);
                sendState = SwitchConstant.OPEN.equals(deviceControlDto.getState()) ? airconditionProperties.getAirconditionOpen():airconditionProperties.getAirconditionClose();
                deviceControlDto.setState(sendState);
            }
            if(SwitchConstant.OPEN.equals(state)){
                deviceControlDto.setState(null);
                deviceControlDto.setAirconditionConfigOrder(airconditionConfigOrder);
                temp.setAirconditionConfigOrder(airconditionConfigOrder);
            }
            if (ObjectUtil.isNotNull(deviceControlDto.getState())) {
                sealHeadControlDto.setValue(deviceControlDto.getState());
                MqttUtils.send(sealHeadControlDto, EnvironmentDeviceRequestTopicEnum.devicePointControlGetRequest.getTopic()+ UUID.randomUUID());
            }
            if(ObjectUtil.isNotNull(airconditionConfigOrder)){
                sealHeadControlDto.setValue(airconditionConfigOrder.toString());
                MqttUtils.send(sealHeadControlDto,EnvironmentDeviceRequestTopicEnum.devicePointControlGetRequest.getTopic()+ UUID.randomUUID());
            }
        }else{
            if(EnvironmentDeviceType.dehumidify.getValue().equals(temp.getType())){
                if(ObjectUtil.isNotNull(deviceControlDto.getTemp())){
                    temp.setTemp(deviceControlDto.getTemp());
                }
                if(ObjectUtil.isNotNull(deviceControlDto.getHumidity())){
                    temp.setHumidity(deviceControlDto.getHumidity());
                }
                sealHeadControlDto.setValue(deviceControlDto.getState());
                MqttUtils.send(sealHeadControlDto,EnvironmentDeviceRequestTopicEnum.devicePointControlGetRequest.getTopic()+ UUID.randomUUID());
            }
        }
        if(ObjectUtil.isNotNull(state)){
            temp.setState(state);
        }
        this.updateById(temp);
    }



    @Override
    public void syncDevicePoint(ResponeDto responeDto) {
        List<EnvironmentDevicePointResponeDto> environmentDevicePointResponeDtos = JSONUtil.toList(responeDto.getData().toString(), EnvironmentDevicePointResponeDto.class);
        if(CollUtil.isNotEmpty(environmentDevicePointResponeDtos)){
            DonghuanDevice device = lambdaQuery().eq(DonghuanDevice::getDeviceCode,environmentDevicePointResponeDtos.get(0).getDeviceId()).one();
            if(ObjectUtil.isNull(device)){
                log.info("设备ID：{}未找到设备信息",environmentDevicePointResponeDtos.get(0).getDeviceId());
                return;
            }
            String type = device.getType();
            for(EnvironmentDevicePointResponeDto environmentDevicePointResponeDto:environmentDevicePointResponeDtos) {
                boolean isEnvironmentDeviceType = (EnvironmentDeviceType.aircondition.getValue().equals(type) ||
                        EnvironmentDeviceType.exhaustFan.getValue().equals(type) ||
                        EnvironmentDeviceType.dehumidify.getValue().equals(type) ||
                        EnvironmentDeviceType.lightControl.getValue().equals(type) ||
                        EnvironmentDeviceType.waterPump.getValue().equals(type));

                boolean isControlPointName = (airconditionProperties.getAirconditionOrder().equals(environmentDevicePointResponeDto.getSzPointName()) ||
                        exhaustFanProperties.getExhaustFanOrder().equals(environmentDevicePointResponeDto.getSzPointName()) ||
                        dehumidifyProperties.getTemp().equals(environmentDevicePointResponeDto.getSzPointName()) ||
                        dehumidifyProperties.getHumidity().equals(environmentDevicePointResponeDto.getSzPointName()) ||
                        lightControlProperties.getLightControlOrder().equals(environmentDevicePointResponeDto.getSzPointName()) ||
                        waterPumpProperties.getWaterPumpOrder().equals(environmentDevicePointResponeDto.getSzPointName()));
                if (isEnvironmentDeviceType && isControlPointName) {
                    //保存控制点位用于边侧下发指令到动环设备
                    DevicePointControl devicePointControl = devicePointControlService.selectByDeviceIdAndSzId(device.getId(),environmentDevicePointResponeDto.getSzID());
                    if (ObjectUtil.isNull(devicePointControl)) {
                        DevicePointControl save = new DevicePointControl();
                        save.setSzId(environmentDevicePointResponeDto.getSzID());
                        save.setDeviceId(device.getId());
                        devicePointControlService.save(save);
                    }
                } else if (EnvironmentDeviceType.aircondition.getValue().equals(type) ||
                        EnvironmentDeviceType.temperatureSensor.getValue().equals(type) ||
                        EnvironmentDeviceType.dehumidify.getValue().equals(type)
                ) {
                    //设置温湿度
                    if (airconditionProperties.getTemp().equals(environmentDevicePointResponeDto.getSzPointName()) ||
                            temperatureSensorProperties.getTemp().equals(environmentDevicePointResponeDto.getSzPointName()) ||
                            dehumidifyProperties.getTemp().equals(environmentDevicePointResponeDto.getSzPointName())
                    ) {
                        device.setTemp(Float.parseFloat(environmentDevicePointResponeDto.getValue()));
                    } else if (airconditionProperties.getHumidity().equals(environmentDevicePointResponeDto.getSzPointName()) ||
                            temperatureSensorProperties.getHumidity().equals(environmentDevicePointResponeDto.getSzPointName()) ||
                            dehumidifyProperties.getHumidity().equals(environmentDevicePointResponeDto.getSzPointName())
                    ) {
                        device.setHumidity(Float.parseFloat(environmentDevicePointResponeDto.getValue()));
                    }
                    this.updateById(device);
                }
            }
            for(EnvironmentDevicePointResponeDto environmentDevicePointResponeDto:environmentDevicePointResponeDtos) {
                boolean isMatchingDeviceType = EnvironmentDeviceType.aircondition.getValue().equals(type) ||
                        EnvironmentDeviceType.temperatureSensor.getValue().equals(type) ||
                        EnvironmentDeviceType.waterSensor.getValue().equals(type) ||
                        EnvironmentDeviceType.dehumidify.getValue().equals(type);
                boolean isMatchingPointName = temperatureSensorProperties.getState().equals(environmentDevicePointResponeDto.getSzPointName());
                if (isMatchingDeviceType && isMatchingPointName) {
                    //处理设备在线离线状态
                    device.setRunState(environmentDevicePointResponeDto.getValue());
                    if ("1".equals(environmentDevicePointResponeDto.getValue())) {
                        //如果设备是离线状态那么温度、湿度需要清空
                        device.setTemp(null);
                        device.setHumidity(null);
                        if (EnvironmentDeviceType.waterSensor.getValue().equals(type)) {
                            //如果设备是离线状态那么 水浸告警 设置成正常
                            device.setWaterImmersionAlarm("0");
                        }
                    }
                    this.updateById(device);
                }
            }
        }
    }


    @Override
    public void alarm(String deviceCode, String alarm, String pointName) {
        DonghuanDevice device = lambdaQuery().eq(DonghuanDevice::getDeviceCode,deviceCode).one();
        if(ObjectUtil.isNull(device)){
            log.info("上报告警失败，未找到相应的设备信息。siteId：{}，deviceCode：{}，alarm：{}，pointName：{}",deviceCode,alarm,pointName);
        }
        if(EnvironmentDeviceType.waterSensor.getValue().equals(device.getType())){
            if(waterSensorProperties.getFloodingAlarm().equals(pointName)){
                device.setWaterImmersionAlarm("1");
                this.updateById(device);
                DeviceAlarm deviceAlarm = new DeviceAlarm();
                deviceAlarm.setDeviceId(device.getId());
                deviceAlarm.setDescribe(pointName);
                deviceAlarmService.add(deviceAlarm);
            }
        }else if(EnvironmentDeviceType.dehumidify.getValue().equals(device.getType())){
            if(dehumidifyProperties.getMalfunction().equals(pointName)){
                device.setFaultState("1");
            }else if(dehumidifyProperties.getHighWaterLevelAlarm().equals(pointName)){
                device.setHighWaterLevel("1");
            }else if(dehumidifyProperties.getLowWaterLevelAlarm().equals(pointName)){
                device.setLowWaterLevel("1");
            }else if(dehumidifyProperties.getLeakageAlarm().equals(pointName)){
                device.setLeakage("1");
            }else if(dehumidifyProperties.getOtherAlarm().equals(pointName)){
                device.setOther("1");
            }
            DeviceAlarm deviceAlarm = new DeviceAlarm();
            deviceAlarm.setDeviceId(device.getId());
            deviceAlarm.setDescribe(pointName);
            deviceAlarmService.add(deviceAlarm);
            this.updateById(device);
        }
    }


//    @Scheduled(cron = "*/10 * * * * ?")
    public void syncDevicePoint() {
        toExec();
    }

    /**
     * 同步设备点位
     */
    private void toExec(){
        List<DonghuanDevice> devices = this.list();
        log.info("同步设备点位信息：{}", JSONUtil.toJsonStr(devices));
        for(DonghuanDevice device:devices){
            SealHeadControlDto sealHeadControlDto = new SealHeadControlDto();
            sealHeadControlDto.setDeviceId(device.getDeviceCode());
            MqttUtils.send(sealHeadControlDto,"pointGetRequest");
        }
    }

}
