package com.teamway.environment.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.service.RegionService;
import com.teamway.common.entity.PageModel;
import com.teamway.environment.dto.GetDonghuanHostsDto;
import com.teamway.environment.entity.DonghuanHost;
import com.teamway.environment.mapper.DonghuanHostMapper;
import com.teamway.environment.service.DonghuanHostService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Slf4j
@Service
public class DonghuanHostServiceImpl extends ServiceImpl<DonghuanHostMapper, DonghuanHost> implements DonghuanHostService {

    @Autowired(required = false)
    private RegionService regionService;

    @Override
    public PageModel<List<DonghuanHost>> getDonghuanHosts(GetDonghuanHostsDto getDonghuanHostsDto) {
        LambdaQueryWrapper<DonghuanHost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(getDonghuanHostsDto.getName()), DonghuanHost::getName, getDonghuanHostsDto.getName());
        Page<DonghuanHost> page = new Page<>(getDonghuanHostsDto.getPageIndex(), getDonghuanHostsDto.getPageSize());
        baseMapper.selectPage(page, queryWrapper);
        PageModel<List<DonghuanHost>> pageModel = new PageModel<>();
        List<DonghuanHost> records = page.getRecords();
        records.forEach(donghuanHost -> {
            if (donghuanHost.getRegionId() != null) {
                donghuanHost.setRegionName(regionService.getById(donghuanHost.getRegionId()).getName());
            }
        });
        pageModel.setCount(page.getTotal());
        pageModel.setData(records);
        return pageModel;
    }

}
