{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "人员进出记录查询接口", "description": "获取人员进出记录列表，支持多维度筛选", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "根目录", "id": 46254030, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "SHARED", "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "人员进出记录管理", "id": 55296478, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "人员进出记录查询相关接口", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "获取人员进出记录列表", "api": {"id": "288109408", "method": "post", "path": "/accessManagement/securityDevice/getAccessListByPerson", "parameters": {}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "662164008", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回码，0表示成功"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "array", "items": {"type": "object", "properties": {"eventType": {"type": "string", "description": "事件类型，-1 开门失败；0：不能确定；1：刷卡开门；2：人脸识别开门；3：指纹开门；4：远程开门"}, "imageUrl": {"type": "string", "description": "图片地址"}, "personName": {"type": "string", "description": "姓名"}, "securityDeviceName": {"type": "string", "description": "出入口"}, "securityDeviceDoorName": {"type": "string", "description": "门禁点名称"}, "direction": {"type": "string", "description": "进出方向"}, "directionDesc": {"type": "string", "description": "进出方向描述"}, "time": {"type": "string", "description": "过闸时间"}, "workGroupName": {"type": "string", "description": "部门"}, "cardNo": {"type": "string", "description": "卡号"}, "workUnit": {"type": "string", "description": "单位"}, "workUnitDesc": {"type": "string", "description": "单位名称描述"}, "personJobNum": {"type": "string", "description": "工号"}, "personType": {"type": "string", "description": "工种"}, "personTypeDesc": {"type": "string", "description": "工种名称描述"}, "type": {"type": "string", "description": "人员类型"}, "authType": {"type": "string", "description": "授权类型"}, "personPost": {"type": "string", "description": "岗位"}, "contractorUnitCode": {"type": "string", "description": "总包单位编码"}, "contractorUnitName": {"type": "string", "description": "总包单位名称"}, "subcontractUnitCode": {"type": "string", "description": "分包单位编码"}, "subcontractUnitName": {"type": "string", "description": "分包单位名称"}}}, "description": "人员进出记录列表"}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\r\n  \"code\": 0,\r\n  \"message\": \"成功\",\r\n  \"data\": [\r\n    {\r\n      \"eventType\": \"1\",\r\n      \"imageUrl\": \"http://example.com/image.jpg\",\r\n      \"personName\": \"张三\",\r\n      \"securityDeviceName\": \"主门\",\r\n      \"securityDeviceDoorName\": \"主门-进口\",\r\n      \"direction\": \"1\",\r\n      \"directionDesc\": \"进入\",\r\n      \"time\": \"2025-01-17 09:00:00\",\r\n      \"workGroupName\": \"施工部\",\r\n      \"cardNo\": \"123456789\",\r\n      \"workUnit\": \"001\",\r\n      \"workUnitDesc\": \"华能海门电厂\",\r\n      \"personJobNum\": \"EMP001\",\r\n      \"personType\": \"1\",\r\n      \"personTypeDesc\": \"电工\",\r\n      \"type\": \"1\",\r\n      \"authType\": \"1\",\r\n      \"personPost\": \"高级电工\",\r\n      \"contractorUnitCode\": \"001\",\r\n      \"contractorUnitName\": \"华能海门电厂\",\r\n      \"subcontractUnitCode\": \"002\",\r\n      \"subcontractUnitName\": \"江西水电工程\"\r\n    }\r\n  ]\r\n}", "responseId": 662164008, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"type": "object", "properties": {"pageIndex": {"type": "integer", "title": "页码", "description": "当前页码，从1开始"}, "pageSize": {"type": "integer", "title": "每页大小", "description": "每页记录数量"}, "startTime": {"type": "string", "title": "开始时间", "description": "查询开始时间，格式：yyyy-MM-dd HH:mm:ss"}, "endTime": {"type": "string", "title": "结束时间", "description": "查询结束时间，格式：yyyy-MM-dd HH:mm:ss"}, "eventTime": {"type": "string", "title": "事件时间", "description": "事件发生日期，格式：yyyy-MM-dd（会自动设置开始和结束时间）"}, "eventType": {"type": "string", "title": "事件类型", "description": "事件类型，-1 开门失败；0：不能确定；1：刷卡开门；2：人脸识别开门；3：指纹开门；4：远程开门", "pattern": "|(^[0-4]|-1$)"}, "personName": {"type": "string", "title": "人员名称", "description": "人员姓名，支持模糊查询"}, "regionId": {"type": "string", "title": "区域ID", "description": "区域标识"}, "workGroupId": {"type": "string", "title": "工作组ID", "description": "工作组标识"}, "personIdentityType": {"type": "string", "title": "人员身份类型", "description": "人员身份类型，1：业主；2：监理；3：总包；4：施工人员", "pattern": "|1|2|3|4"}, "workUnit": {"type": "string", "title": "工作单位", "description": "工作单位，支持编码或名称查询"}, "authType": {"type": "string", "title": "授权类型", "description": "授权类型，1：普通人员；2：访客"}, "subcontractUnitCode": {"type": "string", "title": "分包单位编码", "description": "分包单位编码，精确匹配"}, "subcontractUnitName": {"type": "string", "title": "分包单位名称", "description": "分包单位名称，支持模糊查询"}, "personPost": {"type": "string", "title": "岗位", "description": "人员岗位，支持模糊查询"}, "securityDeviceDoorName": {"type": "string", "title": "门禁点名称", "description": "门禁点名称，支持模糊查询"}, "personType": {"type": "string", "title": "工种", "description": "工种类型，支持编码精确匹配或名称模糊查询"}, "direction": {"type": "string", "title": "进场方向", "description": "进场方向，1：进入；2：离开"}}, "x-apifox-orders": ["pageIndex", "pageSize", "startTime", "endTime", "eventTime", "eventType", "personName", "regionId", "workGroupId", "personIdentityType", "workUnit", "authType", "subcontractUnitCode", "subcontractUnitName", "personPost", "securityDeviceDoorName", "personType", "direction"]}, "mediaType": "", "examples": [{"mediaType": "application/json", "value": "{\r\n  \"pageIndex\": 1,\r\n  \"pageSize\": 10,\r\n  \"eventTime\": \"2025-01-17\",\r\n  \"personName\": \"张三\",\r\n  \"eventType\": \"1\",\r\n  \"direction\": \"1\",\r\n  \"personType\": \"电工\",\r\n  \"subcontractUnitName\": \"江西水电工程\",\r\n  \"personPost\": \"高级电工\",\r\n  \"securityDeviceDoorName\": \"主门\"\r\n}"}], "oasExtensions": ""}, "description": "获取人员进出记录列表，支持多维度筛选查询。包括基础信息筛选（时间、人员、事件类型等）和扩展筛选（分包单位、岗位、门禁点、工种、进场方向等）", "tags": [], "status": "developing", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 10, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.662164008"], "visibility": "INHERITED", "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}]}], "socketCollection": [], "docCollection": [], "webSocketCollection": [], "socketIOCollection": [], "responseCollection": [{"_databaseId": 5782737, "updatedAt": "2024-11-29T07:23:59.000Z", "name": "根目录", "type": "root", "children": [], "parentId": 0, "id": 5782737, "items": [{"_databaseId": 340896093, "name": "查询成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "mock": {"mock": "0"}}, "message": {"type": "string", "mock": {"mock": "成功"}}, "data": {"type": "array", "items": {"type": "object"}}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "defaultEnable": false, "folderId": 0, "id": 340896093, "databaseResponseExamples": [], "responseExamples": []}, {"_databaseId": 340896094, "name": "参数错误", "code": 400, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "mock": {"mock": "400"}}, "message": {"type": "string", "mock": {"mock": "参数错误"}}}, "required": ["code", "message"], "x-apifox-orders": ["code", "message"]}, "defaultEnable": false, "folderId": 0, "id": 340896094, "databaseResponseExamples": [], "responseExamples": []}]}], "schemaCollection": [], "securitySchemeCollection": [], "requestCollection": [], "environments": [], "commonScripts": [], "globalVariables": [], "commonParameters": {}, "projectSetting": {"auth": {}, "securityScheme": {}, "servers": [{"id": "default", "name": "默认服务"}], "gateway": [], "language": "zh-CN", "apiStatuses": ["developing", "testing", "released", "deprecated"], "mockSettings": {}, "advancedSettings": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "isDefaultUrlEncoding": 2, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "LEGACY", "enableResourceKeyStandard": true}}}, "customFunctions": [], "projectAssociations": []}