<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.teamway</groupId>
        <artifactId>businessServer-hm</artifactId>
        <version>2.0.1.RELEASE</version>
    </parent>
    <artifactId>haimen-business</artifactId>
    <name>haimen-business</name>
    <url>http://maven.apache.org</url>
    <dependencies>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>base-service-hm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>security-protection</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>common-hm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>exception</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>mqtt</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>personnel</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.teamway</groupId>
                    <artifactId>common-hm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.teamway</groupId>
                    <artifactId>mqtt</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.teamway</groupId>
                    <artifactId>exception</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.teamway</groupId>
                    <artifactId>base-service-hm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jcodec</groupId>
            <artifactId>jcodec</artifactId>
            <version>0.2.5</version>
        </dependency>
        <dependency>
            <groupId>org.jcodec</groupId>
            <artifactId>jcodec-javase</artifactId>
            <version>0.2.5</version>
        </dependency>
    </dependencies>
</project>
