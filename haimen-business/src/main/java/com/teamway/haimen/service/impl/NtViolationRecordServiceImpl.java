package com.teamway.haimen.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.common.constant.AlarmLevelConstant;
import com.teamway.center.entity.AlarmCenter;
import com.teamway.center.enums.ConfirmStatus;
import com.teamway.center.service.AlarmCenterService;
import com.teamway.common.constant.AttachmentConstant;
import com.teamway.common.dto.alarm.AlarmAttachmentDto;
import com.teamway.common.entity.BaseEntity;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.event.PersonFaceMatchEvent;
import com.teamway.common.util.DateUtils;
import com.teamway.common.util.ExcelUtils;
import com.teamway.common.util.ImageUtils;
import com.teamway.exception.ServiceException;
import com.teamway.haimen.dto.score.*;
import com.teamway.haimen.entity.NtInitialScore;
import com.teamway.haimen.entity.NtOfflineViolation;
import com.teamway.haimen.entity.NtScore;
import com.teamway.haimen.entity.NtViolationRecord;
import com.teamway.haimen.enums.PunishedTypeEnum;
import com.teamway.haimen.enums.ViolationTypeEnum;
import com.teamway.haimen.event.AlarmCenterScoreEvent;
import com.teamway.haimen.mapper.NtViolationRecordMapper;
import com.teamway.haimen.service.NtInitialScoreService;
import com.teamway.haimen.service.NtScoreService;
import com.teamway.haimen.service.NtViolationRecordService;
import com.teamway.haimen.vo.FaceDetectVo;
import com.teamway.haimen.vo.ViolationRecordVo;
import com.teamway.personnel.constant.FaceRecognitionProperties;
import com.teamway.personnel.dto.PersonExitDto;
import com.teamway.personnel.service.BasicInfoService;
import com.teamway.personnel.service.QhFaceInfoService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 违章记录实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NtViolationRecordServiceImpl extends ServiceImpl<NtViolationRecordMapper, NtViolationRecord> implements NtViolationRecordService {
    @Autowired
    private NtScoreService scoreService;

    @Autowired
    private AlarmCenterService alarmCenterService;

    @Autowired
    private NtInitialScoreService initialScoreService;

    @Autowired
    private BasicInfoService basicInfoService;

    @Autowired
    private FaceRecognitionProperties faceRecognitionProperties;

    @Autowired
    private QhFaceInfoService faceInfoService;

    /**
     * 默认的调用人脸识别的超时时间，单位毫秒
     **/
    private static final int DEFAULT_TIME_OUT = 2000;

    @Override
    public IPage<ViolationRecordVo> getViolationRecords(ViolationRecordListDto dto) {
        QueryWrapper<NtOfflineViolation> wrapper = new QueryWrapper<>();
        wrapper.eq(Objects.nonNull(dto.getPersonId()), "vr.person_id", dto.getPersonId());
        wrapper.ge(StrUtil.isNotBlank(dto.getStartTime()), "DATE_FORMAT(vr.create_time, '%Y-%m-%d')", DateUtils.getStartTimeByDate(dto.getStartTime()));
        wrapper.le(StrUtil.isNotBlank(dto.getEndTime()), "DATE_FORMAT(vr.create_time, '%Y-%m-%d')", DateUtils.getEndTimeByDate(dto.getEndTime()));
        wrapper.orderByDesc("vr.update_time");
        Page<NtViolationRecord> page = new Page<>(dto.getPageIndex(), dto.getPageSize());
        return baseMapper.getViolationRecords(page, wrapper);
    }

    @Override
    public List<ScoreChangeDto> scoreChange(ViolationRecordChangeDto dto) {
        NtScore ntScore = scoreService.getByPersonId(dto.getPersonId());
        LambdaQueryWrapper<NtViolationRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(dto.getPersonId()), NtViolationRecord::getPersonId, dto.getPersonId());
        queryWrapper.ge(StrUtil.isNotBlank(dto.getStartTime()), NtViolationRecord::getCreateTime, DateUtils.getStartTimeByDate(dto.getStartTime()));
        queryWrapper.le(StrUtil.isNotBlank(dto.getEndTime()), NtViolationRecord::getCreateTime, DateUtils.getEndTimeByDate(dto.getEndTime()));
        queryWrapper.orderByAsc(NtViolationRecord::getCreateTime);
        List<NtViolationRecord> list = this.list(queryWrapper);
        //按日期分组
        Map<LocalDate, List<NtViolationRecord>> dateGroup = list.stream().collect(Collectors.groupingBy(i -> i.getCreateTime().toLocalDate()));
        LocalDate today = LocalDate.now();
        LocalDate startTime = LocalDate.parse(dto.getStartTime());
        LocalDate endTime = LocalDate.parse(dto.getEndTime());
        if (startTime.isAfter(today)) {
            startTime = today;
        }
        if (endTime.isAfter(today)) {
            endTime = today;
        }

        //获取大于开始时间的违规记录
        LambdaQueryWrapper<NtViolationRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(NtViolationRecord::getPersonId, ntScore.getPersonId());
        lambdaQueryWrapper.le(StrUtil.isNotBlank(dto.getStartTime()),
                NtViolationRecord::getCreateTime,
                DateUtils.getEndTimeByDate(dto.getStartTime())
        );
        lambdaQueryWrapper.last("limit 1");
        lambdaQueryWrapper.orderByAsc(NtViolationRecord::getCreateTime);
        NtViolationRecord lastViolationRecord = getOne(lambdaQueryWrapper);

        List<ScoreChangeDto> result = new ArrayList<>();

        Stream.iterate(startTime, i -> i.plusDays(1)).
                limit(ChronoUnit.DAYS.between(startTime, endTime) + 1).
                forEach(date -> {
                    List<NtViolationRecord> records = dateGroup.get(date);
                    int score = ntScore.getScore();
                    if (CollUtil.isEmpty(records)) {
                        if (result.isEmpty()) {
                            //如果该日期不存在违规记录，且前面也没有积分数据，则取最新的违规记录的积分数据
                            if (Objects.nonNull(lastViolationRecord)) {
                                score = lastViolationRecord.getCurrentScore() - lastViolationRecord.getDeductScore();
                            }
                        } else {
                            //如果该日期不存在违规记录,则取上一条的积分数据
                            score = result.getLast().getCurrentScore();
                        }
                    } else {
                        records.sort(Comparator.comparing(BaseEntity::getCreateTime));
                        //取当天最后一条的积分变更数据
                        NtViolationRecord last = records.getLast();
                        score = last.getCurrentScore() - last.getDeductScore();
                    }
                    result.add(ScoreChangeDto.builder().createTime(date.toString()).currentScore(score).build());
                });
        if (!result.isEmpty()) {
            ScoreChangeDto last = result.getLast();
            //如果最后查询的是今天，则将今天的积分置为该人员当前积分
            if (Objects.equals(last.getCreateTime(), today.toString())) {
                last.setCurrentScore(ntScore.getScore());
            }
        }
        return result;
    }

    @Override
    public void relateCompany(AlarmRelPersonDto alarmRelPersonDto) {
        //更新告警信息
        AlarmCenter alarmCenter = alarmCenterService.getById(alarmRelPersonDto.getId());
        alarmCenter.setPersonId(alarmRelPersonDto.getPersonId());
        alarmCenterService.updateById(alarmCenter);

        NtViolationRecord record = getByAlarmId(alarmRelPersonDto.getId());
        //如果该告警原先已关联了其它公司，则同时修改违章记录
        if (Objects.nonNull(record)) {
            if (!Objects.equals(alarmRelPersonDto.getPersonId(), record.getPersonId())) {
                NtScore beforeScore = scoreService.getByPersonId(record.getPersonId());
                //先返还原来关联公司的积分
                if (Objects.nonNull(beforeScore)) {
                    beforeScore.setScore(beforeScore.getScore() + record.getDeductScore());
                    scoreService.updateById(beforeScore);
                }
                NtScore afterScore = scoreService.getByPersonId(alarmRelPersonDto.getPersonId());
                //修改违规记录
                record.setPersonId(afterScore.getPersonId());
                record.setCurrentScore(afterScore.getScore());
                record.setCreateTime(LocalDateTime.now());
                record.setUpdateTime(LocalDateTime.now());
                updateById(record);
                //再扣除当前关联公司的积分
                afterScore.setScore(Math.max(afterScore.getScore() - record.getDeductScore(), 0));
                scoreService.updateById(afterScore);
            }
        } else {
            //如果没有违规记录，判断是否是确认状态，是的话扣除积分
            AlarmCenterScoreEvent alarmCenterConfirmDto = new AlarmCenterScoreEvent(
                    this,
                    List.of(alarmCenter.getId()),
                    alarmCenter.getState().toString()
            );
            changeScore(alarmCenterConfirmDto);
        }
    }

    @Override
    public void batchDel(List<Long> ids) {
        List<NtViolationRecord> list = listByIds(ids);
        //顺带删除告警中心的数据
        List<Long> alarmIds = list.stream().map(NtViolationRecord::getAlarmId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(alarmIds)) {
            //返还积分
            changeScore(new AlarmCenterScoreEvent(this, alarmIds, ConfirmStatus.FALSE_ALARM.getCode().toString()));
            alarmCenterService.removeBatchByIds(alarmIds);
        }
        removeBatchByIds(ids);
    }

    @Override
    public void export(BatchIds<Long> batchIds, HttpServletResponse response) {
        if (CollUtil.isEmpty(batchIds.getIds())) {
            throw new ServiceException(ResultCode.NOT_NULL, "ids");
        }
        List<NtViolationRecord> records = listByIds(batchIds.getIds());

        if (CollUtil.isEmpty(records)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "数据不存在，请刷新界面");
        }

        records.sort(Comparator.comparing(NtViolationRecord::getUpdateTime).reversed());
        List<NtViolationRecordExcelDto> exportList = records.stream().map(r -> {
            byte[] bytes = null;
            try {
                if (StrUtil.isNotBlank(r.getImageUrl())) {
                    bytes = ImageUtils.getImageStreamByUrl(r.getImageUrl());
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            NtScore score = scoreService.getByPersonId(r.getPersonId());
            return NtViolationRecordExcelDto.builder()
                    .personName(score.getPersonName())
                    .level(r.getLevel())
                    .type(r.getType())
                    .content(r.getContent())
                    .deductScore(r.getDeductScore())
                    .createTime(DateUtils.localDateTime2String(r.getCreateTime()))
                    .img(bytes)
                    .build();
        }).collect(Collectors.toList());
        ExportParams exportParams = new ExportParams("违章记录列表", "违章记录列表", ExcelType.HSSF);
        try {
            ExcelUtils.exportExcel(exportList, NtViolationRecordExcelDto.class, "违章记录列表", exportParams, response);
        } catch (IOException e) {
            throw new ServiceException(ResultCode.EXPORT_FAIL, e);
        }
    }

    @Override
    public void changeScore(AlarmCenterScoreEvent alarmCenterConfirmDto) {
        alarmCenterConfirmDto.getAlarmIds().forEach(id -> {
            if (Objects.equals(alarmCenterConfirmDto.getState(), ConfirmStatus.CONFIRMED.getCode().toString())) {
                AlarmCenter alarmCenter = alarmCenterService.getById(id);
                NtViolationRecord exist = getByAlarmId(id);
                if (Objects.nonNull(exist)) {
                    return;
                }
                if (Objects.nonNull(alarmCenter.getPersonId())) {
                    //扣除个人积分,并生成一条违规记录
                    NtInitialScore initialScore = initialScoreService.getOne(null);
                    NtScore score = scoreService.getByPersonId(alarmCenter.getPersonId());
                    NtViolationRecord violationRecord = new NtViolationRecord();
                    violationRecord.setCurrentScore(score.getScore());
                    violationRecord.setPersonId(score.getPersonId());
                    if (Objects.equals(alarmCenter.getLevel().toString(), AlarmLevelConstant.URGENCY)) {
                        violationRecord.setDeductScore(Math.min(initialScore.getDeductScoreUrgent(), score.getScore()));
                    } else if (Objects.equals(alarmCenter.getLevel().toString(), AlarmLevelConstant.SIGNIFICANCE)) {
                        violationRecord.setDeductScore(Math.min(initialScore.getDeductScoreImportant(), score.getScore()));
                    } else {
                        violationRecord.setDeductScore(Math.min(initialScore.getDeductScoreCommon(), score.getScore()));
                    }
                    violationRecord.setLevel(alarmCenter.getLevel());
                    violationRecord.setType(alarmCenter.getType());
                    violationRecord.setContent(String.format("%s[%s]", alarmCenter.getAlgName(), alarmCenter.getAlarmDesc()));
                    violationRecord.setAlarmId(id);
                    if (CollUtil.isNotEmpty(alarmCenter.getAttachmentList())) {
                        alarmCenter.getAttachmentList().stream().filter(i -> Objects.equals(i.getType(), AttachmentConstant.PICTURE)).findFirst().ifPresent(attachement -> {
                            violationRecord.setImageUrl(attachement.getUrl());
                        });
                    }
                    //修改个人积分
                    score.setScore(Math.max(score.getScore() - violationRecord.getDeductScore(), 0));
                    scoreService.updateById(score);
                    //保存违规记录
                    save(violationRecord);
                    //参加培训
                    attendTraining(score, initialScore);
                }
            } else if (Objects.equals(alarmCenterConfirmDto.getState(), ConfirmStatus.FALSE_ALARM.getCode().toString())) {
                //误报,返还分数
                Optional.ofNullable(getByAlarmId(id)).ifPresent(record -> {
                    NtScore score = scoreService.getByPersonId(record.getPersonId());
                    if (Objects.nonNull(score)) {
                        score.setScore(score.getScore() + record.getDeductScore());
                        scoreService.updateById(score);
                        //删除违规记录
                        removeById(record.getId());
                    }
                });
            }
        });
    }

    @Override
    public void changeScore(NtOfflineViolation offlineViolation) {
        if (Objects.isNull(offlineViolation.getPersonId())) {
            return;
        }
        NtInitialScore initialScore = initialScoreService.getOne(null);
        NtScore score = scoreService.getByPersonId(offlineViolation.getPersonId());
        NtViolationRecord violationRecord = new NtViolationRecord();
        violationRecord.setCurrentScore(score.getScore());
        violationRecord.setPersonId(score.getPersonId());
        if (Objects.equals(offlineViolation.getLevel().toString(), AlarmLevelConstant.URGENCY)) {
            violationRecord.setDeductScore(Math.min(initialScore.getDeductScoreUrgent(), score.getScore()));
        }
        if (Objects.equals(offlineViolation.getLevel().toString(), AlarmLevelConstant.SIGNIFICANCE)) {
            violationRecord.setDeductScore(Math.min(initialScore.getDeductScoreImportant(), score.getScore()));
        }
        if (Objects.equals(offlineViolation.getLevel().toString(), AlarmLevelConstant.GENERAL)) {
            violationRecord.setDeductScore(Math.min(initialScore.getDeductScoreCommon(), score.getScore()));
        }
        violationRecord.setLevel(offlineViolation.getLevel());
        if (ViolationTypeEnum.PERSONNEL_SECURITY.getCode().equals(offlineViolation.getType())) {
            violationRecord.setType(PunishedTypeEnum.PERSONNEL_SAFETY.getCode());
        }
        if (ViolationTypeEnum.OTHER.getCode().equals(offlineViolation.getType())) {
            violationRecord.setType(PunishedTypeEnum.OTHER.getCode());
        }
        violationRecord.setContent(offlineViolation.getEvent());
        if (StringUtils.isNotEmpty(offlineViolation.getAttachment())) {
            AlarmAttachmentDto alarmAttachmentDto = JSON.parseObject(offlineViolation.getAttachment(), AlarmAttachmentDto.class);
            violationRecord.setImageUrl(alarmAttachmentDto.getUrl());
        }
        //修改个人积分
        score.setScore(Math.max(score.getScore() - violationRecord.getDeductScore(), 0));
        scoreService.updateById(score);
        //保存违规记录
        save(violationRecord);
        //参加培训
        attendTraining(score, initialScore);
    }

    @Override
    public void handlePersonFaceMatchEvent(PersonFaceMatchEvent personFaceMatchEvent) {
        List<AlarmAttachmentDto> attachmentList = personFaceMatchEvent.getAttachmentList();
        if (CollectionUtils.isEmpty(attachmentList)) {
            return;
        }
        String imageUrl = null;
        for (AlarmAttachmentDto alarmAttachmentDto : attachmentList) {
            if (AttachmentConstant.PICTURE.equals(alarmAttachmentDto.getType())) {
                imageUrl = alarmAttachmentDto.getUrl();
            }
        }
        if (StringUtils.isBlank(imageUrl)) {
            return;
        }
        String recognitionURI = buildUrl(
                faceRecognitionProperties.getProtocol(),
                faceRecognitionProperties.getIpAddress(),
                faceRecognitionProperties.getPort(),
                faceRecognitionProperties.getDetectURL()
        );
        FaceDetectDto faceDetectDto = new FaceDetectDto();
        try {
            faceDetectDto.setImageData(imageUrlToBase64(imageUrl));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String responseEntity = HttpUtil.post(recognitionURI, JSONUtil.toJsonStr(faceDetectDto), DEFAULT_TIME_OUT);
        JSONObject resultModel = JSON.parseObject(responseEntity);
        if (!resultModel.getInteger("code").equals(ResultCode.SUCCESS.getCode())) {
            return;
        }
        List<FaceDetectVo> faceDetectVos = JSON.parseArray(resultModel.getJSONArray("data").toJSONString(), FaceDetectVo.class);
        FaceDetectVo faceDetectVo = null;
        if (CollectionUtils.isNotEmpty(faceDetectVos)) {
            faceDetectVo = faceDetectVos.get(0);
        }
        if (Objects.isNull(faceDetectVo)) {
            return;
        }
        // TODO 查询人脸库存在此人 如果有数据说明能查询到 进行积分扣除处理
        if (faceInfoService.existsSamePerson(faceDetectVo.getPersonId(), faceDetectVo.getPersonName())) {
            //修改告警信息
            AlarmCenter alarmCenter = alarmCenterService.getById(personFaceMatchEvent.getAlarmId());
            if (Objects.isNull(alarmCenter)) {
                alarmCenter.setPersonId(faceDetectVo.getPersonId());
                alarmCenterService.updateById(alarmCenter);
            }
            //扣除积分
            List<Long> alarmIds = new ArrayList<>(1);
            alarmIds.add(personFaceMatchEvent.getAlarmId());
            changeScore(new AlarmCenterScoreEvent(this, alarmIds, ConfirmStatus.CONFIRMED.getCode().toString()));
        }

    }

    /**
     * 根据告警中心ID查询违规记录
     *
     * @param alarmId 告警中心ID
     */
    private NtViolationRecord getByAlarmId(Long alarmId) {
        return lambdaQuery().eq(NtViolationRecord::getAlarmId, alarmId).one();
    }

    /**
     * 参加培训
     *
     * @param score        积分信息
     * @param initialScore 初始积分信息
     */
    private void attendTraining(NtScore score, NtInitialScore initialScore) {
        if (Objects.isNull(score)) {
            return;
        }
        if (score.getScore() < initialScore.getMinThreshold()) {
            // TODO 小于阈值 发送培训
            PersonExitDto personExitDto = new PersonExitDto();
            personExitDto.setId(score.getPersonId());
            personExitDto.setExitReason("积分不够");
            personExitDto.setRemarks("需参加考试,恢复积分方可入场");
            basicInfoService.personExit(personExitDto);
        }
    }

    /**
     * 从URL下载图片并转换为Base64编码的字符串
     *
     * @param imageUrl 图片的URL
     * @return Base64编码的字符串
     * @throws IOException
     */
    public static String imageUrlToBase64(String imageUrl) throws IOException {
        // 创建URL对象
        URL url = new URL(imageUrl);
        // 获取图片的输入流
        try (InputStream inputStream = url.openStream()) {
            // 将输入流读取到字节数组中
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, length);
            }
            // 获取图片的字节数据
            byte[] imageBytes = byteArrayOutputStream.toByteArray();
            // 将字节数据编码为Base64字符串
            String encodeToString = Base64.getEncoder().encodeToString(imageBytes);
            log.debug("人脸图片{}转换base64长度为：{}", imageUrl, imageBytes.length);
            return encodeToString;
        }
    }

    /**
     * 构建URL
     *
     * @param protocol  协议
     * @param ipAddress IP地址
     * @param port      端口
     * @param uri       资源
     */
    private String buildUrl(String protocol, String ipAddress, int port, String uri) {
        return protocol + "://" + ipAddress + ":" + port + uri;
    }
}




