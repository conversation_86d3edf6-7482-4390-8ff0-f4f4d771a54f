package com.teamway.personnel.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.common.entity.BaseEntity;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.entity.ResultModel;
import com.teamway.exception.ServiceException;
import com.teamway.personnel.dto.PageSubManageDto;
import com.teamway.personnel.dto.SubManageAddAndUpdateDto;
import com.teamway.personnel.entity.PositionManagement;
import com.teamway.personnel.entity.SubcontractorManagement;
import com.teamway.personnel.mapper.SubcontractorManagementMapper;
import com.teamway.personnel.service.SubcontractorManagementService;
import com.teamway.personnel.transfer.SubcontractorManageTransfer;
import com.teamway.personnel.vo.PageSubManageVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分包商管理Service实现类
 * <AUTHOR>
 */
@Service
public class SubcontractorManagementServiceImpl extends ServiceImpl<SubcontractorManagementMapper, SubcontractorManagement> implements SubcontractorManagementService {

    @Autowired
    private SubcontractorManageTransfer subcontractorManageTransfer;

    /**
     * 新增分包商
     * @param addDto
     * @return
     */
    @Override
    public Boolean addSubcontractor(SubManageAddAndUpdateDto addDto) {
        SubcontractorManagement subcontractorManagements = list(new LambdaQueryWrapper<SubcontractorManagement>()
                .eq(SubcontractorManagement::getSubcontractorName, addDto.getSubcontractorName())).stream().findFirst().orElse(null);
        if(ObjectUtil.isNotEmpty(subcontractorManagements)) {
            throw new ServiceException(ResultCode.EXIST,"分包商名称");
        }
        SubcontractorManagement convert = Convert.convert(SubcontractorManagement.class, addDto);
        return save(convert);
    }


    /**
     * 删除分包商
     * @param batchIds
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteSubcontractor(BatchIds<Long> batchIds) {
        return removeByIds(batchIds.getIds());
    }

    /**
     * 修改分包商
     * @param updateDto
     * @return
     */
    @Override
    public Boolean updateSubcontractor(SubManageAddAndUpdateDto updateDto) {
        SubcontractorManagement subcontractorManagements = list(new LambdaQueryWrapper<SubcontractorManagement>()
                .eq(SubcontractorManagement::getSubcontractorName, updateDto.getSubcontractorName())
                .ne(BaseEntity::getId,updateDto.getId()))
                .stream().findFirst().orElse(null);
        if(ObjectUtil.isNotEmpty(subcontractorManagements)) {
            throw new ServiceException(ResultCode.EXIST,"分包商名称");
        }
        SubcontractorManagement convert = Convert.convert(SubcontractorManagement.class, updateDto);
        return updateById(convert);
    }


    /**
     * 分页查询分包商
     * @param pageDto
     * @return
     */
    @Override
    public Page<PageSubManageVo> pageSubcontractor(PageSubManageDto pageDto) {
        LambdaQueryWrapper<SubcontractorManagement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(pageDto.getSubcontractorType()),SubcontractorManagement::getSubcontractorType,pageDto.getSubcontractorType());
        queryWrapper.like(StrUtil.isNotBlank(pageDto.getSubcontractorName()),SubcontractorManagement::getSubcontractorName,pageDto.getSubcontractorName());
        queryWrapper.like(StrUtil.isNotBlank(pageDto.getSocialCreditCode()),SubcontractorManagement::getSocialCreditCode,pageDto.getSocialCreditCode());
        queryWrapper.orderByDesc(BaseEntity::getCreateTime);
        Page<SubcontractorManagement> page = page(new Page<>(pageDto.getPageIndex(), pageDto.getPageSize()), queryWrapper);
        Page<PageSubManageVo> voPage = new Page<>();
        voPage.setTotal(page.getTotal());
        voPage.setCurrent(page.getCurrent());
        voPage.setSize(page.getSize());
        voPage.setRecords(subcontractorManageTransfer.subcontractorToSubcontractorVo(page.getRecords()));
        return voPage;
    }

    /**
     * 查询分包商列表
     * @return
     */
    @Override
    public List<PageSubManageVo> pageSubcontractorList() {
        List<SubcontractorManagement> managementList = list(new LambdaQueryWrapper<SubcontractorManagement>()
                .select(BaseEntity::getId, SubcontractorManagement::getSubcontractorName)
                .orderByDesc(SubcontractorManagement::getCreateTime));
        return subcontractorManageTransfer.subcontractorToSubcontractorVo(managementList);
    }

    /**
     * 获取分包单位名称到ID的映射
     * @return 分包单位名称到ID的映射关系
     */
    @Override
    public Map<String, String> getSubcontractorNameToIdMap() {
        List<SubcontractorManagement> activeSubcontractors = getActiveSubcontractors();
        return activeSubcontractors.stream()
                .collect(Collectors.toMap(
                        SubcontractorManagement::getSubcontractorName,
                        subcontractor -> subcontractor.getId().toString(),
                        (v1, v2) -> v1
                ));
    }

    /**
     * 获取所有有效的分包单位列表
     * @return 有效的分包单位列表
     */
    @Override
    public List<SubcontractorManagement> getActiveSubcontractors() {
        return lambdaQuery()
                .eq(SubcontractorManagement::getIsActive, "1")
                .orderByAsc(SubcontractorManagement::getSubcontractorName)
                .list();
    }
}