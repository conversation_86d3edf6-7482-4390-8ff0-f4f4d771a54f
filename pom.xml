<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.teamway</groupId>
    <artifactId>businessServer-hm</artifactId>
    <version>2.0.1.RELEASE</version>
    <packaging>pom</packaging>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.1</version>
        <relativePath/>
    </parent>
    <modules>
        <module>gather</module>
        <module>alarm-center</module>
        <module>base-service</module>
        <module>mqtt</module>
<!--        <module>environment</module>-->
        <module>common</module>
        <module>exception</module>
        <module>security-protection</module>
        <module>statistics</module>
        <module>personnel</module>
        <module>timed-photos</module>
        <module>haimen-business</module>
        <module>common-api</module>
    </modules>

    <properties>
        <server.version>2.0.1.RELEASE</server.version>
        <springboot.version>3.2.1</springboot.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <java.version>21</java.version>
        <mysql.version>8.0.16</mysql.version>
        <commons-pool.version>2.9.0</commons-pool.version>
        <hutool.version>5.8.29</hutool.version>
        <druid-spring-boot.version>1.2.22</druid-spring-boot.version>
        <fastjson.version>1.2.75</fastjson.version>
        <onvif.version>2.6.2.RELEASE</onvif.version>
        <mqtt.version>1.2.5</mqtt.version>
        <lombok.version>1.18.34</lombok.version>
        <base-service.version>3.0.0.RELEASE</base-service.version>
        <algorithm-config-service.version>2.0.0</algorithm-config-service.version>
        <general-thermal.version>3.0.0-RELEASE</general-thermal.version>
        <ins-service.version>3.0.2.RELEASE</ins-service.version>
        <slf4j-api.version>2.0.13</slf4j-api.version>
        <jakarta.servlet.version>6.0.0</jakarta.servlet.version>
        <jakarta.annotation.version>2.1.1</jakarta.annotation.version>
        <google.guava.version>33.3.1-jre</google.guava.version>
        <freemarker.version>2.3.31</freemarker.version>
        <tomcat.version>10.1.41</tomcat.version>
    </properties>
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://10.0.60.26:8181/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://10.0.60.26:8181/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <!--仅用于管理版本，而不会为自己以及子模块导入依赖，因此在dependencyManagement中声明依赖后，对应的子模块仍然需要在dependencies中加入依赖-->
    <dependencyManagement>
        <dependencies>
            <!--内部依赖-->
            <dependency>
                <groupId>com.teamway</groupId>
                <artifactId>base-service-hm</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teamway</groupId>
                <artifactId>common-hm</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teamway</groupId>
                <artifactId>security-protection</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teamway</groupId>
                <artifactId>alarm-center</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teamway</groupId>
                <artifactId>statistics</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teamway</groupId>
                <artifactId>personnel</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teamway</groupId>
                <artifactId>timed-photos</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teamway</groupId>
                <artifactId>mqtt</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teamway</groupId>
                <artifactId>exception</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teamway</groupId>
                <artifactId>haimen-business</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teamway</groupId>
                <artifactId>common-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--外部依赖-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.tomcat.embed</groupId>
                        <artifactId>tomcat-embed-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tomcat.embed</groupId>
                        <artifactId>tomcat-embed-el</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tomcat.embed</groupId>
                        <artifactId>tomcat-embed-websocket</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.tomcat.embed</groupId>
                        <artifactId>tomcat-embed-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tomcat.embed</groupId>
                        <artifactId>tomcat-embed-el</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tomcat.embed</groupId>
                        <artifactId>tomcat-embed-websocket</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid-spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <!--解决javax.annotation.PostConstruct失效-->
            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${jakarta.annotation.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${google.guava.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>${jakarta.servlet.version}</version>
            </dependency>
            <!--mapstruct 实体转换工具-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <!-- Tomcat Dependencies -->
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <!--通过maven设置jdk的版本 防止idea设置jdk版本之后-->
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <source>21</source>
                        <target>21</target>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>