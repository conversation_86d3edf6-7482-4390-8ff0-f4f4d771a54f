<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.teamway</groupId>
        <artifactId>businessServer-hm</artifactId>
        <version>2.0.1.RELEASE</version>
    </parent>
    <artifactId>security-protection</artifactId>
    <name>security-protection</name>
    <url>http://maven.apache.org</url>
    <dependencies>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>base-service-hm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>mqtt</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>common-hm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>alarm-center</artifactId>
        </dependency>
        <!--mapstruct 实体转换工具-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.yulichang</groupId>
            <artifactId>mybatis-plus-join</artifactId>
            <version>1.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>common-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teamway</groupId>
            <artifactId>personnel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>RELEASE</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
