package com.teamway.security.controller.accessManagement.securityDevice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.accessManagement.car.EventListCarDto;
import com.teamway.security.dto.accessManagement.person.EventListPersonDto;
import com.teamway.security.dto.accessManagement.person.EventListPersonMisDto;
import com.teamway.security.dto.accessManagement.securityDevice.AccessStatisticsQueryDto;
import com.teamway.security.entity.SecurityConfiguration.SecurityDeviceDoor;
import com.teamway.security.service.SecurityDeviceCameraService;
import com.teamway.security.vo.accessManagement.car.EventListCarVo;
import com.teamway.security.vo.accessManagement.person.EventListPersonVo;
import com.teamway.security.vo.accessManagement.securityDevice.AccessStatisticsResultVo;
import com.teamway.security.vo.accessManagement.securityDevice.BandedCameraVo;
import com.teamway.security.vo.accessManagement.securityDevice.IndexAllStatisticsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/25
 */
@RestController
@RequestMapping("/accessManagement/securityDevice")
public class AccessSecurityDeviceController {

    private SecurityDeviceCameraService securityDeviceCameraService;

    @Autowired
    public void setSecurityDeviceCameraService(SecurityDeviceCameraService securityDeviceCameraService) {
        this.securityDeviceCameraService = securityDeviceCameraService;
    }

    @GetMapping("/getBandedCamera/{securityDeviceId}")
    public ResultModel<BandedCameraVo> getBandedCamera(@PathVariable("securityDeviceId") Long securityDeviceId){
        BandedCameraVo vo = securityDeviceCameraService.getBandedCameraWithCameraInfo(securityDeviceId);
        return ResultModel.success(vo);
    }

    /**
     * 出入口信息统计  进 出
     * @param queryDto
     * @return
     */
    @PostMapping("/accessManagementStatistics")
    public ResultModel<AccessStatisticsResultVo> accessManagementStatistics(@RequestBody AccessStatisticsQueryDto queryDto){
        AccessStatisticsResultVo vo = securityDeviceCameraService.accessManagementStatistics(queryDto);
        return ResultModel.success(vo);
    }

    /**
     * 人员事件查询列表
     * @param queryDto
     * @return
     */
    @PostMapping("/getEventListByPerson")
    public ResultModel<List<EventListPersonVo>> eventListByPerson(@RequestBody @Valid EventListPersonDto queryDto){
        IPage<EventListPersonVo> vo = securityDeviceCameraService.eventListByPerson(queryDto);
        return ResultModel.success(vo);
    }

    /**
     * 实时事件列表-人员
     * @param queryDto
     * @return
     */
    @PostMapping("/getAccessListByPerson")
    public ResultModel<List<EventListPersonVo>> getAccessListByPerson(@RequestBody @Valid EventListPersonDto queryDto){
        return ResultModel.success(securityDeviceCameraService.eventListByPerson(queryDto));
    }

    /**
     * 实时事件列表-人员
     * @param queryDto
     * @return
     */
    @PostMapping("/getAccessListByPersonMis")
    public ResultModel<List<EventListPersonVo>> getAccessListByPersonMis(@RequestBody @Valid EventListPersonMisDto queryDto){
        return ResultModel.success(securityDeviceCameraService.eventListByPersonMis(queryDto));
    }

    /**
     * 免登录 实时事件列表-人员
     * @param queryDto
     * @return
     */
    @PostMapping("/getAccessListByPersonNoLogin")
    public ResultModel<List<EventListPersonVo>> getAccessListByPersonNoLogin(@RequestBody @Valid EventListPersonDto queryDto){
        return ResultModel.success(securityDeviceCameraService.eventListByPerson(queryDto));
    }

    /**
     * 车辆事件查询列表
     * @param queryDto
     * @return
     */
    @PostMapping("/getEventListByCar")
    public ResultModel<List<EventListCarVo>> eventListByCar(@RequestBody @Valid EventListCarDto queryDto){
        IPage<EventListCarVo> vo = securityDeviceCameraService.eventListByCar(queryDto);
        return ResultModel.success(vo);
    }

    /**
     * 实时事件列表-车辆
     * @param queryDto
     * @return
     */
    @PostMapping("/getAccessListByCar")
    public ResultModel<List<EventListCarVo>> getAccessListByCar(@RequestBody @Valid EventListCarDto queryDto){
        return ResultModel.success(securityDeviceCameraService.eventListByCar(queryDto));
    }


    /**
     * 获取指定安防设备下出入点位
     * @param deviceId
     * @return
     */
    @GetMapping("/getSecurityDeviceDoorByDeviceId/{deviceId}")
    public ResultModel<List<SecurityDeviceDoor>> getSecurityDeviceDoorByDeviceId(@PathVariable("deviceId") Long deviceId){
        List<SecurityDeviceDoor> vo = securityDeviceCameraService.getSecurityDeviceDoorByDeviceId(deviceId);
        return ResultModel.success(vo);
    }

    /**
     * 导出门禁进出记录
     * @param queryDto
     * @param response
     */
    @PostMapping("/exportEventListByPerson")
    public void exportEventListByPerson(@RequestBody @Valid EventListPersonDto queryDto, HttpServletResponse response) {
        securityDeviceCameraService.exportEventListByPerson(queryDto, response);
    }
}
