package com.teamway.security.controller.accessManagement.securityDevice;

import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.DeviceControlResDto;
import com.teamway.security.dto.accessManagement.securityDevice.SecurityDeviceControlQueryDto;
import com.teamway.security.service.DeviceControlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/accessManagement/securityDevice")
public class DeviceControlController {

    private DeviceControlService deviceControlService;

    @Autowired
    public void setDeviceControlService(DeviceControlService deviceControlService) {
        this.deviceControlService = deviceControlService;
    }

    /**
     * 设备控制
     * @param queryDto
     * @return
     */
    @PostMapping("/control")
    public ResultModel<DeviceControlResDto> securityDeviceControl(@RequestBody @Valid SecurityDeviceControlQueryDto queryDto) {
        return ResultModel.success(deviceControlService.securityDeviceControl(queryDto));
    }
}
