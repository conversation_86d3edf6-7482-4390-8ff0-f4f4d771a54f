package com.teamway.security.controller.accessManagement.visitor;

import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.accessManagement.visitor.*;
import com.teamway.security.dto.securityConfiguration.person.*;
import com.teamway.security.entity.accessManagement.visitorReservationManage.Reservation;
import com.teamway.security.service.ReservationService;
import com.teamway.security.vo.securityConfiguration.person.PersonAuthedDeviceVo;
import com.teamway.security.vo.securityConfiguration.securityDevice.Sync4aPersonVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/17
 */
@RestController
@RequestMapping("/accessManage/visitorReservationManage")
public class VisitorReservationManageController {

    private ReservationService reservationService;

    @Autowired
    public void setReservationService(ReservationService reservationService) {
        this.reservationService = reservationService;
    }

    @Autowired
    public VisitorReservationManageController(ReservationService reservationService) {
        this.reservationService = reservationService;
    }

    /**
     * 添加预约信息
     * @param dto
     * @return
     */
    @PostMapping("/addVisitorReservation")
    public ResultModel<String> addVisitorReservation(@RequestBody @Valid VisitorReservationAddQueryDto dto){
        reservationService.addVisitorReservationByController(dto);
        return ResultModel.success();
    }

    /**
     * 修改预约信息
     * @param dto 修改的数据
     * @return 无
     */
    @PostMapping("/updateVisitorReservation")
    public ResultModel<String> updateVisitorReservation(@RequestBody @Valid VisitorReservationAddQueryDto dto){
        reservationService.updateVisitorReservation(dto);
        return ResultModel.success();
    }

    /**
     * 删除预约信息
     * @param ids 删除的ID集合
     * @return 无
     */
    @PostMapping("/deleteVisitorReservation")
    public ResultModel<String> deleteVisitorReservation(@RequestBody BatchIds<String> ids) {
        reservationService.deleteVisitorReservation(ids.getIds());
        return ResultModel.success();
    }


    /**
     * 保存审批结果
     * @param dto
     * @return
     */
    @PostMapping("/notifyTheApprovalResult")
    public ResultModel<String> notifyTheApprovalResult(@RequestBody @Valid ApprovalResultNotificationQueryDto dto){
        reservationService.notifyTheApprovalResult(dto);
        return ResultModel.success();
    }

    /**
     * 开卡
     * @param dto
     * @return
     */
    @PostMapping("/openCard")
    public ResultModel<String> openCard(@RequestBody @Valid CardOpenQueryDto dto){
        reservationService.cardOpen(dto);
        return ResultModel.success();
    }

    /**
     * 同步基建系统访客数据
     * @return
     */
    @GetMapping("/synchronizingVisitorData")
    public ResultModel synchronizingVisitorData(){
        return ResultModel.success(reservationService.synchronizingVisitorData());
    }

    /**
     * 获取预约列表
     * @param dto
     * @return
     */
    @PostMapping("/listReservation")
    public ResultModel<List<Reservation>> listReservation(@RequestBody @Valid ReservationListQueryDto dto){
        return ResultModel.success(reservationService.listReservation(dto));
    }

    /**
     * 访客开卡-添加人员
     * @param dto
     * @return
     */
    @PostMapping("/reservationAddPerson")
    public ResultModel<String> reservationAddPerson(@RequestBody @Valid CardOpenQueryDto dto){
        reservationService.reservationAddPerson(dto);
        return ResultModel.success();
    }

    /**
     * 访客开卡-添加卡片
     * @param dto
     * @return
     */
    @PostMapping("/reservationAddCard")
    public ResultModel<String> reservationAddCard(@RequestBody @Valid CardOpenQueryDto dto){
        reservationService.reservationAddCard(dto);
        return ResultModel.success();
    }

    /**
     * 访客开卡-人员授权
     * @param dto
     * @return
     */
    @PostMapping("/reservationPersonAuth")
    public ResultModel<String> reservationPersonAuth(@RequestBody @Valid CardOpenQueryDto dto){
        reservationService.reservationPersonAuth(dto);
        return ResultModel.success();
    }


    /**
     * 复用安防人员 人员授权
     * @param dto
     * @return
     */
    @PostMapping("/personAuthorization")
    public ResultModel<Map<String, AuthResultDto>> personAuthorization(@RequestBody @Valid PersonAuthorizationQueryDto dto) {
        Map<String, AuthResultDto> map = reservationService.personAuthorization(dto);
        return ResultModel.success(map);
    }

    /**
     * 复用安防人员 修改人员授权时间
     * @param dto
     * @return
     */
    @PostMapping("/editPersonAuthorization")
    public ResultModel<Sync4aPersonVo> editPersonAuthorization(@RequestBody @Valid EditPersonAuthorizationQueryDto dto) {
        return ResultModel.success(reservationService.editPersonAuthorization(dto));
    }


    /**
     * 复用安防人员 删除人员授权
     * @param dto
     * @return
     */
    @PostMapping("/deletePersonAuthorization")
    public ResultModel<Sync4aPersonVo> deletePersonAuthorization(@RequestBody @Valid DeletePersonAuthorizationQueryDto dto) {
        return ResultModel.success(reservationService.deletePersonAuthorization(dto));
    }

    /**
     * 复用安防人员 获取人员已授权列表
     * @param personId
     * @return
     */
    @GetMapping("/getPersonAuthedDevice/{personId}")
    public ResultModel<List<PersonAuthedDeviceDto>> getPersonAuthedDevice(@PathVariable("personId") Long personId) {
        return ResultModel.success(reservationService.getPersonAuthedDevice(personId));
    }

    /**
     * 批量导出人员
     * @param dto
     * @param response
     */
    @PostMapping("/export")
    public void exportPerson(@RequestBody @Valid ReservationExportRequestDto dto, HttpServletResponse response) {
        reservationService.exportPerson(dto, response);
    }

    /**
     * 获取所有导出数据的id
     * @param dto
     * @return
     */
    @PostMapping("/getAllExportDataIds")
    public ResultModel<List<Long>> getAllExportDataIds(@RequestBody @Valid ReservationListQueryDto dto){
        return ResultModel.success(reservationService.getAllExportDataIds(dto));
    }



    /**
     * 访客开卡-车辆添加
     * @param dto
     * @return
     */
    @PostMapping("/reservationCarAdd")
    public ResultModel<String> reservationCarAdd(@RequestBody @Valid CardOpenQueryDto dto){
        reservationService.reservationCarAdd(dto);
        return ResultModel.success();
    }

    /**
     * 访客开卡-车辆授权
     * @param dto
     * @return
     */
    @PostMapping("/reservationCarAuth")
    public ResultModel<String> reservationCarAuth(@RequestBody @Valid CardOpenQueryDto dto){
        reservationService.reservationCarAuth(dto);
        return ResultModel.success();
    }



}
