package com.teamway.security.controller.securityConfiguration;

import com.teamway.common.entity.ResultModel;
import com.teamway.security.entity.AccessAlertConfig;
import com.teamway.security.service.AccessAlertConfigService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 外设表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@RestController
@RequestMapping("/securityConfiguration/accessAlertConfig")
public class AccessAlertConfigController {

    @Autowired
    private AccessAlertConfigService accessAlertConfigService;

    /**
     * 查出入告警配置
     * @return
     */
    @GetMapping("/select")
    public ResultModel getAccessAlertConfig(){
        return ResultModel.success(accessAlertConfigService.getAccessAlertConfig());
    }

    /**
     * 更新出入告警配置
     * @param alertConfig
     * @return
     */
    @PostMapping("/update")
    public ResultModel updateAccessAlertConfig(@RequestBody @Valid AccessAlertConfig alertConfig){
        return accessAlertConfigService.updateAccessAlertConfig(alertConfig) ? ResultModel.success() : ResultModel.fail();
    }
}