package com.teamway.security.controller.securityConfiguration.Card;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.securityConfiguration.card.CardListQueryDto;
import com.teamway.security.dto.securityConfiguration.card.CardReportTheLossQueryDto;
import com.teamway.security.dto.securityConfiguration.card.CardReturnQueryDto;
import com.teamway.security.service.CardService;
import com.teamway.security.vo.securityConfiguration.card.CardListVo;
import com.teamway.security.vo.securityConfiguration.card.CardReturnVo;
import com.teamway.security.vo.securityConfiguration.card.ReadCardDeviceVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/14
 */
@RestController
@RequestMapping("/securityConfiguration/card")
public class CardController {

    private CardService cardService;

    @Autowired
    public void setCardService(CardService cardService) {
        this.cardService = cardService;
    }

    /**
     * 获取卡片信息
     * @param dto
     * @return
     */
    @PostMapping("/list")
    public ResultModel<List<CardListVo>> listCard(@RequestBody @Valid CardListQueryDto dto){
        IPage<CardListVo> list =  cardService.listCard(dto);
        return  ResultModel.success(list);
    }

    /**
     * 退卡
     * @param dto
     * @return
     */
    @PostMapping("/returnCard")
    public ResultModel<CardReturnVo> returnCard(@RequestBody @Valid CardReturnQueryDto dto){
        return ResultModel.success(cardService.returnCard(dto));
    }

    /**
     * 卡挂失
     * @param dto
     * @return
     */
    @PostMapping("/reportTheLoss")
    public ResultModel<String> cardReportTheLoss(@RequestBody @Valid CardReportTheLossQueryDto dto){
        return cardService.cardReportTheLoss(dto) ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 获取读卡程序下载链接
     * @return
     */
    @GetMapping("/getReadCardDeviceUrl")
    public ResultModel<String> getReadCardDeviceUrl(){
        return ResultModel.success(cardService.getReadCardDeviceUrl());
    }

    /**
     * 获取读卡设备号密码
     * @return
     */
    @GetMapping("/getReadCardDevice")
    public ResultModel<ReadCardDeviceVo> getReadCardDevice(){
        return ResultModel.success(cardService.getReadCardDevice());
    }
}
