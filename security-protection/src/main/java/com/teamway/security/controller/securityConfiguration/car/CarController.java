package com.teamway.security.controller.securityConfiguration.car;

import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.securityConfiguration.*;
import com.teamway.security.dto.securityConfiguration.car.CarAddAndUpdateReqDto;
import com.teamway.security.dto.securityConfiguration.person.AuthResultDto;
import com.teamway.security.dto.securityConfiguration.person.PersonOrCarBlackResDto;
import com.teamway.security.service.CarService;
import com.teamway.security.vo.securityConfiguration.person.PersonAuthedDeviceVo;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@RestController
@RequestMapping("/securityConfiguration/car")
public class CarController {

    @Autowired
    private CarService carService;

    public void setCarService(CarService carService) {
        this.carService = carService;
    }

    /**
     * 获取车辆列表
     * @param carGetReqDto
     * @return
     */
    @PostMapping("/get")
    public ResultModel<List<CarGetResDataListDto>> findPageList(@RequestBody @Valid CarGetReqDto carGetReqDto) {
        return ResultModel.success(carService.findPageList(carGetReqDto));
    }

    /**
     * 新增车辆
     * @param carAddAndUpdateReqDto
     * @return
     */
    @PostMapping("/add")
    public ResultModel<String> addCar(@RequestBody @Valid CarAddAndUpdateReqDto carAddAndUpdateReqDto) {
        return carService.addCar(carAddAndUpdateReqDto) ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 更新车辆
     * @param carUpdateReqDto
     * @return
     */
    @PostMapping("/update")
    public ResultModel<String> updateCar(@RequestBody @Valid CarAddAndUpdateReqDto carUpdateReqDto) {
        return carService.updateCar(carUpdateReqDto) ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 删除车辆
     * @param carDelReqDto
     * @return
     */
    @PostMapping("/del")
    public ResultModel<String> delCar(@RequestBody @Valid CarDelReqDto carDelReqDto) {
        return carService.delCar(carDelReqDto) ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 车辆授权
     * @param carAuthorizeReqDto
     * @return
     */
    @PostMapping("/authorize")
    public ResultModel<Map<String, AuthResultDto>> authorize(@RequestBody @Valid CarAuthorizeReqDto carAuthorizeReqDto) {
        return carService.authorize(carAuthorizeReqDto);
    }

    @PostMapping("/addToBlackList")
    public ResultModel<PersonOrCarBlackResDto> addToBlackList(@RequestBody @Valid CarIdsDto carIdsDto) {
        return carService.addToBlackList(carIdsDto);
    }

    @PostMapping("/delFromBlackList")
    public ResultModel<PersonOrCarBlackResDto> delFromBlackList(@RequestBody @Valid CarIdsDto carIdsDto) {
        return carService.delFromBlackList(carIdsDto);
    }

    /**
     * 下载车辆模板
     *
     * @param response
     */
    @PostMapping("/downloadCarTemplate")
    public void downloadCarTemplate(HttpServletResponse response) {
        carService.downloadCarTemplate(response);
    }

    /**
     * 导入车辆
     *
     * @param file
     * @param response
     */
    @PostMapping("/import")
    public void importCar(@RequestParam("file") MultipartFile file, HttpServletResponse response) {
        carService.importCar(file, response);
    }

    /**
     * 导出车辆
     * @param carGetReqDto
     * @param response
     */
    @PostMapping("/export")
    public void exportCar(@RequestBody @Valid CarGetReqDto carGetReqDto, HttpServletResponse response) {
        carService.exportCar(carGetReqDto, response);
    }

    /**
     * 获取车辆已授权设备
     * @param carId 车辆id
     * @return
     */
    @GetMapping("/getCarAuthedDevice/{carId}")
    public ResultModel<List<PersonAuthedDeviceVo>> getCarAuthedDevice(@PathVariable("carId") Long carId){
        return ResultModel.success(carService.getCarAuthedDevice(carId));
    }

}
