package com.teamway.security.controller.securityConfiguration.park;


import com.teamway.common.entity.PageModel;
import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.SecurityParkGetReqDto;
import com.teamway.security.dto.SecurityParkRelateBarrierReqDto;
import com.teamway.security.entity.SecurityPark;
import com.teamway.security.service.SecurityParkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
@RestController
@RequestMapping("/securityPark")
public class SecurityParkController {

    private SecurityParkService securityParkService;

    @Autowired
    public void setSecurityParkService(SecurityParkService securityParkService) {
        this.securityParkService = securityParkService;
    }

    @PostMapping("/get")
    public ResultModel<List<SecurityPark>> findPageList(@RequestBody @Valid SecurityParkGetReqDto securityParkGetReqDto) {
        PageModel<List<SecurityPark>> pageModel = securityParkService.findPageList(securityParkGetReqDto);
        return ResultModel.success(pageModel.getData(), pageModel.getCount());
    }

    @PostMapping("/sync")
    public ResultModel<String> sync() {
        return securityParkService.sync();
    }

    @PostMapping("/relateBarrier")
    public ResultModel<String> relateBarrier(@RequestBody @Valid SecurityParkRelateBarrierReqDto securityParkRelateBarrierReqDto) {
        return securityParkService.relateBarrier(securityParkRelateBarrierReqDto);
    }
}
