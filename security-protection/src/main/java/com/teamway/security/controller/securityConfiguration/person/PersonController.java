package com.teamway.security.controller.securityConfiguration.person;

import cn.hutool.json.JSONUtil;
import com.teamway.common.entity.ResultModel;
import com.teamway.personnel.dto.PageHmBasicInfoSyncDto;
import com.teamway.security.dto.accessManagement.person.PersonAlarmDto;
import com.teamway.security.dto.securityConfiguration.person.*;
import com.teamway.security.dto.sync.PersonSyncBatchResultDto;
import com.teamway.security.service.CardService;
import com.teamway.security.service.PersonService;
import com.teamway.security.vo.securityConfiguration.card.CardVo;
import com.teamway.security.vo.securityConfiguration.person.PersonAuthedDeviceVo;
import com.teamway.security.vo.securityConfiguration.person.PersonListVo;
import com.teamway.security.vo.securityConfiguration.securityDevice.Sync4aPersonVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.teamway.personnel.vo.PageHmBasicInfoVo;
import com.teamway.base.service.CommonTypeService;
import com.teamway.base.entity.CommonType;
import com.teamway.common.constant.CommonConstant;

import lombok.extern.slf4j.Slf4j;

/**
 * @ClossName PersonController
 * @Description Person前端控制器
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
@RestController
@RequestMapping("/securityConfiguration/person")
@Slf4j
public class PersonController {

    private PersonService personService;
    private CardService cardService;

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setCardService(CardService cardService) {
        this.cardService = cardService;
    }


    /**
     * 添加人员
     * @param dto
     * @return
     */
    @PostMapping("/add")
    public ResultModel<String> addPerson(@RequestBody @Valid PersonAddOrUpdateQueryDto dto) {
        return personService.addPerson(dto) ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 批量添加人员
     * @param dtoList 人员列表
     * @return
     */
    @PostMapping("/addByApprovalBatch")
    public ResultModel<PersonSyncBatchResultDto> addByApprovalBatch(@RequestBody @Valid List<PersonAddByApprovalDto> dtoList) {
        PersonSyncBatchResultDto result = personService.addByApprovalBatchWithResult(dtoList);
        return ResultModel.success(result);
    }



    /**
     * 获取人员列表
     * @param dto
     * @return
     */
    @PostMapping("/list")
    public ResultModel<List<PersonListVo>> listPerson(@RequestBody @Valid PersonListQueryDto dto) {
        return ResultModel.success(personService.listPerson(dto));
    }

    /**
     * 更新人员
     * @param dto
     * @return
     */
    @PostMapping("/update")
    public ResultModel<String> updatePerson(@RequestBody @Valid PersonAddOrUpdateQueryDto dto) {
        personService.updatePerson(dto);
        return ResultModel.success();
    }

    /**
     * 批量删除人员
     * @param dto
     * @return
     */
    @PostMapping("/batchDel")
    public ResultModel<String> batchDelPerson(@RequestBody @Valid PersonBatchDelQueryDto dto) {
        personService.batchDelPerson(dto);
        return ResultModel.success();
    }

    /**
     * 人员绑定卡片
     * @param dto
     * @return
     */
    @PostMapping("/bindingCard")
    public ResultModel<String> bindingCard(@RequestBody @Valid PersonBindingCardQueryDto dto) {
        personService.bindingCard(dto);
        return ResultModel.success();
    }

    /**
     * 获取人员下绑定的卡片
     * @param personId
     * @return
     */
    @GetMapping("/getCardByPersonId/{personId}")
    public ResultModel<List<CardVo>> getCardListByPersonId(@PathVariable("personId") Long personId) {
        return ResultModel.success(cardService.getCardListByPersonId(personId));
    }

    /**
     * 人员授权
     * @param dto
     * @return
     */
    @PostMapping("/personAuthorization")
    public ResultModel<Map<String, AuthResultDto>> personAuthorization(@RequestBody @Valid PersonAuthorizationQueryDto dto) {
        Map<String, AuthResultDto> map = personService.personAuthorization(dto);
        return ResultModel.success(map);
    }

    /**
     * 获取人员已授权的设备
     * @param personId
     * @return
     */
    @GetMapping("/getAuthedByPersonId/{personId}")
    public ResultModel<List<PersonAuthedDeviceVo>> getAuthedByPersonId(@PathVariable("personId") Long personId) {
        return ResultModel.success(personService.getAuthedByPersonId(personId));
    }

    /**
     * 获取人员已授权列表
     * @param personId
     * @return
     */
    @GetMapping("/getPersonAuthedDevice/{personId}")
    public ResultModel<List<PersonAuthedDeviceDto>> getPersonAuthedDevice(@PathVariable("personId") Long personId) {
        return ResultModel.success(personService.getPersonAuthedDevice(personId));
    }

    /**
     * 下载人员导入模板（基础版本）
     * @param response
     */
    @PostMapping("/downloadPersonExcelTemplateBasic")
    public void downloadPersonExcelTemplateBasic(HttpServletResponse response) {
        personService.downloadPersonExcelTemplate(response);
    }

    /**
     * 下载人员导入模板（增强版本）
     * @param response
     */
    @PostMapping("/downloadPersonExcelTemplate")
    public void downloadPersonExcelTemplate(HttpServletResponse response) {
        personService.downloadPersonExcelTemplateEnhanced(response);
    }

    /**
     * 导入人员（基础版本）
     * @param file
     * @param response
     */
    @PostMapping(value = "/importBasic")
    public void importPersonBasic(@RequestParam("file") MultipartFile file, HttpServletResponse response) {
        personService.importPerson(file, response);
    }

    /**
     * 导入人员（增强版本）
     * @param file
     * @param response
     */
    @PostMapping(value = "/import")
    public void importPerson(@RequestParam("file") MultipartFile file, HttpServletResponse response) {
        personService.importPersonEnhanced(file, response);
    }

    /**
     * 导出人员
     * @param dto
     * @param response
     */
    @PostMapping("/export")
    public void exportPerson(@RequestBody @Valid PersonListQueryDto dto, HttpServletResponse response) {
        personService.exportPerson(dto, response);
    }

    /**
     * 人脸采集
     * @return
     */
    @GetMapping("/personFaceCollect")
    public ResultModel<String> personFaceCollect() {
        String faceUrl = personService.personFaceCollect();
        return ResultModel.success(faceUrl);
    }

    /**
     * 获取人员单位列表
     * @return
     */
    @GetMapping("/getPersonUnitList")
    public ResultModel<List<String>> getPersonUnitList() {
        return ResultModel.success(personService.getPersonUnitList());
    }

    /**
     * 人员添加黑名单
     * @param queryDto
     * @return
     */
    @PostMapping("/addBlackList")
    public ResultModel<PersonOrCarBlackResDto> addBlackList(@RequestBody @Valid PersonBlackListAddQueryDto queryDto) {
        return ResultModel.success(personService.addBlackList(queryDto));
    }

    /**
     * 人员移除黑名单
     * @param queryDto
     * @return
     */
    @PostMapping("/removeBlackList")
    public ResultModel<PersonOrCarBlackResDto> removeBlackList(@RequestBody @Valid PersonBlackListAddQueryDto queryDto) {
        return ResultModel.success(personService.removeBlackList(queryDto));
    }

    /**
     * 获取人员部门列表
     * @return
     */
    @GetMapping("/getPersonDepartmentList")
    public ResultModel<List<String>> getPersonDepartmentList() {
        return ResultModel.success(personService.getPersonDepartmentList());
    }

    /**
     * 获取人员工种
     * @return
     */
    @GetMapping("/getPersonJobList ")
    public ResultModel<List<String>> getPersonJobList() {
        return ResultModel.success(personService.getPersonDepartmentList());
    }

    /**
     * 获取人员岗位列表
     * @return
     */
    @GetMapping("/getPersonPostList")
    public ResultModel<List<String>> getPersonPostList() {
        return ResultModel.success(personService.getPersonPostList());
    }

    /**
     * 修改人员授权时间
     * @param dto
     * @return
     */
    @PostMapping("/editPersonAuthorization")
    public ResultModel<Sync4aPersonVo> editPersonAuthorization(@RequestBody @Valid EditPersonAuthorizationQueryDto dto) {
        return ResultModel.success(personService.editPersonAuthorization(dto));
    }

    /**
     * 删除人员授权
     * @param dto
     * @return
     */
    @PostMapping("/deletePersonAuthorization")
    public ResultModel<Sync4aPersonVo> deletePersonAuthorization(@RequestBody @Valid DeletePersonAuthorizationQueryDto dto) {
        return ResultModel.success(personService.deletePersonAuthorization(dto));
    }

    /**
     * 查僵尸数据
     * @return
     */
    @GetMapping("/selectZombiePerson")
    public ResultModel<String> selectZombiePerson() {
//        personService.selectZombiePerson();
        return ResultModel.success();
    }


    /**
     * 清理僵尸数据
     * @return
     */
    @PostMapping("/deleteZombiePerson")
    public ResultModel<String> deleteZombiePerson() {
        personService.deleteZombiePerson();
        return ResultModel.success();
    }


    /**
     * 分页查门禁告警记录
     * @param personAlarmDto
     * @return
     */
    @PostMapping("/selectAlertRecord")
    public ResultModel selectAlertRecord(@RequestBody @Valid PersonAlarmDto personAlarmDto) {
        return ResultModel.success(personService.selectAlertRecord(personAlarmDto));
    }

    /**
     * 批量导入人员图片
     * @param dto
     * @return
     */
    @PostMapping(value = "/addPersonPic",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResultModel<PersonAddPicResponseDto> addPersonPic(@ModelAttribute @Valid PersonAddPicQueryDto dto){
        return ResultModel.success(personService.addPersonPic(dto));
    }

    /**
     * 同步人员信息
     * @param personList 人员信息列表
     * @return
     */
    @PostMapping("/syncPersonInfo")
    public ResultModel<PersonSyncBatchResultDto> syncPersonInfo(@RequestBody List<PageHmBasicInfoSyncDto> personList) {
        PersonSyncBatchResultDto result = personService.syncPersonInfo(personList);
        return ResultModel.success(result);
    }

}
