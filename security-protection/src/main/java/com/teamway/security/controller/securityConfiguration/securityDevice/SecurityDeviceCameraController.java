package com.teamway.security.controller.securityConfiguration.securityDevice;

import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.securityConfiguration.BandingCameraQueryDto;
import com.teamway.security.service.SecurityDeviceCameraService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/8
 */

@RestController
@RequestMapping("/securityConfiguration/securityDeviceCamera")
public class SecurityDeviceCameraController {

    private SecurityDeviceCameraService securityDeviceCameraService;

    @Autowired
    public void setSecurityDeviceCameraService(SecurityDeviceCameraService securityDeviceCameraService) {
        this.securityDeviceCameraService = securityDeviceCameraService;
    }

    /**
     * 查询安防设备绑定的摄像机
     * @param securityDeviceId
     * @return
     */
    @GetMapping("/getBandedCamera/{securityDeviceId}")
    public ResultModel<BandingCameraQueryDto> getBandedCamera(@PathVariable("securityDeviceId") Long securityDeviceId){
        BandingCameraQueryDto data = securityDeviceCameraService.getBandedCamera(securityDeviceId);
        return ResultModel.success(data);
    }

}
