package com.teamway.security.controller.securityConfiguration.securityDevice;

import com.teamway.common.entity.ResultModel;
import com.teamway.security.common.tree.TreeData;
import com.teamway.security.dto.AccessRecordQueryDto;
import com.teamway.security.dto.accessManagement.securityDevice.SecurityDeviceTreeBuildedQueryDto;
import com.teamway.security.dto.securityConfiguration.BandingCameraQueryDto;
import com.teamway.security.dto.securityConfiguration.SecurityDeviceAddOrUpdateQueryDto;
import com.teamway.security.dto.securityConfiguration.SecurityDeviceDelQueryDto;
import com.teamway.security.dto.securityConfiguration.SecurityDeviceListQueryDto;
import com.teamway.security.dto.securityConfiguration.securityDevice.SecurityDeviceListForAuth;
import com.teamway.security.entity.SecurityConfiguration.DeviceFunction;
import com.teamway.security.entity.SecurityConfiguration.SecurityDevice;
import com.teamway.security.service.SecurityDeviceRecordService;
import com.teamway.security.service.SecurityDeviceService;
import com.teamway.security.vo.securityConfiguration.securityDevice.SyncDeviceVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/8
 */
@RestController
@RequestMapping("/securityConfiguration/securityDevice")
public class SecurityDeviceController {

    private SecurityDeviceService securityDeviceService;
    private SecurityDeviceRecordService recordService;

    @Autowired
    public void setSecurityDeviceService(SecurityDeviceService securityDeviceService) {
        this.securityDeviceService = securityDeviceService;
    }
    @Autowired
    public void setRecordService(SecurityDeviceRecordService recordService) {
        this.recordService = recordService;
    }

    /**
     * 添加安防设备
     * @param dto
     * @return
     */
    @PostMapping("/add")
    public ResultModel<String> addSecurityDevice(@RequestBody @Valid SecurityDeviceAddOrUpdateQueryDto dto){
        securityDeviceService.addSecurityDevice(dto);
        return ResultModel.success();
    }

    /**
     * 查询安防设备
     * @param dto
     * @return
     */
    @PostMapping("/list")
    public ResultModel<List<SecurityDevice>> listSecurityDevices(@RequestBody @Valid SecurityDeviceListQueryDto dto){
        return ResultModel.success(securityDeviceService.listSecurityDevices(dto));
    }

    /**
     * 查询安防设备
     * @param dto 查询条件
     * @return 分页返回结果
     */
    @PostMapping("/selectPage")
    public ResultModel<List<SecurityDevice>> selectPage(@RequestBody @Valid SecurityDeviceListQueryDto dto){
        return ResultModel.success(securityDeviceService.selectPage(dto));
    }

    /**
     * 修改安防设备
     * @param dto 修改参数
     * @return 是否修改成功
     */
    @PostMapping("/update")
    public ResultModel<String> updateSecurityDevice(@RequestBody @Valid SecurityDeviceAddOrUpdateQueryDto dto){
        securityDeviceService.updateSecurityDevice(dto);
        return ResultModel.success();
    }

    /**
     * 批量删除安防设备
     * @param dto
     * @return
     */
    @PostMapping("/batchDel")
    public ResultModel<String> batchDelSecurityDevice(@RequestBody @Valid SecurityDeviceDelQueryDto dto){
        securityDeviceService.batchDelSecurityDevice(dto);
        return ResultModel.success();
    }

    /**
     * 安防设备绑定摄像机
     * @param dto
     * @return
     */
    @PostMapping("/bandingCamera")
    public ResultModel<String> bandingCamera(@RequestBody @Valid BandingCameraQueryDto dto){
        return securityDeviceService.bandingCamera(dto) ? ResultModel.success() : ResultModel.fail();
    }


    /**
     * 获取门禁区域摄像机信息
     * @return
     */
    @PostMapping("/regionCameraList")
    public ResultModel<List<TreeData>> getGuardRegCameraTree(@RequestBody @Valid SecurityDeviceTreeBuildedQueryDto dto) {
        return ResultModel.success(securityDeviceService.getRegCameraTree(dto));
    }





    /**
     * 出入记录分页
     * @param type 1门禁，2道闸,3所有
     * @return
     */
    @PostMapping("/getAccessRecordPage/{type}")
    public ResultModel< ? > getAccessRecordPage(@PathVariable("type") Integer type, @RequestBody AccessRecordQueryDto request) {
        return ResultModel.success(recordService.getAccessRecordPage(type, request));
    }

    /**
     * 设备授权统计
     * @param type 1门禁，2道闸
     * @return
     */
    @GetMapping("/getDeviceAccStat/{type}")
    public ResultModel<?> getDeviceAccStat(@PathVariable("type") Integer type) {
        return ResultModel.success(recordService.getDeviceAccStat(type));
    }

    /**
     * 根据设备类别同步设备数据
     *
     * @param syncDeviceType 设备类别
     * @return 同步结果
     */
    @PostMapping("/syncDevice")
    public ResultModel<SyncDeviceVo> syncDevice(@RequestParam("syncDeviceType") String syncDeviceType) {
        SyncDeviceVo vo = securityDeviceService.syncDevice(syncDeviceType);
        return ResultModel.success(vo);
    }

    @GetMapping("/getAllFunction")
    public ResultModel<List<DeviceFunction>> getAllFunction() {
        return ResultModel.success(securityDeviceService.getAllFunction());
    }

    /**
     * 查询人员未授权的安防设备列表
     * @param dto
     * @return
     */
    @PostMapping("/listForAuth")
    public ResultModel<List<SecurityDeviceListForAuth>> listForAuth(@RequestBody @Valid SecurityDeviceListQueryDto dto){
        return ResultModel.success(securityDeviceService.listForAuthSecurityDevices(dto));
    }
}
