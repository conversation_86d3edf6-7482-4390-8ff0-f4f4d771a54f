package com.teamway.security.controller.securityConfiguration.workGroup;

import com.teamway.common.entity.ResultModel;
import com.teamway.security.entity.SecurityConfiguration.WorkGroup;
import com.teamway.security.service.WorkGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/11/7
 */
@RestController
@RequestMapping("/workGroup")
public class WorkGroupController {

    private WorkGroupService workGroupService;

    @Autowired
    public void setWorkGroupService(WorkGroupService workGroupService) {
        this.workGroupService = workGroupService;
    }

    /**
     * 添加工作组
     * @param workGroup
     * @return
     */
    @PostMapping("/add")
    public ResultModel<String> addWorkGroup(@RequestBody @Valid WorkGroup workGroup){
        return workGroupService.addWorkGroup(workGroup) ? ResultModel.success() : ResultModel.fail();
    }
    /**
     * 修改工作组
     * @param workGroup
     * @return
     */
    @PostMapping("/update")
    public ResultModel<String> updateWorkGroup(@RequestBody @Valid WorkGroup workGroup){
        return workGroupService.updateWorkGroup(workGroup) ? ResultModel.success() : ResultModel.fail();
    }
    /**
     * 获取所有工作组树
     * @return
     */
    @GetMapping("/getAll")
    public ResultModel<List<WorkGroup>> getWorkGroupTree(){
        return ResultModel.success(workGroupService.getWorkGroupTree());
    }
    /**
     * 删除工作组
     * @param id
     * @return
     */
    @GetMapping("/del/{id}")
    public ResultModel<String> delWorkGroup(@PathVariable Long id){
        return workGroupService.delWorkGroup(id) ? ResultModel.success() : ResultModel.fail();
    }
}
