package com.teamway.security.controller.statistics;

import com.teamway.common.entity.ResultModel;
import com.teamway.security.entity.CockpitAlarmConfig;
import com.teamway.security.service.CockpitAlarmConfigService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @ClossName CockpitAlarmConfigController
 * @Description 驾驶舱亮点告警图片数据配置 前端控制器
 * <AUTHOR>
 * @createDate 2025/08/19
 * @version 1.0
 **/
@RestController
@RequestMapping("/cockpitAlarmConfig")
public class CockpitAlarmConfigController {

    @Autowired
    private CockpitAlarmConfigService cockpitAlarmConfigService;

    /**
     * 更新驾驶舱亮点告警配置
     * 
     * 功能说明：
     * 1. 支持新增亮点告警图片1-3及对应描述信息
     * 2. 支持编辑、上传后预览功能
     * 3. 保存后图片+描述显示至驾驶舱告警图片展示
     *
     * @return 操作结果
     * @param cockpitAlarmConfig 驾驶舱亮点告警配置对象
     */
    @PostMapping("/updateCockpitAlarmConfig")
    public ResultModel<String> updateCockpitAlarmConfig(@RequestBody @Valid CockpitAlarmConfig cockpitAlarmConfig) {
        Boolean operationResult = cockpitAlarmConfigService.insertOrUpdateCockpitAlarmConfig(cockpitAlarmConfig);
        return operationResult ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 查询驾驶舱亮点告警配置
     * 
     * 功能说明：
     * 1. 获取当前驾驶舱亮点告警配置信息
     * 2. 返回亮点告警图片1-3及对应描述信息
     * 3. 用于前端回显和预览
     * 
     * @return 驾驶舱亮点告警配置信息
     */
    @GetMapping("/selectCockpitAlarmConfig")
    public ResultModel<CockpitAlarmConfig> selectCockpitAlarmConfig() {
        CockpitAlarmConfig configResult = cockpitAlarmConfigService.selectCockpitAlarmConfig();
        return ResultModel.success(configResult);
    }
}