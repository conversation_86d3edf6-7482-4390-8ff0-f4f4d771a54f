package com.teamway.security.controller.statistics;

import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.DangerousProjectDailyQueryDto;
import com.teamway.security.entity.DangerousProjectDaily;
import com.teamway.security.service.DangerousProjectDailyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <p>
 * 当日危大工程表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Slf4j
@RestController
@RequestMapping("/dangerousProjectDaily")
public class DangerousProjectDailyController {

    @Autowired
    private DangerousProjectDailyService dangerousProjectDailyService;

    /**
     * 分页查询当日危大工程列表
     */
    @PostMapping("/page")
    public ResultModel<List<DangerousProjectDaily>> page(@RequestBody @Valid DangerousProjectDailyQueryDto queryDto) {
        return ResultModel.success(dangerousProjectDailyService.findPageList(queryDto));
    }

    /**
     * 获取当日危大工程详情
     */
    @GetMapping("/{id}")
    public ResultModel<DangerousProjectDaily> getById(@PathVariable("id") Long id) {
        return ResultModel.success(dangerousProjectDailyService.getDangerousProjectDailyById(id));
    }

    /**
     * 添加当日危大工程记录
     */
    @PostMapping("/add")
    public ResultModel<Boolean> add(@RequestBody @Valid DangerousProjectDaily dangerousProjectDaily) {
        return ResultModel.success(dangerousProjectDailyService.addDangerousProjectDaily(dangerousProjectDaily));
    }

    /**
     * 更新当日危大工程记录
     */
    @PostMapping("/update")
    public ResultModel<Boolean> update(@RequestBody @Valid DangerousProjectDaily dangerousProjectDaily) {
        return ResultModel.success(dangerousProjectDailyService.updateDangerousProjectDaily(dangerousProjectDaily));
    }

    /**
     * 批量删除当日危大工程记录
     */
    @PostMapping("/delete")
    public ResultModel<Boolean> delete(@RequestBody BatchIds request) {
        return ResultModel.success(dangerousProjectDailyService.batchDeleteDangerousProjectDaily(request));
    }
}