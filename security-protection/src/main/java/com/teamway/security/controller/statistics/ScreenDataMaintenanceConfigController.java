package com.teamway.security.controller.statistics;

import com.teamway.base.vo.CameraVo;
import com.teamway.common.entity.ResultModel;
import com.teamway.security.entity.ScreenDataMaintenanceConfig;
import com.teamway.security.service.ScreenDataMaintenanceConfigService;
import com.teamway.security.service.SecurityStatisticsService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClossName ScreenDataMaintenanceConfigController
 * @Description 大屏数据维护配置表，存储大屏数据配置及维护信息 前端控制器
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
@RestController
@RequestMapping("/screenDataMaintenanceConfig")
public class ScreenDataMaintenanceConfigController {

    @Autowired
    private ScreenDataMaintenanceConfigService screenDataMaintenanceConfigService;
    @Autowired
    private SecurityStatisticsService securityStatisticsService;
    /**
     * 更新大屏数据后台配置
     * @param screenDataMaintenanceConfigVo
     * @return
     */
    @PostMapping("/updateConfig")
    public ResultModel<String> insertOrUpdate(@RequestBody @Valid ScreenDataMaintenanceConfig screenDataMaintenanceConfigVo) {
        return screenDataMaintenanceConfigService.insertOrUpdate(screenDataMaintenanceConfigVo) ? ResultModel.success() : ResultModel.fail();
    }

    /**
     * 查大屏数据后台配置
     * @return
     */
    @GetMapping("/selectConfig")
    public ResultModel<ScreenDataMaintenanceConfig> selectConfig() {
        return ResultModel.success(screenDataMaintenanceConfigService.selectConfig());
    }


    /**
     * 根据大屏数据后台配置查摄像机信息
     * @return
     */
    @GetMapping("/selectCarmeraByconfig")
    public ResultModel<List<CameraVo>> selectCarmeraByconfig() {
        return ResultModel.success(securityStatisticsService.selectCarmeraByconfig());
    }
}
