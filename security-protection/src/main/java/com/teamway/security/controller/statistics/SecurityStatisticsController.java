package com.teamway.security.controller.statistics;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.mis.DeliveryPlanQueryDTO;
import com.teamway.security.dto.mis.MilestonePlanQueryDTO;
import com.teamway.security.service.SecurityDeviceRecordService;
import com.teamway.security.service.SecurityStatisticsService;
import com.teamway.security.vo.ProjectStatusInfoVo;
import com.teamway.security.vo.mis.DeliveryPlanStatVO;
import com.teamway.security.vo.mis.MilestonePlanVO;
import com.teamway.security.vo.statistics.*;
import com.teamway.security.dto.statistics.FactoryPersonDetailQueryDto;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClossName SecurityStatisticsController
 * @Description 安防统计前端控制器
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
@RestController
@RequestMapping("/securityStatistics")
public class SecurityStatisticsController {

    @Autowired
    private SecurityDeviceRecordService securityDeviceRecordService;
    @Autowired
    private SecurityStatisticsService securityStatisticsService;

    /**
     *获取项目状态信息：获取已施工天数，以及摄像机、门禁、道闸的总数、在线数量、离线数量、在线率
     * @return
     */
    @GetMapping("/getLiveProjectStatus")
    public ResultModel<ProjectStatusInfoVo> getLiveProjectStatus() {
        return ResultModel.success(securityStatisticsService.getLiveProjectStatus());
    }

    /**
     * 按类型统计当日车辆进出数量
     * @return
     */
    @GetMapping("/getCarStatistics")
    public ResultModel<List<CarTypeCountVO>> getCarStatistics() {
        return ResultModel.success(securityDeviceRecordService.getCarStatistics());
    }

    /**
     * 统计当日车辆进出场情况
     * @return 车辆进出场统计数据
     */
    @GetMapping("/getCarInOutStatistics")
    public ResultModel<CarInOutStatVO> getCarInOutStatistics() {
        CarInOutStatVO statistics = securityDeviceRecordService.getCarInOutStatistics();
        return ResultModel.success(statistics);
    }

    /**
     * 统计当日进厂人数、出厂人数、在场人数、不同工种在场人数
     * @return
     */
    @GetMapping("/getPersonPieStatistics")
    public ResultModel<PersonPieVO> getPersonPieStatistics() {
        return ResultModel.success(securityDeviceRecordService.getPersonPieStatistics());
    }

    /**
     * 免登录 统计当日进厂人数、出厂人数、在场人数、不同工种在场人数 统计访客
     * @return
     */
    @GetMapping("/getPersonPieStatisticsNoLogin")
    public ResultModel<PersonPieVO> getPersonPieStatisticsNoLogin() {
        return ResultModel.success(securityDeviceRecordService.getPersonPieStatisticsNoLogin());
    }

    /**
     * 统计注册总数、施工总数，按单位统计注册总数以及当日人员施工总数
     * @return
     */
    @GetMapping("/getPersonColStatistics")
    public ResultModel<PersonColVO> getPersonColStatistics() {
        return ResultModel.success(securityDeviceRecordService.getPersonColStatistics());
    }

    /**
     * 免登录 统计注册总数、施工总数，按单位统计注册总数以及当日人员施工总数 统计访客
     * @return
     */
    @GetMapping("/getPersonColStatisticsNoLogin")
    public ResultModel<PersonColVO> getPersonColStatisticsNoLogin() {
        return ResultModel.success(securityDeviceRecordService.getPersonColStatisticsNoLogin());
    }


    /**
     * 图纸交付统计
     * @param queryDTO
     * @return
     */
    @PostMapping("/getDeliveryPlanStatList")
    public ResultModel<List<DeliveryPlanStatVO>> getDeliveryPlanStatList(@RequestBody @Valid DeliveryPlanQueryDTO queryDTO) {
        return ResultModel.success(securityDeviceRecordService.getDeliveryPlanStatList(queryDTO));
    }

    /**
     * 里程碑计划
     * @param queryDTO
     * @return
     */
    @PostMapping("/misplustask/list")
    public ResultModel<List<MilestonePlanVO>> getMilestonePlanList(@RequestBody @Valid MilestonePlanQueryDTO queryDTO) {
        return ResultModel.success(securityStatisticsService.getMilestonePlanList(queryDTO));
    }

    /**
     * 横屏展示内容拼接
     */
    @GetMapping("/contentStitching/{id}")
    public ResultModel<String> contentStitching(@PathVariable("id") Long id) {
        return ResultModel.success(securityStatisticsService.contentStitching(id));
    }

    /**
     * 免登录 获取今日人员进出场趋势分析
     * @param timeRange 时间范围（小时数）
     * @param currentTime 当前时间（格式：yyyy-MM-dd HH:mm）
     * @return 人员进出场趋势数据列表
     */
    @GetMapping("/getPersonTrendStatisticsNoLogin")
    public ResultModel<List<PersonPieVO>> getPersonTrendStatisticsNoLogin(
            @RequestParam(required = false) Integer timeRange,
            @RequestParam(required = false) String currentTime) {
        return ResultModel.success(securityDeviceRecordService.getPersonTrendStatisticsNoLogin(timeRange, currentTime));
    }

    /**
     * 获取单位人员进出统计数据
     * @return 单位统计数据列表
     */
    @GetMapping("/getUnitStatistics")
    public ResultModel<List<UnitStatisticsVO>> getUnitStatistics() {
        List<UnitStatisticsVO> statistics = securityDeviceRecordService.getUnitStatistics();
        return ResultModel.success(statistics);
    }

    /**
     * 获取工人类型统计数据
     * @param timeRange 统计时间范围：day-日, week-周, month-月
     * @return 工人类型统计数据
     */
    @GetMapping("/getWorkerTypeStatistics")
    public ResultModel<List<WorkerTypeStatVO>> getWorkerTypeStatistics(@RequestParam String timeRange) {
        List<WorkerTypeStatVO> statistics = securityDeviceRecordService.getWorkerTypeStatistics(timeRange);
        return ResultModel.success(statistics);
    }

    /**
     * 获取工种统计数据
     * @param timeRange 统计时间范围：day-日, week-周, month-月
     * @return 工种统计数据
     */
    @GetMapping("/getWorkerJobStatistics")
    public ResultModel<List<JobTypeStatVO>> getWorkerJobStatistics(@RequestParam String timeRange) {
        List<JobTypeStatVO> statistics = securityDeviceRecordService.getWorkerJobStatistics(timeRange);
        return ResultModel.success(statistics);
    }

    /**
     * 获取特殊工种统计数据
     * @param timeRange 统计时间范围：day-日, week-周, month-月
     * @return 特殊工种统计数据
     */
    @GetMapping("/getSpecialWorkerStatistics")
    public ResultModel<List<SpecialWorkerStatVO>> getSpecialWorkerStatistics(@RequestParam String timeRange) {
        List<SpecialWorkerStatVO> statistics = securityDeviceRecordService.getSpecialWorkerStatistics(timeRange);
        return ResultModel.success(statistics);
    }

    /**
     * 免登录 统计全厂人员数据（进场人员、在场人员、出场人员及在场人员分类统计）
     * @return 全厂人员统计数据
     */
    @GetMapping("/getFactoryPersonStatisticsNoLogin")
    public ResultModel<FactoryPersonStatisticsVO> getFactoryPersonStatisticsNoLogin() {
        return ResultModel.success(securityDeviceRecordService.getFactoryPersonStatisticsNoLogin());
    }

    /**
     * 根据总包单位统计工人类型分类数据
     * @param unitCode 总包单位编码
     * @return 总包单位工人类型统计数据列表
     */
    @GetMapping("/getContractorWorkerTypeStatistics")
    public ResultModel<List<ContractorWorkerTypeStatVO>> getContractorWorkerTypeStatistics(@RequestParam String unitCode) {
        List<ContractorWorkerTypeStatVO> statistics = securityDeviceRecordService.getContractorWorkerTypeStatistics(unitCode);
        return ResultModel.success(statistics);
    }

    /**
     * 查询全厂在场人员详细信息
     *
     * @param queryDto 查询条件
     * @return 全厂在场人员详情分页数据
     */
    @PostMapping("/getFactoryPersonDetailPage")
    public ResultModel<List<FactoryPersonDetailVO>> getFactoryPersonDetailPage(@RequestBody FactoryPersonDetailQueryDto queryDto) {
        IPage<FactoryPersonDetailVO> pageData = securityDeviceRecordService.getFactoryPersonDetailPage(queryDto);
        return ResultModel.success(pageData);
    }

    /**
     * 根据总包单位编码获取分包单位下拉列表
     * 
     * 功能说明：
     * 1. 返回总包下的所有分包单位列表，包含总包自身
     * 2. 列表按单位名称排序，总包单位排在第一位
     * 3. 前端可根据业务需要自行决定默认选中项
     * 
     * @param contractorUnitCode 总包单位编码
     * @return 分包单位下拉列表
     */
    @GetMapping("/getSubcontractDropdownList")
    public ResultModel<List<SubcontractDropdownVO>> getSubcontractDropdownList(@RequestParam String contractorUnitCode) {
        List<SubcontractDropdownVO> dropdownList = securityDeviceRecordService.getSubcontractDropdownList(contractorUnitCode);
        return ResultModel.success(dropdownList);
    }

    /**
     * 根据单位编码获取工种岗位统计数据
     * 
     * 功能说明：
     * 1. 根据选择的单位获取工种和岗位统计数据
     * 2. 工种和岗位数据合并为一个列表，按人数降序排列
     * 3. 数据适用于条状图展示，颜色建议使用红色保持与单位统计在场类型颜色一致
     * 4. targetUnitCode只会传分包编码，不传则使用所有数据（总包维度）
     * 
     * @param contractorUnitCode 总包单位编码
     * @param targetUnitCode 目标分包单位编码（可选，不传则使用所有数据）
     * @return 工种岗位统计数据
     */
    @GetMapping("/getJobPostStatistics")
    public ResultModel<JobPostStatVO> getJobPostStatistics(
            @RequestParam String contractorUnitCode,
            @RequestParam(required = false) String targetUnitCode) {
        JobPostStatVO statistics = securityDeviceRecordService.getJobPostStatistics(contractorUnitCode, targetUnitCode);
        return ResultModel.success(statistics);
    }

}
