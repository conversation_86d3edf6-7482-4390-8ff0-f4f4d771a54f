package com.teamway.security.controller.sync;

import cn.hutool.json.JSONUtil;
import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.sync.PersonSyncResultDto;
import com.teamway.security.service.PersonSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClossName PersonSyncController
 * @Description 人员数据同步控制器，负责将审批服务的人员数据同步到安防平台
 * <AUTHOR>
 * @createDate 2025/5/28
 * @version 1.0
 **/
@RestController
@RequestMapping("/sync/person")
@RequiredArgsConstructor
@Slf4j
public class PersonSyncController {

    private final PersonSyncService personSyncService;

    /**
     * 同步已审批通过的人员数据到安防平台
     * @return 同步结果包含成功数量、失败数量、跳过数量及详细错误信息
     */
    @PostMapping("/syncApprovedPersons")
    public ResultModel<PersonSyncResultDto> syncApprovedPersons() {
        PersonSyncResultDto result = personSyncService.syncApprovedPersons();
        return ResultModel.success(result);
    }
} 