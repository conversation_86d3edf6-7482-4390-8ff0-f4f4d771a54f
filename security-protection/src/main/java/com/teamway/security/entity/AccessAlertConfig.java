package com.teamway.security.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 外设表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Getter
@Setter
@TableName("b_sec_access_alert_config")
public class AccessAlertConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 开始工作时间
     */
    @TableField("start_work_time")
    private String startWorkTime;

    /**
     * 结束工作时间
     */
    @TableField("end_work_time")
    private String endWorkTime;

    /**
     * 非工作时间作业是否告警
     */
    @TableField("is_alarm")
    private String isAlarm;

    /**
     * 超过几天定义为僵尸人员
     */
    /**
     * 超过几天定义为僵尸人员
     */
    @TableField("is_zombie_time")
    @Pattern(regexp = "^[1-9]\\d*$", message = "僵尸人员天数必须为正整数")
    private String isZombieTime;

    /**
     * 进出次数超过几次
     */
    @TableField("max_access_count")
    @Pattern(regexp = "^[1-9]\\d*$", message = "最大进出次数必须为正整数")
    private String maxAccessCount;

    /**
     * 滞留几小时告警
     */
    @TableField("stay_alert_time")
    @Pattern(regexp = "^[1-9]\\d*$", message = "滞留告警时间必须为正整数")
    private String stayAlertTime;

    /**
     * 配置版本
     */
    @TableField("config_version")
    private String configVersion;
}
