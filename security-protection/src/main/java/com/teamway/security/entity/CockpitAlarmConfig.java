package com.teamway.security.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClossName CockpitAlarmConfig
 * @Description 驾驶舱亮点告警图片数据配置表，存储驾驶舱告警图片及描述信息
 * <AUTHOR>
 * @createDate 2025/08/19
 * @version 1.0
 **/
@Getter
@Setter
@TableName("cockpit_alarm_config")
public class CockpitAlarmConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 亮点告警图片1
     */
    @TableField("alarm_image_1")
    private String alarmImage1;

    /**
     * 亮点告警描述1
     */
    @TableField("alarm_desc_1")
    private String alarmDesc1;

    /**
     * 亮点告警图片2
     */
    @TableField("alarm_image_2")
    private String alarmImage2;

    /**
     * 亮点告警描述2
     */
    @TableField("alarm_desc_2")
    private String alarmDesc2;

    /**
     * 亮点告警图片3
     */
    @TableField("alarm_image_3")
    private String alarmImage3;

    /**
     * 亮点告警描述3
     */
    @TableField("alarm_desc_3")
    private String alarmDesc3;

    /**
     * 配置名称
     */
    @TableField("config_name")
    private String configName;

    /**
     * 配置版本
     */
    @TableField("config_version")
    private String configVersion;
}