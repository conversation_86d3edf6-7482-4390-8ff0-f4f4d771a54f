package com.teamway.security.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.teamway.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 当日危大工程表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Getter
@Setter
@TableName("dangerous_project_daily")
public class DangerousProjectDaily extends BaseEntity<DangerousProjectDaily> {

    private static final long serialVersionUID = 1L;

    /**
     * 作业项目
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 危险因素
     */
    @TableField("danger_factors")
    private String dangerFactors;

    /**
     * 可能导致的后果
     */
    @TableField("possible_consequences")
    private String possibleConsequences;

    /**
     * 管控措施
     */
    @TableField("control_measures")
    private String controlMeasures;

    /**
     * 责任单位
     */
    @TableField("responsible_unit")
    private String responsibleUnit;

    /**
     * 责任人
     */
    @TableField("responsible_person")
    private String responsiblePerson;

    /**
     * 责任人联系电话
     */
    @TableField("responsible_person_phone")
    private String responsiblePersonPhone;

    /**
     * 建设单位负责人
     */
    @TableField("construction_unit_person")
    private String constructionUnitPerson;

    /**
     * 建设单位负责人联系电话
     */
    @TableField("construction_unit_person_phone")
    private String constructionUnitPersonPhone;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField("record_date")
    private Date recordDate;

    /**
     * 状态（0正常 1停用）
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}