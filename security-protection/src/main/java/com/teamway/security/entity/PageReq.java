package com.teamway.security.entity;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @describe:分页查询基类
 * @Date: 2021/02/22
 */
@Data
public class PageReq implements Serializable {

    private static final long serialVersionUID = -2820435827351876569L;
    /**
     * 页码
     */
    @Range(min = 1, max = 9999, message = "页码的取值范围是1-9999")
    private Integer pageIndex;
    /**
     * 页的大小
     */
    @Range(min = 1, max = 1000, message = "单页数据量的取值范围是1-1000")
    private Integer pageSize;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 是否升序   true升序  false降序
     */
    private Boolean isAsc = true;

    public String getSortBy() {
        //驼峰转下划线
        return StrUtil.toUnderlineCase(sortBy);
    }

}
