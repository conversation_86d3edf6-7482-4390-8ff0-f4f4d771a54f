package com.teamway.security.entity;

import lombok.Data;

/**
 * @ClossName PersonUnitCount
 * @Description 单位人员数量类
 * <AUTHOR>
 * @createDate 2024/12/20           
 * @version 1.0
 **/
@Data
public class PersonUnitCount {
    /**
     * 单位
     */
    private String personUnit;
    /**
     * 单位名称
     */
    private String personUnitName;

    /**
     * 注册总数
     */
    private Long  registerCount;
    /**
     * 施工区人数
     */
    private Long  workingCount;

    // 构造器
    public PersonUnitCount() {
        this.registerCount = registerCount;
        this.workingCount = workingCount;
    }
}
