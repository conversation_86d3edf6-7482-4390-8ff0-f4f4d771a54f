package com.teamway.security.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.teamway.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @ClossName ScreenDataMaintenanceConfig
 * @Description 大屏数据维护配置表，存储大屏数据配置及维护信息
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
@Getter
@Setter
@TableName("screen_data_maintenance_config")
public class ScreenDataMaintenanceConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 多个摄像头ID的JSON数组
     */
    @TableField("camera_ids")
    private String cameraIds;

    /**
     * 多个图片URL的JSON数组
     */
    @TableField("image_urls")
    private String imageUrls;

    /**
     * 施工开始时间，记录施工开始的时间
     */
    @TableField("start_time")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 配置名称，标识不同的大屏数据维护配置
     */
    @TableField("config_name")
    private String configName;
        /**
     * 配置版本
     */
    @TableField("config_version")
    private String configVersion;
}
