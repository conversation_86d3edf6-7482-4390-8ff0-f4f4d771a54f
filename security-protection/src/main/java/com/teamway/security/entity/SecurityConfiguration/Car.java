package com.teamway.security.entity.SecurityConfiguration;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.teamway.common.entity.BaseEntity;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("b_sec_car")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Car extends BaseEntity<Car> {

    private static final long serialVersionUID = 1L;

    /**
     * 人员ID
     */
    @TableField("person_id")
    private Long personId;

    /**
     * 车牌号
     */
    @TableField("car_no")
    private String carNo;

    /**
     * 车辆类型，
     */
    @TableField("car_type")
    private Integer carType;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 是否授权，0表示未授权，1表示授权
     */
    @TableField("is_auth")
    private Integer isAuth;

    /**
     * 0：不是访客，1：访客
     */
    @TableField("auth_type")
    private Integer authType;

    /**
     * 车辆在组件的唯一标识
     */
    @TableField("car_index")
    private String carIndex;

    /**
     * 授权开始时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "begin_time")
    private LocalDateTime beginTime;

    /**
     * 授权结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "end_time")
    private LocalDateTime endTime;

    /**
     * 车辆状态，是否在黑名单中  1：在黑名单   0：不在黑名单
     */
    @TableField(value = "state")
    private Integer state = 0;

    /**
     * 车辆在组件黑名单的唯一标识
     */
    @TableField("black_index")
    private String blackIndex;

    private String color;

    private String carImageUrl;
}
