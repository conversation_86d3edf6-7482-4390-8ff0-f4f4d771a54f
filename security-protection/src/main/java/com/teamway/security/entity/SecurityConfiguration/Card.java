package com.teamway.security.entity.SecurityConfiguration;

import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_sec_card")
public class Card extends BaseEntity<Card> {
    /**
     * 卡片编号
     */
    @Pattern(regexp = "^[A-F0-9]{8,16}$",message = "卡号格式：8~16个字符，支持数字和A~F大写字母组合。")
    @NotBlank(message = "卡片编号不能为空")
    private String cardNo;
    /**
     * 卡片类型 1：普通卡 2：临时卡
     */
    @NotNull(message = "卡片类型不能为空")
    @Pattern(regexp = "[12]",message = "卡片类型错误：1：普通卡 2：临时卡")
    private String cardType;
    /**
     * 人员id
     */
    private Long personId;
    /**
     * 卡片状态 1：正常，2：挂失
     */
    private String cardState;
}
