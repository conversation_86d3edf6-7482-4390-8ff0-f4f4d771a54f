package com.teamway.security.entity.SecurityConfiguration;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/11/9
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@TableName("b_sec_device_function")
public class DeviceFunction {

    private Long id;

    private String functionName;

}
