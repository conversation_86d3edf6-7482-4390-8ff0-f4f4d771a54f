package com.teamway.security.entity.SecurityConfiguration;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import com.teamway.security.dto.securityConfiguration.person.BiometricCharacteristicsDto;
import lombok.*;

/**
 * @Description 人员实体类
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_sec_person")
public class Person extends BaseEntity<Person> {

    /**
     * 人员证件号
     */
    private String personCertificateNum;
    /**
     * 人员证件类型，1：身份证  2：其他
     */
    private String personCertificateType;
    /**
     * 人员工号
     */
    private String personJobNum;
    /**
     * 人员工种  1：焊接工 2：安装工 3：塔吊操作工 4：机修工 5：土建工 6：普工 7：普通员工
     */
    private String personJobType;
    /**
     * 人员姓名
     */
    private String personName;
    /**
     * 人员手机号
     */
    private String personPhone;
    /**
     * 人员性别，1：男  2：女
     */
    private String personSex;
    /**
     * 人员类别，1：普通人员  2：访客人员  3:僵尸人员
     */
    private String personType;
    /**
     * 人员单位
     */
    private String personUnit;
    /**
     * 人员脸信息
     */
    private String personFaceUrl;
    /**
     * 人员在组件中的标识
     */
    private String personSourceIndex;

    @TableField(exist = false)
    private String numberOfCards;

    /**
     * 生物特征
     */
    @TableField(exist = false)
    private BiometricCharacteristicsDto biometricCharacteristics;

    /**
     * 人员状态，是否在黑名单中  1：在黑名单   0：不在黑名单
     */
    private String state;

    /**
     * 人员岗位
     */
    private String personPost;

    /**
     * 人员部门信息
     */
    private String personDepartment;
    /**
     * 工作组ID
     */
    private String personUnitId;
    /**
     * 出生日期
     */
    private String personBirthDate;
    /**
     * 人员身份类型 1：业主 2：监理 3：总包 4：施工人员
     */
    private String personIdentityType;
    /**
     * 办公室电话
     */
    private String personOfficePhone;
    /**
     * 人员邮箱
     */
    private String personEmail;
    /**
     * 人员地址
     */
    private String personAddress;

    /**
     * 工人类型
     */
    private String workerType;

    /**
     * 是否特殊工种，0：否 1：是
     */
    private String isSpecialWorker;

    /**
     * 分包单位
     */
    private String subcontractUnit;

    /**
     * 分包单位ID
     */
    private String subcontractUnitId;
}