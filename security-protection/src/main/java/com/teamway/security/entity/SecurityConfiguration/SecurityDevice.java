package com.teamway.security.entity.SecurityConfiguration;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.teamway.common.entity.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 安防设备
 * @Date create in 2023/5/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_sec_security_device")
public class SecurityDevice extends BaseEntity<SecurityDevice> {
    /**
     * 厂家，1：海康  2：大华
     */
    private String manufacturer;

    /**
     * 区域
     */
    private String regionId;

    /**
     * 安防设备名称
     */
    private String securityDeviceName;


    /**
     * 设备在组件中的唯一标识
     */
    private String deviceSourceIndex;

    /**
     * 设备类别 1：门禁，2：道闸
     */
    private String securityDeviceType;

    /**
     * 道闸所在车道编号
     */
    private String barrierRoadWayCode;

    /**
     * 设备类型
     */
    private String deviceTypeCode;

    /**
     * 安防设备下入口列表
     */
    @TableField(exist = false)
    private List<SecurityDeviceDoor> securityDevicePointList;

    /**
     * 设备功能 1远程开门、2人脸识别、3指纹识别、4卡片识别
     * 注：清源项目中，该字段保留，默认所有功能，由于组件需要，页面不做展示
     */
    @NotBlank(message = "设备功能不能为空")
    @TableField(exist = false)
    @JsonIgnore
    private List<String> function;

    /**
     * 门禁状态 1在线 0离线
     */
    private String online;

    /**
     * 离线时间
     */
    private String offLineTime;
}
