package com.teamway.security.entity.SecurityConfiguration;

import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import lombok.*;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_sec_security_device_camera")
public class SecurityDeviceCamera extends BaseEntity<SecurityDeviceCamera> {

    /**
     * 安防设备ID
     */
    private Long securityDeviceId;

    /**
     * 摄像机ID
     */
    private Long cameraId;

    /**
     * 预置位ID
     */
    private Long presetId;

}
