package com.teamway.security.entity.SecurityConfiguration;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.teamway.common.entity.BaseEntity;
import com.teamway.common.valid.annotation.IP;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/7/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_sec_security_device_door")
public class SecurityDeviceDoor extends BaseEntity<SecurityDeviceDoor> {

    /**
     * 安防设备硬件标识
     */
    private String doorId;
    /**
     * 安防设备下点位名称
     */
    @JsonProperty("securityDeviceName")
    private String doorName;
    /**
     * 安防设备ID
     */
    private Long securityDeviceId;
    @IP(ignoreEmptyStr = true)
    private String ip;

    private String username;

    private String password;
    @Pattern(regexp = "^([0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-5]{2}[0-3][0-5])$",message = "端口范围：0-65535")
    private String port;
    /**
     * 标识 0：不区分 1：进 2：出
     */
    @Pattern(regexp = "[012]",message = "进出标识错误：0：不区分 1：进 2：出")
    private String type;

    /**
     * 设备在组件中的唯一标识
     */
    private String deviceSourceIndex;

    /**
     * 设备在线状态 0：离线 1：在线
     */
    private String onlineStatus;
}
