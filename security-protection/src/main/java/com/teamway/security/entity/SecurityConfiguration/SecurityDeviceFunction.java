package com.teamway.security.entity.SecurityConfiguration;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description 设备功能关联实体
 * @Date create in 2023/11/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("b_sec_security_device_function")
public class SecurityDeviceFunction {

    private Long id;

    private Long securityDeviceId;

    private Long deviceFunctionId;

    public SecurityDeviceFunction(Long securityDeviceId, Long deviceFunctionId) {
        this.securityDeviceId = securityDeviceId;
        this.deviceFunctionId = deviceFunctionId;
    }
}
