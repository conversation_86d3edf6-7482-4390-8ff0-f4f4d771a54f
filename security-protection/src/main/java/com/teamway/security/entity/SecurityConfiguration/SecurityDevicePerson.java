package com.teamway.security.entity.SecurityConfiguration;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.teamway.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_sec_security_device_person")
public class SecurityDevicePerson extends BaseEntity<SecurityDevicePerson> {
    /**
     * 人员ID
     */
    private Long personId;
    /**
     * 安防设备下门禁点ID
     */
    private Long deviceId;
    /**
     * 开始时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startTime;
    /**
     * 结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endTime;

}
