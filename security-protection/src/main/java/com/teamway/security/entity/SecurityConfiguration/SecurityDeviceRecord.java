package com.teamway.security.entity.SecurityConfiguration;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.base.entity.Region;
import com.teamway.common.util.DateUtils;
import com.teamway.security.common.enums.DeviceType;
import com.teamway.security.common.enums.OperateType;
import com.teamway.security.dto.PersonCarReportDto;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2023/5/15 17:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_sec_security_device_record")
public class SecurityDeviceRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 开关门瞬时时间
     */
    private Date date;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 门禁点名称
     */
    private String securityDeviceDoorName;

    /**
     * 出入标志，标志出入记录：0：不能确定；1：进场记录；2：出场记录；3：不需要区分进出
     */
    private Integer state;

    /**
     * 用户名
     */
    private String personName;

    /**
     * 调用法唯一id标识 当前设置为安防人员id
     */
    private String personIndex;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车辆调用方唯一标识
     */
    private String carIndex;

    /**
     * 人员工种 1：焊接工 2：安装工 3：塔吊操作工 4：机修工 5：土建工 6：普工 7：普通员工
     */
    private Integer personType;

    /**
     * 车辆类型，
     */
    private Integer carType;

    /**
     * 授权类型1：普通人员  2：访客
     */
    private Integer authType;

    /**
     * 图片地址
     */
    private String picUrl;

    /**
     * 0：用户，1车辆
     */
    private Integer recordType;

    /**
     * 开门方式：0：不能确定；1：刷卡开门；2：人脸识别开门；3：指纹开门；4：远程开门 5:按钮开门 6：车辆通行事件 7:设备上线
     */
    private Integer openType;

    private String deviceId;
    /**
     * 区域ID
     */
    private String regionId;
    /**
     * 区域名称
     */
    private String regionName;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 身份证
     */
    private String idCard;


    public SecurityDeviceRecord(Person person, PersonCarReportDto response, String deviceName, Long deviceId, Region region) {
        this.date = response.getDate() == null ? null : DateUtils.formatDate(response.getDate());
        this.deviceName = deviceName;
        this.state = response.getSign();
        this.personName = person.getPersonName();
        this.workUnit = person.getPersonUnit();
        this.plateNo = person.getPersonSourceIndex();
        this.personType = StringUtils.isEmpty(person.getPersonJobType())
                ? null : Integer.parseInt(person.getPersonJobType());
        this.authType = StringUtils.isEmpty(person.getPersonType()) ? null : Integer.parseInt(person.getPersonType());
        this.picUrl = response.getPicUrl();
        this.recordType = OperateType.ServiceType.PERSON.getCode();
        this.openType = Integer.parseInt(response.getEventCode());
        this.deviceId = deviceId+"";
        this.personIndex = person.getPersonSourceIndex();
        this.regionName = ObjectUtil.isEmpty(region) ? "" : region.getName();
        this.regionId = ObjectUtil.isEmpty(region) ? null : region.getId().toString();
    }

    public SecurityDeviceRecord(Car car, PersonCarReportDto response, String securityDeviceName, Long deviceId,Region region) {
        this.date = response.getDate() == null ? null : DateUtils.formatDate(response.getDate());
        this.deviceName = securityDeviceName;
        this.state = response.getSign();
        this.carType = car.getCarType();
        this.picUrl = response.getPicUrl();
        this.recordType = OperateType.ServiceType.CAR.getCode();
        this.openType = Integer.parseInt(response.getEventCode());
        this.deviceId = deviceId+"";
        this.carIndex = car.getCarIndex();
        this.plateNo = car.getCarNo();
        this.authType = org.springframework.util.StringUtils.isEmpty(car.getAuthType()) ? null : car.getAuthType();
        this.regionId = ObjectUtil.isEmpty(region) ? null : region.getId().toString();
        this.regionName = ObjectUtil.isEmpty(region) ? "" : region.getName();
    }

    public SecurityDeviceRecord(String deviceId, String date, DeviceType deviceType,String deviceName) {

        this.deviceId = deviceId;
        this.date = date == null ? null : DateUtils.formatDate(date);
        this.personName = "未知(远程开门)";
        this.plateNo = "未知(远程开门)";
        this.authType = 0; // 未知
        this.recordType = deviceType == DeviceType.GUARD ? OperateType.ServiceType.PERSON.getCode()
                : OperateType.ServiceType.CAR.getCode();
        this.openType = 4;
        this.state = 0; //不能确定
        this.deviceName = StrUtil.isEmpty(deviceName) ? deviceType.getDesc() : deviceName;
    }

    public SecurityDeviceRecord(String deviceId, String date, DeviceType deviceType,String deviceName,String eventCode) {

        this.deviceId = deviceId;
        this.date = date == null ? null : DateUtils.formatDate(date);
        this.personName = "未知(远程开门)";
        this.plateNo = "未知(远程开门)";
        this.authType = 0; // 未知
        this.recordType = deviceType == DeviceType.GUARD ? OperateType.ServiceType.PERSON.getCode()
                : OperateType.ServiceType.CAR.getCode();
        this.openType = Integer.parseInt(eventCode);
        this.state = 0; //不能确定
        this.deviceName = StrUtil.isEmpty(deviceName) ? deviceType.getDesc() : deviceName;
        switch (eventCode){
            case "5":
                this.personName = "按钮开门";
                break;
        }
    }
}