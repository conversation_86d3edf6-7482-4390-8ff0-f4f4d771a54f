package com.teamway.security.entity.SecurityConfiguration;

import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_sec_security_park")
public class SecurityParkCar extends BaseEntity<SecurityParkCar> {
    /**
     * 停车场ID
     */
    private Long securityParkId;
    /**
     * 车辆ID
     */
    private Long carId;
    /**
     * 授权开始时间
     */
    private LocalDateTime startTime;
    /**
     * 授权结束时间
     */
    private LocalDateTime endTime;
    /**
     * 是否授权：1：已授权，0：未授权
     */
    private String isAuth;

}
