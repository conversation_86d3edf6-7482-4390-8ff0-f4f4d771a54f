package com.teamway.security.entity.SecurityConfiguration;

import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/8/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_sec_security_peripheral")
public class SecurityPeripheral extends BaseEntity<SecurityPeripheral> {

    private String ip;

    private String username;

    private String password;

    /**
     * 类型：1 多功能扫描仪
     */
    private String type;

    private String url;

    private String remark;

}
