package com.teamway.security.entity.SecurityConfiguration;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 工作组
 * @Date create in 2023/11/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_sec_work_group")
public class WorkGroup extends BaseEntity<WorkGroup> {
    /**
     * 工作组名称
     */
    @NotBlank(message = "工作组名称不能为空")
    @Length(max = 50,message = "工作组名称长度不能超过50字符")
    private String name;
    /**
     * 工作组简称
     */
    @Length(max = 20,message = "工作组简称长度不能超过20字符")
    private String simpleName;
    /**
     * 工作组父ID
     */
    @NotNull(message = "工作组父ID不能为空")
    private Long pid;
    /**
     * 工作组子类
     */
    @TableField(exist = false)
    private List<WorkGroup> children;

}
