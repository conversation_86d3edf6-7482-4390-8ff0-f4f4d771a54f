package com.teamway.security.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("b_sec_security_park")
public class SecurityPark extends BaseEntity<SecurityPark> {

    private static final long serialVersionUID = 1L;

    /**
     * 停车库在组件中的唯一标识
     */
    @TableField("park_source_index")
    private String parkSourceIndex;

    /**
     * 停车库名称
     */
    @TableField("name")
    private String name;

    /**
     * 关联的道闸
     */
    @TableField(exist = false)
    private String barriers;
}
