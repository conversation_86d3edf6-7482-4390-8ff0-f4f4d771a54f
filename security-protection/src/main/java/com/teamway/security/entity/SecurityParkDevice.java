package com.teamway.security.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.teamway.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("b_sec_security_park_device")
public class SecurityParkDevice extends BaseEntity<SecurityParkDevice> {

    private static final long serialVersionUID = 1L;

    /**
     * 停车场Id
     */
    @TableField("park_id")
    private Long parkId;

    /**
     * 设备Id
     */
    @TableField("device_id")
    private Long deviceId;

}
