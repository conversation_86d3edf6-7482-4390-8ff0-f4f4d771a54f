package com.teamway.security.entity.accessManagement.visitorReservationManage;

import com.teamway.security.common.valid.annotation.CarNoCheck;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccompanyingPerson{
    /**
     * 车牌号
     */
    @CarNoCheck(ignoreEmptyStr = true)
    private String carNo;
    /**
     * 车辆类型
     */
    @Pattern(regexp = "^\\d+$", message = "随行人车辆类型不正确")
    private String carType;
    /**
     * 身份证
     */
    @Pattern(regexp = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)",message = "随行人身份证格式错误")
    @NotBlank(message = "随行人身份证不能为空")
    private String certification;
    /**
     * 姓名
     */
    @NotBlank(message = "随行人姓名不能为空")
    @Pattern(regexp = "[\\u4e00-\\u9fa5]{1,32}",message = "人员姓名错误：只能中文，长度1~32")
    private String name;
    /**
     * 手机号
     */
    @NotBlank(message = "随行人手机号不能为空")
    @Pattern(regexp = "^\\d{11}$",message = "随行人手机号格式错误")
    private String phone;
    /**
     * 性别
     */
    @Pattern(regexp = "[012]",message = "随行人性别错误")
    @NotNull(message = "随行人性别不能为空")
    private String sex;

    /**
     * 人脸图片
     */
    private String personFaceUrl;

    /**
     * 人员携带物品
     */
    private String personCarryItems;
    /**
     * 车辆运载物品
     */
    private String transportGoods;
    /**
     * 车辆品牌
     */
    private String carBrand;
}