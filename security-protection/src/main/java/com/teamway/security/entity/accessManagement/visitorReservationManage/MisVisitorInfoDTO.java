package com.teamway.security.entity.accessManagement.visitorReservationManage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;

/**
 * 访客个人信息
 */
@Data
public class MisVisitorInfoDTO {
    /** 访客信息ID */
    private String id;

    /** 创建人ID */
    private String sysCreateby;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysCreatetime;

    /** 更新人ID */
    private String sysUpdateby;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysUpdatetime;

    /** 所属岗位ID */
    private String sysPostId;

    /** 部门ID */
    private String sysDivisionId;

    /** 组织ID */
    private String sysOrgId;

    /** 删除标记 */
    private Boolean sysDeleteflag;

    /** 关联的主表ID */
    private String tvgVisitorMainId;

    /** 访客姓名 */
    private String visitorName;

    /** 访客性别 */
    private String visitorSex;

    /** 访客手机号 */
    private String visitorPhone;

    /** 访客身份证号 */
    private String visitorCertification;

    /** 访客车牌号 */
    private String visitorCarNo;

    /** 访客车辆类型 */
    private String visitorCarType;

    /** 随身携带物品 */
    private String visitorBelongings;

    /** 车辆运载物品 */
    private String visitorCarrythings;
}