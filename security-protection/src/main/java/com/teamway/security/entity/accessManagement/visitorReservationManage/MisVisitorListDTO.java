package com.teamway.security.entity.accessManagement.visitorReservationManage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 访客预约响应DTO（MIS系统）
 */
@Data
public class MisVisitorListDTO {
    /** 响应状态码 200表示成功 */
    private Integer code;

    /** 访客数据列表 */
    private List<MisVisitorMainDTO> data;

    /** 是否需要转换用户ID */
    private Boolean convertUserId;
}