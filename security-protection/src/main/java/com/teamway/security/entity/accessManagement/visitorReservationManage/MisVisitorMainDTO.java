package com.teamway.security.entity.accessManagement.visitorReservationManage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 访客主表信息
 */
@Data
public class MisVisitorMainDTO {
    /** 主键ID */
    private String id;

    /** 创建人ID */
    private String sysCreateby;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysCreatetime;

    /** 更新人ID */
    private String sysUpdateby;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysUpdatetime;

    /** 所属岗位ID */
    private String sysPostId;

    /** 部门ID */
    private String sysDivisionId;

    /** 组织ID */
    private String sysOrgId;

    /** 删除标记 */
    private Boolean sysDeleteflag;

    /** 接待人ID */
    private String tfrPersonId;

    /** 接待人姓名 */
    private String tfrPerson;

    /** 接待人部门ID */
    private String tfrCompId;

    /** 接待人部门名称 */
    private String tfrComp;

    /** 接待人手机号 */
    private String phoneNumber;

    /** 预约来源(1:APP 2:访客机 3:平台) */
    private String source;

    /** 流程实例ID */
    private String flowInstanceId;

    /** 流程编号 */
    private String flowId;

    /** 流程状态 */
    private String flowStartflag;

    /** 流程业务状态 */
    private String flowBizstate;

    /** 来访单位 */
    private String visitComp;

    /** 预约结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date visitEndTime;

    /** 预约开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date visitStartTime;

    /** 来访事由 */
    private String visitReason;

    /** 访客信息列表 */
    private List<MisVisitorInfoDTO> visitorInfoList;
}