package com.teamway.security.entity.accessManagement.visitorReservationManage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 访客预约响应DTO
 * 用于封装访客预约相关的所有数据
 */
@Data
public class MisVisitorResponseDTO {
    /** 响应状态码 200表示成功 */
    private Integer code;

    /** 是否需要转换用户ID */
    private Boolean convertUserId;

    /** 分页数据内容 */
    private PageData data;

    /**
     * 分页数据封装类
     */
    @Data
    public static class PageData {
        /** 总记录数 */
        private Long total;

        /** 每页显示记录数 */
        private Integer pageSize;

        /** 当前页码 */
        private Integer currentPage;

        /** 总页数 */
        private Integer totalPages;

        /** 当前页的数据记录列表 */
        private List<MisVisitorMainDTO> records;

        /** 第一页页码 */
        private Integer firstPage;

        /** 上一页页码 */
        private Integer prevPage;

        /** 是否存在上一页 */
        private Boolean hasPreviousPage;

        /** 是否存在下一页 */
        private Boolean hasNextPage;

        /** 导航页码数量 */
        private Integer navigatePages;

        /** 所有导航页号 */
        private List<Integer> navigatePageNums;

        /** 是否为最后一页 */
        private Boolean lastPage;
    }
}