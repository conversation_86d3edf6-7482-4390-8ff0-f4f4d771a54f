package com.teamway.security.entity.accessManagement.visitorReservationManage;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.teamway.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_sec_reservation")
public class Reservation extends BaseEntity<Reservation> {
    /**
     * 访客编号
     */
    @TableField(exist = false)
    private String visitorNumber;
    /**
     * 自增序号
     */
    private Long serialNumber;
    /**
     * 预约编号
     */
    private String reservationId;
    /**
     * 来访人名称
     */
    private String visitorName;
    /**
     * 来访人性别
     */
    private String visitorSex;
    /**
     * 来访人手机号
     */
    private String visitorPhone;
    /**
     * 来访人身份证
     */
    private String visitorCertification;
    /**
     * 来访人车牌号
     */
    private String visitorCarNo;
    /**
     * 来访人车辆类型
     */
    private String visitorCarType;
    /**
     * 来访人单位
     */
    private String visitorUnit;
    /**
     * 预约来源 1：APP 2：访客机 3：平台
     */
    private String source;
    /**
     * 接待人名称（被访对象）
     */
    private String receptionistName;
    /**
     * 接待人手机号（被访对象）
     */
    private String receptionistPhone;
    /**
     * 接待人部门（被访对象）
     */
    private String receptionistDepartment;
    /**
     * 来访事由
     */
    private String visitorReason;
    /**
     * 预约状态：1：审核中 2：审批拒绝 3：审批通过 4：已开卡 5:开卡中
     */
    private String reservationState;
    /**
     * 预计来访时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reservationStartTime;
    /**
     * 预计离开时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reservationEndTime;
    /**
     * 访客人脸图片
     */
    private String personFaceUrl;

    /**
     * 人员添加标识 0：未添加 1：已添加
     */
    private String personAddFlag;
    /**
     * 卡片添加标识 0：未添加 1：已添加
     */
    private String cardAddFlag;
    /**
     * 人员授权标识 0：未授权 1：已授权
     */
    private String personAuthFlag;
    /**
     * 车辆添加标识 0：未添加 1：已添加
     */
    private String carAddFlag;
    /**
     * 车辆授权标识 0：未授权 1：已授权
     */
    private String carAuthFlag;

    /**
     * 人员携带物品
     */
    private String personCarryItems;
    /**
     * 车辆运载物品
     */
    private String transportGoods;
    /**
     * 车辆品牌
     */
    private String carBrand;


    //MIS基建系统新增字段
    /**
     * 所属岗位ID
     */
    @TableField(exist = false)
    private String sysPostId;

    /**
     * 部门ID
     */
    @TableField(exist = false)
    private String sysDivisionId;

    /**
     * 组织ID
     */
    @TableField(exist = false)
    private String sysOrgId;

    /**
     * 删除标记
     */
    @TableField(exist = false)
    private Boolean sysDeleteflag;

    /**
     * 接待人ID
     */
    @TableField(exist = false)
    private String tfrPersonId;

    /**
     * 接待人部门ID
     */
    @TableField(exist = false)
    private String tfrCompId;

    /**
     * 流程实例ID
     */
    @TableField(exist = false)
    private String flowInstanceId;

    /**
     * 流程编号
     */
    @TableField(exist = false)
    private String flowId;

    /**
     * 流程状态
     */
    @TableField(exist = false)
    private String flowStartflag;
}
