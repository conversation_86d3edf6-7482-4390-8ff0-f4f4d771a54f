package com.teamway.security.entity.accessManagement.visitorReservationManage.access;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/12/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("b_sec_in_plant_record")
public class InPlantRecord {
    /**
     * 在场记录表主键ID
     */
    @TableField(value = "id", fill = FieldFill.INSERT)
    private Long id;

    /**
     * 对象ID（人员/车辆）
     */
    private String objectId;

    /**
     * 进入时间
     */
    private Date entryTime;

    /**
     * 记录类型 1：人员  2：车辆
     */
    private String recordType;
}
