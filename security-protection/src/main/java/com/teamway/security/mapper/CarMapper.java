package com.teamway.security.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.teamway.security.dto.securityConfiguration.CarGetReqDto;
import com.teamway.security.dto.securityConfiguration.CarGetResDataListDto;
import com.teamway.security.entity.SecurityConfiguration.Car;
import com.teamway.security.entity.SecurityConfiguration.Person;
import com.teamway.security.vo.securityConfiguration.person.PersonAuthedDeviceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
public interface CarMapper extends BaseMapper<Car> {
    IPage<CarGetResDataListDto> findPageList(IPage<?> page, @Param("dto") CarGetReqDto carGetReqDto);

    /**
     * 获取车辆已授权设备
     * @param carId
     * @return
     */
    List<PersonAuthedDeviceVo> getAuthedByCarId(Long carId);

    /**
     * 根据车牌号获取车主信息信息
     * @param licensePlate
     * @return
     */
    Person getCarPersonInfo(String licensePlate);
}
