package com.teamway.security.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.teamway.security.dto.securityConfiguration.card.CardListQueryDto;
import com.teamway.security.entity.SecurityConfiguration.Card;
import com.teamway.security.vo.securityConfiguration.card.CardListVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/13
 */
public interface CardMapper extends BaseMapper<Card> {
    /**
     * 分页查询卡列表
     * @param cardListVoPage
     * @param dto
     * @return
     */
    IPage<CardListVo> listCard(Page<CardListVo> cardListVoPage, @Param("dto") CardListQueryDto dto);
}
