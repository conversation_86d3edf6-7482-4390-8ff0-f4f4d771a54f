package com.teamway.security.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseMapper;
import com.teamway.security.dto.accessManagement.person.PersonAlarmDto;
import com.teamway.security.dto.securityConfiguration.person.PersonAuthedDeviceDto;
import com.teamway.security.dto.securityConfiguration.person.PersonListQueryDto;
import com.teamway.security.entity.SecurityConfiguration.Person;
import com.teamway.security.vo.accessManagement.person.personAlarmVo;
import com.teamway.security.vo.securityConfiguration.person.PersonAuthedDeviceVo;
import com.teamway.security.vo.securityConfiguration.person.PersonListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/8
 */
@Mapper
public interface PersonMapper extends MPJBaseMapper<Person> {
    /**
     * 获取人员已授权设备
     * @param personId
     * @return
     */
    List<PersonAuthedDeviceVo> getAuthedByPersonId(Long personId);

    /**
     * 获取人员已授权设备
     * @param personId
     * @return
     */
    List<PersonAuthedDeviceDto> getPersonAuthedDevice(@Param("personId") Long personId);

    /**
     * 分页查人员列表
     * @param page
     * @param dto
     * @return
     */
    IPage<PersonListVo> selectPersonList(Page<?> page,@Param("dto") PersonListQueryDto dto);

    /**
     * 分页查门禁告警记录
     * @param page
     * @param dto
     * @return
     */
    IPage<personAlarmVo> selectAlertRecord(Page page,@Param("dto") PersonAlarmDto dto);

    List<PersonAuthedDeviceDto> getPersonAuthedDeviceWithDetails(Long personId);

    /**
     * 分页查询全厂在场人员详情
     * @param page 分页参数
     * @param personIds 人员身份证号列表
     * @param personName 人员姓名（模糊查询）
     * @param contractorUnitName 总包单位（模糊查询）
     * @param subcontractUnitName 分包单位（模糊查询）
     * @param jobTypeName 工种（模糊查询）
     * @param personPost 岗位（模糊查询）
     * @return 分页结果
     */
    IPage<Person> selectFactoryPersonDetailPage(Page<Person> page, 
                                               @Param("personIds") List<String> personIds,
                                               @Param("personName") String personName,
                                               @Param("contractorUnitName") String contractorUnitName,
                                               @Param("subcontractUnitName") String subcontractUnitName,
                                               @Param("jobTypeName") String jobTypeName,
                                               @Param("personPost") String personPost);
}
