package com.teamway.security.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teamway.security.dto.securityConfiguration.BandingCameraQueryDto;
import com.teamway.security.entity.SecurityConfiguration.SecurityDeviceCamera;
import com.teamway.security.vo.accessManagement.securityDevice.BandedCameraVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/8
 */
@Mapper
public interface SecurityDeviceCameraMapper extends BaseMapper<SecurityDeviceCamera> {
    /**
     * 查询安防设备绑定的摄像机
     * @param securityDeviceId
     * @return
     */
    BandingCameraQueryDto getCameraBySecurityDeviceId(Long securityDeviceId);

    /**
     * 查询安防设备绑定的摄像机（包含摄像机信息）
     * @param securityDeviceId
     * @return
     */
    BandedCameraVo getCameraInfoBySecurityDeviceId(Long securityDeviceId);
}
