package com.teamway.security.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseMapper;
import com.teamway.security.dto.securityConfiguration.SecurityDeviceListQueryDto;
import com.teamway.security.entity.SecurityConfiguration.SecurityDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/8
 */
@Mapper
public interface SecurityDeviceMapper extends MPJBaseMapper<SecurityDevice> {

    IPage<SecurityDevice> selectPage(@Param("dto") SecurityDeviceListQueryDto dto, Page<SecurityDevice> page);
}
