package com.teamway.security.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseMapper;
import com.teamway.security.dto.AccessRecordQueryDto;
import com.teamway.security.dto.accessManagement.car.EventListCarDto;
import com.teamway.security.dto.accessManagement.person.EventListPersonDto;
import com.teamway.security.dto.accessManagement.person.EventListPersonMisDto;
import com.teamway.security.entity.SecurityConfiguration.SecurityDeviceRecord;
import com.teamway.security.vo.accessManagement.car.EventListCarVo;
import com.teamway.security.vo.accessManagement.person.EventListPersonVo;
import com.teamway.security.vo.statistics.CarTypeCountVO;
import com.teamway.security.vo.statistics.CarInOutStatVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @ClossName SecurityDeviceRecordMapper
 * @Description 安防设备记录 mapper数据访问层
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
public interface SecurityDeviceRecordMapper extends MPJBaseMapper<SecurityDeviceRecord> {
    List<SecurityDeviceRecord> getAccessRecordPage(Integer type, AccessRecordQueryDto request);

    /**
     * 获取所有的记录人员id
     * @return
     */
    List<String> getRecordPersonIdList();

    /**
     * 获取指定人员下的所有设备id
     * @return
     */
    List<String> getRecordPersonDeviceIdList(String personId);

    /**
     * 获取人员列表
     *
     * @param queryDto
     * @param page
     * @return
     */
    IPage<EventListPersonVo> eventListByPerson(@Param("queryDto") EventListPersonDto queryDto, Page<EventListPersonVo> page);

    /**
     * 获取人员列表
     *
     * @param queryDto
     * @return
     */
    List<EventListPersonVo> eventListByPersonMis(@Param("queryDto") EventListPersonMisDto queryDto);

    /**
     * 获取车辆列表
     * @param queryDto
     * @param page
     * @return
     */
    IPage<EventListCarVo> eventListByCar(@Param("queryDto") EventListCarDto queryDto, Page<EventListCarVo> page);

    /**
     * 获取今天的记录
     * @param personId
     * @return
     */
    Integer selectTodyRecordCountByPersonId(String personId);

    /**
     * 查最后一次进的记录
     * @return
     */
    List<SecurityDeviceRecord> getLatestAccessControlRecord();
    /**
     * 按类型统计当日车辆进出数量
     * @return
     */
    List<CarTypeCountVO> getCarStatistics();

    /**
     * 获取当日车辆进出记录
     * @return 车辆进出记录列表
     */
    List<Map<String, Object>> getCarInOutStatistics();
}


