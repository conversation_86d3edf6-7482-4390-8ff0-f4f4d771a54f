package com.teamway.security.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teamway.security.entity.SecurityConfiguration.WorkGroup;
import com.teamway.security.vo.WorkGroupPathVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/11/7
 */
@Mapper
public interface WorkGroupMapper extends BaseMapper<WorkGroup> {
    List<WorkGroupPathVO> selectWithFullPath();
}
