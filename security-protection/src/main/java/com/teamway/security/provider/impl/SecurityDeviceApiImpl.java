package com.teamway.security.provider.impl;

import com.teamway.commonapi.api.security.SecurityDeviceApi;
import com.teamway.commonapi.entity.SecurityDeviceInfo;
import com.teamway.security.entity.SecurityConfiguration.SecurityDevice;
import com.teamway.security.service.SecurityDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName SecurityDeviceApiImpl
 * @Description 安防设备API接口实现类
 * <AUTHOR>
 * @createDate 2025/5/14
 * @version 1.0
 **/
@Service
public class SecurityDeviceApiImpl implements SecurityDeviceApi {
    
    @Autowired
    private SecurityDeviceService securityDeviceService;
    
    @Override
    public SecurityDeviceInfo getSecurityDeviceById(String deviceId) {
        // 调用实际的服务获取数据
        SecurityDevice device = securityDeviceService.getById(deviceId);
        if (device == null) {
            return null;
        }
        
        // 转换为DTO对象
        return convertToInfo(device);
    }

    /**
     * 将实体对象转换为DTO
     *
     * @param device 安防设备实体
     * @return 安防设备信息DTO
     */
    private SecurityDeviceInfo convertToInfo(SecurityDevice device) {
        if (device == null) {
            return null;
        }

        SecurityDeviceInfo info = new SecurityDeviceInfo();

        // 手动设置必要属性
        info.setId(device.getId() != null ? device.getId().toString() : null);
        info.setRegionId(Long.valueOf(device.getRegionId()));
        info.setSecurityDeviceName(device.getSecurityDeviceName());
        info.setSecurityDeviceType(device.getSecurityDeviceType());
        info.setOnline(device.getOnline());

        return info;
    }
}