package com.teamway.security.service;

import com.teamway.security.entity.AccessAlertConfig;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 外设表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
public interface AccessAlertConfigService extends IService<AccessAlertConfig> {
    /**
     * 更新告警配置
     * @param alertConfig
     * @return
     */
    boolean updateAccessAlertConfig(AccessAlertConfig alertConfig);

    /**
     * 查询告警配置
     * @return
     */
    AccessAlertConfig getAccessAlertConfig();
}
