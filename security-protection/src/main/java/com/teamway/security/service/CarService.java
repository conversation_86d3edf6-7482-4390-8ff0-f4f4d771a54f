package com.teamway.security.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.securityConfiguration.*;
import com.teamway.security.dto.securityConfiguration.car.CarAddAndUpdateReqDto;
import com.teamway.security.dto.securityConfiguration.person.AuthResultDto;
import com.teamway.security.dto.securityConfiguration.person.PersonOrCarBlackResDto;
import com.teamway.security.entity.SecurityConfiguration.Car;
import com.teamway.security.entity.SecurityConfiguration.Person;
import com.teamway.security.vo.securityConfiguration.person.PersonAuthedDeviceVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
public interface CarService extends IService<Car> {

    IPage<CarGetResDataListDto> findPageList(CarGetReqDto carGetReqDto);

    boolean addCar(CarAddAndUpdateReqDto carAddAndUpdateReqDto);

    boolean updateCar(CarAddAndUpdateReqDto carUpdateReqDto);

    boolean delCar(CarDelReqDto carDelReqDto);

    ResultModel<Map<String, AuthResultDto>> authorize(CarAuthorizeReqDto carAuthorizeReqDto);

    /**
     * 车辆授权
     *
     * @param parkIdList 停车场
     * @param id
     * @param format
     * @param format1
     */
    void authorizeWithPark(String[] parkIdList, Long id, String format, String format1);

    ResultModel<PersonOrCarBlackResDto> addToBlackList(CarIdsDto carIdsDto);

    ResultModel<PersonOrCarBlackResDto> delFromBlackList(CarIdsDto carIdsDto);

    /**
     * 下载车辆模板
     * @return
     */
    void downloadCarTemplate(HttpServletResponse response);

    /**
     * 导入车辆
     * @param file
     * @param response
     */
    void importCar(MultipartFile file, HttpServletResponse response);

    /**
     * 导出车辆
     * @param carGetReqDto
     * @param response
     */
    void exportCar(CarGetReqDto carGetReqDto, HttpServletResponse response);

    List<PersonAuthedDeviceVo> getCarAuthedDevice(Long carId);

    Person getCarPersonInfo(String licensePlate);
}
