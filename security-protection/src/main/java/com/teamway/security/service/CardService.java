package com.teamway.security.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.security.dto.securityConfiguration.card.CardListQueryDto;
import com.teamway.security.dto.securityConfiguration.card.CardReportTheLossQueryDto;
import com.teamway.security.dto.securityConfiguration.card.CardReturnQueryDto;
import com.teamway.security.entity.SecurityConfiguration.Card;
import com.teamway.security.vo.securityConfiguration.card.CardListVo;
import com.teamway.security.vo.securityConfiguration.card.CardReturnVo;
import com.teamway.security.vo.securityConfiguration.card.CardVo;
import com.teamway.security.vo.securityConfiguration.card.ReadCardDeviceVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/13
 */
public interface CardService extends IService<Card> {
    /**
     * 人员绑定卡片
     *
     * @param cardList
     * @param personSourceIndex
     */
    void bindingCard(List<Card> cardList, String personSourceIndex);

    /**
     * 校验卡片是否被绑定
     * @param card
     * @return
     */
    boolean checkCardIsBinded(Card card);

    /**
     * 删除人员绑定的所有卡片
     * @param id
     */
    void delCardByPersonId(Long id);



    /**
     * 查询人员绑定的卡片
     *
     * @param personId
     * @return
     */
    List<CardVo> getCardListByPersonId(Long personId);

    /**
     * 获取卡列表
     * @param dto
     * @return
     */
    IPage<CardListVo> listCard(CardListQueryDto dto);

    /**
     * 退卡
     *
     * @param dto
     * @return
     */
    CardReturnVo returnCard(CardReturnQueryDto dto);

    /**
     * 卡挂失（更新卡状态）
     * @param dto
     * @return
     */
    boolean cardReportTheLoss(CardReportTheLossQueryDto dto);

    /**
     * 获取读卡程序下载链接
     * @return
     */
    String getReadCardDeviceUrl();

    /**
     * 获取读卡设备号密码
     * @return
     */
    ReadCardDeviceVo getReadCardDevice();

}
