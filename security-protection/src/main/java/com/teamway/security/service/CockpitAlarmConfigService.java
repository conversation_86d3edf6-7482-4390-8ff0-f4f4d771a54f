package com.teamway.security.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.security.entity.CockpitAlarmConfig;

/**
 * @ClossName CockpitAlarmConfigService
 * @Description 驾驶舱亮点告警图片数据配置 服务接口
 * <AUTHOR>
 * @createDate 2025/08/19
 * @version 1.0
 **/
public interface CockpitAlarmConfigService extends IService<CockpitAlarmConfig> {

    /**
     * 新增或更新驾驶舱亮点告警配置
     * @param cockpitAlarmConfig 驾驶舱亮点告警配置对象
     * @return 操作是否成功
     */
    Boolean insertOrUpdateCockpitAlarmConfig(CockpitAlarmConfig cockpitAlarmConfig);

    /**
     * 查询驾驶舱亮点告警配置
     * @return 驾驶舱亮点告警配置对象
     */
    CockpitAlarmConfig selectCockpitAlarmConfig();
}