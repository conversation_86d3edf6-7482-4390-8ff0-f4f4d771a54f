package com.teamway.security.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.common.entity.BatchIds;
import com.teamway.security.dto.DangerousProjectDailyQueryDto;
import com.teamway.security.entity.DangerousProjectDaily;

import java.util.List;

/**
 * <p>
 * 当日危大工程表 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface DangerousProjectDailyService extends IService<DangerousProjectDaily> {

    /**
     * 添加当日危大工程记录
     *
     * @param dangerousProjectDaily 当日危大工程信息
     * @return 是否成功
     */
    Boolean addDangerousProjectDaily(DangerousProjectDaily dangerousProjectDaily);

    /**
     * 更新当日危大工程记录
     *
     * @param dangerousProjectDaily 当日危大工程信息
     * @return 是否成功
     */
    Boolean updateDangerousProjectDaily(DangerousProjectDaily dangerousProjectDaily);

    /**
     * 批量删除当日危大工程记录
     *
     * @param request 包含ID列表的请求
     * @return 是否成功
     */
    Boolean batchDeleteDangerousProjectDaily(BatchIds request);

    /**
     * 根据ID获取当日危大工程详情
     *
     * @param id 当日危大工程ID
     * @return 当日危大工程信息
     */
    DangerousProjectDaily getDangerousProjectDailyById(Long id);

    /**
     * 条件查询当日危大工程列表
     *
     * @param queryDto 查询条件
     * @return 当日危大工程列表
     */
    List<DangerousProjectDaily> findPageList(DangerousProjectDailyQueryDto queryDto);
}