package com.teamway.security.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.security.entity.SecurityConfiguration.SecurityDeviceRecord;
import com.teamway.security.entity.accessManagement.visitorReservationManage.access.InPlantRecord;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/12/22
 */
public interface InPlantRecordService extends IService<InPlantRecord> {

    /**
     * 保存在场人员记录
     * @param deviceRecord
     */
    void saveInPlantPersonRecord(SecurityDeviceRecord deviceRecord);

    /**
     * 保存在场车辆记录
     * @param deviceRecord
     */
    void saveInPlantCarRecord(SecurityDeviceRecord deviceRecord);

}
