package com.teamway.security.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.security.dto.accessManagement.person.PersonAlarmDto;
import com.teamway.security.dto.securityConfiguration.person.*;
import com.teamway.security.dto.sync.PersonSyncBatchResultDto;
import com.teamway.security.entity.SecurityConfiguration.Person;
import com.teamway.personnel.dto.PageHmBasicInfoSyncDto;
import com.teamway.security.vo.accessManagement.person.personAlarmVo;
import com.teamway.security.vo.securityConfiguration.person.PersonAuthedDeviceVo;
import com.teamway.security.vo.securityConfiguration.person.PersonListVo;
import com.teamway.security.vo.securityConfiguration.securityDevice.Sync4aPersonVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @ClossName PersonService
 * @Description 人员信息信息 服务类
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
public interface PersonService extends IService<Person> {
    boolean addPerson(PersonAddOrUpdateQueryDto dto);

    boolean addByApprovalBatch(List<PersonAddByApprovalDto> dtoList);

    /**
     * 批量添加人员（审批服务）并返回详细结果
     * @param dtoList 人员信息列表
     * @return 批量添加结果
     */
    PersonSyncBatchResultDto addByApprovalBatchWithResult(List<PersonAddByApprovalDto> dtoList);

    /**
     * 查询人员
     * @param dto
     * @return
     */
    IPage<PersonListVo> listPerson(PersonListQueryDto dto);

    /**
     * 更新人员信息
     * @param dto
     * @return
     */
    void updatePerson(PersonAddOrUpdateQueryDto dto);

    /**
     * 人员绑定卡片
     * @param dto
     */
    void bindingCard(PersonBindingCardQueryDto dto);

    /**
     * 人员授权
     * @param dto
     */
    Map<String, AuthResultDto> personAuthorization(PersonAuthorizationQueryDto dto);

    /**
     * 获取人员已授权设备
     * @param personId
     * @return
     */
    List<PersonAuthedDeviceVo> getAuthedByPersonId(Long personId);

    /**
     * 批量删除人员
     * @param dto
     */
    void batchDelPerson(PersonBatchDelQueryDto dto);

    /**
     * 下载人员导入模板
     * @param response
     */
    void downloadPersonExcelTemplate(HttpServletResponse response);

    /**
     * 下载人员导入模板（增强版本）
     * @param response
     */
    void downloadPersonExcelTemplateEnhanced(HttpServletResponse response);

    /**
     * 导入人员
     * @param file
     * @param response
     */
    void importPerson(MultipartFile file, HttpServletResponse response);

    /**
     * 导入人员（增强版本）
     * @param file
     * @param response
     */
    void importPersonEnhanced(MultipartFile file, HttpServletResponse response);

    /**
     * 导出人员
     * @param dto
     * @param response
     */
    void exportPerson(PersonListQueryDto dto, HttpServletResponse response);

    /**
     * 人脸采集
     * @return
     */
    String personFaceCollect();

    /**
     * 获取人员单位列表
     * @return
     */
    List<String> getPersonUnitList();

    /**
     * 将ids对应的人员加入黑名单
     * @param queryDto
     * @return
     */
    PersonOrCarBlackResDto addBlackList(PersonBlackListAddQueryDto queryDto);

    /**
     * 获取人员授权设备列表
     * @param personId
     * @return
     */
    List<PersonAuthedDeviceDto> getPersonAuthedDevice(Long personId);

    /**
     * 移除人员黑名单
     * @param queryDto
     * @return
     */
    PersonOrCarBlackResDto removeBlackList(PersonBlackListAddQueryDto queryDto);

    /**
     * 检查图片是否可用
     * @param personFaceUrl
     */
    void checkImageUrlIsUseful(String personFaceUrl);

    /**
     * 获取人员部门列表
     * @return
     */
    List<String> getPersonDepartmentList();

    /**
     * 获取人员岗位列表
     * @return
     */
    List<String> getPersonPostList();

    /**
     * 修改人员授权时间
     *
     * @param dto
     * @return
     */
    Sync4aPersonVo editPersonAuthorization(EditPersonAuthorizationQueryDto dto);

    /**
     * 删除人员授权
     *
     * @param dto
     * @return
     */
    Sync4aPersonVo deletePersonAuthorization(DeletePersonAuthorizationQueryDto dto);

    /**
     * 删除僵尸人员数据
     */
    void deleteZombiePerson();

    /**
     * 分页查门禁告警记录
     * @param personAlarmDto
     * @return
     */
    IPage<personAlarmVo> selectAlertRecord(PersonAlarmDto personAlarmDto);

    /**
     * 添加人员图片
     *
     * @param dto
     * @return
     */
    PersonAddPicResponseDto addPersonPic(PersonAddPicQueryDto dto);

    /**
     * 同步人员信息（从人事系统）
     * @param personList 人员信息列表
     * @return 同步结果
     */
    PersonSyncBatchResultDto syncPersonInfo(List<PageHmBasicInfoSyncDto> personList);

}
