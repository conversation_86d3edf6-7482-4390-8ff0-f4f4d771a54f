package com.teamway.security.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.accessManagement.visitor.*;
import com.teamway.security.dto.securityConfiguration.person.*;
import com.teamway.security.entity.accessManagement.visitorReservationManage.Reservation;
import com.teamway.security.vo.accessManagement.visitor.ReservationSynMsgVo;
import com.teamway.security.vo.securityConfiguration.securityDevice.Sync4aPersonVo;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/17
 */
public interface ReservationService extends IService<Reservation> {
    /**
     * 新增预约
     * @param dto
     */
    void addVisitorReservation(VisitorReservationAddQueryDto dto);

    /**
     * 新增预约 controller层新增
     * @param dto
     */
    void addVisitorReservationByController(VisitorReservationAddQueryDto dto);

    /**
     * 修改预约信息
     * @param dto 修改的数据
     */
    void updateVisitorReservation(VisitorReservationAddQueryDto dto);

    /**
     * 删除预约信息
     * @param ids 删除的ID集合
     */
    void deleteVisitorReservation(List<String> ids);

    /**
     * 删除访客预约信息
     * @param dto
     * @return
     */
    void deleteVisitorReservation(ReservationBatchDelQueryDto dto);

    /**
     * 接收审批结果
     * @param dto
     */
    void notifyTheApprovalResult(ApprovalResultNotificationQueryDto dto);

    /**
     * 开卡
     * @param dto
     */
    void cardOpen(CardOpenQueryDto dto);

    /**
     * 获取访客列表
     *
     * @param dto
     * @return
     */
    IPage<Reservation> listReservation(ReservationListQueryDto dto);

    /**
     * 访客开卡-添加人员
     * @param dto
     */
    void reservationAddPerson(CardOpenQueryDto dto);

    /**
     * 访客开卡-添加卡片
     * @param dto
     */
    void reservationAddCard(CardOpenQueryDto dto);

    /**
     * 访客开卡-人员授权
     * @param dto
     */
    void reservationPersonAuth(CardOpenQueryDto dto);

    /**
     * 访客开卡-车辆添加
     * @param dto
     */
    void reservationCarAdd(CardOpenQueryDto dto);

    /**
     * 访客开卡-车辆授权
     * @param dto
     */
    void reservationCarAuth(CardOpenQueryDto dto);


    /**
     * 同步基建系统访客数据
     * @return
     */
    ReservationSynMsgVo synchronizingVisitorData();
    /**
     * 复用安防人员 人员授权
     * @param dto
     * @return
     */
    Map<String, AuthResultDto> personAuthorization(PersonAuthorizationQueryDto dto);


    /**
     * 复用安防人员 删除人员授权
     * @param dto
     * @return
     */
    Sync4aPersonVo deletePersonAuthorization(DeletePersonAuthorizationQueryDto dto);

    /**
     * 复用安防人员 修改人员授权时间
     * @param dto
     * @return
     */
    Sync4aPersonVo editPersonAuthorization(EditPersonAuthorizationQueryDto dto);

    /**
     * 复用安防人员 获取人员已授权列表
     * @param personId
     * @return
     */
    List<PersonAuthedDeviceDto> getPersonAuthedDevice(Long personId);

    /**
     * 导出访客列表
     * @param dto
     * @param response
     */
    void exportPerson(ReservationExportRequestDto dto, HttpServletResponse response);

    /**
     * 获取所有导出数据的id
     * @param dto
     * @return
     */
    List<Long> getAllExportDataIds(ReservationListQueryDto dto);
}
