package com.teamway.security.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.security.entity.ScreenDataMaintenanceConfig;

/**
 * @ClossName ScreenDataMaintenanceConfigService
 * @Description 大屏数据维护配置表，存储大屏数据配置及维护信息 服务类
 * <AUTHOR>
 * @createDate 2024/12/19
 * @version 1.0
 **/
public interface ScreenDataMaintenanceConfigService extends IService<ScreenDataMaintenanceConfig> {
    /**
     * 更新大屏数据后台配置
     * @param screenDataMaintenanceConfigVo
     * @return
     */
    Boolean insertOrUpdate(ScreenDataMaintenanceConfig screenDataMaintenanceConfigVo);
    /**
     * 查大屏数据后台配置
     *
     * @return
     */
    ScreenDataMaintenanceConfig selectConfig();
}
