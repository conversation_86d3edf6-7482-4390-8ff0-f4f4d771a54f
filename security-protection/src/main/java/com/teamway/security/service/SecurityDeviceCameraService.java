package com.teamway.security.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.security.dto.accessManagement.car.EventListCarDto;
import com.teamway.security.dto.accessManagement.person.EventListPersonDto;
import com.teamway.security.dto.accessManagement.person.EventListPersonMisDto;
import com.teamway.security.dto.accessManagement.securityDevice.AccessStatisticsQueryDto;
import com.teamway.security.dto.securityConfiguration.BandingCameraQueryDto;
import com.teamway.security.entity.SecurityConfiguration.SecurityDeviceCamera;
import com.teamway.security.entity.SecurityConfiguration.SecurityDeviceDoor;
import com.teamway.security.vo.accessManagement.car.EventListCarVo;
import com.teamway.security.vo.accessManagement.person.EventListPersonVo;
import com.teamway.security.vo.accessManagement.securityDevice.AccessStatisticsResultVo;
import com.teamway.security.vo.accessManagement.securityDevice.BandedCameraVo;
import com.teamway.security.vo.accessManagement.securityDevice.IndexAllStatisticsVo;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/8
 */

public interface SecurityDeviceCameraService extends IService<SecurityDeviceCamera> {

    /**
     * 查询安防设备绑定的摄像机
     * @param securityDeviceId
     * @return
     */
    BandingCameraQueryDto getBandedCamera(Long securityDeviceId);

    /**
     * 查询安防设备绑定的摄像机（包含摄像机信息）
     *
     * @param securityDeviceId
     * @return
     */
    BandedCameraVo getBandedCameraWithCameraInfo(Long securityDeviceId);

    /**
     * 出入口信息统计  进 出
     * @param queryDto
     * @return
     */
    AccessStatisticsResultVo accessManagementStatistics(AccessStatisticsQueryDto queryDto);

    /**
     * 人员出入记录查询列表
     * @param queryDto
     * @return
     */
    IPage<EventListPersonVo> eventListByPerson(EventListPersonDto queryDto);

    /**
     * 车辆出入记录查询列表
     * @param queryDto
     * @return
     */
    IPage<EventListCarVo> eventListByCar(EventListCarDto queryDto);

    /**
     * 获取指定门禁下的门禁点
     * @param deviceId
     * @return
     */
    List<SecurityDeviceDoor> getSecurityDeviceDoorByDeviceId(Long deviceId);

    /**
     * 人员出入记录查询列表 Mis
     * @param queryDto
     * @return
     */
    List<EventListPersonVo> eventListByPersonMis(EventListPersonMisDto queryDto);

    /**
     * 导出人员出入记录
     * @param queryDto
     * @param response
     */
    void exportEventListByPerson(EventListPersonDto queryDto, HttpServletResponse response);
}
