package com.teamway.security.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.security.dto.AccessRecordQueryDto;
import com.teamway.security.dto.mis.DeliveryPlanQueryDTO;
import com.teamway.security.dto.statistics.FactoryPersonDetailQueryDto;
import com.teamway.security.entity.SecurityConfiguration.Person;
import com.teamway.security.entity.SecurityConfiguration.SecurityDeviceRecord;
import com.teamway.security.vo.DeviceAccStatVo;
import com.teamway.security.vo.mis.DeliveryPlanStatVO;
import com.teamway.security.vo.statistics.*;

import java.util.List;

/**
 * @ClossName SecurityDeviceRecordService
 * @Description 安防设备信息 服务接口
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
public interface SecurityDeviceRecordService extends IService<SecurityDeviceRecord> {

    /**
     * 用户车辆事件上报
     * @param message
     * @param topic
     */
    void securityRecordReport(String message, String topic);

    /**
     * 分页获取设备上报记录
     * @param type
     * @param request
     * @return
     */
    Page<SecurityDeviceRecord> getAccessRecordPage(Integer type, AccessRecordQueryDto request);

    /**
     * 统计设备授权记录
     * @param type
     * @return
     */
    DeviceAccStatVo getDeviceAccStat(Integer type);

    /**
     * 获取最后一次进门禁的记录
     * @return
     */
    List<SecurityDeviceRecord> getLatestAccessControlRecord();

    /**
     * 获取僵尸人员id
     *
     * @param isZombieTime
     * @return
     */
    List<String> getZombieRecord(String isZombieTime);
    /**
     * 按类型统计当日车辆进出数量
     * @return
     */
    List<CarTypeCountVO> getCarStatistics();

    /**
     * 统计当日车辆进出场情况
     * @return 车辆进出场统计数据
     */
    CarInOutStatVO getCarInOutStatistics();
    /**
     * 统计当日进厂人数、出厂人数、在场人数、不同工种在场人数
     * @return
     */
    PersonPieVO getPersonPieStatistics();
    /**
     * 统计注册总数、施工总数，按单位统计注册总数以及当日人员施工总数
     * @return
     */
    PersonColVO getPersonColStatistics();

    /**
     * 免登录 统计注册总数、施工总数，按单位统计注册总数以及当日人员施工总数 统计访客
     *
     * @return
     */
    PersonColVO getPersonColStatisticsNoLogin();

    /**
     * 免登录 统计当日进厂人数、出厂人数、在场人数、不同工种在场人数 统计访客
     * @return
     */
    PersonPieVO getPersonPieStatisticsNoLogin();

    /**
     * 图纸交付统计
     * @param queryDTO
     * @return
     */
    List<DeliveryPlanStatVO> getDeliveryPlanStatList(DeliveryPlanQueryDTO queryDTO);

    /**
     * 免登录 获取人员进出场趋势分析
     * @param timeRange 时间范围（小时数）
     * @param currentTime 当前时间（格式：yyyy-MM-dd HH:mm）
     * @return 人员进出场趋势数据列表
     */
    List<PersonPieVO> getPersonTrendStatisticsNoLogin(Integer timeRange, String currentTime);

    /**
     * 获取单位人员进出统计数据
     * @return 单位统计数据列表
     */
    List<UnitStatisticsVO> getUnitStatistics();

    /**
     * 获取工人类型统计数据
     * @param timeRange 统计时间范围：day-日, week-周, month-月
     * @return 工人类型统计数据
     */
    List<WorkerTypeStatVO> getWorkerTypeStatistics(String timeRange);

    /**
     * 获取工种统计数据
     * @param timeRange 统计时间范围：day-日, week-周, month-月
     * @return 工种统计数据
     */
    List<JobTypeStatVO> getWorkerJobStatistics(String timeRange);

    /**
     * 获取特殊工种统计数据
     * @param timeRange 统计时间范围：day-日, week-周, month-月
     * @return 特殊工种统计数据
     */
    List<SpecialWorkerStatVO> getSpecialWorkerStatistics(String timeRange);

    /**
     * 免登录 统计全厂人员数据（进场人员、在场人员、出场人员及在场人员分类统计）
     * @return 全厂人员统计数据
     */
    FactoryPersonStatisticsVO getFactoryPersonStatisticsNoLogin();

    /**
     * 根据总包单位统计工人类型分类数据
     * @param unitCode 总包单位编码
     * @return 总包单位工人类型统计数据列表
     */
    List<ContractorWorkerTypeStatVO> getContractorWorkerTypeStatistics(String unitCode);

    /**
     * 查询全厂在场人员详细信息
     * @param queryDto 查询条件
     * @return 全厂在场人员详情分页数据
     */
    IPage<FactoryPersonDetailVO> getFactoryPersonDetailPage(FactoryPersonDetailQueryDto queryDto);

    /**
     * 根据总包单位编码获取分包单位下拉列表
     * @param contractorUnitCode 总包单位编码
     * @return 分包单位下拉列表
     */
    List<SubcontractDropdownVO> getSubcontractDropdownList(String contractorUnitCode);

    /**
     * 根据单位编码获取工种岗位统计数据
     * @param contractorUnitCode 总包单位编码
     * @param targetUnitCode 目标分包单位编码（可选，不传则使用所有数据）
     * @return 工种岗位统计数据
     */
    JobPostStatVO getJobPostStatistics(String contractorUnitCode, String targetUnitCode);
}