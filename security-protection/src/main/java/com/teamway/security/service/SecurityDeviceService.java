package com.teamway.security.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.security.common.tree.TreeData;
import com.teamway.security.dto.accessManagement.securityDevice.SecurityDeviceTreeBuildedQueryDto;
import com.teamway.security.dto.securityConfiguration.BandingCameraQueryDto;
import com.teamway.security.dto.securityConfiguration.SecurityDeviceAddOrUpdateQueryDto;
import com.teamway.security.dto.securityConfiguration.SecurityDeviceDelQueryDto;
import com.teamway.security.dto.securityConfiguration.SecurityDeviceListQueryDto;
import com.teamway.security.dto.securityConfiguration.securityDevice.SecurityDeviceListForAuth;
import com.teamway.security.entity.SecurityConfiguration.DeviceFunction;
import com.teamway.security.entity.SecurityConfiguration.SecurityDevice;
import com.teamway.security.vo.securityConfiguration.securityDevice.SyncDeviceVo;
import com.teamway.security.vo.statistics.AccessStatisticsVo;
import com.teamway.security.vo.statistics.BarriersStatisticsVo;

import java.util.List;

/**
 * @ClossName SecurityDeviceService
 * @Description 安防设备 服务接口
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
public interface SecurityDeviceService extends IService<SecurityDevice> {
    /**
     * 添加安防设备
     * @param dto
     * @return
     */
    void addSecurityDevice(SecurityDeviceAddOrUpdateQueryDto dto);

    /**
     * 查询安防设备
     * @param dto
     * @return
     */
    IPage<SecurityDevice> listSecurityDevices(SecurityDeviceListQueryDto dto);

    /**
     * 更新安防设备
     * @param dto
     * @return
     */
    void updateSecurityDevice(SecurityDeviceAddOrUpdateQueryDto dto);

    /**
     * 批量删除安防设备
     * @param dto
     * @return
     */
    void batchDelSecurityDevice(SecurityDeviceDelQueryDto dto);

    /**
     * 安防设备绑定摄像机
     * @param dto
     * @return
     */
    boolean bandingCamera(BandingCameraQueryDto dto);

    /**
     * 根据设备类别同步设备数据
     *
     * @param syncDeviceType 设备类别
     * @return 同步结果
     */
    SyncDeviceVo syncDevice(String syncDeviceType);

    IPage<SecurityDevice> selectPage(SecurityDeviceListQueryDto dto);

    /**
     * 获取所有设备功能
     * @return
     */
    List<DeviceFunction> getAllFunction();

    /**
     * 获取门禁区域摄像机信息
     * @return
     */
    List<TreeData> getRegCameraTree(SecurityDeviceTreeBuildedQueryDto dto);

    /**
     * 查询门禁安防设备（人员授权用）
     * @param dto
     * @return
     */
    List<SecurityDeviceListForAuth> listForAuthSecurityDevices(SecurityDeviceListQueryDto dto);

    /**
     * 根据设备类别获取设备索引（请求IOT组件时deviceId用）
     * @param deviceType
     * @return
     */
    String getSecurityDeviceIndexByType(String deviceType);
    /**
     * 门禁的在线数量、总数、及在线率
     * @return
     */
    AccessStatisticsVo getAccessStatistics();
    /**
     * 道闸的在线数量、总数、及在线率
     * @return
     */
    BarriersStatisticsVo getBarriersStatistics();
}
