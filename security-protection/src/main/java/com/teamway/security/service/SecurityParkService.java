package com.teamway.security.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.common.entity.PageModel;
import com.teamway.common.entity.ResultModel;
import com.teamway.security.dto.SecurityParkGetReqDto;
import com.teamway.security.dto.SecurityParkRelateBarrierReqDto;
import com.teamway.security.entity.SecurityPark;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
public interface SecurityParkService extends IService<SecurityPark> {

    PageModel<List<SecurityPark>> findPageList(SecurityParkGetReqDto securityParkGetReqDto);

    ResultModel<String> relateBarrier(SecurityParkRelateBarrierReqDto securityParkRelateBarrierReqDto);

    ResultModel<String> sync();

    /**
     * 获取安防组件停车场的ID
     * @return ID集合
     */
    String getIdsByComponent();
}
