package com.teamway.security.service;


import com.teamway.base.vo.CameraVo;
import com.teamway.security.dto.mis.MilestonePlanQueryDTO;
import com.teamway.security.vo.ProjectStatusInfoVo;
import com.teamway.security.vo.mis.MilestonePlanVO;
import com.teamway.security.vo.statistics.CameraNumberVo;
import com.teamway.security.vo.statistics.RegionCameraNumVo;

import java.util.List;
import java.util.Map;

/**
 * @ClossName StatisticsService
 * @Description StatisticsService接口
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
public interface SecurityStatisticsService {


    /**
     * 获取摄像机数量
     *
     * @return
     */
    CameraNumberVo getCameraStatistics();

    /**
     * 获取摄像机离线数
     *
     * @param limit 行数
     * @return key摄像机名称 value离线数量
     */
    List<Map<String, Object>> getCameraOffLineNum(Integer limit);

    /**
     * 根据区域统计摄像机数量
     *
     * @param regionId
     * @return
     */
    List<RegionCameraNumVo> getRegionCameraNum(Long regionId);
    /**
     *获取已施工天数
     * @return
     */
    String getConstructionDays();
    /**
     * 根据大屏数据后台配置查摄像机信息
     * @return
     */
    List<CameraVo> selectCarmeraByconfig();

    /**
     *获取项目状态信息：获取已施工天数，以及摄像机、门禁、道闸的总数、在线数量、离线数量、在线率
     * @return
     */
    ProjectStatusInfoVo getLiveProjectStatus();

    /**
     * 横屏展示内容拼接
     */
    String contentStitching(Long id);

    /**
     * 里程碑计划
     * @param queryDTO
     * @return
     */
    List<MilestonePlanVO> getMilestonePlanList(MilestonePlanQueryDTO queryDTO);
}
