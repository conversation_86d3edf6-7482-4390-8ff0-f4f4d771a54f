package com.teamway.security.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teamway.security.entity.SecurityConfiguration.WorkGroup;
import com.teamway.security.vo.WorkGroupPathVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/11/7
 */
public interface WorkGroupService extends IService<WorkGroup> {
    /**
     * 添加工作组
     * @param workGroup
     * @return
     */
    boolean addWorkGroup(WorkGroup workGroup);

    /**
     * 修改工作组
     * @param workGroup
     * @return
     */
    boolean updateWorkGroup(WorkGroup workGroup);

    /**
     * 获取所有工作组树
     * @return
     */
    List<WorkGroup> getWorkGroupTree();

    /**
     * 删除工作组
     * @param id
     * @return
     */
    boolean delWorkGroup(Long id);

    /**
     * 获取工作组下的所有工作组id，包括当前工作组
     * @param id
     * @return
     */
    List<Long> getWorkGroupIdListByWorkGroupId(long id);

    /**
     * 查询所有工作组，带完整路径
     * @return 包含完整路径的工作组列表
     */
    List<WorkGroupPathVO> listWithFullPath();
}
