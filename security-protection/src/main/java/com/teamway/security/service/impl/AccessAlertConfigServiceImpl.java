package com.teamway.security.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.teamway.security.entity.AccessAlertConfig;
import com.teamway.security.mapper.AccessAlertConfigMapper;
import com.teamway.security.service.AccessAlertConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.teamway.security.common.constant.AlertConfigVersionConstant.FIRST_EDITION;

/**
 * <p>
 * 外设表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Service
public class AccessAlertConfigServiceImpl extends ServiceImpl<AccessAlertConfigMapper, AccessAlertConfig> implements AccessAlertConfigService {

    @Autowired
    private AccessAlertConfigMapper accessAlertConfigMapper;

    @Override
    public boolean updateAccessAlertConfig(AccessAlertConfig alertConfig) {
        LambdaUpdateWrapper<AccessAlertConfig> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(AccessAlertConfig::getConfigVersion, FIRST_EDITION);
        return accessAlertConfigMapper.update(alertConfig, lambdaUpdateWrapper) > 0 ? true : false;
    }

    @Override
    public AccessAlertConfig getAccessAlertConfig() {
        LambdaQueryWrapper<AccessAlertConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AccessAlertConfig::getConfigVersion, FIRST_EDITION);
        return accessAlertConfigMapper.selectOne(lambdaQueryWrapper);
    }

}
