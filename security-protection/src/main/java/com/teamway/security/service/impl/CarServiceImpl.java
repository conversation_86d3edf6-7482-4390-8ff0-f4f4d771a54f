package com.teamway.security.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.entity.CommonType;
import com.teamway.base.service.CommonTypeService;
import com.teamway.common.constant.CommonConstant;
import com.teamway.common.dto.excel.ExcelSelectorDTO;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.entity.ResultModel;
import com.teamway.common.util.ExcelUtils;
import com.teamway.exception.ServiceException;
import com.teamway.security.common.constant.CacheConstant;
import com.teamway.security.common.constant.SecurityDeviceConstant;
import com.teamway.security.common.enums.SecurityTopicEnum;
import com.teamway.security.common.properties.SecurityComponentProperties;
import com.teamway.security.common.transfer.SecurityConfiguration.CarTransfer;
import com.teamway.security.dto.ResponseDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.SecurityDevice.MqttComponentTemplateDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.SecurityDevice.MqttSecuritySyncResultDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.car.MqttCarCancelAuthDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.car.MqttCarDelQueryDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.car.MqttCarSyncQueryDto;
import com.teamway.security.dto.securityConfiguration.*;
import com.teamway.security.dto.securityConfiguration.car.CarAddAndUpdateReqDto;
import com.teamway.security.dto.securityConfiguration.car.CarMappable;
import com.teamway.security.dto.securityConfiguration.car.ExcelExportCarDto;
import com.teamway.security.dto.securityConfiguration.car.ExcelImportCarDto;
import com.teamway.security.dto.securityConfiguration.person.AuthResultDto;
import com.teamway.security.dto.securityConfiguration.person.PersonOrCarBlackResDto;
import com.teamway.security.entity.SecurityConfiguration.*;
import com.teamway.security.entity.SecurityPark;
import com.teamway.security.mapper.CarMapper;
import com.teamway.security.service.*;
import com.teamway.security.util.SecurityComponentUtil;
import com.teamway.security.util.SecurityImageUtils;
import com.teamway.security.vo.securityConfiguration.person.PersonAuthedDeviceVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class CarServiceImpl extends ServiceImpl<CarMapper, Car> implements CarService {

    private CarMapper carMapper;
    private PersonService personService;
    private SecurityParkService securityParkService;
    private SecurityParkCarService securityParkCarService;
    private SecurityDeviceService securityDeviceService;
    private SecurityDeviceDoorService securityDeviceDoorService;
    private SecurityDeviceCarService securityDeviceCarService;
    private SecurityComponentUtil securityComponentUtil;
    private CarTransfer carTransfer;
    private SecurityComponentProperties securityComponentProperties;
    private TransactionTemplate transactionTemplate;
    @Autowired
    private CommonTypeService commonTypeService;
    // 全局的ConcurrentHashMap用于保存错误信息
    private static Map<String, String> errorMap = new ConcurrentHashMap<>();
    private static Map<Long, AuthResultDto> authResultDtoMap = new ConcurrentHashMap<>();
    private static List<Long> batchAuthCarIdErrorList = new CopyOnWriteArrayList<>();

    @Autowired
    public void setCarMapper(CarMapper carMapper) {
        this.carMapper = carMapper;
    }

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setSecurityParkService(SecurityParkService securityParkService) {
        this.securityParkService = securityParkService;
    }

    @Autowired
    public void setSecurityParkCarService(SecurityParkCarService securityParkCarService) {
        this.securityParkCarService = securityParkCarService;
    }

    @Autowired
    public void setSecurityDeviceService(SecurityDeviceService securityDeviceService) {
        this.securityDeviceService = securityDeviceService;
    }

    @Autowired
    public void setSecurityDeviceDoorService(SecurityDeviceDoorService securityDeviceDoorService) {
        this.securityDeviceDoorService = securityDeviceDoorService;
    }

    @Autowired
    public void setSecurityDeviceCarService(SecurityDeviceCarService securityDeviceCarService) {
        this.securityDeviceCarService = securityDeviceCarService;
    }

    @Autowired
    public void setSecurityComponentUtil(SecurityComponentUtil securityComponentUtil) {
        this.securityComponentUtil = securityComponentUtil;
    }

    @Autowired
    public void setCarTransfer(CarTransfer carTransfer) {
        this.carTransfer = carTransfer;
    }

    @Autowired
    public void setSecurityComponentProperties(SecurityComponentProperties securityComponentProperties) {
        this.securityComponentProperties = securityComponentProperties;
    }

    @Autowired
    public void setTransactionTemplate(TransactionTemplate transactionTemplate) {
        this.transactionTemplate = transactionTemplate;
    }

    @Override
    public IPage<CarGetResDataListDto> findPageList(CarGetReqDto carGetReqDto) {
        return carMapper.findPageList(new Page<>(carGetReqDto.getPageIndex(), carGetReqDto.getPageSize()), carGetReqDto);
    }

    @Override
    public boolean addCar(CarAddAndUpdateReqDto carAddAndUpdateReqDto) {
        checkAddAndUpdateReqDto(carAddAndUpdateReqDto);

        Car car = carTransfer.dto2Entity(carAddAndUpdateReqDto);
        car.setState(0);
        // 请求组件 添加车辆
        saveCarSyncDtoToSecurityComponent(car);
        // 保存车辆信息
        return save(car);
    }

    /**
     * 保存车辆同步信息到安防组件
     *
     * @param car
     */
    private void saveCarSyncDtoToSecurityComponent(Car car) {
        MqttComponentTemplateDto<MqttCarSyncQueryDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<MqttCarSyncQueryDto>(securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.BARRIER_TYPE));
        MqttCarSyncQueryDto mqttCarSyncQueryDto = MqttCarSyncQueryDto.builder()
                .plateNo(car.getCarNo())
                .vehicleColor(car.getColor())
                .build();
        if (StrUtil.isNotBlank(car.getCarIndex())) {
            mqttCarSyncQueryDto.setVehicleId(car.getCarIndex());
        }
        mqttComponentTemplateDto.setParams(mqttCarSyncQueryDto);
        String carIndex = sendCarToComponent(mqttComponentTemplateDto);
        if (ObjectUtil.isEmpty(car.getId())) {
            car.setCarIndex(carIndex);
        }
    }

    /**
     * 发送mqtt到组件
     *
     * @param mqttComponentTemplateDto
     * @return
     */
    private String sendCarToComponent(MqttComponentTemplateDto<MqttCarSyncQueryDto> mqttComponentTemplateDto) {
        ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(
                mqttComponentTemplateDto,
                SecurityTopicEnum.ADD_CAR.getTopic(),
                CacheConstant.syncCarTopicKey,
                mqttComponentTemplateDto.getSerialId());
        if (!Objects.equals(ResultCode.SUCCESS.getCode(), responseDto.getCode())) {
            String errorMsg = responseDto.getMessage();
            if (responseDto.getMessage().contains("PlateNo Already Exists")) {
                errorMsg = "车辆已存在于安防平台";
            }
            errorMap.put(mqttComponentTemplateDto.getParams().getPlateNo(), errorMsg);
            throw new ServiceException(ResultCode.OPERATION_FAILURE, errorMsg);
        }
        return Optional.ofNullable(responseDto.getData()).map(data -> JSONUtil.toBean(JSON.toJSONString(data), MqttSecuritySyncResultDto.class).getVehicleId()).orElse(null);
    }


    private Car checkAddAndUpdateReqDto(CarAddAndUpdateReqDto queryDto) {
        Car car = null;
        Long carId = queryDto.getId();
        if (ObjectUtil.isNotEmpty(carId)) {
            car = this.getById(carId);
            if (ObjectUtil.isEmpty(car)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "车辆不存在");
            }
        }
        LambdaQueryWrapper<Car> lambdaQueryWrapper = new LambdaQueryWrapper<Car>()
                .ne(ObjectUtil.isNotEmpty(carId), Car::getId, carId)
                .eq(Car::getCarNo, queryDto.getCarNo());
        List<Car> list = this.list(lambdaQueryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            throw new ServiceException(ResultCode.EXIST, "车牌号");
        }
//        if (ObjectUtil.isEmpty(personService.getById(queryDto.getPersonId()))) {
//            throw new ServiceException(ResultCode.NOT_EXIST, "人员");
//        }
//        if (ObjectUtil.isNotEmpty(car) && !car.getCarNo().equals(queryDto.getCarNo())) {
//            LambdaQueryWrapper<SecurityParkCar> securityParkCarLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            securityParkCarLambdaQueryWrapper.eq(SecurityParkCar::getCarId, carId);
//            List<SecurityParkCar> securityParkCars = securityParkCarService.list(securityParkCarLambdaQueryWrapper);
//            if (securityParkCars != null && securityParkCars.size() > 0) {
//                throw new ServiceException(ResultCode.CAR_HAD_AUTH_CANNOT_AUTH);
//            }
//        }
        return car;
    }

    private Car checkUpdateReqDto(CarAddAndUpdateReqDto queryDto) {
        Car car = null;
        Long carId = queryDto.getId();
        if (ObjectUtil.isNotEmpty(carId)) {
            car = this.getById(carId);
            if (ObjectUtil.isEmpty(car)) {
                throw new ServiceException(ResultCode.NOT_EXIST, "车辆");
            }
        }
        if (ObjectUtil.isEmpty(personService.getById(queryDto.getPersonId()))) {
            throw new ServiceException(ResultCode.NOT_EXIST, "人员");
        }
        return car;
    }

    @Override
    public boolean updateCar(CarAddAndUpdateReqDto carUpdateReqDto) {
        Car car = checkUpdateReqDto(carUpdateReqDto);
        car.setCarNo(carUpdateReqDto.getCarNo());
        car.setCarType(carUpdateReqDto.getCarType());
        car.setPersonId(Long.parseLong(carUpdateReqDto.getPersonId()));
        car.setRemarks(carUpdateReqDto.getRemarks());
        car.setColor(carUpdateReqDto.getColor());
        car.setCarImageUrl(carUpdateReqDto.getCarImageUrl());
        // 请求组件 修改车辆
        saveCarSyncDtoToSecurityComponent(car);
        // 修改车辆信息
        return updateById(car);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delCar(CarDelReqDto carDelReqDto) {
        // 校验
        List<Car> cars = checkDelReqDto(carDelReqDto);
        List<String> carIndexList = cars.stream().filter(i -> StrUtil.isNotBlank(i.getCarIndex())).map(Car::getCarIndex).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(carIndexList)) {
            // todo：需要考虑删除车辆相关的数据、授权、黑白名单等
            // 请求组件 删除车辆
            MqttComponentTemplateDto<MqttCarDelQueryDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.BARRIER_TYPE));
            MqttCarDelQueryDto mqttCarDelQueryDto = new MqttCarDelQueryDto();
            mqttCarDelQueryDto.setVehicleIds(carIndexList);
            mqttComponentTemplateDto.setParams(mqttCarDelQueryDto);
            ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(
                    mqttComponentTemplateDto,
                    SecurityTopicEnum.DELETE_CAR.getTopic(),
                    CacheConstant.syncCarTopicKey,
                    mqttComponentTemplateDto.getSerialId());
            if (!Objects.equals(ResultCode.SUCCESS.getCode(), responseDto.getCode())) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, responseDto.getMessage());
            }
        }
        // 删除车辆信息
        return removeBatchByIds(carDelReqDto.getIds());
    }

    private List<Car> checkDelReqDto(CarDelReqDto carDelReqDto) {
        List<String> ids = carDelReqDto.getIds();
        if (ids.size() > 100) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "单次删除车辆数最多100辆");
        }
        List<Car> cars = new ArrayList<>();
        for (String id : ids) {
            Car car = this.getById(id);
            if (ObjectUtil.isEmpty(car)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "不存在的车辆Id" + id);
            }
            cars.add(car);
        }
        return cars;
    }


    @Override
    public ResultModel<Map<String, AuthResultDto>> authorize(CarAuthorizeReqDto carAuthorizeReqDto) {
        // 校验输入参数
        checkCarAuthorizeReqDto(carAuthorizeReqDto);
        String[] carIdList = carAuthorizeReqDto.getCarIdList();
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNamePrefix("authorizeWithPark-").build();
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100), threadFactory);
        // 若起止时间为空，则表示永久授权
        if (carAuthorizeReqDto.getStartTime() == null || carAuthorizeReqDto.getEndTime() == null) {
            carAuthorizeReqDto.setStartTime(null);
            carAuthorizeReqDto.setEndTime(null);
        }
        authResultDtoMap.clear();
        batchAuthCarIdErrorList.clear();
        for (String carId : carIdList) {
            threadPoolExecutor.execute(() -> {
                // 开始授权
                try {
                    authorizeWithSecurityDevice(carAuthorizeReqDto.getSecurityDeviceIdList().toArray(new String[0]), Long.parseLong(carId), carAuthorizeReqDto.getStartTime(), carAuthorizeReqDto.getEndTime());
                } catch (Exception e) {
                    log.info("车辆授权操作-授权失败，车辆id：{}，异常信息：{}", carId, e.getMessage());
                    AuthResultDto authResultDto = authResultDtoMap.get(Long.parseLong(carId));
                    if (ObjectUtil.isEmpty(authResultDto)) {
                        authResultDto = new AuthResultDto();
                    }
                    if (e instanceof ServiceException) {
                        ServiceException serviceException = (ServiceException) e;
                        authResultDto.getFailed().add(serviceException.getErrorMessage());
                    } else {
                        authResultDto.getFailed().add(e.getMessage());
                    }
                    authResultDtoMap.put(Long.parseLong(carId), authResultDto);
                }
            });
        }
        threadPoolExecutor.shutdown();
        try {
            threadPoolExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("线程池等待被中断：{}", e.getMessage());
        }
        Map<String, AuthResultDto> resultMap = new HashMap<>();
        if (CollUtil.isNotEmpty(authResultDtoMap)) {
            authResultDtoMap.forEach((carId, authResultDto) -> {
                Car car = this.getById(carId);
                resultMap.put(car.getCarNo(), authResultDto);
            });
        }
        return CollUtil.isNotEmpty(resultMap) ? ResultModel.success(resultMap) : ResultModel.fail();
    }

    private void authorizeWithSecurityDevice(String[] securityDeviceIdList, Long carId, String begin, String end) {
        Car car = this.getById(carId);
        String parkId = securityParkService.getIdsByComponent();
        // 取消旧的数据的授权
        cancelAuthCarAndOldDevice(car, parkId);
        Map<Long, SecurityDevice> deviceMap = securityDeviceService.list().stream().collect(Collectors.toMap(SecurityDevice::getId, Function.identity()));
        // 授权新的数据
        for (String securityDeviceId : securityDeviceIdList) {
            // 先调用安防组件授权
            SecurityDevice securityDevice = deviceMap.get(Long.parseLong(securityDeviceId));
            MqttComponentTemplateDto<SecurityCarAuthReqDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDevice.getDeviceSourceIndex());
            SecurityCarAuthReqDto securityCarAuthReqDto = SecurityCarAuthReqDto.builder()
                    .plateNo(car.getCarNo())
                    .parkSyscode(parkId)
                    .startTime(begin)
                    .endTime(end)
                    .build();
            mqttComponentTemplateDto.setParams(securityCarAuthReqDto);
            ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(
                    mqttComponentTemplateDto,
                    SecurityTopicEnum.CAR_AUTHORIZATION.getTopic(),
                    SecurityTopicEnum.CAR_AUTHORIZATION.getRedisTypeKey(),
                    mqttComponentTemplateDto.getSerialId());
            if (!Objects.equals(ResultCode.SUCCESS.getCode(), responseDto.getCode())) {
                log.info("车辆授权操作-请求安防设备组件-授权失败，返回信息：{}", responseDto.getMessage());
                AuthResultDto authResultDto = authResultDtoMap.get(carId);
                if (ObjectUtil.isEmpty(authResultDto)) {
                    authResultDto = new AuthResultDto();
                }
                authResultDto.getFailed().add(securityDevice.getSecurityDeviceName() + ": " + responseDto.getMessage());
                authResultDtoMap.put(carId, authResultDto);
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "道闸 " + securityDevice.getSecurityDeviceName() + " 授权失败");
            } else {
                AuthResultDto authResultDto = authResultDtoMap.get(carId);
                if (ObjectUtil.isEmpty(authResultDto)) {
                    authResultDto = new AuthResultDto();
                }
                authResultDto.getSucceed().add(securityDevice.getSecurityDeviceName());
                authResultDtoMap.put(carId, authResultDto);
            }
            // 进行入库保存操作
            SecurityDeviceCar securityDeviceCar = SecurityDeviceCar.builder()
                    .carId(carId)
                    .deviceId(Long.parseLong(securityDeviceId))
                    .startTime(begin)
                    .endTime(end)
                    .build();
            securityDeviceCarService.save(securityDeviceCar);
        }
    }

    private void cancelAuthCarAndOldDevice(Car car, String parkId) {
        List<SecurityDeviceCar> oldList = securityDeviceCarService.lambdaQuery().eq(SecurityDeviceCar::getCarId, car.getId()).list();
        if (CollUtil.isNotEmpty(oldList)) {
            Map<Long, SecurityDevice> deviceMap = securityDeviceService.list().stream().collect(Collectors.toMap(SecurityDevice::getId, Function.identity()));
            //先调用安防组件取消授权
            SecurityDevice securityDevice = deviceMap.get(oldList.get(0).getDeviceId());
            if (Objects.nonNull(securityDevice)) {
                MqttComponentTemplateDto<SecurityCarAuthReqDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDevice.getDeviceSourceIndex());
                SecurityCarAuthReqDto securityCarAuthReqDto = SecurityCarAuthReqDto.builder()
                        .plateNo(car.getCarNo())
                        .parkSyscode(parkId)
                        .build();
                mqttComponentTemplateDto.setParams(securityCarAuthReqDto);
                ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(
                        mqttComponentTemplateDto,
                        SecurityTopicEnum.CAR_CANCEL_AUTH.getTopic(),
                        SecurityTopicEnum.CAR_CANCEL_AUTH.getRedisTypeKey(),
                        mqttComponentTemplateDto.getSerialId());
                if (!Objects.equals(ResultCode.SUCCESS.getCode(), responseDto.getCode())) {
                    log.error("车辆授权操作-请求安防设备组件-取消授权失败，返回信息：{}", responseDto.getMessage());
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "道闸 " + securityDevice.getSecurityDeviceName() + " 取消授权失败");
                }
            }
            for (SecurityDeviceCar securityDeviceCar : oldList) {
                // 进行入库保存操作
                securityDeviceCarService.lambdaUpdate().eq(SecurityDeviceCar::getCarId, car.getId()).eq(SecurityDeviceCar::getDeviceId, securityDeviceCar.getDeviceId()).remove();
            }
        }
    }

    private void checkCarAuthorizeReqDto(CarAuthorizeReqDto carAuthorizeReqDto) {
        String[] carIdList = carAuthorizeReqDto.getCarIdList();
        if (carIdList.length > 100) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "单次授权车辆数最多100辆");
        }
        for (String carId : carIdList) {
            if (ObjectUtil.isEmpty(this.getById(carId))) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "不存在的车辆Id");
            }
        }
        for (String secirutyDeviceId : carAuthorizeReqDto.getSecurityDeviceIdList()) {
            SecurityDevice securityDevice = securityDeviceService.lambdaQuery()
                    .eq(SecurityDevice::getId, secirutyDeviceId)
                    .one();
            if (ObjectUtil.isEmpty(securityDevice)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "道闸不存在");
            }
            if (!SecurityDeviceConstant.BARRIER_TYPE.equals(securityDevice.getSecurityDeviceType())) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "设备类型不是道闸");
            }
        }
        // 校验起止时间与终止时间是否符合正则："^\\d{4}-\\d{2}-\\d{2}\\s\\d{2}:\\d{2}:\\d{2}$"
        if (!carAuthorizeReqDto.getStartTime().matches("^\\d{4}-\\d{2}-\\d{2}\\s\\d{2}:\\d{2}:\\d{2}$")) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "起始时间格式不正确");
        }
        if (!carAuthorizeReqDto.getEndTime().matches("^\\d{4}-\\d{2}-\\d{2}\\s\\d{2}:\\d{2}:\\d{2}$")) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "终止时间格式不正确");
        }

        // 校验起止时间与终止时间是否合法
        if (carAuthorizeReqDto.getStartTime() != null && carAuthorizeReqDto.getEndTime() != null) {
            if (carAuthorizeReqDto.getStartTime().compareTo(carAuthorizeReqDto.getEndTime()) > 0) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "起始时间不能大于终止时间");
            }
            // 开始时间和终止时间间隔至少是两个小时
            String begin = carAuthorizeReqDto.getStartTime();
            String end = carAuthorizeReqDto.getEndTime();
            if (DateUtil.between(DateUtil.parse(begin, "yyyy-MM-dd HH:mm:ss"), DateUtil.parse(end, "yyyy-MM-dd HH:mm:ss"), DateUnit.HOUR, false) < 2) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "时间间隔至少是两个小时");
            }
        }
    }

    /**
     * 该接口为车辆授权多个停车场接口，未做任何校验，先校验在使用，当长期授权，传递的starttime和endtime都要为null，若不是，则为临时授权
     *
     * @param parkIdList 停车场
     * @param carId      车辆id
     * @param startTime  开始时间
     * @param endTime    结束时间
     */
    @Override
    public void authorizeWithPark(String[] parkIdList, Long carId, String startTime, String endTime) {
        Car car = this.getById(carId);
        // 取消旧的数据的授权
        cancelAuthCarAndOldPark(car, parkIdList);
        for (String parkId : parkIdList) {
            // 先调用安防组件授权
            SecurityPark securityPark = securityParkService.getById(parkId);
            MqttComponentTemplateDto<SecurityCarAuthReqDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityPark.getParkSourceIndex());
            SecurityCarAuthReqDto securityCarAuthReqDto = SecurityCarAuthReqDto.builder()
                    .plateNo(car.getCarNo())
                    .parkSyscode(securityPark.getParkSourceIndex())
                    .startTime(startTime)
                    .endTime(endTime)
                    .build();
            mqttComponentTemplateDto.setParams(securityCarAuthReqDto);
            ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(mqttComponentTemplateDto,
                    SecurityTopicEnum.CAR_AUTHORIZATION.getTopic(),
                    CacheConstant.syncCarTopicKey,
                    mqttComponentTemplateDto.getSerialId());
            if (!Objects.equals(ResultCode.SUCCESS.getCode(), responseDto.getCode())) {
                log.info("车辆授权操作-请求安防设备组件-授权失败，返回信息：{}", responseDto.getMessage());
                if (responseDto.getMessage().contains("No spare")) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "停车场 " + securityPark.getName() + " 没有多余的预订空间");
                }
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "停车场 " + securityPark.getName() + " 授权失败");
            }
            // 进行入库保存操作
            securityParkCarService.lambdaUpdate().eq(SecurityParkCar::getCarId, carId).eq(SecurityParkCar::getSecurityParkId, parkId).remove();
            SecurityParkCar securityParkCar = SecurityParkCar.builder()
                    .carId(carId)
                    .isAuth("1")
                    .endTime(StrUtil.isEmpty(endTime) ? null : LocalDateTimeUtil.parse(endTime, DatePattern.NORM_DATETIME_PATTERN))
                    .startTime(StrUtil.isEmpty(startTime) ? null : LocalDateTimeUtil.parse(startTime, DatePattern.NORM_DATETIME_PATTERN))
                    .securityParkId(Long.parseLong(parkId))
                    .build();
            // 将车辆关联停车场记录进行保存
            securityParkCarService.save(securityParkCar);
        }
    }

    /**
     * 二期安防组件没有这个接口
     */
    @Override
    public ResultModel<PersonOrCarBlackResDto> addToBlackList(CarIdsDto carIdsDto) {
        PersonOrCarBlackResDto personOrCarBlackResDto = new PersonOrCarBlackResDto();
        personOrCarBlackResDto.setTotal(carIdsDto.getIds().size());
        List<String> fail = new ArrayList<>();
        List<String> success = new ArrayList<>();
        List<Car> list = this.lambdaQuery().in(Car::getId, carIdsDto.getIds()).list();
        Map<Long, Car> carMap = list.stream().collect(Collectors.toMap(Car::getId, Function.identity()));
        for (String id : carIdsDto.getIds()) {
            Car car = carMap.get(Long.valueOf(id));
            if (car == null) {
                continue;
            }
            //如果车辆不在黑名单中
            if (car.getState().equals(CommonConstant.NOT_ON_THE_CAR_BLACKLIST)) {
                String parkId = securityParkService.getIdsByComponent();
                //获取旧数据
                List<SecurityDeviceCar> oldList = securityDeviceCarService.lambdaQuery().eq(SecurityDeviceCar::getCarId, car.getId()).list();
                if (CollUtil.isNotEmpty(oldList)) {
                    Map<Long, SecurityDevice> deviceMap = securityDeviceService.list().stream().collect(Collectors.toMap(SecurityDevice::getId, Function.identity()));
                    //先调用安防组件取消授权
                    SecurityDevice securityDevice = deviceMap.get(oldList.get(0).getDeviceId());
                    if (Objects.nonNull(securityDevice)) {
                        MqttComponentTemplateDto<SecurityCarAuthReqDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDevice.getDeviceSourceIndex());
                        SecurityCarAuthReqDto securityCarAuthReqDto = SecurityCarAuthReqDto.builder()
                                .plateNo(car.getCarNo())
                                .parkSyscode(parkId)
                                .build();
                        mqttComponentTemplateDto.setParams(securityCarAuthReqDto);
                        ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(
                                mqttComponentTemplateDto,
                                SecurityTopicEnum.CAR_CANCEL_AUTH.getTopic(),
                                SecurityTopicEnum.CAR_CANCEL_AUTH.getRedisTypeKey(),
                                mqttComponentTemplateDto.getSerialId());
                        if (!Objects.equals(ResultCode.SUCCESS.getCode(), responseDto.getCode())) {
                            log.error("车辆授权操作-请求安防设备组件-取消授权失败，返回信息：{}", responseDto.getMessage());
                            throw new ServiceException(ResultCode.OPERATION_FAILURE, "道闸 " + securityDevice.getSecurityDeviceName() + " 取消授权失败");
                        } else {
                            success.add(car.getCarNo());
                            car.setBlackIndex((String) responseDto.getData());
                            car.setState(CommonConstant.ON_THE_CAR_BLACKLIST);
                            this.updateById(car);
                        }
                    }
                }else { //车辆未授权的情况
                    success.add(car.getCarNo());
                    car.setState(CommonConstant.ON_THE_CAR_BLACKLIST);
                    this.updateById(car);
                }
            } else { //在黑名单
                success.add(car.getCarNo());
            }
        }
        List<String> collect = carIdsDto.getIds().stream().filter(car -> !success.contains(carMap.get(Long.valueOf(car)).getCarNo())).map(car -> carMap.get(Long.valueOf(car)).getCarNo()).collect(Collectors.toList());
        fail.addAll(collect);
        personOrCarBlackResDto.setSuccess(success);
        personOrCarBlackResDto.setFail(fail);
        personOrCarBlackResDto.setSuccessNum(success.size());
        personOrCarBlackResDto.setFailNum(fail.size());
        return ResultModel.success(personOrCarBlackResDto);
    }

    /**
     * 二期安防组件没有这个接口
     */
    @Override
    public ResultModel<PersonOrCarBlackResDto> delFromBlackList(CarIdsDto carIdsDto) {
        PersonOrCarBlackResDto personOrCarBlackResDto = new PersonOrCarBlackResDto();
        personOrCarBlackResDto.setTotal(carIdsDto.getIds().size());
        List<String> fail = new ArrayList<>();
        List<String> success = new ArrayList<>();
        List<Car> list = this.lambdaQuery().in(Car::getId, carIdsDto.getIds()).list();
        Map<Long, Car> carMap = list.stream().collect(Collectors.toMap(Car::getId, Function.identity()));
        String parkId = securityParkService.getIdsByComponent();
        for (String id : carIdsDto.getIds()) {
            Car car = carMap.get(Long.valueOf(id));
            List<SecurityDeviceCar> oldList = securityDeviceCarService.lambdaQuery().eq(SecurityDeviceCar::getCarId, car.getId()).list();
            Map<Long, SecurityDevice> deviceMap = securityDeviceService.list().stream().collect(Collectors.toMap(SecurityDevice::getId, Function.identity()));
            if (car == null) {
                continue;
            }
            //添加授权
            for (SecurityDeviceCar deviceCar : oldList) {
                // 先调用安防组件授权
                SecurityDevice securityDevice = deviceMap.get(deviceCar.getDeviceId());
                MqttComponentTemplateDto<SecurityCarAuthReqDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDevice.getDeviceSourceIndex());
                SecurityCarAuthReqDto securityCarAuthReqDto = SecurityCarAuthReqDto.builder()
                        .plateNo(car.getCarNo())
                        .parkSyscode(parkId)
                        .startTime(deviceCar.getStartTime())
                        .endTime(deviceCar.getEndTime())
                        .build();
                mqttComponentTemplateDto.setParams(securityCarAuthReqDto);
                ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(
                        mqttComponentTemplateDto,
                        SecurityTopicEnum.CAR_AUTHORIZATION.getTopic(),
                        SecurityTopicEnum.CAR_AUTHORIZATION.getRedisTypeKey(),
                        mqttComponentTemplateDto.getSerialId());
                if (!Objects.equals(ResultCode.SUCCESS.getCode(), responseDto.getCode())) {
                    log.info("车辆授权操作-请求安防设备组件-授权失败，返回信息：{}", responseDto.getMessage());
                    AuthResultDto authResultDto = authResultDtoMap.get(car.getId());
                    if (ObjectUtil.isEmpty(authResultDto)) {
                        authResultDto = new AuthResultDto();
                    }
                    authResultDto.getFailed().add(securityDevice.getSecurityDeviceName() + ": " + responseDto.getMessage());
                    authResultDtoMap.put(car.getId(), authResultDto);
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "道闸 " + securityDevice.getSecurityDeviceName() + " 授权失败");
                } else {
                    AuthResultDto authResultDto = authResultDtoMap.get(car.getId());
                    if (ObjectUtil.isEmpty(authResultDto)) {
                        authResultDto = new AuthResultDto();
                    }
                    authResultDto.getSucceed().add(securityDevice.getSecurityDeviceName());
                    authResultDtoMap.put(car.getId(), authResultDto);
                }
            }
            success.add(car.getCarNo());
            car.setState(CommonConstant.NOT_ON_THE_CAR_BLACKLIST);
            car.setBlackIndex(null);
            this.updateById(car);
        }
        personOrCarBlackResDto.setSuccess(success);
        personOrCarBlackResDto.setFail(fail);
        personOrCarBlackResDto.setSuccessNum(success.size());
        personOrCarBlackResDto.setFailNum(fail.size());
        return ResultModel.success(personOrCarBlackResDto);
    }

    /**
     * 取消旧的车辆授权记录
     *
     * @param car
     * @param parkIdList
     */
    private void cancelAuthCarAndOldPark(Car car, String[] parkIdList) {
        List<SecurityParkCar> oldList = securityParkCarService.lambdaQuery().eq(SecurityParkCar::getCarId, car.getId()).list();
        if (CollUtil.isNotEmpty(oldList)) {
            List<Long> oldParkIdList = oldList.stream().map(SecurityParkCar::getSecurityParkId).collect(Collectors.toList());
            // 取出oldParkIdList中不在parkIdList中的停车场id
            List<String> newParkIdList = Arrays.asList(parkIdList);
            List<Long> cancelParkIdList = oldParkIdList.stream().filter(item -> !newParkIdList.contains(item.toString())).collect(Collectors.toList());
            for (Long parkId : cancelParkIdList) {
                SecurityPark securityPark = securityParkService.getById(parkId);
                MqttCarCancelAuthDto mqttDto = MqttCarCancelAuthDto.builder().parkId(securityPark.getParkSourceIndex()).plateId(car.getCarIndex()).build();
                ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(mqttDto, "car", "cancelAuth", securityComponentProperties.getDeviceFactory(), IdUtil.simpleUUID(), CacheConstant.syncCarTopicKey);
                if (responseDto.getCode() != 0) {
                    log.info("车辆授权操作-请求安防设备组件-取消授权失败，返回信息：{}", responseDto.getMessage());
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "停车场 " + securityPark.getName() + " 取消授权失败");
                }
                // 删除授权记录
                securityParkCarService.lambdaUpdate().eq(SecurityParkCar::getCarId, car.getId()).eq(SecurityParkCar::getSecurityParkId, parkId).remove();
            }
        }
    }

    /**
     * 校验已经绑定过停车库的车辆，返回旧的授权时间与新的授权时间的并集
     *
     * @param securityParkCar 库中旧的授权数据
     * @param startTime       新的授权开始时间
     * @param endTime         新的授权结束时间
     * @return
     */
    private SecurityParkCar checkAuthedTime(SecurityParkCar securityParkCar, String startTime, String endTime) {
        LocalDateTime[] oldTime = {securityParkCar.getStartTime(), securityParkCar.getEndTime()};
        LocalDateTime[] newTime = {LocalDateTimeUtil.parse(startTime), LocalDateTimeUtil.parse(endTime)};
        LocalDateTime[] result = new LocalDateTime[2];
        if (oldTime[1].isBefore(newTime[0]) || oldTime[0].isAfter(newTime[1])) {
            // 两段时间没有交集
            result[0] = newTime[0];
            result[1] = newTime[1];
        } else {
            // 有交集
            result[0] = oldTime[0].isBefore(newTime[0]) ? oldTime[0] : newTime[0];
            result[1] = oldTime[1].isAfter(newTime[1]) ? oldTime[1] : newTime[1];
        }
        securityParkCar.setStartTime(result[0]);
        securityParkCar.setEndTime(result[1]);
        return securityParkCar;
    }

    @Override
    public void downloadCarTemplate(HttpServletResponse response) {
        InputStream inputStream = null;
        try {
            ClassPathResource classPathResource = new ClassPathResource("excelTemplate/车辆导入模版.xlsx");
            inputStream = classPathResource.getInputStream();
            Workbook workbook = new XSSFWorkbook(inputStream);
            List<ExcelSelectorDTO> selectorDTOList = this.generateSelectors();
            ExcelUtils.defaultExport(workbook, selectorDTOList, "车辆导入模板", response);
        } catch (Exception e) {
            throw new ServiceException(ResultCode.DOWNLOAD_FAIL, e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("关闭输入流异常", e);
            }
        }
    }

    @Override
    public void importCar(MultipartFile file, HttpServletResponse response) {
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        params.setStartRows(0);
        params.setNeedVerify(false);
        // 创建自定义处理器实例
        ExcelImportResult<ExcelImportCarDto> importExcelMore = null;
        try {
            importExcelMore = ExcelImportUtil.importExcelMore(file.getInputStream(), ExcelImportCarDto.class, params);
            typeCodeMapping(importExcelMore.getList());
        } catch (Exception e) {
            throw new ServiceException(ResultCode.IMPORT_FAIL, e);
        }
        //获取违反注解校验的错误信息
        List<ExcelImportCarDto> failList = importExcelMore.getFailList();
        //获取正常数据
        List<ExcelImportCarDto> list = importExcelMore.getList();
        //整合一起
        list.addAll(failList);
        //校验参数合法
        Map<String, Object> resultMap = this.checkImportParam(list);
        List<Car> normalCarList = (List<Car>) resultMap.get("normalCarList");
        List<ExcelImportCarDto> failCarList = (List<ExcelImportCarDto>) resultMap.get("failCarList");

        batchSaveCar(normalCarList, list, failCarList, response);
        log.info("导入车辆信息，正常数据：{}", JSONUtil.toJsonStr(normalCarList));
    }

    /**
     * 类型名称映射为code
     */
    private void typeCodeMapping(List<ExcelImportCarDto> resultList) {
        // 获取所有类型并按category分组创建name->code映射
        List<CommonType> types = commonTypeService.list();
        Map<String, String> colorMap = getTypeCodeMap(types, CommonConstant.TYPE_FIVE);
        Map<String, String> carTypeMap = getTypeCodeMap(types, CommonConstant.TYPE_SIX);

        // 批量更新
        resultList.forEach(data -> {
            data.setColor(colorMap.getOrDefault(data.getColor(), data.getColor()));
            data.setCarType(carTypeMap.getOrDefault(data.getCarType(), data.getCarType()));
        });
    }

    /**
     * 类型code映射为名称
     * @param resultList 结果列表
     * @param <T> 数据类型，必须实现CarMappable接口
     */
    private <T extends CarMappable> void typeNameMapping(List<T> resultList) {
        if (CollUtil.isEmpty(resultList)) {
            return;
        }

        // 获取所有类型并按category分组创建code->name映射
        List<CommonType> types = commonTypeService.list();
        Map<String, String> colorMap = getTypeNameMap(types, CommonConstant.TYPE_FIVE);
        Map<String, String> carTypeMap = getTypeNameMap(types, CommonConstant.TYPE_SIX);

        // 批量更新
        resultList.forEach(data -> {
            data.setColor(colorMap.getOrDefault(data.getColor(), data.getColor()));
            data.setCarType(carTypeMap.getOrDefault(data.getCarType(), data.getCarType()));
        });
    }

    /**
     * 创建name->code的映射
     */
    private Map<String, String> getTypeCodeMap(List<CommonType> types, String category) {
        return types.stream()
                .filter(type -> category.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getName,     // key是name
                        CommonType::getTypeCode, // value是code
                        (v1, v2) -> v1
                ));
    }

    /**
     * 创建code->name的映射
     */
    private Map<String, String> getTypeNameMap(List<CommonType> types, String category) {
        return types.stream()
                .filter(type -> category.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode, // key是code
                        CommonType::getName,     // value是name
                        (v1, v2) -> v1
                ));
    }

    private void batchSaveCar(List<Car> normalCarList, List<ExcelImportCarDto> list, List<ExcelImportCarDto> failCarList, HttpServletResponse response) {
        errorMap.clear();
        if (CollUtil.isEmpty(normalCarList) && CollUtil.isEmpty(failCarList)) {
            log.info("导入的数据为空:所有列表：{}，校验成功列表：{}，校验失败列表：{}", JSON.toJSONString(list), JSON.toJSONString(normalCarList), JSON.toJSONString(failCarList));
            return;
        }
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNamePrefix("car-import-pool-%d").build();
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new LinkedBlockingDeque<>(), threadFactory);
        for (Car car : normalCarList) {
            CarAddAndUpdateReqDto dto = carTransfer.entity2Dto(car);
            threadPoolExecutor.execute(() -> {
                try {
                    addCar(dto);
                } catch (Exception e) {
                    log.error("保存人员信息异常，异常信息：{}", e.getMessage());
                    // 添加失败，将错误信息存入全局map
                    String key = car.getCarNo();
                    String errorMessage = e.getMessage();
                    errorMap.put(key, errorMessage);
                }
            });
        }
        // 等待所有线程执行完毕
        threadPoolExecutor.shutdown();
        try {
            threadPoolExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
        } catch (InterruptedException e) {
            log.error("线程池等待被中断：{}", e.getMessage());
        }
        List<ExcelImportCarDto> resultList = new ArrayList<>(failCarList);
        // 检查全局map是否为空
        if (!errorMap.isEmpty()) {
            // 根据需要处理添加失败的情况，比如输出错误信息，进行日志记录等
            for (Map.Entry<String, String> entry : errorMap.entrySet()) {
                log.error("车辆添加失败，key: {}, 错误信息: {}", entry.getKey(), entry.getValue());
                String carNo = entry.getKey();
                for (ExcelImportCarDto excelCarDto : list) {
                    if (carNo.equals(excelCarDto.getCarNo())) {
                        excelCarDto.setErrorMsg(entry.getValue());
                        resultList.add(excelCarDto);
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(resultList)) {
            typeNameMapping(resultList);
            List<ExcelSelectorDTO> selectorDTOList = this.generateSelectors();
            try {
                ExcelUtils.exportExcel(resultList, selectorDTOList, "车辆信息", "车辆信息001", ExcelImportCarDto.class, "车辆错误信息", response);
            } catch (IOException e) {
                throw new ServiceException(ResultCode.EXPORT_FAIL, e);
            }
        }

    }

    private Map<String, Object> checkImportParam(List<ExcelImportCarDto> excelCarDtoList) {
        Map<String, Object> resultMap = new HashMap<>();
        //正常数据
        List<Car> normalCarList = new ArrayList<>();
        //错误数据
        List<ExcelImportCarDto> failCarList = new ArrayList<>();

        Map<String, Integer> map = new HashMap<>();

        for (ExcelImportCarDto excelCarDto : excelCarDtoList) {
            Person person = null;
            StringJoiner joiner = new StringJoiner(",");
            // 校验人员是否存在，是否重复
            String personInfo = excelCarDto.getPersonInfo();
            if (StrUtil.isNotEmpty(personInfo) && personInfo.contains("-")) {
                String[] split = personInfo.split("-");
                String personName = split[0];
                String personCertificateNum = split[1];
                person = personService.lambdaQuery().eq(Person::getPersonName, personName).eq(Person::getPersonCertificateNum, personCertificateNum).one();
                if (ObjectUtil.isEmpty(person)) {
                    joiner.add("人员 " + personInfo + " 不存在");
                }
            } else {
                joiner.add("人员信息格式错误：人员名称-证件号");
            }

            // 校验车牌号是否存在，是否重复
            String carNo = excelCarDto.getCarNo();
            Car car = this.lambdaQuery().eq(Car::getCarNo, carNo).one();
            if (ObjectUtil.isNotEmpty(car)) {
                joiner.add("车牌号 " + carNo + " 已存在");
            }
            if (map.containsKey(excelCarDto.getCarNo())) {
                joiner.add("车牌号重复");
                Integer num = map.get(excelCarDto.getCarNo());
                map.put(excelCarDto.getCarNo(), num + 1);
            } else {
                map.put(excelCarDto.getCarNo(), 1);
            }
            if (joiner.length() > 0) {
                String errorMsg = excelCarDto.getErrorMsg();
                if (StrUtil.isNotBlank(errorMsg)) {
                    excelCarDto.setErrorMsg(errorMsg + "," + joiner.toString());
                } else {
                    excelCarDto.setErrorMsg(joiner.toString());
                }
            }
            //添加错误的数据
            if (StrUtil.isNotBlank(excelCarDto.getErrorMsg())) {
                failCarList.add(excelCarDto);
            } else {
                //正常数据
                car = carTransfer.dto2entity(excelCarDto);
                car.setPersonId(person.getId());
                String carFaceUrlFile = excelCarDto.getCarImageUrl();
                if (StrUtil.isNotBlank(carFaceUrlFile)) {
                    String carFaceUrl = SecurityImageUtils.uploadImage(carFaceUrlFile, "personinfo");
                    car.setCarImageUrl(carFaceUrl);
                }
                normalCarList.add(car);
            }
        }
        resultMap.put("failCarList", failCarList);
        resultMap.put("normalCarList", normalCarList);
        return resultMap;
    }

    @Override
    public void exportCar(CarGetReqDto dto, HttpServletResponse response) {
        dto.setPageIndex(1);
        dto.setPageSize(10000);
        List<CarGetResDataListDto> carList = this.findPageList(dto).getRecords();
        if (CollUtil.isEmpty(carList)) {
            throw new ServiceException(ResultCode.NOT_NULL, "导出的数据");
        }
        List<ExcelExportCarDto> carDtoList = new ArrayList<>();
        for (CarGetResDataListDto carDto : carList) {
            byte[] file = null;
            String carImageUrl = carDto.getCarImageUrl();
            if (StrUtil.isNotBlank(carImageUrl)) {
                file = SecurityImageUtils.getImageStreamByUrl(carImageUrl);
            }
            ExcelExportCarDto excelCarDto = ExcelExportCarDto.builder()
                    .personInfo(StrUtil.isNotEmpty(carDto.getPersonName()) ? carDto.getPersonName() + "-" + carDto.getPersonCertificateNum() : "")
                    .carNo(carDto.getCarNo())
                    .carType(carDto.getCarType())
                    .remarks(carDto.getRemarks())
                    .color(carDto.getColor())
                    .carImageUrl(file)
                    .build();
            carDtoList.add(excelCarDto);
        }
        List<ExcelSelectorDTO> excelSelectorDTOS = this.generateSelectors();
        try {
            typeNameMapping(carDtoList);
            ExportParams exportParams = new ExportParams("车辆信息", "车辆信息001", ExcelType.HSSF);
            ExcelUtils.exportExcel(carDtoList, ExcelExportCarDto.class, "车辆信息", exportParams, response);
        } catch (IOException e) {
            throw new ServiceException(ResultCode.EXPORT_FAIL, e);
        }
    }

    private List<ExcelSelectorDTO> generateSelectors() {
        List<ExcelSelectorDTO> selectorList = new ArrayList<>();

        List<Person> personList = personService.list();
        List<CommonType> commonTypeList = commonTypeService.list();
        String[] carTypeArray = commonTypeList.stream().filter(v -> v.getCategory().equals(CommonConstant.TYPE_SIX)).map(commonType -> commonType.getName()).toArray(String[]::new);
        String[] carColorArray = commonTypeList.stream().filter(v -> v.getCategory().equals(CommonConstant.TYPE_FIVE)).map(commonType -> commonType.getName()).toArray(String[]::new);
        // 遍历人员列表，将人员名称和证件号拼接成字符串，放到数组中
        String[] personData = new String[personList.size()];
        for (int i = 0; i < personList.size(); i++) {
            Person person = personList.get(i);
            personData[i] = person.getPersonName() + "-" + person.getPersonCertificateNum();
        }
        // 人员
        ExcelSelectorDTO personSelector = new ExcelSelectorDTO();
        personSelector.setSheetIndex(0);
        personSelector.setLastRow(65535);
        personSelector.setFirstRow(2);
        personSelector.setFirstCol(0);
        personSelector.setLastCol(0);
        personSelector.setData(personData);
        selectorList.add(personSelector);

        // 车辆类型
        ExcelSelectorDTO carTypeSelector = new ExcelSelectorDTO();
        carTypeSelector.setSheetIndex(0);
        carTypeSelector.setLastRow(65535);
        carTypeSelector.setFirstRow(2);
        carTypeSelector.setFirstCol(2);
        carTypeSelector.setLastCol(2);
        carTypeSelector.setData(carTypeArray);
        selectorList.add(carTypeSelector);

        // 车辆颜色
        ExcelSelectorDTO carColorSelector = new ExcelSelectorDTO();
        carColorSelector.setSheetIndex(0);
        carColorSelector.setLastRow(65535);
        carColorSelector.setFirstRow(2);
        carColorSelector.setFirstCol(4);
        carColorSelector.setLastCol(4);
        carColorSelector.setData(carColorArray);
        selectorList.add(carColorSelector);

        return selectorList;
    }

    @Override
    public List<PersonAuthedDeviceVo> getCarAuthedDevice(Long carId) {
        Car car = this.getById(carId);
        if (ObjectUtil.isEmpty(car)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "车辆不存在");
        }
        List<PersonAuthedDeviceVo> resultList = new ArrayList<>();
        resultList = carMapper.getAuthedByCarId(carId);
        if (CollUtil.isNotEmpty(resultList)) {
            List<Long> deviceIdList = resultList.stream().map(e -> e.getSecurityDeviceId()).collect(Collectors.toList());
            List<SecurityDeviceCar> list = securityDeviceCarService.lambdaQuery().eq(SecurityDeviceCar::getCarId, carId).in(SecurityDeviceCar::getDeviceId, deviceIdList).list();
            for (PersonAuthedDeviceVo personAuthedDeviceVo : resultList) {
                list.forEach(securityDeviceCar -> {
                    List<SecurityDeviceDoor> securityDeviceDoorList = new ArrayList<>();
                    SecurityDeviceDoor securityDeviceDoor = new SecurityDeviceDoor();
                    securityDeviceDoor.setDoorName(personAuthedDeviceVo.getSecurityDeviceName());
                    securityDeviceDoor.setSecurityDeviceId(personAuthedDeviceVo.getSecurityDeviceId());
                    securityDeviceDoorList.add(securityDeviceDoor);
                    personAuthedDeviceVo.setStartTime(securityDeviceCar.getStartTime());
                    personAuthedDeviceVo.setEndTime(securityDeviceCar.getEndTime());
                    personAuthedDeviceVo.setSecurityDeviceDoorList(securityDeviceDoorList);
                });
            }
        }
        return resultList;
    }

    @Override
    public Person getCarPersonInfo(String licensePlate) {
        Person person = carMapper.getCarPersonInfo(licensePlate);
        return person;
    }
}
