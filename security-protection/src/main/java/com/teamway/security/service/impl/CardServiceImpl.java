package com.teamway.security.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import com.teamway.security.common.constant.CacheConstant;
import com.teamway.security.common.constant.CardStateConstant;
import com.teamway.security.common.constant.SecurityDeviceConstant;
import com.teamway.security.common.constant.VariousConstant;
import com.teamway.security.common.enums.SecurityTopicEnum;
import com.teamway.security.common.properties.SecurityComponentProperties;
import com.teamway.security.common.transfer.SecurityConfiguration.CardListTransfer;
import com.teamway.security.common.transfer.SecurityConfiguration.CardTransfer;
import com.teamway.security.dto.ResponseDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.SecurityDevice.MqttComponentTemplateDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.card.MqttDelCardDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.card.MqttSyncCardDto;
import com.teamway.security.dto.securityConfiguration.card.CardListQueryDto;
import com.teamway.security.dto.securityConfiguration.card.CardReportTheLossQueryDto;
import com.teamway.security.dto.securityConfiguration.card.CardReturnQueryDto;
import com.teamway.security.entity.SecurityConfiguration.Card;
import com.teamway.security.entity.SecurityConfiguration.Person;
import com.teamway.security.entity.SecurityConfiguration.SecurityPeripheral;
import com.teamway.security.mapper.CardMapper;
import com.teamway.security.service.CardService;
import com.teamway.security.service.PersonService;
import com.teamway.security.service.SecurityDeviceService;
import com.teamway.security.service.SecurityPeripheralService;
import com.teamway.security.util.SecurityComponentUtil;
import com.teamway.security.vo.securityConfiguration.card.CardListVo;
import com.teamway.security.vo.securityConfiguration.card.CardReturnVo;
import com.teamway.security.vo.securityConfiguration.card.CardVo;
import com.teamway.security.vo.securityConfiguration.card.ReadCardDeviceVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/13
 */
@Service
@Slf4j
public class CardServiceImpl extends ServiceImpl<CardMapper, Card> implements CardService {

    private PersonService personService;
    private CardTransfer cardTransfer;
    private CardListTransfer cardListTransfer;
    private SecurityComponentUtil securityComponentUtil;
    private CardMapper cardMapper;
    private SecurityPeripheralService securityPeripheralService;
    private SecurityComponentProperties securityComponentProperties;
    private TransactionTemplate transactionTemplate;

    @Autowired
    @Lazy
    private SecurityDeviceService securityDeviceService;
    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setCardTransfer(CardTransfer cardTransfer) {
        this.cardTransfer = cardTransfer;
    }

    @Autowired
    public void setCardListTransfer(CardListTransfer cardListTransfer) {
        this.cardListTransfer = cardListTransfer;
    }

    @Autowired
    public void setSecurityComponentUtil(SecurityComponentUtil securityComponentUtil) {
        this.securityComponentUtil = securityComponentUtil;
    }

    @Autowired
    public void setCardMapper(CardMapper cardMapper) {
        this.cardMapper = cardMapper;
    }

    @Autowired
    public void setSecurityPeripheralService(SecurityPeripheralService securityPeripheralService) {
        this.securityPeripheralService = securityPeripheralService;
    }

    @Autowired
    public void setSecurityComponentProperties(SecurityComponentProperties securityComponentProperties) {
        this.securityComponentProperties = securityComponentProperties;
    }

    @Autowired
    public void setTransactionTemplate(TransactionTemplate transactionTemplate) {
        this.transactionTemplate = transactionTemplate;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindingCard(List<Card> cardList, String personSourceIndex) {
        // todo 绑卡需要修改逻辑，改为请求组件修改人员，携带卡片参数实现
        if (CollUtil.isNotEmpty(cardList)) {
            Long personId = cardList.get(0).getPersonId();
            Long count = this.lambdaQuery().eq(Card::getPersonId, personId).count();
            if (cardList.size() + count > VariousConstant.PERSON_BINDING_CARD_NUM) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员最多绑定五张卡");
            }
        }
        saveBatch(cardList);
    }

    @Override
    public boolean checkCardIsBinded(Card card) {
        Card cardInDb = this.lambdaQuery()
                .eq(Card::getCardNo, card.getCardNo())
                .eq(Card::getCardType, card.getCardType())
                .ne(ObjectUtil.isNotEmpty(card.getPersonId()), Card::getPersonId, card.getPersonId())
                .one();
        return ObjectUtil.isNotEmpty(cardInDb);
    }

    @Override
    public void delCardByPersonId(Long id) {
        List<Card> cardList = this.lambdaQuery().eq(Card::getPersonId, id).list();
        for (Card card : cardList) {
            MqttDelCardDto mqttDelCardDto = cardTransfer.entity2MqttDelDto(card);
            Person person = personService.getById(card.getPersonId());
            mqttDelCardDto.setPersonId(person.getPersonSourceIndex());
            ResponseDto resultModel = securityComponentUtil.sendMqttToSecurityComponent(mqttDelCardDto, "card", "del", securityComponentProperties.getDeviceFactory(), card.getCardNo(), CacheConstant.syncCardTopicKey);
            if (resultModel.getCode() != 0) {
                log.info("删除卡片失败,卡号为:{}", card.getCardNo());
                continue;
            }
            removeById(card);
        }
    }

    @Override
    public List<CardVo> getCardListByPersonId(Long personId) {
        List<Card> cardList = this.lambdaQuery().eq(Card::getPersonId, personId).list();
        return cardListTransfer.entityList2VoList(cardList);
    }

    @Override
    public IPage<CardListVo> listCard(CardListQueryDto dto) {
        IPage<CardListVo> list = cardMapper.listCard(new Page<CardListVo>(dto.getPageIndex(), dto.getPageSize()), dto);
        return list;
    }

    @Override
    public CardReturnVo returnCard(CardReturnQueryDto dto) {
        checkCardReturnQueryDto(dto);
        String[] cardList = dto.getCardList();
        List<String> successList = new ArrayList<>();
        List<String> failList = new ArrayList<>();
        Map<String, Card> cardMap = lambdaQuery()
                .in(Card::getCardNo, cardList)
                .list()
                .stream()
                .collect(Collectors.toMap(Card::getCardNo, card -> card));
        for (String cardId : cardList) {
            Card card = cardMap.get(cardId);
            Person person = personService.getById(card.getPersonId());
            // 先执行组件操作，再执行数据库操作
            MqttDelCardDto mqttDelCardDto = MqttDelCardDto.builder().personId(person.getPersonSourceIndex()).cardNo(card.getCardNo()).build();
            MqttComponentTemplateDto<MqttDelCardDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.DOOR_TYPE));
            mqttComponentTemplateDto.setParams(mqttDelCardDto);
            ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(mqttComponentTemplateDto, SecurityTopicEnum.DELETE_CARD.getTopic(), SecurityTopicEnum.DELETE_CARD.getRedisTypeKey(), mqttComponentTemplateDto.getSerialId());
            if (responseDto.getCode() != 0) {
                log.info("退卡操作-请求安防设备组件-退卡失败，返回信息：{}", responseDto.getMessage());
                failList.add(card.getCardNo());
                continue;
            }
            successList.add(card.getCardNo());
            this.removeById(card);
        }
        return CardReturnVo.builder().success(successList).fail(failList).build();
    }

    /**
     * 校验退卡参数，卡片是否存在，卡片是否正常
     * @param dto 退卡参数
     */
    private void checkCardReturnQueryDto(CardReturnQueryDto dto) {
        String[] cardList = dto.getCardList();
        Map<String, Card> cardMap = lambdaQuery()
                .in(Card::getCardNo, cardList)
                .list()
                .stream()
                .collect(Collectors.toMap(Card::getCardNo, card -> card));
        for (String cardId : cardList) {
            if (!cardMap.keySet().contains(cardId)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "卡片不存在");
            } else if (!CardStateConstant.NORMAL.equals(cardMap.get(cardId).getCardState())) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "卡片 " + cardId + " 不是正常状态");
            }
        }
    }

    @Override
    public boolean cardReportTheLoss(CardReportTheLossQueryDto dto) {
        Card card = getById(dto.getCardId());
        if (ObjectUtil.isEmpty(card)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "卡片不存在");
        }
        Person person = personService.getById(card.getPersonId());
        transactionTemplate.execute(status -> {
            try {
                this.lambdaUpdate().eq(Card::getId, dto.getCardId()).set(Card::getCardState, dto.getState()).update();
                MqttSyncCardDto mqttSyncCardDto = MqttSyncCardDto.builder().cardNo(card.getCardNo()).cardState(dto.getState()).personId(person.getPersonSourceIndex()).build();
                ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(mqttSyncCardDto, "card", "sync", securityComponentProperties.getDeviceFactory(), card.getCardNo(), CacheConstant.syncCardTopicKey);
                if (responseDto.getCode() != 0) {
                    log.info("卡片状态操作-请求安防设备组件-更新失败，返回信息：{}", responseDto.getMessage());
                    throw new RuntimeException();
                }
                status.flush();
            } catch (Exception e) {
                //事务回滚
                status.setRollbackOnly();
                e.printStackTrace();
                log.info("挂失过程异常，异常信息：{}", e.getMessage());
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "挂失失败");
            }
            return null;
        });
        return true;
    }

    @Override
    public String getReadCardDeviceUrl() {
        List<SecurityPeripheral> list = securityPeripheralService.lambdaQuery().eq(SecurityPeripheral::getType, "2").list();
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "读卡程序下载链接未初始化");
        }
        return list.get(0).getUrl();
    }

    @Override
    public ReadCardDeviceVo getReadCardDevice() {
        List<SecurityPeripheral> list = securityPeripheralService.lambdaQuery().eq(SecurityPeripheral::getType, "2").list();
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "读卡程序下载链接未初始化");
        }
        SecurityPeripheral securityPeripheral = list.get(0);
        ReadCardDeviceVo readCardDeviceVo = new ReadCardDeviceVo();
        readCardDeviceVo.setUser(securityPeripheral.getUsername());
        readCardDeviceVo.setPsd(securityPeripheral.getPassword());
        return readCardDeviceVo;
    }
}
