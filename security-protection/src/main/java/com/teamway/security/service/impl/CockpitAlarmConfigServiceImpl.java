package com.teamway.security.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.security.entity.CockpitAlarmConfig;
import com.teamway.security.mapper.CockpitAlarmConfigMapper;
import com.teamway.security.service.CockpitAlarmConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClossName CockpitAlarmConfigServiceImpl
 * @Description 驾驶舱亮点告警图片数据配置 服务实现类
 * <AUTHOR>
 * @createDate 2025/08/19
 * @version 1.0
 **/
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class CockpitAlarmConfigServiceImpl extends ServiceImpl<CockpitAlarmConfigMapper, CockpitAlarmConfig> implements CockpitAlarmConfigService {

    @Override
    public Boolean insertOrUpdateCockpitAlarmConfig(CockpitAlarmConfig cockpitAlarmConfig) {
        log.info("开始新增或更新驾驶舱亮点告警配置，参数：{}", JSONUtil.toJsonStr(cockpitAlarmConfig));
        
        try {
            // 查询现有配置
            CockpitAlarmConfig existingConfig = selectCockpitAlarmConfig();
            
            boolean operationResult;
            if (existingConfig != null) {
                // 更新现有配置
                Long existingId = existingConfig.getId();
                cockpitAlarmConfig.setId(existingId);
                cockpitAlarmConfig.setUpdateTime(LocalDateTime.now());
                operationResult = this.updateById(cockpitAlarmConfig);
                log.info("更新驾驶舱亮点告警配置完成，结果：{}", operationResult);
            } else {
                // 新增配置
                cockpitAlarmConfig.setCreateTime(LocalDateTime.now());
                cockpitAlarmConfig.setUpdateTime(LocalDateTime.now());
                operationResult = this.save(cockpitAlarmConfig);
                log.info("新增驾驶舱亮点告警配置完成，结果：{}", operationResult);
            }
            
            return operationResult;
        } catch (Exception e) {
            log.error("新增或更新驾驶舱亮点告警配置异常，参数：{}，异常信息：{}", JSONUtil.toJsonStr(cockpitAlarmConfig), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public CockpitAlarmConfig selectCockpitAlarmConfig() {
        log.info("开始查询驾驶舱亮点告警配置");
        
        try {
            LambdaQueryWrapper<CockpitAlarmConfig> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(CockpitAlarmConfig::getUpdateTime)
                       .last("LIMIT 1");
            
            List<CockpitAlarmConfig> configList = this.list(queryWrapper);
            CockpitAlarmConfig result = configList.isEmpty() ? null : configList.get(0);
            
            log.info("查询驾驶舱亮点告警配置完成，结果：{}", JSONUtil.toJsonStr(result));
            return result;
        } catch (Exception e) {
            log.error("查询驾驶舱亮点告警配置异常，异常信息：{}", e.getMessage(), e);
            throw e;
        }
    }
}