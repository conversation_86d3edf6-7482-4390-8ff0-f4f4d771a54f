package com.teamway.security.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.common.entity.BatchIds;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import com.teamway.security.dto.DangerousProjectDailyQueryDto;
import com.teamway.security.entity.DangerousProjectDaily;
import com.teamway.security.mapper.DangerousProjectDailyMapper;
import com.teamway.security.service.DangerousProjectDailyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 当日危大工程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Slf4j
@Service
public class DangerousProjectDailyServiceImpl extends ServiceImpl<DangerousProjectDailyMapper, DangerousProjectDaily> implements DangerousProjectDailyService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addDangerousProjectDaily(DangerousProjectDaily dangerousProjectDaily) {
        if (dangerousProjectDaily == null) {
            throw new ServiceException(ResultCode.NOT_NULL, "参数");
        }

        if (dangerousProjectDaily.getRecordDate() == null) {
            dangerousProjectDaily.setRecordDate(new Date());
        }

        return save(dangerousProjectDaily);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDangerousProjectDaily(DangerousProjectDaily dangerousProjectDaily) {
        if (dangerousProjectDaily == null || dangerousProjectDaily.getId() == null) {
            throw new ServiceException(ResultCode.NOT_NULL, "参数或ID");
        }

        DangerousProjectDaily existRecord = getById(dangerousProjectDaily.getId());
        if (existRecord == null) {
            throw new ServiceException(ResultCode.NOT_EXIST, "当日危大工程记录");
        }

        return updateById(dangerousProjectDaily);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteDangerousProjectDaily(BatchIds request) {
        if (request == null || request.getIds() == null || request.getIds().isEmpty()) {
            throw new ServiceException(ResultCode.NOT_NULL, "ID列表");
        }

        return removeByIds(request.getIds());
    }

    @Override
    public DangerousProjectDaily getDangerousProjectDailyById(Long id) {
        if (id == null) {
            throw new ServiceException(ResultCode.NOT_NULL, "ID");
        }

        return getById(id);
    }

    @Override
    public List<DangerousProjectDaily> findPageList(DangerousProjectDailyQueryDto queryDto) {
        LambdaQueryWrapper<DangerousProjectDaily> queryWrapper = new LambdaQueryWrapper<>();

        // 使用简化的条件构造方式
        queryWrapper
                .like(StrUtil.isNotBlank(queryDto.getProjectName()),
                        DangerousProjectDaily::getProjectName, queryDto.getProjectName())
                .like(StrUtil.isNotBlank(queryDto.getResponsibleUnit()),
                        DangerousProjectDaily::getResponsibleUnit, queryDto.getResponsibleUnit())
                .like(StrUtil.isNotBlank(queryDto.getResponsiblePerson()),
                        DangerousProjectDaily::getResponsiblePerson, queryDto.getResponsiblePerson())
                .eq(StrUtil.isNotBlank(queryDto.getStatus()),
                        DangerousProjectDaily::getStatus, queryDto.getStatus())
                .ge(Objects.nonNull(queryDto.getStartDate()),
                        DangerousProjectDaily::getRecordDate, queryDto.getStartDate())
                .le(Objects.nonNull(queryDto.getEndDate()),
                        DangerousProjectDaily::getRecordDate, queryDto.getEndDate())
                .orderByDesc(DangerousProjectDaily::getCreateTime);

        Page<DangerousProjectDaily> page = new Page<>(queryDto.getPageIndex(), queryDto.getPageSize());
        Page<DangerousProjectDaily> result = page(page, queryWrapper);

        return result.getRecords();
    }
}