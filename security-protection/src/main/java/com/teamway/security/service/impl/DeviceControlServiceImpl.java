package com.teamway.security.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.teamway.common.constant.CommonConstant;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import com.teamway.security.common.constant.CacheConstant;
import com.teamway.security.common.constant.SecurityDeviceConstant;
import com.teamway.security.common.enums.SecurityTopicEnum;
import com.teamway.security.common.properties.SecurityComponentProperties;
import com.teamway.security.dto.DeviceControlResDto;
import com.teamway.security.dto.ResponseDto;
import com.teamway.security.dto.accessManagement.securityDevice.SecurityDeviceControlQueryDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.SecurityDevice.MqttComponentTemplateDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.SecurityDevice.MqttSecurityDeviceControlQueryDto;
import com.teamway.security.entity.SecurityConfiguration.SecurityDevice;
import com.teamway.security.entity.SecurityConfiguration.SecurityDeviceDoor;
import com.teamway.security.service.DeviceControlService;
import com.teamway.security.service.SecurityDeviceDoorService;
import com.teamway.security.service.SecurityDeviceService;
import com.teamway.security.util.SecurityComponentUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class DeviceControlServiceImpl implements DeviceControlService {

    private  SecurityComponentUtil securityComponentUtil;
    private  SecurityDeviceDoorService securityDeviceDoorService;
    private  SecurityComponentProperties securityComponentProperties;
    private  SecurityDeviceService securityDeviceService;
    private static Map<String,String> deviceControlFailedMap = new ConcurrentHashMap<>();
    private static List<String> deviceControlSuccessList = new CopyOnWriteArrayList<>();
    @Autowired
    public void setSecurityComponentUtil(SecurityComponentUtil securityComponentUtil) {
        this.securityComponentUtil = securityComponentUtil;
    }
    @Autowired
    public void setSecurityDeviceDoorService(SecurityDeviceDoorService securityDeviceDoorService) {
        this.securityDeviceDoorService = securityDeviceDoorService;
    }
    @Autowired
    public void setSecurityComponentProperties(SecurityComponentProperties securityComponentProperties) {
        this.securityComponentProperties = securityComponentProperties;
    }
    @Autowired
    public void setSecurityDeviceService(SecurityDeviceService securityDeviceService) {
        this.securityDeviceService = securityDeviceService;
    }

    @Override
    public DeviceControlResDto securityDeviceControl(SecurityDeviceControlQueryDto queryDto) {

        if (StrUtil.isEmpty(queryDto.getSecurityDeviceType())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "安放设备类型不能为空");
        }
        if (!(queryDto.getAction().equals(CommonConstant.OPEN_ALL) || queryDto.getAction().equals(CommonConstant.CLOSE_ALL))) {
            // 不属于操作所有
            if (CollUtil.isEmpty(queryDto.getSecurityDevicePointIdList())) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "请选择需要操作的设备");
            }
        }

        deviceControlSuccessList.clear();
        deviceControlFailedMap.clear();
        String action;
        if (CommonConstant.OPEN_ALL.equals(queryDto.getAction())) {
            action = CommonConstant.OPEN;
        } else if (CommonConstant.CLOSE_ALL.equals(queryDto.getAction())) {
            action = CommonConstant.CLOSE;
        } else {
            action = queryDto.getAction();
        }
        int total;
        if (Objects.equals(queryDto.getSecurityDeviceType(), SecurityDeviceConstant.DOOR_TYPE)) {
            //操作门禁，操作的是门禁点
            List<SecurityDeviceDoor> securityDeviceDoors;
            if (queryDto.getAction().equals(CommonConstant.OPEN_ALL) || queryDto.getAction().equals(CommonConstant.CLOSE_ALL)) {
                securityDeviceDoors = securityDeviceDoorService.list();
            } else {
                securityDeviceDoors = securityDeviceDoorService.listByIds(queryDto.getSecurityDevicePointIdList());
            }
            securityDeviceDoors.forEach(door -> {
                SecurityDevice securityDevice = securityDeviceService.getById(door.getSecurityDeviceId());
                doorControl(securityDevice.getDeviceSourceIndex(), door.getDoorId(), action, securityDevice.getSecurityDeviceType(), door.getDoorName());
            });
            total = securityDeviceDoors.size();
        } else {
            //操作道闸，操作的是设备
            List<SecurityDevice> devices;
            if (queryDto.getAction().equals(CommonConstant.OPEN_ALL) || queryDto.getAction().equals(CommonConstant.CLOSE_ALL)) {
                devices = securityDeviceService.list(new LambdaQueryWrapper<SecurityDevice>().eq(SecurityDevice::getSecurityDeviceType, SecurityDeviceConstant.BARRIER_TYPE));
            } else {
                devices = securityDeviceService.listByIds(queryDto.getSecurityDevicePointIdList());
            }
            devices.forEach(device -> {
                doorControl(device.getDeviceSourceIndex(), null, action, device.getSecurityDeviceType(), device.getSecurityDeviceName());
            });
            total = devices.size();
        }
        return DeviceControlResDto.builder()
                .total(total)
                .fail(deviceControlFailedMap)
                .success(deviceControlSuccessList)
                .build();
    }

    /**
     * 设备控制请求
     */
    private void doorControl(String securityDeviceId, String doorId, String action, String securityDeviceType, String deviceName) {
        try{
            MqttComponentTemplateDto<MqttSecurityDeviceControlQueryDto> template = new MqttComponentTemplateDto<>(securityDeviceId);
            MqttSecurityDeviceControlQueryDto mqttSecurityDeviceControlQueryDto = new MqttSecurityDeviceControlQueryDto();
            mqttSecurityDeviceControlQueryDto.setAction(action);
            mqttSecurityDeviceControlQueryDto.setDeviceType(securityDeviceType);
            mqttSecurityDeviceControlQueryDto.setDoorId(doorId);
            template.setParams(mqttSecurityDeviceControlQueryDto);
            ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(
                    template,
                    SecurityTopicEnum.SECURITY_CONTROL.getTopic(),
                    CacheConstant.syncDeviceTopicKey,
                    template.getSerialId()
            );
            if (!Objects.equals(ResultCode.SUCCESS.getCode(), responseDto.getCode())) {
                String message = responseDto.getMessage();
                if (message.contains("device is not online")){
                    message = "设备不在线";
                }
                throw new ServiceException(ResultCode.OPERATION_FAILURE, message);
            }
            deviceControlSuccessList.add(deviceName);
        }catch (Exception e){
            String message = "";
            if (e instanceof ServiceException) {
                ServiceException e1 = (ServiceException) e;
                message = e1.getErrorMessage();
                log.error("安防设备控制异常，{}",message);
            }else {
                message = "安防设备控制异常";
                log.error("安防设备控制异常，{}",e.getMessage());
            }
            deviceControlFailedMap.put(deviceName, message);
        }
    }
}
