package com.teamway.security.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.security.entity.SecurityConfiguration.DeviceFunction;
import com.teamway.security.mapper.DeviceFunctionMapper;
import com.teamway.security.service.DeviceFunctionService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/11/9
 */
@Service
public class DeviceFunctionServiceImpl extends ServiceImpl<DeviceFunctionMapper, DeviceFunction> implements DeviceFunctionService {
}
