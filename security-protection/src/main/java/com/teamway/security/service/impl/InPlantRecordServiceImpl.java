package com.teamway.security.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.security.entity.SecurityConfiguration.Car;
import com.teamway.security.entity.SecurityConfiguration.Person;
import com.teamway.security.entity.SecurityConfiguration.SecurityDeviceRecord;
import com.teamway.security.entity.accessManagement.visitorReservationManage.access.InPlantRecord;
import com.teamway.security.mapper.InPlantRecordMapper;
import com.teamway.security.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/12/22
 */
@Slf4j
@Service
public class InPlantRecordServiceImpl extends ServiceImpl<InPlantRecordMapper, InPlantRecord> implements InPlantRecordService {

    private CarService carService;
    private PersonService personService;
    private SecurityDeviceService securityDeviceService;
    private SecurityDeviceRecordService securityDeviceRecordService;
    @Autowired
    public void setCarService(CarService carService) {
        this.carService = carService;
    }
    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }
    @Autowired
    public void setSecurityDeviceService(SecurityDeviceService securityDeviceService) {
        this.securityDeviceService = securityDeviceService;
    }
    @Autowired
    public void setSecurityDeviceRecordService(SecurityDeviceRecordService securityDeviceRecordService) {
        this.securityDeviceRecordService = securityDeviceRecordService;
    }

    @Override
    public void saveInPlantPersonRecord(SecurityDeviceRecord deviceRecord) {
        String personIndex = deviceRecord.getPersonIndex();
        if ("0".equals(deviceRecord.getOpenType()) || "4".equals(deviceRecord.getOpenType()) || StrUtil.isBlank(personIndex)){
            // 不处理不确定和远程开门的记录
            return ;
        }

        Person person = personService.lambdaQuery().eq(Person::getPersonSourceIndex, personIndex).one();
        if (ObjectUtil.isEmpty(person)){
            log.info("人员索引：{} 未找到对应的人员信息", personIndex);
            return ;
        }

        String personId = person.getId().toString();
        SecurityDeviceRecord lastDeviceRecord = securityDeviceRecordService.lambdaQuery()
                .eq(SecurityDeviceRecord::getPersonIndex, personIndex)
                .orderByDesc(SecurityDeviceRecord::getDate)
                .last("limit 1")
                .one();

        if (1 == deviceRecord.getState()){
            // 进场
            if (lastDeviceRecord == null || lastDeviceRecord.getState() == 2) {
                // 上一次记录是出场，新增一条进场记录
                InPlantRecord inPlantRecord = InPlantRecord.builder()
                        .objectId(personId)
                        .entryTime(deviceRecord.getDate())
                        .recordType("1")
                        .build();
                this.save(inPlantRecord);
            } else if (lastDeviceRecord.getState() == 1) {
                // 上一次记录是进场，更新进场时间
                InPlantRecord inPlantRecord = this.lambdaQuery()
                        .eq(InPlantRecord::getObjectId, personId)
                        .eq(InPlantRecord::getRecordType, "1")
                        .one();
                if (inPlantRecord == null) {
                    inPlantRecord = InPlantRecord.builder()
                            .objectId(personId)
                            .entryTime(deviceRecord.getDate())
                            .recordType("1")
                            .build();
                } else {
                    inPlantRecord.setEntryTime(deviceRecord.getDate());
                }
                this.saveOrUpdate(inPlantRecord);
            } else {
                log.info("人员索引：{} 在场记录为不需要加入在场人员记录表的记录，上一次的进入记录：{}", personIndex, JSON.toJSON(lastDeviceRecord));
            }
        } else if (2 == deviceRecord.getState()) {
            // 出场
            if (lastDeviceRecord != null && (lastDeviceRecord.getState() == 1 || lastDeviceRecord.getState() == 2)) {
                // 上一次记录是进场，则删除厂内人员记录
                this.lambdaUpdate()
                        .eq(InPlantRecord::getObjectId, personId)
                        .eq(InPlantRecord::getRecordType, "1")
                        .remove();
            }
        }
    }


    @Override
    public void saveInPlantCarRecord(SecurityDeviceRecord deviceRecord) {
        String plateNo = deviceRecord.getPlateNo();
        if (StrUtil.isBlank(plateNo)){
            // 不处理不确定和远程开门的记录
            return ;
        }
        Car car = carService.lambdaQuery().eq(Car::getCarNo, plateNo).one();
        if (ObjectUtil.isEmpty(car)){
            log.info("车牌号：{} 未找到对应的车辆信息", plateNo);
            return ;
        }
        SecurityDeviceRecord securityDeviceRecord = securityDeviceRecordService.lambdaQuery()
                .eq(SecurityDeviceRecord::getPlateNo, plateNo)
                .orderByDesc(SecurityDeviceRecord::getDate)
                .last("limit 1")
                .one();

        if (1 == deviceRecord.getState()){
            // 进场
            if (securityDeviceRecord == null || securityDeviceRecord.getState() == 2) {
                // 上一次记录是出场，新增一条进场记录
                InPlantRecord inPlantRecord = InPlantRecord.builder()
                        .objectId(plateNo)
                        .entryTime(deviceRecord.getDate())
                        .recordType("2")
                        .build();
                this.save(inPlantRecord);
            } else if (securityDeviceRecord.getState() == 1) {
                // 上一次记录是进场，更新进场时间
                InPlantRecord inPlantRecord = this.lambdaQuery()
                        .eq(InPlantRecord::getObjectId, plateNo)
                        .eq(InPlantRecord::getRecordType, "2")
                        .one();
                if (inPlantRecord == null) {
                    inPlantRecord = InPlantRecord.builder()
                            .objectId(plateNo)
                            .entryTime(deviceRecord.getDate())
                            .recordType("2")
                            .build();
                } else {
                    inPlantRecord.setEntryTime(deviceRecord.getDate());
                }
                this.saveOrUpdate(inPlantRecord);
            } else {
                log.info("车牌号：{} 在场记录为不需要加入在场车辆记录表的记录，上一次的进入记录：{}", plateNo, JSON.toJSON(securityDeviceRecord));
            }
        } else if (2 == deviceRecord.getState()) {
            // 出场
            if (securityDeviceRecord != null && (securityDeviceRecord.getState() == 1 || securityDeviceRecord.getState() == 2)) {
                // 上一次记录是进场，则删除厂内车辆记录
                this.lambdaUpdate()
                        .eq(InPlantRecord::getObjectId, plateNo)
                        .eq(InPlantRecord::getRecordType, "2")
                        .remove();
            }
        }
    }
}
