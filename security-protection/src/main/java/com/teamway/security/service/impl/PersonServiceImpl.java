package com.teamway.security.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.entity.CommonType;
import com.teamway.base.service.CommonTypeService;
import com.teamway.base.service.RegionService;
import com.teamway.personnel.dto.PageHmBasicInfoSyncDto;
import com.teamway.base.util.ValidateUtil;
import com.teamway.common.constant.AlarmCenterAlarmType;
import com.teamway.common.constant.CommonConstant;
import com.teamway.common.dto.excel.ExcelSelectorDTO;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.util.*;
import com.teamway.exception.ServiceException;
import com.teamway.security.common.constant.*;
import com.teamway.security.common.constant.cache.SecurityDeviceAuthCacheConstant;
import com.teamway.security.common.enums.PersonTypeEnum;
import com.teamway.security.common.enums.SecurityTopicEnum;
import com.teamway.security.common.properties.SecurityComponentProperties;
import com.teamway.security.common.transfer.SecurityConfiguration.PersonTransfer;
import com.teamway.security.common.transfer.SecurityConfiguration.SecurityDevicePersonTransfer;
import com.teamway.security.dto.ResponseDto;
import com.teamway.security.dto.accessManagement.person.PersonAlarmDto;
import com.teamway.security.dto.mqttdto.MqttPersonFaceAddQueryDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.SecurityDevice.MqttComponentTemplateDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.person.*;
import com.teamway.security.dto.securityConfiguration.card.CardReturnQueryDto;
import com.teamway.security.dto.securityConfiguration.person.*;
import com.teamway.security.entity.SecurityConfiguration.*;
import com.teamway.security.entity.accessManagement.visitorReservationManage.Reservation;
import com.teamway.personnel.service.SubcontractorManagementService;
import com.teamway.personnel.entity.SubcontractorManagement;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import java.io.InputStream;
import com.teamway.security.mapper.PersonMapper;
import com.teamway.security.service.*;
import com.teamway.security.util.SecurityComponentUtil;
import java.util.stream.Collectors;
import com.teamway.security.util.SecurityImageUtils;
import com.teamway.security.util.PersonSyncResultManager;
import com.teamway.security.vo.WorkGroupPathVO;
import com.teamway.security.vo.accessManagement.person.personAlarmVo;
import com.teamway.security.vo.securityConfiguration.person.PersonAuthedDeviceVo;
import com.teamway.security.vo.securityConfiguration.person.PersonListVo;
import com.teamway.security.vo.securityConfiguration.securityDevice.Sync4aPersonVo;
import com.teamway.security.dto.sync.PersonSyncBatchResultDto;
import com.teamway.security.dto.sync.PersonSyncFailDetail;
import com.teamway.security.dto.sync.SyncLogInfo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;



/**
 * @ClossName PersonServiceImpl
 * @Description Person服务实现类
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
@Service
@Slf4j
public class PersonServiceImpl extends ServiceImpl<PersonMapper, Person> implements PersonService {

    private PersonTransfer personTransfer;
    private SecurityDevicePersonTransfer securityDevicePersonTransfer;
    private CardService cardService;
    private SecurityDeviceService securityDeviceService;
    private SecurityDevicePersonService securityDevicePersonService;
    private SecurityComponentUtil securityComponentUtil;
    private SecurityDeviceDoorService securityDeviceDoorService;
    private PersonMapper personMapper;
    private SecurityPeripheralService securityPeripheralService;
    private SecurityComponentProperties securityComponentProperties;
    private WorkGroupService workGroupService;
    private RedisUtil redisUtil;
    private RegionService regionService;
    @Autowired
    private CommonTypeService commonTypeService;
    @Autowired
    @Lazy
    private ReservationService reservationService;

    @Autowired
    private SubcontractorManagementService subcontractorManagementService;

    private TransactionTemplate transactionTemplate;
    // 全局的ConcurrentHashMap用于保存错误信息
    private static Map<String, String> errorMap = new ConcurrentHashMap<>();
    private static Map<String, String> successMap = new ConcurrentHashMap<>();
    private static Map<String, String> delBlackErrorMap = new ConcurrentHashMap<>();
    private static Map<String, String> delBlackSuccessMap = new ConcurrentHashMap<>();
    private static Map<Long, AuthResultDto> batchAuthPersonIdResultMap = new HashMap<>();
    private static List<Long> batchAuthPersonIdErrorList = new CopyOnWriteArrayList<>();

    @Autowired
    public void setPersonTransfer(PersonTransfer personTransfer) {
        this.personTransfer = personTransfer;
    }

    @Autowired
    public void setSecurityDevicePersonTransfer(SecurityDevicePersonTransfer securityDevicePersonTransfer) {
        this.securityDevicePersonTransfer = securityDevicePersonTransfer;
    }

    @Autowired
    public void setCardService(CardService cardService) {
        this.cardService = cardService;
    }

    @Autowired
    @Lazy
    public void setSecurityDeviceService(SecurityDeviceService securityDeviceService) {
        this.securityDeviceService = securityDeviceService;
    }

    @Autowired
    public void setSecurityDevicePersonService(SecurityDevicePersonService securityDevicePersonService) {
        this.securityDevicePersonService = securityDevicePersonService;
    }

    @Autowired
    public void setSecurityComponentUtil(SecurityComponentUtil securityComponentUtil) {
        this.securityComponentUtil = securityComponentUtil;
    }

    @Autowired
    public void setSecurityDeviceDoorService(SecurityDeviceDoorService securityDeviceDoorService) {
        this.securityDeviceDoorService = securityDeviceDoorService;
    }

    @Autowired
    public void setPersonMapper(PersonMapper personMapper) {
        this.personMapper = personMapper;
    }

    @Autowired
    public void setSecurityPeripheralService(SecurityPeripheralService securityPeripheralService) {
        this.securityPeripheralService = securityPeripheralService;
    }

    @Autowired
    public void setSecurityComponentProperties(SecurityComponentProperties securityComponentProperties) {
        this.securityComponentProperties = securityComponentProperties;
    }

    @Autowired
    public void setWorkGroupService(WorkGroupService workGroupService) {
        this.workGroupService = workGroupService;
    }

    @Autowired
    public void setRedisUtil(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
    }

    @Autowired
    public void setRegionService(RegionService regionService) {
        this.regionService = regionService;
    }

    @Autowired
    public void setTransactionTemplate(TransactionTemplate transactionTemplate) {
        this.transactionTemplate = transactionTemplate;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPerson(PersonAddOrUpdateQueryDto dto) {
        checkPersonAddOrUpdateQueryDto(dto);
        if (StrUtil.isBlank(dto.getPersonUnit())) {
            dto.setPersonUnit(CommonConstant.TYPE_ZEOR);
        }
        String personUnit = dto.getPersonUnit();
        //名称映射
        nameMapping(dto);
        Person person = savePersonWithSecurityComponent(dto);
        // 入库保存
        person.setPersonUnit(personUnit);
        save(person);
        if (CollUtil.isNotEmpty(dto.getPersonCardList())) {
            dto.getPersonCardList().forEach(card -> card.setPersonId(person.getId()));
            // 绑定卡片
            cardService.saveBatch(dto.getPersonCardList());
        }
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addByApprovalBatch(List<PersonAddByApprovalDto> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return false;
        }
        
        List<PersonAddOrUpdateQueryDto> queryDtoList = dtoList.stream()
                .map(dto -> {
                    // 先序列化为JSON字符串再反序列化，实现对象转换
                    String jsonString = JSONUtil.toJsonStr(dto);
                    return JSONUtil.toBean(jsonString, PersonAddOrUpdateQueryDto.class);
                })
                .collect(Collectors.toList());

        // 批量处理每个人员信息
        for (PersonAddOrUpdateQueryDto dto : queryDtoList) {
            checkPersonAddOrUpdateQueryDto(dto);
            if (StrUtil.isBlank(dto.getPersonUnit())) {
                dto.setPersonUnit(CommonConstant.TYPE_ZEOR);
            }
            String personUnit = dto.getPersonUnit();
            //名称映射
            nameMapping(dto);
            Person person = savePersonWithSecurityComponent(dto);
            // 入库保存
            person.setPersonUnit(personUnit);
            save(person);
            if (CollUtil.isNotEmpty(dto.getPersonCardList())) {
                dto.getPersonCardList().forEach(card -> card.setPersonId(person.getId()));
                // 绑定卡片
                cardService.saveBatch(dto.getPersonCardList());
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PersonSyncBatchResultDto addByApprovalBatchWithResult(List<PersonAddByApprovalDto> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return PersonSyncResultManager.createEmptyResult();
        }
        
        List<PersonAddOrUpdateQueryDto> queryDtoList = dtoList.stream()
                .map(dto -> {
                    // 先序列化为JSON字符串再反序列化，实现对象转换
                    String jsonString = JSONUtil.toJsonStr(dto);
                    return JSONUtil.toBean(jsonString, PersonAddOrUpdateQueryDto.class);
                })
                .collect(Collectors.toList());

        List<PersonSyncFailDetail> failDetails = new ArrayList<>();
        List<String> successList = new ArrayList<>();

        // 批量处理每个人员信息
        for (PersonAddOrUpdateQueryDto dto : queryDtoList) {
            try {
                checkPersonAddOrUpdateQueryDto(dto);
                if (StrUtil.isBlank(dto.getPersonUnit())) {
                    dto.setPersonUnit(CommonConstant.TYPE_ZEOR);
                }
                String personUnit = dto.getPersonUnit();
                //名称映射
                nameMapping(dto);
                Person person = savePersonWithSecurityComponent(dto);
                // 入库保存
                person.setPersonUnit(personUnit);
                save(person);
                if (CollUtil.isNotEmpty(dto.getPersonCardList())) {
                    dto.getPersonCardList().forEach(card -> card.setPersonId(person.getId()));
                    // 绑定卡片
                    cardService.saveBatch(dto.getPersonCardList());
                }
                
                String personIdentifier = dto.getPersonName() + "-" + dto.getPersonCertificateNum();
                successList.add(personIdentifier);
                SyncLogInfo successLog = SyncLogInfo.createPersonOperation(personIdentifier, "人员添加成功", null);
                log.info("人员添加成功：{}", JSONUtil.toJsonStr(successLog));
                
            } catch (Exception e) {
                String errorMessage = e.getMessage();
                if (e instanceof ServiceException) {
                    errorMessage = ((ServiceException) e).getErrorMessage();
                }
                PersonSyncFailDetail failDetail = PersonSyncFailDetail.of(dto.getPersonName(), dto.getPersonCertificateNum(), dto.getPersonPhone(), errorMessage);
                failDetails.add(failDetail);
                
                String personIdentifier = dto.getPersonName() + "-" + dto.getPersonCertificateNum();
                SyncLogInfo errorLog = SyncLogInfo.createPersonOperation(personIdentifier, "人员添加失败", errorMessage);
                log.error("人员添加失败：{}", JSONUtil.toJsonStr(errorLog), e);
            }
        }

        return PersonSyncResultManager.buildDetailedResult(successList, failDetails, queryDtoList.size());
    }

    /**
     * 名称映射
     * @param dto
     */
    private void nameMapping(PersonAddOrUpdateQueryDto dto) {
        // 获取映射关系 (typeCode -> name)
        Map<String, String> typeNameMap = commonTypeService.list().stream()
                .filter(type -> CommonConstant.TYPE_FOUR.equals(type.getCategory()))  // 4人员单位
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));
        // 设置类型名称
        String typeName = typeNameMap.get(dto.getPersonUnit());
        if (StrUtil.isNotEmpty(typeName)) {
            dto.setPersonUnit(typeName);
        }
    }

    /**
     * 保存人员到数据库，同时添加到安防设备组件
     * @param dto
     * @return
     */
    private Person savePersonWithSecurityComponent(PersonAddOrUpdateQueryDto dto) {
        Person person = personTransfer.addDto2Entity(dto);
        List<String> cardNoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dto.getPersonCardList())) {
            cardNoList = dto.getPersonCardList().stream().map(Card::getCardNo).collect(Collectors.toList());
        }
        // 请求组件添加人员
        savePersonSyncDtoToSecurityComponent(person, cardNoList);
        return person;
    }

    /**
     * 请求组件添加人员
     * @param person
     */
    private void savePersonSyncDtoToSecurityComponent(Person person, List<String> cardNoList) {
        String deviceId = securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.DOOR_TYPE);
        MqttComponentTemplateDto<MqttPersonSyncQueryDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(deviceId);
        mqttComponentTemplateDto.setParams(buildPersonSyncParamDto(person, cardNoList));
        person.setPersonSourceIndex(sendPersonToComponent(mqttComponentTemplateDto));
    }


    private String sendPersonToComponent(MqttComponentTemplateDto<MqttPersonSyncQueryDto> mqttComponentTemplateDto) {
        ResponseDto resultModel = securityComponentUtil.sendMqttToSecurityComponent(mqttComponentTemplateDto, "guard/person/add/request", CacheConstant.syncPersonTopicKey, mqttComponentTemplateDto.getSerialId());
        if (resultModel.getCode() != 0) {
            String message = resultModel.getMessage();
            String errMsg = "";
            log.info("操作人员-请求安防设备组件-添加失败，返回信息：{}", message);
            if (message.contains("Unqiue Field[certificateNo] Already Exists")) {
                errMsg = "海康平台证件号已存在";
                throw new ServiceException(ResultCode.HAD_SAME_ID_CARD);
            }
            throw new ServiceException(ResultCode.OPERATION_FAILURE, StrUtil.isNotEmpty(errMsg) ? errMsg : "人员操作失败，请重试");
        }
        if (ObjectUtil.isEmpty(mqttComponentTemplateDto.getParams().getPersonId())) {
            return (String) resultModel.getData();
        }
        return "";
    }

    /**
     * 构建请求安防组件添加人员的参数
     * @param person
     * @return
     */
    private MqttPersonSyncQueryDto buildPersonSyncParamDto(Person person, List<String> cardNoList) {
        MqttPersonSyncQueryDto mqttPersonSyncQueryDto = MqttPersonSyncQueryDto.builder()
                .name(person.getPersonName())
                .sex(Integer.parseInt(person.getPersonSex()))
                .phone(person.getPersonPhone())
                .cardType(person.getPersonCertificateType())
                .idCard(person.getPersonType().equals(CommonConstant.VISITOR) ? IdCardUtils.generateRandomIdCard() : person.getPersonCertificateNum())
                .jobNo(person.getPersonJobNum())
                .company(person.getPersonUnit())
                .cardNoList(cardNoList)
                .build();
        if (StrUtil.isNotEmpty(person.getPersonFaceUrl())) {
            mqttPersonSyncQueryDto.setPhotoUrl(Collections.singletonList(ImageUtils.getOssVisitUrl(person.getPersonFaceUrl())));
        }
        if (ObjectUtil.isNotEmpty(person.getId())) {
            // 如果id不为空，说明是修改，需要传入id
            mqttPersonSyncQueryDto.setPersonId(person.getPersonSourceIndex());
        }
        return mqttPersonSyncQueryDto;
    }

    /**
     * 构造请求安防组件删除人员的参数
     * @param personSourceIndex
     */
    private MqttPersonDelQueryDto buildPersonDelParamDto(String personSourceIndex) {
        return MqttPersonDelQueryDto.builder()
                .personId(personSourceIndex)
                .build();
    }

    private void savePersonWithSecurityComponent(Person person) {
        MqttPersonSyncQueryDto mqttPersonDto = personTransfer.entity2MqttDto(person);
        mqttPersonDto.setCardType(person.getPersonCertificateType());
        mqttPersonDto.setIdCard(person.getPersonCertificateNum());
        if (StrUtil.isNotEmpty(person.getPersonFaceUrl())) {
            mqttPersonDto.setPhotoUrl(Collections.singletonList(person.getPersonFaceUrl()));
        }
        String serialId = SnowflakeUtils.getSnowflakeId().toString();
        MqttComponentTemplateDto<MqttPersonSyncQueryDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.DOOR_TYPE));
        mqttComponentTemplateDto.setParams(mqttPersonDto);
        mqttComponentTemplateDto.setSerialId(serialId);
        ResponseDto resultModel = securityComponentUtil.sendMqttToSecurityComponent(mqttComponentTemplateDto, "guard/person/add/request", CacheConstant.syncPersonTopicKey, serialId);
        if (resultModel.getCode() != 0) {
            log.info("添加人员-请求安防设备组件-添加失败，返回信息：{}", resultModel.getMessage());
            // 添加失败，将错误信息存入全局map
            String key = person.getPersonName() + "-" + person.getPersonCertificateNum();
            String errorMessage = resultModel.getMessage();
            errorMap.put(key, errorMessage);
            return;
        }
        person.setPersonSourceIndex(resultModel.getData().toString());
    }


    @Override
    public IPage<PersonListVo> listPerson(PersonListQueryDto dto) {
        if (StrUtil.isEmpty(dto.getBlackListType())) {
            dto.setBlackListType("0");
        }
        dto.setPersonType(CommonConstant.COMMON);
        IPage<PersonListVo> page = personMapper.selectPersonList(new Page<>(dto.getPageIndex(), dto.getPageSize()), dto);
        // 给page里的记录的每一个person设置NumberofCards属性，属性值为该person的卡片数量，这个卡片数量是查询这个person下绑定的卡片数量
        page.getRecords().forEach(person -> {
            List<Card> cardList = cardService.lambdaQuery().eq(Card::getPersonId, person.getId()).list();
            person.setPersonCardList(cardList);
            person.setNumberOfCards(String.valueOf(cardList.size()));
            person.setBiometricCharacteristics(BiometricCharacteristicsDto.builder().faceNum(0).fingerNum(0).build());
            if (StrUtil.isNotBlank(person.getPersonFaceUrl())) {
                person.getBiometricCharacteristics().setFaceNum(1);
            }
        });
        return page;
    }

    @Override
    public void updatePerson(PersonAddOrUpdateQueryDto dto) {
        checkPersonUpdateQueryDto(dto);
        // 更新卡片
        try {
            // 使用一次查询获取卡号并同时删除
            List<String> cardNos = cardService.lambdaQuery()
                    .select(Card::getCardNo)  // 只查询需要的字段
                    .eq(Card::getPersonId, dto.getId())
                    .list()
                    .stream()
                    .map(Card::getCardNo)
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(cardNos)) {
                // 海康平台同步解绑
                CardReturnQueryDto cardReturnQueryDto = new CardReturnQueryDto();
                cardReturnQueryDto.setCardList(cardNos.toArray(new String[0]));
                cardService.returnCard(cardReturnQueryDto);
                // 删除卡片
                cardService.lambdaUpdate()
                        .eq(Card::getPersonId, dto.getId())
                        .remove();
            }
        } catch (Exception e) {
            log.error("退卡操作失败 - personId: {}, error: {}", dto.getId(), e.getMessage(), e);
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "更新人员 删除卡片操作失败");
        }
        String personUnit = dto.getPersonUnit();
        //名称映射
        nameMapping(dto);
        //更新安防人员信息
        Person person = updatePersonWithSecurityComponent(dto);
        //入库保存
        person.setPersonUnit(personUnit);
        updateById(person);
        if (CollUtil.isNotEmpty(dto.getPersonCardList())) {
            // 再添加新的卡片
            dto.getPersonCardList().forEach(card -> card.setPersonId(person.getId()));
            cardService.saveBatch(dto.getPersonCardList());
        }
    }

    /**
     * 修改人员到数据库，同时修改到安防设备组件
     * @param dto
     * @return
     */
    private Person updatePersonWithSecurityComponent(PersonAddOrUpdateQueryDto dto) {
        Person person = personTransfer.addDto2Entity(dto);
        person.setId(dto.getId());
        List<String> cardNoList = dto.getPersonCardList().stream().map(Card::getCardNo).collect(Collectors.toList());
        updatePersonSyncDtoToSecurityComponent(person, cardNoList);
        return person;
    }

    private void updatePersonSyncDtoToSecurityComponent(Person person, List<String> cardNoList) {
        String deviceId = securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.DOOR_TYPE);
        MqttComponentTemplateDto<MqttPersonSyncQueryDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(deviceId);
        mqttComponentTemplateDto.setParams(buildPersonSyncParamDto(person, cardNoList));
        sendPersonToComponent(mqttComponentTemplateDto);
    }

    private void checkPersonUpdateQueryDto(PersonAddOrUpdateQueryDto dto) {
        if (ObjectUtil.isEmpty(dto.getId())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员id不能为空");
        }
        Person person = null;
        String personJobNum = dto.getPersonJobNum();
        Person personInDb = this.getById(dto.getId());
        if (ObjectUtil.isEmpty(personInDb)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员不存在");
        }
        dto.setPersonSourceIndex(personInDb.getPersonSourceIndex());
        if (StrUtil.isNotBlank(personJobNum)) {
            person = this.lambdaQuery().eq(Person::getPersonJobNum, personJobNum).ne(Person::getId, dto.getId()).one();
            if (ObjectUtil.isNotEmpty(person)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员工号已存在");
            }
        } else if (StrUtil.isNotBlank(dto.getPersonPhone()) && DockingConstant.DEFAULT_PHONE.equals(dto.getPersonPhone())) {
            person = this.lambdaQuery().eq(Person::getPersonPhone, dto.getPersonPhone()).ne(Person::getId, dto.getId()).one();
            if (ObjectUtil.isNotEmpty(person)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "手机号已存在");
            }
        }
        person = this.lambdaQuery().eq(Person::getPersonCertificateType, dto.getPersonCertificateType()).eq(Person::getPersonCertificateNum, dto.getPersonCertificateNum()).ne(Person::getId, dto.getId()).one();
        if (ObjectUtil.isNotEmpty(person)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "证件号已存在");
        }
        checkPersonCardList(dto.getPersonCardList(), dto.getId());
        checkPersonWorkGroup(dto.getPersonUnitId());
        checkImageUrlIsUseful(dto.getPersonFaceUrl());
    }

    @Override
    public void bindingCard(PersonBindingCardQueryDto dto) {
        if (!checkPersonBindingCardQueryDto(dto)) {
            return;
        }
        String personId = dto.getPersonId();
        Person person = getById(personId);
        if (ObjectUtil.isEmpty(person)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员不存在");
        }
        List<Card> cardList = new ArrayList<>();
        PersonBindingCardDto personBindingCardDto = dto.getCardList();
        if (ObjectUtil.isNotEmpty(personBindingCardDto.getId())) {
            // 如果id不为空，说明是修改，先删除原来的卡片
            CardReturnQueryDto cardReturnQueryDto = new CardReturnQueryDto();
            // 将卡片id去除，放到一个string[]数组中
            String[] cardIdList = {personBindingCardDto.getId().toString()};
            cardReturnQueryDto.setCardList(cardIdList);
            cardService.returnCard(cardReturnQueryDto);
        }
        Card card = Card.builder()
                .cardNo(personBindingCardDto.getCardNo())
                .cardState("1")
                .cardType(personBindingCardDto.getCardType())
                .personId(person.getId())
                .build();
        cardList.add(card);
        cardService.bindingCard(cardList, person.getPersonSourceIndex());
    }

    private boolean checkPersonBindingCardQueryDto(PersonBindingCardQueryDto dto) {
        PersonBindingCardDto personBindingCardDto = dto.getCardList();
        Long id = dto.getCardList().getId();
        Card oldCard = cardService.getById(id);
        if (ObjectUtil.isNotEmpty(id) && ObjectUtil.isEmpty(oldCard)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "卡片不存在");
        }
        if (ObjectUtil.isNotEmpty(oldCard) && oldCard.getCardNo().equals(personBindingCardDto.getCardNo()) && oldCard.getCardType().equals(personBindingCardDto.getCardType())) {
            return false;
        }
        cardService.lambdaQuery()
                .eq(Card::getCardNo, personBindingCardDto.getCardNo())
                .ne(ObjectUtil.isNotEmpty(personBindingCardDto.getId()), Card::getId, personBindingCardDto.getId())
                .oneOpt()
                .ifPresent(card -> {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "卡片" + personBindingCardDto.getCardNo() + "已存在");
                });
        return true;
    }

    @Override
    public Map<String, AuthResultDto> personAuthorization(PersonAuthorizationQueryDto dto) {
        // 校验参数
        Map<Long, Boolean> isChangedMap = checkPersonAuthorizationQueryDto(dto);

        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNamePrefix("personAuthorization-").build();
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100), threadFactory);

        batchAuthPersonIdResultMap.clear();
        for (Long personId : dto.getPersonIdList()) {
            if (!isChangedMap.get(personId)) {
                log.info("人员授权-人员信息未改变，不需要重新授权，人员id：{}", personId);
                continue;
            }
            threadPoolExecutor.execute(() -> {
                try {
                    log.info("人员授权-开始授权流程，人员id：{}", personId);
                    Person person = this.getById(personId);
                    //如果是访客 授权时间改为访客的访问开始时间和访问结束时间
                    if (person.getPersonType().equals(CommonConstant.VISITOR)){
                        Reservation reservation = reservationService.lambdaQuery().eq(Reservation::getVisitorCertification, person.getPersonCertificateNum()).orderByDesc(Reservation::getCreateTime).last("limit 1") .one();
                        if (ObjectUtil.isNotEmpty(reservation)){
                            dto.setStartTime(
                                    Optional.ofNullable(reservation.getReservationStartTime())
                                            .map(time -> time.format(DateUtils.DATETIME_FORMATTER))
                                            .orElse(null)
                            );
                            dto.setEndTime(
                                    Optional.ofNullable(reservation.getReservationEndTime())
                                            .map(time -> time.format(DateUtils.DATETIME_FORMATTER))
                                            .orElse(null)
                            );
                        }
                    }
                    // 将之前授权的设备取消授权
                    if ("2".equals(dto.getType())) {
                        cancelDevicePersonAuth(person);
                    }
                    // 构建参数
                    List<SecurityDevicePerson> securityDevicePersonList = new ArrayList<>();
                    buildRequestParams(dto, person, securityDevicePersonList);
                    // 授权 保存授权信息入库 向安防设备组件请求
                    devicePersonAuth(securityDevicePersonList, person);
                } catch (Exception e) {
                    batchAuthPersonIdErrorList.add(personId);
                    e.printStackTrace();
                    log.error("人员授权-授权失败，人员id：{}，错误信息：{}", personId, e.getMessage());
                    AuthResultDto authResultDto = batchAuthPersonIdResultMap.get(personId);
                    if (ObjectUtil.isEmpty(authResultDto)) {
                        authResultDto = new AuthResultDto();
                    }
                    if (e instanceof ServiceException) {
                        ServiceException serviceException = (ServiceException) e;
                        authResultDto.getFailed().add(serviceException.getErrorMessage());
                    } else {
                        authResultDto.getFailed().add(e.getMessage());
                    }
                    batchAuthPersonIdResultMap.put(personId, authResultDto);
                }
            });
            redisUtil.deleteObject(SecurityDeviceAuthCacheConstant.SECURITY_DEVICE_PERSON_CACHE + personId);
        }
        threadPoolExecutor.shutdown();
        try {
            threadPoolExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("线程池等待被中断：{}", e.getMessage());
        }
        Map<String, AuthResultDto> resultMap = new HashMap<>();
        if (CollUtil.isNotEmpty(batchAuthPersonIdResultMap)) {
            batchAuthPersonIdResultMap.forEach((personId, authResultDto) -> {
                Person person = this.getById(personId);
                resultMap.put(person.getPersonName() + "-" + personId, authResultDto);
            });
        }
        return resultMap;
    }

    /**
     * 开始调用安防组件授权
     * @param securityDevicePersonList
     * @param person
     */
    private void devicePersonAuth(List<SecurityDevicePerson> securityDevicePersonList, Person person) {
        for (SecurityDevicePerson securityDevicePerson : securityDevicePersonList) {
            MqttSecurityDevicePersonDto mqttSecurityDevicePersonDto = securityDevicePersonTransfer.entity2AuthDto(securityDevicePerson);
            // 需要用组件中的人员ID
            MqttComponentTemplateDto<MqttSecurityDevicePersonDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.DOOR_TYPE));
            mqttSecurityDevicePersonDto.setPersonId(person.getPersonSourceIndex());
            SecurityDeviceDoor byId = securityDeviceDoorService.getById(mqttSecurityDevicePersonDto.getDeviceId());
            mqttSecurityDevicePersonDto.setType("2");
            mqttSecurityDevicePersonDto.setDeviceId(byId.getDoorId());
            mqttComponentTemplateDto.setParams(mqttSecurityDevicePersonDto);
            ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(mqttComponentTemplateDto, "guard/person/auth/request", CacheConstant.syncPersonTopicKey, mqttComponentTemplateDto.getSerialId());
            SecurityDeviceDoor securityDeviceDoor = securityDeviceDoorService.getById(securityDevicePerson.getDeviceId());
            if (responseDto.getCode() != 0) {
                log.info("人员授权-请求安防设备组件-授权失败，返回信息：{}", responseDto.getMessage());
                AuthResultDto authResultDto = batchAuthPersonIdResultMap.get(person.getId());
                if (ObjectUtil.isEmpty(authResultDto)) {
                    authResultDto = new AuthResultDto();
                }
                authResultDto.getFailed().add(securityDeviceDoor.getDoorName() + ": " + responseDto.getMessage());
                batchAuthPersonIdResultMap.put(person.getId(), authResultDto);
                continue;
            }
            // 保存授权信息入库
            if (ObjectUtil.isNotEmpty(securityDevicePerson.getId())) {
                securityDevicePersonService.lambdaUpdate().set(SecurityDevicePerson::getStartTime, securityDevicePerson.getStartTime())
                        .set(SecurityDevicePerson::getEndTime, securityDevicePerson.getEndTime())
                        .eq(SecurityDevicePerson::getId, securityDevicePerson.getId())
                        .update();
            } else {
                securityDevicePersonService.save(securityDevicePerson);
            }
            AuthResultDto authResultDto = batchAuthPersonIdResultMap.get(person.getId());
            if (ObjectUtil.isEmpty(authResultDto)) {
                authResultDto = new AuthResultDto();
            }
            authResultDto.getSucceed().add(securityDeviceDoor.getDoorName());
            batchAuthPersonIdResultMap.put(person.getId(), authResultDto);
        }
    }

    /**
     * 构建请求参数
     * @param dto
     * @param person
     * @param securityDevicePersonList
     */
    private void buildRequestParams(PersonAuthorizationQueryDto dto, Person person, List<SecurityDevicePerson> securityDevicePersonList) {
        Long[] doorDeviceIdList = dto.getDoorDeviceIdList();
        for (Long doorId : doorDeviceIdList) {
            // 批量入库参数
            String startTime = null;
            String endTime = null;
            if (StrUtil.isNotBlank(dto.getStartTime()) && StrUtil.isNotBlank(dto.getEndTime())) {
                startTime = dto.getStartTime();
                endTime = dto.getEndTime();
            }
            SecurityDevicePerson securityDevicePerson = securityDevicePersonService.lambdaQuery().eq(SecurityDevicePerson::getPersonId, person.getId()).eq(SecurityDevicePerson::getDeviceId, doorId).one();
            if (ObjectUtil.isNotEmpty(securityDevicePerson)) {
                // 说明之前的取消授权失败了，但又对同一个安防设备进行了授权操作，这里给数据库做修改覆盖操作；
                securityDevicePerson.setStartTime(startTime);
                securityDevicePerson.setEndTime(endTime);
            } else {
                securityDevicePerson = SecurityDevicePerson.builder().personId(person.getId()).deviceId(doorId).startTime(startTime).endTime(endTime).build();
            }
            securityDevicePersonList.add(securityDevicePerson);
        }
    }

    /**
     * 取消设备人员授权
     * @param person
     */
    private void cancelDevicePersonAuth(Person person) {
        List<SecurityDevicePerson> oldSecurityDevice = securityDevicePersonService.lambdaQuery().eq(SecurityDevicePerson::getPersonId, person.getId()).list();
        List<String> deviceIdList = new ArrayList<>();
        List<Long> personIdList = new ArrayList<>();
        for (SecurityDevicePerson securityDevicePerson : oldSecurityDevice) {
            SecurityDeviceDoor securityDeviceDoor = securityDeviceDoorService.getById(securityDevicePerson.getDeviceId());
            SecurityDevice securityDevice = securityDeviceService.getById(securityDeviceDoor.getSecurityDeviceId());
            if (ObjectUtil.isEmpty(securityDeviceDoor) || ObjectUtil.isEmpty(securityDevice)) {
                log.info("设备门禁点不存在，设备门禁点id：{}，设备ID：{}", securityDevicePerson.getDeviceId(), securityDeviceDoor.getSecurityDeviceId());
                continue;
            }
            deviceIdList.add(securityDeviceDoor.getDoorId());
            personIdList.add(person.getId());
        }
        if (CollUtil.isNotEmpty(deviceIdList)) {
            // 取消授权
            MqttComponentTemplateDto<MqttSecurityDevicePersonCancelAuthDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.DOOR_TYPE));
            MqttSecurityDevicePersonCancelAuthDto mqttDto = MqttSecurityDevicePersonCancelAuthDto.builder()
                    .type("2")
                    .personId(person.getPersonSourceIndex())
                    .deviceIdList(deviceIdList)
                    .build();
            mqttComponentTemplateDto.setParams(mqttDto);
            ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(mqttComponentTemplateDto, "guard/person/cancelAuth/request", CacheConstant.syncPersonTopicKey, mqttComponentTemplateDto.getSerialId());
            if (responseDto.getCode() != 0) {
                log.info("取消授权-请求安防设备组件-取消授权失败，返回信息：{}", responseDto.getMessage());
            }
        }
        // 删除数据库中的授权信息
        if (CollUtil.isNotEmpty(personIdList)) {
            securityDevicePersonService.removeBatchByIds(personIdList);
        }
    }

    private void cancelDevicePersonAuth(Person person, SecurityDeviceDoor securityDeviceDoor) {
        SecurityDevicePerson securityDevicePerson = securityDevicePersonService.lambdaQuery()
                .eq(SecurityDevicePerson::getPersonId, person.getId())
                .eq(SecurityDevicePerson::getDeviceId, securityDeviceDoor.getId())
                .one();
        if (ObjectUtil.isEmpty(securityDevicePerson)) {
            successMap.put(securityDeviceDoor.getId().toString(), securityDeviceDoor.getDoorName());
            return;
        }
        SecurityDevice securityDevice = securityDeviceService.getById(securityDeviceDoor.getSecurityDeviceId());
        if (ObjectUtil.isEmpty(securityDevice)) {
            log.info("设备不存在，设备ID：{}", securityDeviceDoor.getSecurityDeviceId());
            errorMap.put(securityDeviceDoor.getDoorName(), "该门禁点不存在");
            return;
        }
        // 取消授权
        MqttComponentTemplateDto<MqttSecurityDevicePersonCancelAuthDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.DOOR_TYPE));
        MqttSecurityDevicePersonCancelAuthDto mqttDto = MqttSecurityDevicePersonCancelAuthDto.builder().personId(person.getPersonSourceIndex()).deviceIdList(Collections.singletonList(securityDeviceDoor.getDoorId())).type(IotComponentConstant.SECURITY_DEVICE_DOOR).build();
        mqttComponentTemplateDto.setParams(mqttDto);
        ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(mqttComponentTemplateDto, "guard/person/cancelAuth/request", CacheConstant.syncPersonTopicKey, mqttComponentTemplateDto.getSerialId());
        if (responseDto.getCode() != 0) {
            log.info("取消授权-请求安防设备组件-取消授权失败，返回信息：{}", responseDto.getMessage());
            errorMap.put(securityDeviceDoor.getDoorName(), responseDto.getMessage());
            return;
        }
        // 删除数据库中的授权信息
        if (securityDevicePersonService.removeById(securityDevicePerson.getId())) {
            log.debug("取消授权-清空人员：{} 授权纪录-取消授权成功", person.getPersonName());
            successMap.put(securityDeviceDoor.getId().toString(), securityDeviceDoor.getDoorName());
        } else {
            log.error("取消授权-清空人员：{} 授权纪录-取消授权失败，人员设备授权记录ID：{}", person.getPersonName(), securityDevicePerson.getId());
            errorMap.put(securityDeviceDoor.getDoorName(), "取消授权失败");
        }
    }

    private Map<Long, Boolean> checkPersonAuthorizationQueryDto(PersonAuthorizationQueryDto dto) {
        Long[] personIdList = dto.getPersonIdList();
        if (personIdList.length > 100) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "单次授权人员数最多100人");
        }
        for (Long personId : personIdList) {
            Person person = this.getById(personId);
            if (ObjectUtil.isEmpty(person)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员不存在");
            }
        }
        Long[] doorDeviceIdList = dto.getDoorDeviceIdList();
        for (Long doorId : doorDeviceIdList) {
            SecurityDeviceDoor securityDeviceDoor = securityDeviceDoorService.getById(doorId);
            if (ObjectUtil.isEmpty(securityDeviceDoor)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "设备不存在");
            }
        }
        // 校验时间
        if (StrUtil.isNotBlank(dto.getStartTime()) && StrUtil.isNotBlank(dto.getEndTime())) {
            if (DateUtil.parse(dto.getStartTime(), VariousConstant.DATE_FORMAT).getTime() > DateUtil.parse(dto.getEndTime(), VariousConstant.DATE_FORMAT).getTime()) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "开始时间不能大于结束时间");
            }
            // 时间间隔至少是两小时
            if (DateUtil.between(DateUtil.parse(dto.getStartTime(), VariousConstant.DATE_FORMAT), DateUtil.parse(dto.getEndTime(), VariousConstant.DATE_FORMAT), DateUnit.HOUR) < 2) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "时间间隔至少是两小时");
            }
        }
        Map<Long, Boolean> map = new HashMap<>();
        for (Long personId : personIdList) {
            // 校验参数是否改变
            List<SecurityDevicePerson> securityDevicePersonList = securityDevicePersonService.lambdaQuery().eq(SecurityDevicePerson::getPersonId, personId).list();
            if (securityDevicePersonList.size() != doorDeviceIdList.length) {
                map.put(personId, true);
                continue;
            }
            if (securityDevicePersonList.size() != 0) {
                List<Long> oldDeviceId = securityDevicePersonList.stream().map(SecurityDevicePerson::getDeviceId).collect(Collectors.toList());
                List<Long> newDeviceId = Arrays.asList(doorDeviceIdList);
                // 判断两个集合中的元素是否完全相同，如果不相同，返回true
                if (!new HashSet<>(oldDeviceId).containsAll(newDeviceId)) {
                    map.put(personId, true);
                    continue;
                }
            }
            // 判断时间是否改变
            for (SecurityDevicePerson securityDevicePerson : securityDevicePersonList) {
                if (!dto.getStartTime().equals(securityDevicePerson.getStartTime()) || !dto.getEndTime().equals(securityDevicePerson.getEndTime())) {
                    map.put(personId, true);
                    break;
                }
            }
            map.putIfAbsent(personId, false);
        }
        return map;
    }

    @Override
    public List<PersonAuthedDeviceVo> getAuthedByPersonId(Long personId) {
        Person person = this.getById(personId);
        if (ObjectUtil.isEmpty(person)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员不存在");
        }
        List<PersonAuthedDeviceVo> resultList = new ArrayList<>();
        resultList = personMapper.getAuthedByPersonId(personId);

        for (PersonAuthedDeviceVo personAuthedDeviceVo : resultList) {
            for (SecurityDeviceDoor securityDeviceDoor : personAuthedDeviceVo.getSecurityDeviceDoorList()) {
                securityDevicePersonService.lambdaQuery().eq(SecurityDevicePerson::getPersonId, personId).eq(SecurityDevicePerson::getDeviceId, securityDeviceDoor.getId()).oneOpt().ifPresent(securityDevicePerson -> {
                    personAuthedDeviceVo.setStartTime(securityDevicePerson.getStartTime());
                    personAuthedDeviceVo.setEndTime(securityDevicePerson.getEndTime());
                });
            }
        }

        return resultList;
    }

    @Override
    public void batchDelPerson(PersonBatchDelQueryDto dto) {
        // 删除人员信息
        // 清除权限
        // 退卡
        checkPersonBatchDelQueryDto(dto);
        transactionTemplate.execute(status -> {
            try {
                List<String> personIdList = Arrays.asList(dto.getPersonIdList());
                List<String> personIndexList = new ArrayList<>();
                for (String personId : personIdList) {
                    Person person = getById(personId);
                    personIndexList.add(person.getPersonSourceIndex());
                }
                if (!removeBatchByIds(personIdList)) {
                    log.info("删除人员失败，人员ID：{}", JSONUtil.toJsonStr(personIdList));
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员删除失败");
                }
                // 清除权限
                LambdaUpdateWrapper<SecurityDevicePerson> securityDevicePersonLambdaUpdateWrapper = new LambdaUpdateWrapper<SecurityDevicePerson>().in(SecurityDevicePerson::getPersonId, personIdList);
                securityDevicePersonService.remove(securityDevicePersonLambdaUpdateWrapper);
                // 清除卡片
                LambdaUpdateWrapper<Card> cardLambdaUpdateWrapper = new LambdaUpdateWrapper<Card>().in(Card::getPersonId, personIdList);
                cardService.remove(cardLambdaUpdateWrapper);
                for (String personIndex : personIndexList) {
                    // 调用安防组件删除人员
                    MqttPersonDelQueryDto mqttPersonDelQueryDto = buildPersonDelParamDto(personIndex);
                    MqttComponentTemplateDto<MqttPersonDelQueryDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.DOOR_TYPE));
                    mqttComponentTemplateDto.setParams(mqttPersonDelQueryDto);
                    ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(mqttComponentTemplateDto, "guard/person/delete/request", CacheConstant.syncPersonTopicKey, mqttComponentTemplateDto.getSerialId());
                    if (responseDto.getCode() != 0) {
                        log.info("删除人员-请求安防设备组件-删除失败，返回信息：{}", responseDto.getMessage());
                        if ("该用户不存在".equals(responseDto.getMessage())) {
                            log.debug("删除人员-请求安防设备组件-响应该人员不存在，则业务平台直接将人员删除");
                            continue;
                        }
                        throw new RuntimeException();
                    }
                }
                status.flush();
            } catch (Exception e) {
                //事务回滚
                status.setRollbackOnly();
                e.printStackTrace();
                log.info("人员删除过程异常，异常信息：{}", e.getMessage());
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员删除失败");
            }
            return null;
        });
    }

    private void checkPersonBatchDelQueryDto(PersonBatchDelQueryDto dto) {
        String[] personIdList = dto.getPersonIdList();
        for (String personId : personIdList) {
            if (ObjectUtil.isEmpty(this.getById(personId))) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员不存在");
            }
        }
    }


    private void checkPersonAddOrUpdateQueryDto(PersonAddOrUpdateQueryDto dto) {
        String personJobNum = dto.getPersonJobNum();
        List<Person> list = null;
        if (StrUtil.isNotBlank(personJobNum)) {
            list = this.lambdaQuery().eq(Person::getPersonJobNum, personJobNum).ne(ObjectUtil.isNotEmpty(dto.getId()), Person::getId, dto.getId()).list();
            if (ObjectUtil.isNotEmpty(list)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员工号已存在");
            }
        } else if (StrUtil.isNotBlank(dto.getPersonPhone()) && !DockingConstant.DEFAULT_PHONE.equals(dto.getPersonPhone())) {
            list = this.lambdaQuery().eq(Person::getPersonPhone, dto.getPersonPhone()).ne(ObjectUtil.isNotEmpty(dto.getId()), Person::getId, dto.getId()).list();
            if (CollUtil.isNotEmpty(list)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "手机号已存在");
            }
        }
        list = this.lambdaQuery().eq(Person::getPersonCertificateType, dto.getPersonCertificateType()).eq(Person::getPersonCertificateNum, dto.getPersonCertificateNum()).ne(ObjectUtil.isNotEmpty(dto.getId()), Person::getId, dto.getId()).list();

        if (ObjectUtil.isNotEmpty(list)) {
            for (Person person : list) {
                if(person.getPersonType().equals(CommonConstant.VISITOR)){
                    PersonBatchDelQueryDto personBatchDelQueryDto = new PersonBatchDelQueryDto();
                    personBatchDelQueryDto.setPersonIdList(new String[]{String.valueOf(person.getId())});
                    this.batchDelPerson(personBatchDelQueryDto);
                }
                if (person.getPersonType().equals(CommonConstant.COMMON)){
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "证件号已存在");
                }
            }
        }
        checkPersonCardList(dto.getPersonCardList(), dto.getId());
        checkPersonWorkGroup(dto.getPersonUnitId());
        checkImageUrlIsUseful(dto.getPersonFaceUrl());
    }

    private void checkPersonWorkGroup(String personUnitId) {
        if (StrUtil.isNotBlank(personUnitId)) {
            if (ObjectUtil.isEmpty(workGroupService.getById(personUnitId))) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "工作组不存在");
            }
        }
    }

    private void checkPersonCardList(List<Card> personCardList, Long personId) {
        if (CollUtil.isNotEmpty(personCardList)) {
            if (personCardList.size() > VariousConstant.PERSON_BINDING_CARD_NUM) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员最多绑定五张卡");
            }
            for (Card card : personCardList) {
                if (ObjectUtil.isNotEmpty(personId)) {
                    card.setPersonId(personId);
                }
                if (cardService.checkCardIsBinded(card)) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "卡片已被绑定");
                }
            }
        }
    }

    @Override
    public void checkImageUrlIsUseful(String personFaceUrl) {
        personFaceUrl = ImageUtils.getOssVisitUrl(personFaceUrl);
        if (StrUtil.isNotBlank(personFaceUrl)) {
            if (personFaceUrl.contains(" ")) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "人脸图片地址不能包含空格");
            }
            try {
                URL url = new URL(personFaceUrl);
                if (personFaceUrl.toLowerCase().contains("https")) {
                    SecurityImageUtils.ignoreSsl();
                }
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("HEAD");
                conn.setConnectTimeout(5 * 1000);
                int fileSize = conn.getContentLength();
                // 获取流的大小
                if (fileSize == -1) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "人脸图片无效");
                } else if (!(fileSize >= 10 * 1024 && fileSize <= 200 * 1024 && (personFaceUrl.toLowerCase().contains(".jpg") || personFaceUrl.toLowerCase().contains(".png")))) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "图片文件支持大小10kb~200kb，jpg格式图片");
                }
            } catch (Exception e) {
                log.info("人脸图片无效，图片地址：{}，错误信息：{}", personFaceUrl, e.getMessage());
                if (e instanceof ServiceException) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, ((ServiceException) e).getErrorMessage());
                }
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "人脸图片无效");
            }
        }
    }

    @Override
    public List<String> getPersonDepartmentList() {
        return this.lambdaQuery()
                .select(Person::getPersonDepartment)
                .ne(Person::getPersonDepartment, "")
                .isNotNull(Person::getPersonDepartment)
                .groupBy(Person::getPersonDepartment)
                .list()
                .stream()
                .map(Person::getPersonDepartment)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getPersonPostList() {
        return this.lambdaQuery()
                .select(Person::getPersonPost)
                .ne(Person::getPersonPost, "")
                .isNotNull(Person::getPersonPost)
                .groupBy(Person::getPersonPost)
                .list()
                .stream()
                .map(Person::getPersonPost)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Sync4aPersonVo editPersonAuthorization(EditPersonAuthorizationQueryDto dto) {
        checkAuthTime(dto.getStartTime(), dto.getEndTime());

        successMap.clear();
        errorMap.clear();
        batchAuthPersonIdResultMap.clear();
        List<String> successDeviceName = new ArrayList<>();
        // 校验人员是否存在
        Person person = this.getById(dto.getPersonId());
        if (ObjectUtil.isEmpty(person)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员不存在");
        }
        Map<Long, SecurityDevicePerson> stringSecurityDevicePersonMap = new HashMap<>();
        Map<Long, SecurityDeviceDoor> stringSecurityDeviceDoorMap = new HashMap<>();
        // 校验设备是否存在
        Long[] doorDeviceIdList = dto.getDoorDeviceIdList();
        for (Long doorDeviceId : doorDeviceIdList) {
            SecurityDeviceDoor securityDeviceDoor = securityDeviceDoorService.getById(doorDeviceId);
            if (ObjectUtil.isEmpty(securityDeviceDoor)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "门禁点不存在");
            }
            // 校验人员是否已经授权
            SecurityDevicePerson securityDevicePerson = securityDevicePersonService.lambdaQuery().eq(SecurityDevicePerson::getPersonId, dto.getPersonId())
                    .eq(SecurityDevicePerson::getDeviceId, doorDeviceId)
                    .one();
            if (ObjectUtil.isEmpty(securityDevicePerson)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员未授权该门禁");
            }
            String oldStartTime = null;
            String oldEndTime = null;
            if (ObjectUtil.isNotEmpty(securityDevicePerson.getStartTime())) {
                oldStartTime = securityDevicePerson.getStartTime();
            }
            if (ObjectUtil.isNotEmpty(securityDevicePerson.getEndTime())) {
                oldEndTime = securityDevicePerson.getEndTime();
            }
            String newStartTime = null;
            String newEndTime = null;
            if (StrUtil.isNotBlank(dto.getStartTime())) {
                newStartTime = dto.getStartTime();
            }
            if (StrUtil.isNotBlank(dto.getEndTime())) {
                newEndTime = dto.getEndTime();
            }
            if (!StrUtil.equals(oldStartTime, newStartTime) || !StrUtil.equals(oldEndTime, newEndTime)) {
                securityDevicePerson.setStartTime(newStartTime);
                securityDevicePerson.setEndTime(newEndTime);
                stringSecurityDevicePersonMap.put(doorDeviceId, securityDevicePerson);
            } else {
                successDeviceName.add(securityDeviceDoor.getDoorName());
            }
            stringSecurityDeviceDoorMap.put(doorDeviceId, securityDeviceDoor);
        }

        for (Long doorDeviceId : stringSecurityDevicePersonMap.keySet()) {
            SecurityDevicePerson securityDevicePerson = stringSecurityDevicePersonMap.get(doorDeviceId);
            List<SecurityDevicePerson> requestList = new ArrayList<>();
            requestList.add(securityDevicePerson);
            try {
                devicePersonAuth(requestList, person);
            } catch (Exception e) {
                e.printStackTrace();
                SecurityDeviceDoor securityDeviceDoor = stringSecurityDeviceDoorMap.get(doorDeviceId);
                if (e instanceof ServiceException) {
                    errorMap.put(securityDeviceDoor.getDoorName(), ((ServiceException) e).getErrorMessage());
                } else {
                    log.error("人员授权失败，错误信息：{}", e.getMessage());
                    errorMap.put(securityDeviceDoor.getDoorName(), "人员授权失败");
                }
            }
        }
        redisUtil.deleteObject(SecurityDeviceAuthCacheConstant.SECURITY_DEVICE_PERSON_CACHE + dto.getPersonId());

        for (Long personId : batchAuthPersonIdResultMap.keySet()) {
            AuthResultDto authResultDto = batchAuthPersonIdResultMap.get(personId);
            if (!authResultDto.getSucceed().isEmpty()) {
                successDeviceName.addAll(authResultDto.getSucceed());
            }
        }
        return Sync4aPersonVo.builder()
                .failedAddPersonList(errorMap)
                .addedPersonList(successDeviceName)
                .build();
    }

    @Override
    public Sync4aPersonVo deletePersonAuthorization(DeletePersonAuthorizationQueryDto dto) {
        errorMap.clear();
        successMap.clear();
        Map<Long, SecurityDeviceDoor> stringSecurityDeviceDoorMap = new HashMap<>();
        // 校验人员是否存在
        Person person = this.getById(dto.getPersonId());
        if (ObjectUtil.isEmpty(person)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员不存在");
        }
        // 校验设备是否存在
        Long[] doorDeviceIdList = dto.getDoorDeviceIdList();
        for (Long doorId : doorDeviceIdList) {
            SecurityDeviceDoor securityDeviceDoor = securityDeviceDoorService.getById(doorId);
            if (ObjectUtil.isEmpty(securityDeviceDoor)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "门禁点不存在");
            }
            stringSecurityDeviceDoorMap.put(doorId, securityDeviceDoor);
        }
        for (Long key : stringSecurityDeviceDoorMap.keySet()) {
            SecurityDeviceDoor securityDeviceDoor = stringSecurityDeviceDoorMap.get(key);
            cancelDevicePersonAuth(person, securityDeviceDoor);
        }
        redisUtil.deleteObject(SecurityDeviceAuthCacheConstant.SECURITY_DEVICE_PERSON_CACHE + dto.getPersonId());
        List<String> successDeviceName = new ArrayList<>();
        successMap.keySet().forEach(doorId -> {
            String doorName = successMap.get(doorId);
            successDeviceName.add(doorName);
        });
        return Sync4aPersonVo.builder()
                .failedAddPersonList(errorMap)
                .addedPersonList(successDeviceName)
                .build();
    }

    @Override
    public void deleteZombiePerson() {
        LambdaQueryWrapper<Person> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Person::getPersonType, PersonTypeEnum.ZOMBIE.getCode());
        List<Long> idList = personMapper.selectList(wrapper).stream().map(data -> data.getId()).collect(Collectors.toList());
        //删除相关数据
        personMapper.delete(wrapper);
        //删三方系统数据

    }

    @Override
    public IPage<personAlarmVo> selectAlertRecord(PersonAlarmDto personAlarmDto) {
        personAlarmDto.setAlarmType(
                Objects.requireNonNullElse(personAlarmDto.getAlarmType(), AlarmCenterAlarmType.SECURITY_ALARM)
        );
        return personMapper.selectAlertRecord(new Page<>(personAlarmDto.getPageIndex(), personAlarmDto.getPageSize()), personAlarmDto);
    }

    @Override
    public PersonAddPicResponseDto addPersonPic(PersonAddPicQueryDto dto) {
        Map<String, String> successMap = new HashMap<>();
        Map<String, String> errorMap = new HashMap<>();
        MultipartFile[] files = dto.getFiles();

        Map<String,MultipartFile> fileMap = new HashMap<>();
        for (MultipartFile file : files) {
            String fileName = file.getOriginalFilename();
            if (fileName.contains("/")){
                // 说明带着文件夹
                fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
            }
            fileMap.put(fileName.split("\\.")[0],file);
        }

        // 校验文件内容是否为空，并整理文件名列表
        fileMap.forEach((name, file) -> {
            if (file.isEmpty()) {
                errorMap.put(name, errorMap.getOrDefault(name, "") + "文件内容为空文件");
            }
        });

        List<Person> personList = this.lambdaQuery()
                .isNotNull(Person::getPersonCertificateNum).list();
        Map<String, Person> personMap = personList.stream()
                .collect(Collectors.toMap(Person::getPersonCertificateNum, person -> person, (a, b) -> a.getCreateTime().isBefore(b.getCreateTime()) ? b : a));

        fileMap.keySet().forEach(fileName -> {
            Person person = personMap.get(fileName);
            if (person == null) {
                errorMap.put(fileName, errorMap.getOrDefault(fileName, "") + (errorMap.containsKey(fileName) ? "，" : "") + "系统中不存在身份证号为：" + fileName + " 的人员");
            } else if (StrUtil.isNotEmpty(person.getPersonFaceUrl())) {
                errorMap.put(fileName, errorMap.getOrDefault(fileName, "") + (errorMap.containsKey(fileName) ? "，" : "") + "身份证号为：" + fileName + " 的人员已完成人脸图片上传，请勿重复上传");
            }
        });

        List<String> needChange2JpgFileNameList = new ArrayList<>();

        // 上传图片前校验文件格式和大小
        fileMap.forEach((fileName, file) -> {
            long fileSizeInKb = file.getSize() / 1024;

            // 校验文件格式
            if (!SecurityImageUtils.isJpgFile(file) && !SecurityImageUtils.isPngFile(file)) {
                errorMap.put(fileName, errorMap.getOrDefault(fileName, "") + (errorMap.containsKey(fileName) ? "，" : "") + "文件格式错误，请上传jpg或png格式的图片");
            } else if (fileSizeInKb < 10 || fileSizeInKb > 200) {
                // 校验文件大小范围是否为 10 KB 到 200 KB
                errorMap.put(fileName, errorMap.getOrDefault(fileName, "") + (errorMap.containsKey(fileName) ? "，" : "") + "文件大小不符合要求，请上传10KB-200KB的图片");
            } else {
                if (SecurityImageUtils.isPngFile(file)) {
                    needChange2JpgFileNameList.add(fileName);
                }
                try {
                    successMap.put(fileName, ImageUtils.uploadBytes(file.getBytes(), file.getOriginalFilename(), "security-user-config"));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        });

        // 从 successMap 中删除所有 errorMap 中包含的 key
        successMap.keySet().removeAll(errorMap.keySet());

        // 格式转换为 JPG
        needChange2JpgFileNameList.forEach(fileName -> {
            String str = successMap.get(fileName);
            if (StrUtil.isNotEmpty(str)) {
                String imageUrl = SecurityImageUtils.formatConvertByBase64(str, "jpg", "security-user-config");
                successMap.put(fileName, imageUrl);
            }
        });

        List<String> successList = new ArrayList<>();
        // 用于收集需要批量更新的Person对象
        List<Person> batchUpdateList = new ArrayList<>();

        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            successMap.forEach((personCertificateNum, imageUrl) -> executor.submit(() -> {
                Person person = personMap.get(personCertificateNum);
                String base64 = SecurityImageUtils.convertToBase64(fileMap.get(personCertificateNum));
                MqttComponentTemplateDto<MqttPersonFaceAddQueryDto> mqttDto = new MqttComponentTemplateDto<>(
                        securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.DOOR_TYPE));
                MqttPersonFaceAddQueryDto mqttFaceAddDto = MqttPersonFaceAddQueryDto.builder()
                        .faceData(base64).personId(person.getPersonSourceIndex()).build();
                mqttDto.setParams(mqttFaceAddDto);

                ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(
                        mqttDto, SecurityTopicEnum.PERSON_FACE_ADD.getTopic(), SecurityTopicEnum.PERSON_FACE_ADD.getRedisTypeKey(), mqttDto.getSerialId());

                if (!Objects.equals(ResultCode.SUCCESS.getCode(), responseDto.getCode())) {
                    errorMap.put(personCertificateNum, errorMap.getOrDefault(personCertificateNum, "") + (errorMap.containsKey(personCertificateNum) ? "，" : "") + "请求IOT服务添加人脸失败，服务返回信息：" + responseDto.getMessage());
                    log.info("上传失败，人员姓名：{}，返回信息：{}", person.getPersonName(), responseDto.getMessage());
                    return;
                }

                // 不再单独更新数据库，而是收集更新信息
                synchronized (batchUpdateList) {
                    Person updatePerson = new Person();
                    updatePerson.setId(person.getId());
                    updatePerson.setPersonFaceUrl(imageUrl);
                    batchUpdateList.add(updatePerson);
                    // 记录成功结果
                    successList.add(person.getPersonName() + "-" + personCertificateNum);
                }
            }));
        }

        // 如果有需要更新的记录，进行批量更新
        if (!batchUpdateList.isEmpty()) {
            boolean updateSuccess = this.updateBatchById(batchUpdateList);
            if (!updateSuccess) {
                log.error("批量更新人员人脸URL失败");
                // 处理批量更新失败的情况
                for (Person p : batchUpdateList) {
                    // 找出对应的personCertificateNum
                    for (Map.Entry<String, Person> entry : personMap.entrySet()) {
                        if (entry.getValue().getId().equals(p.getId())) {
                            String personCertificateNum = entry.getKey();
                            errorMap.put(personCertificateNum, errorMap.getOrDefault(personCertificateNum, "") +
                                    (errorMap.containsKey(personCertificateNum) ? "，" : "") +
                                    "更新本地数据库失败，请联系管理员排查原因");
                            // 从成功列表中移除
                            successList.remove(entry.getValue().getPersonName() + "-" + personCertificateNum);
                            break;
                        }
                    }
                }
            }
        }

        return PersonAddPicResponseDto.builder()
                .successResult(successList)
                .failResult(errorMap)
                .build();
    }

    private static void checkAuthTime(String startTime, String endTime) {
        if (StrUtil.isNotBlank(startTime) && StrUtil.isNotBlank(endTime)) {
            if (DateUtil.parse(startTime, VariousConstant.DATE_FORMAT).getTime() > DateUtil.parse(endTime, VariousConstant.DATE_FORMAT).getTime()) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "开始时间不能大于结束时间");
            }
            // 时间间隔至少是两小时
            if (DateUtil.between(DateUtil.parse(startTime, VariousConstant.DATE_FORMAT), DateUtil.parse(endTime, VariousConstant.DATE_FORMAT), DateUnit.HOUR) < 2) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "时间间隔至少是两小时");
            }
        }
    }

    @Override
    public void downloadPersonExcelTemplate(HttpServletResponse response) {
        InputStream inputStream = null;
        try {
            ClassPathResource classPathResource = new ClassPathResource("excelTemplate/人员导入模版.xlsx");
            inputStream = classPathResource.getInputStream();
            Workbook workbook = new XSSFWorkbook(inputStream);
            List<ExcelSelectorDTO> selectorDTOList = this.generateSelectors();
            ExcelUtils.defaultExport(workbook, selectorDTOList, "人员导入模版", response);
        } catch (Exception e) {
            throw new ServiceException(ResultCode.DOWNLOAD_FAIL, e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("关闭输入流异常", e);
            }
        }
    }

    @Override
    public void importPerson(MultipartFile file, HttpServletResponse response) {
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        params.setStartRows(0);
        params.setNeedVerify(false);
        // 创建自定义处理器实例
        ExcelImportResult<ExcelPersonDto> importExcelMore = null;
        try {
            importExcelMore = ExcelImportUtil.importExcelMore(file.getInputStream(), ExcelPersonDto.class, params);
            //数据类型配置映射
            typeCodeMapping(importExcelMore.getList());
        } catch (Exception e) {
            throw new ServiceException(ResultCode.IMPORT_FAIL, e);
        }
        //判断是否是用模板导入
        Sheet sheet = importExcelMore.getWorkbook().getSheetAt(0);
        String title = sheet.getRow(0).getCell(0).getStringCellValue();
        String excelTitle = "人员信息";
        if (!excelTitle.equals(title)) {
            throw new ServiceException(ResultCode.IMPORT_FORMAT_FAIL, "请用模板导入人员信息");
        }
        //获取违反注解校验的错误信息
        List<ExcelPersonDto> failList = importExcelMore.getFailList();
        //获取正常数据
        List<ExcelPersonDto> list = importExcelMore.getList();
        //整合一起
        list.addAll(failList);
        //校验参数合法
        Map<String, Object> resultMap = this.checkImportParam(list);
        List<Person> normalPersonList = (List<Person>) resultMap.get("normalPersonList");
        List<ExcelPersonDto> failPersonList = (List<ExcelPersonDto>) resultMap.get("failPersonList");

        batchSavePerson(normalPersonList, list, failPersonList, response);
        log.info("导入人员信息，正常数据：{}", JSONUtil.toJsonStr(normalPersonList));
    }

    /**
     * 类型名称映射为code
     * @param resultList
     */
    private void typeCodeMapping(List<ExcelPersonDto> resultList) {
        // 1. 工作组映射: fullPath -> id
        List<WorkGroupPathVO> workGroupsWithPath = workGroupService.listWithFullPath();

        // 主映射：完整路径 -> ID
        Map<String, String> workGroupFullPathMap = workGroupsWithPath.stream()
                .collect(Collectors.toMap(
                        WorkGroupPathVO::getFullPath,    // key是完整路径
                        group -> group.getId().toString(),  // value转为String
                        (v1, v2) -> v1  // 处理可能的重复key
                ));

        // 备用映射：简单名称 -> ID (可能有重名，但保留用于兼容)
        Map<String, String> workGroupSimpleNameMap = workGroupsWithPath.stream()
                .collect(Collectors.toMap(
                        WorkGroupPathVO::getName,    // key是简单名称
                        group -> group.getId().toString(),  // value转为String
                        (v1, v2) -> v1  // 处理可能的重复key，保留第一个
                ));

        // 2. 类型映射: name -> typeCode
        List<CommonType> types = commonTypeService.list();
        Map<String, String> unitMap = getTypeCodeMap(types, CommonConstant.TYPE_FOUR);
        Map<String, String> deptMap = getTypeCodeMap(types, CommonConstant.TYPE_TWO);
        Map<String, String> postMap = getTypeCodeMap(types, CommonConstant.TYPE_THREE);
        Map<String, String> jobMap = getTypeCodeMap(types, CommonConstant.TYPE_ONE);

        // 3. 批量更新
        resultList.forEach(data -> {
            // 工作组映射：先尝试完整路径匹配，如果找不到再尝试简单名称匹配
            String workGroupId = data.getPersonUnitId();
            String mappedId = workGroupFullPathMap.get(workGroupId);

            // 如果完整路径没找到，尝试简单名称
            if (mappedId == null) {
                mappedId = workGroupSimpleNameMap.get(workGroupId);
            }

            // 如果还是没找到，保持原值
            data.setPersonUnitId(mappedId != null ? mappedId : workGroupId);

            // 其他映射保持不变
            data.setPersonUnit(unitMap.getOrDefault(data.getPersonUnit(), data.getPersonUnit()));
            data.setPersonDepartment(deptMap.getOrDefault(data.getPersonDepartment(), data.getPersonDepartment()));
            data.setPersonPost(postMap.getOrDefault(data.getPersonPost(), data.getPersonPost()));
            data.setPersonJobType(jobMap.getOrDefault(data.getPersonJobType(), data.getPersonJobType()));
        });
    }

    private Map<String, String> getTypeCodeMap(List<CommonType> types, String category) {
        return types.stream()
                .filter(type -> category.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getName,     // key是String
                        CommonType::getTypeCode, // value是String
                        (v1, v2) -> v1          // 处理可能的重复key
                ));
    }

    /**
     * 类型code映射为名称
     * @param resultList 结果列表
     * @param <T> 数据类型，必须实现PersonMappable接口
     */
    private <T extends PersonMappable> void typeNameMapping(List<T> resultList) {
        if (CollUtil.isEmpty(resultList)) {
            return;
        }

        // 1. 工作组映射: id -> simpleName
        Map<String, String> workGroupMap = workGroupService.list().stream()
                .collect(Collectors.toMap(
                        group -> group.getId().toString(),
                        WorkGroup::getSimpleName,
                        (v1, v2) -> v1
                ));

        // 2. 类型映射: typeCode -> name，按category分组
        List<CommonType> types = commonTypeService.list();
        Map<String, String> unitMap = getTypeNameMap(types, CommonConstant.TYPE_FOUR);
        Map<String, String> deptMap = getTypeNameMap(types, CommonConstant.TYPE_TWO);
        Map<String, String> postMap = getTypeNameMap(types, CommonConstant.TYPE_THREE);
        Map<String, String> jobMap = getTypeNameMap(types, CommonConstant.TYPE_ONE);

        // 3. 批量更新
        resultList.forEach(data -> {
            data.setPersonUnitId(workGroupMap.getOrDefault(data.getPersonUnitId(), data.getPersonUnitId()));
            data.setPersonUnit(unitMap.getOrDefault(data.getPersonUnit(), data.getPersonUnit()));
            data.setPersonDepartment(deptMap.getOrDefault(data.getPersonDepartment(), data.getPersonDepartment()));
            data.setPersonPost(postMap.getOrDefault(data.getPersonPost(), data.getPersonPost()));
            data.setPersonJobType(jobMap.getOrDefault(data.getPersonJobType(), data.getPersonJobType()));
        });
    }

    private Map<String, String> getTypeNameMap(List<CommonType> types, String category) {
        return types.stream()
                .filter(type -> category.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));
    }

    /**
     * 批量保存人员信息(文件导入使用)
     * @param normalPersonList 正常的人员信息，保存用
     * @param list 所有的人员信息，包括正常的和错误的，返回给用户用
     * @param failPersonList 错误的人员信息，返回给用户用
     * @param response 响应
     */
    private void batchSavePerson(List<Person> normalPersonList, List<ExcelPersonDto> list, List<ExcelPersonDto> failPersonList, HttpServletResponse response) {
        errorMap.clear();
        if (CollUtil.isEmpty(normalPersonList) && CollUtil.isEmpty(failPersonList)) {
            return;
        }
        // 使用多线程的方式，批量保存人员信息
        // 创建自定义的 ThreadFactory
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNamePrefix("person-import-pool-%d").build();
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new LinkedBlockingDeque<>(), threadFactory);
        for (Person person : normalPersonList) {
            threadPoolExecutor.execute(() -> {
                try {
                    String personUnit = person.getPersonUnit();
                    //名称映射
                    nameMapping(person);
                    //更新安防人员信息
                    savePersonWithSecurityComponent(person);
                    person.setPersonUnit(personUnit);
                    //入库保存
                    save(person);
                } catch (Exception e) {
                    log.error("保存人员信息异常，异常信息：{}", e.getMessage());
                    // 添加失败，将错误信息存入全局map
                    String key = person.getPersonName() + "-" + person.getPersonCertificateNum();
                    String errorMessage = e.getMessage();
                    errorMap.put(key, errorMessage);
                }
            });
        }
        // 等待所有线程执行完毕
        threadPoolExecutor.shutdown();
        try {
            threadPoolExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
        } catch (InterruptedException e) {
            log.error("线程池等待被中断：{}", e.getMessage());
        }
        List<ExcelPersonDto> resultList = new ArrayList<>(failPersonList);
        // 检查全局map是否为空
        if (!errorMap.isEmpty()) {
            // 根据需要处理添加失败的情况，比如输出错误信息，进行日志记录等
            for (Map.Entry<String, String> entry : errorMap.entrySet()) {
                log.error("人员添加失败，key: {}, 错误信息: {}", entry.getKey(), entry.getValue());
                String[] keyStr = entry.getKey().split("-");
                String name = keyStr[0];
                String certificateNum = keyStr[1];
                for (ExcelPersonDto excelPersonDto : list) {
                    if (name.equals(excelPersonDto.getPersonName()) && certificateNum.equals(excelPersonDto.getPersonCertificateNum())) {
                        excelPersonDto.setErrorMsg(entry.getValue());
                        resultList.add(excelPersonDto);
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(resultList)) {
            //数据类型配置映射
            typeNameMapping(resultList);
            List<ExcelSelectorDTO> selectorDTOList = this.generateSelectors();
            try {
                ExcelUtils.exportExcel(resultList, null, "人员信息", "人员信息001", ExcelPersonDto.class, "人员错误信息", response);
            } catch (IOException e) {
                throw new ServiceException(ResultCode.EXPORT_FAIL, e);
            }
        }
    }

    /**
     * 名称映射
     * @param person
     */
    private void nameMapping(Person person) {
        // 获取映射关系 (typeCode -> name)
        Map<String, String> typeNameMap = commonTypeService.list().stream()
                .filter(type -> CommonConstant.TYPE_FOUR.equals(type.getCategory()))  // 4人员单位
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));
        // 设置类型名称
        String typeName = typeNameMap.get(person.getPersonUnit());
        if (StrUtil.isNotEmpty(typeName)) {
            person.setPersonUnit(typeName);
        }
    }

    private Map<String, Object> checkImportParam(List<ExcelPersonDto> excelPersonList) {
        Map<String, Object> resultMap = new HashMap<>();
        //正常数据
        List<Person> normalPersonList = new ArrayList<>();
        //错误数据
        List<ExcelPersonDto> failPersonList = new ArrayList<>();

        Map<String, Integer> map = new HashMap<>();

        for (ExcelPersonDto excelPersonDto : excelPersonList) {
            StringJoiner joiner = new StringJoiner(",");
            //手动校验注解的内容
            Optional.ofNullable(ValidateUtil.validateParams(excelPersonDto)).ifPresent(joiner::add);
            // 判断人员工号是否存在
            Person person = null;
            // 校验手机号是否存在
            if (StrUtil.isNotBlank(excelPersonDto.getPersonPhone()) && !DockingConstant.DEFAULT_PHONE.equals(excelPersonDto.getPersonPhone())) {
                List<Person> personList = this.lambdaQuery().eq(Person::getPersonPhone, excelPersonDto.getPersonPhone()).list();
                if (CollUtil.isNotEmpty(personList)) {
                    person = personList.get(0);
                    joiner.add("手机号已存在");
                }
                if (StrUtil.isNotEmpty(excelPersonDto.getPersonPhone())) {
                    if (map.containsKey("phone-" + excelPersonDto.getPersonPhone())) {
                        joiner.add("手机号重复");
                        Integer num = map.get("phone-" + excelPersonDto.getPersonPhone());
                        map.put("phone-" + excelPersonDto.getPersonPhone(), num + 1);
                    } else {
                        map.put("phone-" + excelPersonDto.getPersonPhone(), 1);
                    }
                }
            }
            // 校验证件号是否存在
            // 校验证件号及对应格式
            if (PersonTypeConstant.ID_CARD.equals(excelPersonDto.getPersonCertificateType())) {
                // 校验正则：/^[1-9][0-9]{5}(19[0-9]{2}|2[0-9]{3})(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[0-9]{3}[0-9xX]$/
                String personCertificateNum = excelPersonDto.getPersonCertificateNum();
                if (StrUtil.isNotEmpty(personCertificateNum) && !personCertificateNum.matches(PatternConstant.ID_CARD_CHECK)) {
                    joiner.add("身份证格式错误");
                }
            } else if (PersonTypeConstant.OTHER.equals(excelPersonDto.getPersonCertificateType())) {
                // 校验正则：
                String personCertificateNum = excelPersonDto.getPersonCertificateNum();
                if (StrUtil.isNotEmpty(personCertificateNum) && !personCertificateNum.matches(PatternConstant.OTHER_CARD)) {
                    joiner.add("证件格式错误");
                }
            }
            List<Person> personList = this.lambdaQuery().eq(Person::getPersonCertificateType, excelPersonDto.getPersonCertificateType()).eq(Person::getPersonCertificateNum, excelPersonDto.getPersonCertificateNum()).list();
            if (CollUtil.isNotEmpty(personList)) {
                person = personList.get(0);
                joiner.add("证件号已存在");
            }
            if (StrUtil.isNotEmpty(excelPersonDto.getPersonCertificateNum())) {
                if (map.containsKey("idNum-" + excelPersonDto.getPersonCertificateNum())) {
                    joiner.add("证件号重复");
                    Integer num = map.get("idNum-" + excelPersonDto.getPersonCertificateNum());
                    map.put("idNum-" + excelPersonDto.getPersonCertificateNum(), num + 1);
                } else {
                    map.put("idNum-" + excelPersonDto.getPersonCertificateNum(), 1);
                }
            }


            if (joiner.length() > 0) {
                String errorMsg = excelPersonDto.getErrorMsg();
                if (StrUtil.isNotBlank(errorMsg)) {
                    excelPersonDto.setErrorMsg(errorMsg + "," + joiner.toString());
                } else {
                    excelPersonDto.setErrorMsg(joiner.toString());
                }
            }
            //添加错误的数据
            if (StrUtil.isNotBlank(excelPersonDto.getErrorMsg())) {
                failPersonList.add(excelPersonDto);
            } else {
                //正常数据
                person = personTransfer.dto2entity(excelPersonDto);
                String personFaceUrlFile = excelPersonDto.getPersonFaceUrlFile();
                if (StrUtil.isNotBlank(personFaceUrlFile)) {
                    String personFaceUrl = SecurityImageUtils.uploadImage(personFaceUrlFile, "personinfo");
                    person.setPersonFaceUrl(personFaceUrl);
                }
                normalPersonList.add(person);
            }
        }
        resultMap.put("failPersonList", failPersonList);
        resultMap.put("normalPersonList", normalPersonList);
        return resultMap;
    }

    @Override
    public void exportPerson(PersonListQueryDto dto, HttpServletResponse response) {
        dto.setPageIndex(1);
        dto.setPageSize(10000);
        IPage<PersonListVo> personIPage = this.listPerson(dto);
        List<PersonListVo> records = personIPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            throw new ServiceException(ResultCode.NOT_NULL, "导出的数据");
        }
        List<ExcelPersonExportDto> personDtoList = new ArrayList<>();
        // 设置线程池大小
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        List<Future<ExcelPersonExportDto>> futures = new ArrayList<>();

        for (PersonListVo person : records) {
            Future<ExcelPersonExportDto> future = executorService.submit(() -> {
                String personFaceUrl = person.getPersonFaceUrl();
                byte[] file = null;
                if (StrUtil.isNotEmpty(personFaceUrl)) {
                    file = SecurityImageUtils.getImageStreamByUrl(personFaceUrl);
                }
                return ExcelPersonExportDto.builder()
                        .personName(person.getPersonName())
                        .personSex(person.getPersonSex())
                        .personJobNum(person.getPersonJobNum())
                        .personJobType(person.getPersonJobType())
                        .personType(person.getPersonType())
                        .personPhone(person.getPersonPhone())
                        .personUnitId(person.getPersonUnitId())
                        .personUnit(person.getPersonUnit())
                        .personCertificateType(person.getPersonCertificateType())
                        .personCertificateNum(person.getPersonCertificateNum())
                        .personFaceUrlFile(file)
                        .updateTime(person.getUpdateTime())
                        .personDepartment(person.getPersonDepartment())
                        .personPost(person.getPersonPost())
                        .build();
            });
            futures.add(future);
        }
        executorService.shutdown();
        for (Future<ExcelPersonExportDto> future : futures) {
            try {
                ExcelPersonExportDto excelPersonDto = future.get();
                personDtoList.add(excelPersonDto);
            } catch (Exception e) {
                // 处理异常
                log.error("导出人员信息异常，异常信息：{}", e.getMessage());
            }
        }

        personDtoList.sort(Comparator.comparing(ExcelPersonExportDto::getUpdateTime).reversed());
        ExportParams exportParams = new ExportParams("人员信息", "人员信息001", ExcelType.HSSF);
        try {
            typeNameMapping(personDtoList);
            ExcelUtils.exportExcel(personDtoList, ExcelPersonExportDto.class, "人员信息", exportParams, response);
        } catch (IOException e) {
            throw new ServiceException(ResultCode.EXPORT_FAIL, e);
        }
    }

    /**
     * 生成下拉框
     * @return
     */
    private List<ExcelSelectorDTO> generateSelectors() {
        List<ExcelSelectorDTO> selectorList = new ArrayList<>();
        List<CommonType> commonTypeList = commonTypeService.list();

        // 获取各类型数据
        Map<String, List<CommonType>> typeMap = commonTypeList.stream()
                .collect(Collectors.groupingBy(CommonType::getCategory));

        // 获取工作组数据，包含完整路径
        List<WorkGroupPathVO> workGroupWithPath = workGroupService.listWithFullPath();

        // 提取工作组的完整路径
        String[] workGroupPaths = workGroupWithPath.stream()
                .map(WorkGroupPathVO::getFullPath)
                .toArray(String[]::new);

        // 定义选择器配置
        Map<String, SelectorConfig> configMap = new HashMap<String, SelectorConfig>() {{
            put("sex", new SelectorConfig(0, 1, new String[]{"男", "女"}));
            put("jobType", new SelectorConfig(0, 2, typeMap.get(CommonConstant.TYPE_ONE)));
            put("workGroup", new SelectorConfig(0, 5, workGroupPaths)); // 直接使用路径数组
            put("unit", new SelectorConfig(0, 6, typeMap.get(CommonConstant.TYPE_FOUR)));
            put("post", new SelectorConfig(0, 7, typeMap.get(CommonConstant.TYPE_THREE)));
            put("department", new SelectorConfig(0, 8, typeMap.get(CommonConstant.TYPE_TWO)));
            put("certificateType", new SelectorConfig(0, 9, new String[]{"身份证", "其他"}));
        }};

        // 生成选择器
        configMap.forEach((key, config) ->
                selectorList.add(createSelector(config))
        );

        return selectorList;
    }

    private static class SelectorConfig {
        private final int sheetIndex;
        private final int col;
        private final String[] data;

        public SelectorConfig(int sheetIndex, int col, String[] data) {
            this.sheetIndex = sheetIndex;
            this.col = col;
            this.data = data;
        }

        public SelectorConfig(int sheetIndex, int col, List<?> dataList) {
            this.sheetIndex = sheetIndex;
            this.col = col;
            this.data = convertToStringArray(dataList);
        }

        private String[] convertToStringArray(List<?> list) {
            if (list == null) {
                return new String[0];
            }

            if (list.get(0) instanceof CommonType) {
                return list.stream()
                        .map(item -> ((CommonType) item).getName())
                        .toArray(String[]::new);
            } else if (list.get(0) instanceof WorkGroup) {
                return list.stream()
                        .map(item -> ((WorkGroup) item).getSimpleName())
                        .toArray(String[]::new);
            }
            return new String[0];
        }
    }

    private ExcelSelectorDTO createSelector(SelectorConfig config) {
        ExcelSelectorDTO selector = new ExcelSelectorDTO();
        selector.setSheetIndex(config.sheetIndex);
        selector.setLastRow(65535);
        selector.setFirstRow(2);
        selector.setFirstCol(config.col);
        selector.setLastCol(config.col);
        selector.setData(config.data);
        return selector;
    }

    @Override
    public String personFaceCollect() {
        List<SecurityPeripheral> list = securityPeripheralService.lambdaQuery().eq(SecurityPeripheral::getType, "1").list();
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "未配置人脸采集设备");
        }
        SecurityPeripheral securityPeripheral = list.get(0);
        MqttPersonFaceCollectQueryDto queryDto = MqttPersonFaceCollectQueryDto.builder().ip(securityPeripheral.getIp()).user(securityPeripheral.getUsername()).psw(securityPeripheral.getPassword()).build();
        String serialId = SnowflakeUtils.getSnowflakeId().toString();
        MqttComponentTemplateDto<MqttPersonFaceCollectQueryDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.DOOR_TYPE));
        mqttComponentTemplateDto.setParams(queryDto);
        mqttComponentTemplateDto.setSerialId(serialId);
        ResponseDto resultModel = securityComponentUtil.sendMqttToSecurityComponent(mqttComponentTemplateDto, SecurityTopicEnum.PERSON_FACE_COLLECT.getTopic(), CacheConstant.syncPersonTopicKey, serialId);
        if (resultModel.getCode() != 0) {
            log.info("人脸采集-请求安防设备组件-请求失败，返回信息：{}", resultModel.getMessage());
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "人脸采集失败,请重试");
        }
        String faceBase64 = (String) resultModel.getData();
        if (StrUtil.isEmpty(faceBase64)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "人脸采集失败,请重试");
        }
        String imageUrl = SecurityImageUtils.uploadBase64(faceBase64);
        if (StrUtil.isEmpty(imageUrl)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "人脸存储失败,请重试");
        }
        return imageUrl;
    }

    @Override
    public List<String> getPersonUnitList() {
        return this.lambdaQuery()
                .isNotNull(Person::getPersonUnit)
                .groupBy(Person::getPersonUnit)
                .select(Person::getPersonUnit)
                .list()
                .stream()
                .map(Person::getPersonUnit)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toList());
    }

    @Override
    public PersonOrCarBlackResDto addBlackList(PersonBlackListAddQueryDto queryDto) {
        // 校验人员是否存在
        List<String> ids = queryDto.getIds();
        List<Person> personList = checkPersonIsExist(queryDto.getIds());

        Map<String, Person> personMap = new HashMap<>();
        checkPersonInBlackListState(ids, personList, personMap, "1");

        List<SecurityDevice> securityDeviceList = securityDeviceService.lambdaQuery().eq(SecurityDevice::getSecurityDeviceType, "1").list();
        // 将人员添加到黑名单中
        List<String> success = new ArrayList<>();
        List<String> fail = new ArrayList<>();

        errorMap.clear();
        successMap.clear();
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNamePrefix("person-addBlackList-pool-%d").build();
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new LinkedBlockingDeque<>(), threadFactory);
        for (Person person : personList) {
            for (SecurityDevice securityDevice : securityDeviceList) {
                threadPoolExecutor.execute(() -> {
                    try {
                        addPersonBlackList(person, securityDevice);
                    } catch (Exception e) {
                        if (!(e instanceof ServiceException)) {
                            log.info("人员添加到黑名单-操作失败，设备名称：{}，返回信息：{}", securityDevice.getSecurityDeviceName(), e.getMessage());
                            errorMap.put(person.getId().toString(), person.getPersonName());
                        }
                    }
                });
            }
        }
        // 等待所有线程执行完毕
        threadPoolExecutor.shutdown();
        try {
            threadPoolExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
        } catch (InterruptedException e) {
            log.error("线程池等待被中断：{}", e.getMessage());
        }

        if (!errorMap.isEmpty()) {
            for (String key : errorMap.keySet()) {
                String personName = errorMap.get(key);
                fail.add(personName);
            }
        }
        if (!successMap.isEmpty()) {
            for (String key : successMap.keySet()) {
                Person person = personMap.get(key);
                person.setState("1");
                this.updateById(person);
                success.add(person.getPersonName());
            }
        }
        return PersonOrCarBlackResDto.builder()
                .fail(fail)
                .success(success)
                .total(ids.size())
                .build();
    }

    private void checkPersonInBlackListState(List<String> ids, List<Person> personList, Map<String, Person> personMap, String type) {
        // 校验人员是否已经在黑名单中，比对ids中的id和personList中的id，判断state字段是否为1，如果为1，则已经添加到人员黑名单中
        HashSet<String> idsSet = new HashSet<>(ids);
        for (Person person : personList) {
            if (idsSet.contains(person.getId().toString())) {
                String state = person.getState();
                if (type.equals(state)) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "1".equals(type) ? "人员已经在黑名单中" : "人员不在黑名单中");
                }
            }
            personMap.put(person.getId().toString(), person);
        }
    }

    private List<Person> checkPersonIsExist(List<String> ids) {
        List<Person> personList = this.lambdaQuery().in(Person::getId, ids).list();
        if (CollUtil.isEmpty(personList) || ids.size() != personList.size()) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "人员不存在");
        }
        return personList;
    }

    /**
     * 将人员添加到黑名单中
     * @param person
     * @param securityDevice
     */
    private void addPersonBlackList(Person person, SecurityDevice securityDevice) {
        log.info("人员添加到黑名单-开始，设备名称：{}，人员名称：{}", securityDevice.getSecurityDeviceName(), person.getPersonName());
        MqttPersonBlackListQueryDto mqttQueryDto = new MqttPersonBlackListQueryDto(person.getPersonSourceIndex());
        MqttComponentTemplateDto<MqttPersonBlackListQueryDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDevice.getDeviceSourceIndex());
        mqttComponentTemplateDto.setParams(mqttQueryDto);
        ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(mqttComponentTemplateDto, SecurityTopicEnum.PERSON_BLACKLIST_ADD.getTopic(), SecurityTopicEnum.PERSON_BLACKLIST_ADD.getRedisTypeKey(), mqttComponentTemplateDto.getSerialId());
        if (responseDto.getCode() != 0) {
            log.info("人员添加到黑名单-请求安防设备组件-请求失败，设备名称：{}，返回信息：{}", securityDevice.getSecurityDeviceName(), responseDto.getMessage());
            errorMap.put(person.getId().toString(), person.getPersonName());
        } else if (StrUtil.isEmpty(errorMap.get(person.getId().toString()))) {
            successMap.put(person.getId().toString(), person.getPersonName());
        }
    }

    @Override
    public List<PersonAuthedDeviceDto> getPersonAuthedDevice(Long personId) {
        List<PersonAuthedDeviceDto> personAuthedDevices = personMapper.getPersonAuthedDeviceWithDetails(personId);
        return personAuthedDevices;
    }

    @Override
    public PersonOrCarBlackResDto removeBlackList(PersonBlackListAddQueryDto queryDto) {
        // 校验人员是否存在
        List<String> ids = queryDto.getIds();
        List<Person> personList = checkPersonIsExist(queryDto.getIds());

        Map<String, Person> personMap = new HashMap<>();
        checkPersonInBlackListState(ids, personList, personMap, "0");

        List<SecurityDevice> securityDeviceList = securityDeviceService.lambdaQuery().eq(SecurityDevice::getSecurityDeviceType, "1").list();
        // 将人员添加到黑名单中
        List<String> success = new ArrayList<>();
        List<String> fail = new ArrayList<>();

        delBlackErrorMap.clear();
        delBlackSuccessMap.clear();

        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNamePrefix("person-addBlackList-pool-%d").build();
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new LinkedBlockingDeque<>(), threadFactory);
        for (Person person : personList) {
            for (SecurityDevice securityDevice : securityDeviceList) {
                threadPoolExecutor.execute(() -> {
                    try {
                        removePersonBlackList(person, securityDevice);
                    } catch (Exception e) {
                        if (!(e instanceof ServiceException)) {
                            log.info("人员移除黑名单-操作失败，设备名称：{}，返回信息：{}", securityDevice.getSecurityDeviceName(), e.getMessage());
                            errorMap.put(person.getId().toString(), person.getPersonName());
                        }
                    }
                });
            }
        }
        // 等待所有线程执行完毕
        threadPoolExecutor.shutdown();
        try {
            threadPoolExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
        } catch (InterruptedException e) {
            log.error("线程池等待被中断：{}", e.getMessage());
        }

        if (!delBlackErrorMap.isEmpty()) {
            for (String key : delBlackErrorMap.keySet()) {
                String personName = delBlackErrorMap.get(key);
                fail.add(personName);
            }
        }
        if (!delBlackSuccessMap.isEmpty()) {
            for (String key : delBlackSuccessMap.keySet()) {
                Person person = personMap.get(key);
                person.setState("0");
                this.updateById(person);
                success.add(person.getPersonName());
            }
        }
        return PersonOrCarBlackResDto.builder()
                .fail(fail)
                .success(success)
                .total(ids.size())
                .build();
    }

    private void removePersonBlackList(Person person, SecurityDevice securityDevice) {
        log.info("人员删除黑名单-开始，设备名称：{}，人员名称：{}", securityDevice.getSecurityDeviceName(), person.getPersonName());
        MqttPersonBlackListQueryDto mqttQueryDto = new MqttPersonBlackListQueryDto(person.getPersonSourceIndex());
        MqttComponentTemplateDto<MqttPersonBlackListQueryDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityDevice.getDeviceSourceIndex());
        mqttComponentTemplateDto.setParams(mqttQueryDto);
        ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(mqttComponentTemplateDto, SecurityTopicEnum.PERSON_BLACKLIST_DELETE.getTopic(), SecurityTopicEnum.PERSON_BLACKLIST_DELETE.getRedisTypeKey(), mqttComponentTemplateDto.getSerialId());
        if (responseDto.getCode() != 0) {
            log.info("人员删除黑名单-请求安防设备组件-请求失败，设备名称：{}，返回信息：{}", securityDevice.getSecurityDeviceName(), responseDto.getMessage());
            delBlackErrorMap.put(person.getId().toString(), person.getPersonName());
        } else if (StrUtil.isEmpty(delBlackErrorMap.get(person.getId().toString()))) {
            delBlackSuccessMap.put(person.getId().toString(), person.getPersonName());
        }
    }

    @Override
    public PersonSyncBatchResultDto syncPersonInfo(List<PageHmBasicInfoSyncDto> personList) {
        // 预先查询所有需要的数据，避免在循环中重复查询数据库
        
        // 1. 一次性查询所有CommonType数据，然后按category分组
        List<CommonType> allCommonTypes = commonTypeService.list();
        
        // 人员单位类型映射 (category = 4)
        Map<String, String> personUnitMap = allCommonTypes.stream()
                .filter(type -> CommonConstant.TYPE_FOUR.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getDescription,
                        CommonType::getTypeCode,
                        (v1, v2) -> v1
                ));
        
        // 工种类型映射 (category = 1)
        Map<String, String> jobTypeMap = allCommonTypes.stream()
                .filter(type -> CommonConstant.TYPE_ONE.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getName,
                        CommonType::getTypeCode,
                        (v1, v2) -> v1
                ));
        
        // 2. 查询所有工作组映射
        Map<String, String> workGroupMap = workGroupService.list().stream()
                .collect(Collectors.toMap(
                        WorkGroup::getName,
                        workGroup -> workGroup.getId().toString(),
                        (v1, v2) -> v1
                ));
        
        // 使用预查询的数据进行转换
        List<PersonAddByApprovalDto> dtoList = personList.stream()
                .map(vo -> convertToPersonAddByApprovalDto(vo, personUnitMap, jobTypeMap, workGroupMap))
                .collect(Collectors.toList());
        
        // 调用批量添加方法
        return addByApprovalBatchWithResult(dtoList);
    }

    /**
     * 将PageHmBasicInfoSyncDto转换为PersonAddByApprovalDto（优化版，使用预查询数据）
     * @param vo PageHmBasicInfoSyncDto对象
     * @param personUnitMap 人员单位映射
     * @param jobTypeMap 工种映射
     * @param workGroupMap 工作组映射
     * @return PersonAddByApprovalDto对象
     */
    private PersonAddByApprovalDto convertToPersonAddByApprovalDto(PageHmBasicInfoSyncDto vo, 
                                                                  Map<String, String> personUnitMap,
                                                                  Map<String, String> jobTypeMap,
                                                                  Map<String, String> workGroupMap) {
        // 获取总包单位信息
        String contractorName = vo.getHmJobInfoAddOrUpdateDto() != null ? vo.getHmJobInfoAddOrUpdateDto().getContractorName() : null;
        String subcontractUnit = vo.getHmJobInfoAddOrUpdateDto() != null ? vo.getHmJobInfoAddOrUpdateDto().getSubcontractUnit() : null;
        String subcontractUnitId = vo.getHmJobInfoAddOrUpdateDto() != null ? vo.getHmJobInfoAddOrUpdateDto().getId().toString() : null;
        
        // 从预查询的映射中获取数据，避免重复查询数据库
        String personUnitCode = contractorName != null ? personUnitMap.get(contractorName) : null;
        
        // 获取工种信息并转换为code
        String workerInfoName = vo.getHmJobInfoAddOrUpdateDto() != null ? vo.getHmJobInfoAddOrUpdateDto().getWorkerInfo() : null;
        String personJobTypeCode = workerInfoName != null ? jobTypeMap.get(workerInfoName) : null;
        
        // 获取工作组ID
        String personUnitId = contractorName != null ? workGroupMap.get(contractorName) : null;

        return PersonAddByApprovalDto.builder()
                .id(vo.getId())
                .personCertificateNum(vo.getIdCardNumber())
                .personCertificateType(CommonConstant.ID_CARD) // 身份证类型
                .personName(vo.getName())
                .personPhone(vo.getMobilePhoneNumber())
                .personSex(convertGender(vo.getGender()))
                .personType(CommonConstant.COMMON) // 普通人员
                .personUnit(personUnitCode)
                .personUnitId(personUnitId)
                .personPost(vo.getHmJobInfoAddOrUpdateDto() != null ? vo.getHmJobInfoAddOrUpdateDto().getManageInfo() : null)
                .personDepartment(vo.getHmJobInfoAddOrUpdateDto() != null ? vo.getHmJobInfoAddOrUpdateDto().getProjectName() : null)
                .personBirthDate(vo.getBirthDate() != null ? vo.getBirthDate().toString() : null)
                .workerType(vo.getHmJobInfoAddOrUpdateDto() != null ? vo.getHmJobInfoAddOrUpdateDto().getWorkerType() : null)
                .subcontractUnit(subcontractUnit)
                .subcontractUnitId(subcontractUnitId)
                .personAddress(vo.getHomeAddress())
                .personJobType(personJobTypeCode)
                .isSpecialWorker(vo.getIsSpecialJob())
                .personFaceUrl(vo.getPersonPhoto())
                .build();
    }

    /**
     * 转换性别格式
     * @param gender 原始性别格式
     * @return 转换后的性别格式
     */
    private String convertGender(String gender) {
        if ("1".equals(gender)) {
            return "1"; // 男
        } else if ("2".equals(gender)) {
            return "2"; // 女
        } else {
            return "1"; // 未知默认为男
        }
    }

    @Override
    public void downloadPersonExcelTemplateEnhanced(HttpServletResponse response) {
        InputStream inputStream = null;
        try {
            ClassPathResource classPathResource = new ClassPathResource("excelTemplate/人员导入模版.xlsx");
            inputStream = classPathResource.getInputStream();
            Workbook workbook = new XSSFWorkbook(inputStream);
            List<ExcelSelectorDTO> selectorDTOList = this.generateSelectorsEnhanced();
            ExcelUtils.defaultExport(workbook, selectorDTOList, "人员导入增强模版", response);
        } catch (Exception e) {
            throw new ServiceException(ResultCode.DOWNLOAD_FAIL, e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("关闭输入流异常", e);
            }
        }
    }

    @Override
    public void importPersonEnhanced(MultipartFile file, HttpServletResponse response) {
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        params.setStartRows(0);
        params.setNeedVerify(false);
        
        ExcelImportResult<ExcelPersonEnhancedDto> importExcelMore = null;
        try {
            importExcelMore = ExcelImportUtil.importExcelMore(file.getInputStream(), ExcelPersonEnhancedDto.class, params);
            typeCodeMappingEnhanced(importExcelMore.getList());
        } catch (Exception e) {
            throw new ServiceException(ResultCode.IMPORT_FAIL, e);
        }
        
        Sheet sheet = importExcelMore.getWorkbook().getSheetAt(0);
        String title = sheet.getRow(0).getCell(0).getStringCellValue();
        String excelTitle = "人员信息";
        if (!excelTitle.equals(title)) {
            throw new ServiceException(ResultCode.IMPORT_FORMAT_FAIL, "请用模板导入人员信息");
        }
        
        List<ExcelPersonEnhancedDto> failList = importExcelMore.getFailList();
        List<ExcelPersonEnhancedDto> list = importExcelMore.getList();
        list.addAll(failList);
        
        Map<String, Object> resultMap = this.checkImportParamEnhanced(list);
        List<Person> normalPersonList = (List<Person>) resultMap.get("normalPersonList");
        List<ExcelPersonEnhancedDto> failPersonList = (List<ExcelPersonEnhancedDto>) resultMap.get("failPersonList");

        batchSavePersonEnhanced(normalPersonList, list, failPersonList, response);
        log.info("导入人员信息增强版，正常数据：{}", JSONUtil.toJsonStr(normalPersonList));
    }

    /**
     * 增强版类型名称映射为code
     */
    private void typeCodeMappingEnhanced(List<ExcelPersonEnhancedDto> resultList) {
        List<WorkGroupPathVO> workGroupsWithPath = workGroupService.listWithFullPath();
        
        Map<String, String> workGroupFullPathMap = workGroupsWithPath.stream()
                .collect(Collectors.toMap(
                        WorkGroupPathVO::getFullPath,
                        group -> group.getId().toString(),
                        (v1, v2) -> v1
                ));

        Map<String, String> workGroupSimpleNameMap = workGroupsWithPath.stream()
                .collect(Collectors.toMap(
                        WorkGroupPathVO::getName,
                        group -> group.getId().toString(),
                        (v1, v2) -> v1
                ));

        List<CommonType> types = commonTypeService.list();
        Map<String, String> unitMap = getTypeCodeMap(types, CommonConstant.TYPE_FOUR);
        Map<String, String> deptMap = getTypeCodeMap(types, CommonConstant.TYPE_TWO);
        Map<String, String> postMap = getTypeCodeMap(types, CommonConstant.TYPE_THREE);
        Map<String, String> jobMap = getTypeCodeMap(types, CommonConstant.TYPE_ONE);
        
        Map<String, String> workerTypeMap = getWorkerTypeMap();
        Map<String, String> specialWorkerMap = getSpecialWorkerMap();
        Map<String, String> subcontractorMap = subcontractorManagementService.getSubcontractorNameToIdMap();

        resultList.forEach(data -> {
            String workGroupId = data.getPersonUnitId();
            String mappedId = workGroupFullPathMap.get(workGroupId);
            if (mappedId == null) {
                mappedId = workGroupSimpleNameMap.get(workGroupId);
            }
            data.setPersonUnitId(mappedId != null ? mappedId : workGroupId);

            data.setPersonUnit(unitMap.getOrDefault(data.getPersonUnit(), data.getPersonUnit()));
            data.setPersonDepartment(deptMap.getOrDefault(data.getPersonDepartment(), data.getPersonDepartment()));
            data.setPersonPost(postMap.getOrDefault(data.getPersonPost(), data.getPersonPost()));
            data.setPersonJobType(jobMap.getOrDefault(data.getPersonJobType(), data.getPersonJobType()));
            
            data.setWorkerType(workerTypeMap.getOrDefault(data.getWorkerType(), data.getWorkerType()));
            data.setIsSpecialWorker(specialWorkerMap.getOrDefault(data.getIsSpecialWorker(), data.getIsSpecialWorker()));
            
            if (StrUtil.isNotBlank(data.getSubcontractUnit())) {
                String subcontractorId = subcontractorMap.get(data.getSubcontractUnit());
                if (StrUtil.isNotBlank(subcontractorId)) {
                    data.setSubcontractUnit(data.getSubcontractUnit() + ":" + subcontractorId);
                }
            }
        });
    }

    /**
     * 获取工人类型映射
     */
    private Map<String, String> getWorkerTypeMap() {
        Map<String, String> workerTypeMap = new HashMap<>();
        workerTypeMap.put("管理人员", "1");
        workerTypeMap.put("安全管理人员", "2");
        workerTypeMap.put("工人", "3");
        return workerTypeMap;
    }

    /**
     * 获取特殊工种映射
     */
    private Map<String, String> getSpecialWorkerMap() {
        Map<String, String> specialWorkerMap = new HashMap<>();
        specialWorkerMap.put("是", "1");
        specialWorkerMap.put("否", "0");
        return specialWorkerMap;
    }

    /**
     * 增强版参数校验
     */
    private Map<String, Object> checkImportParamEnhanced(List<ExcelPersonEnhancedDto> list) {
        List<Person> normalPersonList = new ArrayList<>();
        List<ExcelPersonEnhancedDto> failPersonList = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();

        for (ExcelPersonEnhancedDto excelPersonDto : list) {
            try {
                Person person = convertToPersonEnhanced(excelPersonDto);
                if (person != null) {
                    normalPersonList.add(person);
                } else {
                    failPersonList.add(excelPersonDto);
                }
            } catch (Exception e) {
                excelPersonDto.setErrorMsg("数据转换失败：" + e.getMessage());
                failPersonList.add(excelPersonDto);
                log.error("人员数据转换失败：{}", JSONUtil.toJsonStr(excelPersonDto), e);
            }
        }

        resultMap.put("normalPersonList", normalPersonList);
        resultMap.put("failPersonList", failPersonList);
        return resultMap;
    }

    /**
     * 将ExcelPersonEnhancedDto转换为Person实体
     */
    private Person convertToPersonEnhanced(ExcelPersonEnhancedDto dto) {
        if (dto == null) {
            return null;
        }

        Person person = new Person();
        person.setPersonName(dto.getPersonName());
        person.setPersonSex(dto.getPersonSex());
        person.setPersonPhone(dto.getPersonPhone());
        person.setPersonJobNum(dto.getPersonJobNum());
        person.setPersonCertificateType(dto.getPersonCertificateType());
        person.setPersonCertificateNum(dto.getPersonCertificateNum());
        person.setPersonUnit(dto.getPersonUnit());
        person.setPersonDepartment(dto.getPersonDepartment());
        person.setPersonUnitId(dto.getPersonUnitId());
        person.setPersonJobType(dto.getPersonJobType());
        person.setWorkerType(dto.getWorkerType());
        person.setIsSpecialWorker(dto.getIsSpecialWorker());
        
        String workerTypeCode = dto.getWorkerType();
        if ("1".equals(workerTypeCode) || "2".equals(workerTypeCode)) {
            person.setPersonPost(dto.getPersonPost());
        } else {
            person.setPersonPost(null);
        }
        
        if ("3".equals(workerTypeCode) && StrUtil.isNotBlank(dto.getPersonJobType())) {
            person.setPersonJobType(dto.getPersonJobType());
        } else if (!"3".equals(workerTypeCode)) {
            person.setPersonJobType(null);
        }
        
        if (StrUtil.isNotBlank(dto.getSubcontractUnit()) && dto.getSubcontractUnit().contains(":")) {
            String[] parts = dto.getSubcontractUnit().split(":", 2);
            person.setSubcontractUnit(parts[0]);
            person.setSubcontractUnitId(parts[1]);
        }
        
        person.setPersonType(CommonConstant.COMMON);
        person.setId(SnowflakeUtils.getSnowflakeId());
        person.setState("0");

        return person;
    }

    /**
     * 批量保存人员增强版
     */
    private void batchSavePersonEnhanced(List<Person> normalPersonList, List<ExcelPersonEnhancedDto> allList, 
                                       List<ExcelPersonEnhancedDto> failPersonList, HttpServletResponse response) {
        if (CollUtil.isNotEmpty(normalPersonList)) {
            try {
                this.saveBatch(normalPersonList);
                log.info("批量保存人员成功，数量：{}", normalPersonList.size());
            } catch (Exception e) {
                log.error("批量保存人员失败", e);
                throw new ServiceException(ResultCode.IMPORT_FAIL, "批量保存人员失败：" + e.getMessage());
            }
        }

        if (CollUtil.isNotEmpty(failPersonList)) {
            try {
                ExportParams exportParams = new ExportParams("人员导入失败数据", "人员导入失败数据", ExcelType.HSSF);
                ExcelUtils.exportExcel(failPersonList, ExcelPersonEnhancedDto.class, "人员导入失败数据", exportParams, response);
            } catch (Exception e) {
                log.error("导出失败数据异常", e);
            }
        }
    }

    /**
     * 生成增强版选择器
     */
    private List<ExcelSelectorDTO> generateSelectorsEnhanced() {
        List<ExcelSelectorDTO> selectorList = new ArrayList<>();
        List<CommonType> commonTypeList = commonTypeService.list();

        Map<String, List<CommonType>> typeMap = commonTypeList.stream()
                .collect(Collectors.groupingBy(CommonType::getCategory));

        List<WorkGroupPathVO> workGroupWithPath = workGroupService.listWithFullPath();

        String[] workGroupPaths = workGroupWithPath.stream()
                .map(WorkGroupPathVO::getFullPath)
                .toArray(String[]::new);

        List<SubcontractorManagement> subcontractors = subcontractorManagementService.getActiveSubcontractors();
        String[] subcontractorNames = subcontractors.stream()
                .map(SubcontractorManagement::getSubcontractorName)
                .toArray(String[]::new);

        Map<String, SelectorConfig> configMap = new HashMap<String, SelectorConfig>() {{
            put("sex", new SelectorConfig(0, 1, new String[]{"男", "女"}));
            put("jobType", new SelectorConfig(0, 2, typeMap.get(CommonConstant.TYPE_ONE)));
            put("workGroup", new SelectorConfig(0, 5, workGroupPaths));
            put("unit", new SelectorConfig(0, 6, typeMap.get(CommonConstant.TYPE_FOUR)));
            put("post", new SelectorConfig(0, 7, typeMap.get(CommonConstant.TYPE_THREE)));
            put("department", new SelectorConfig(0, 8, typeMap.get(CommonConstant.TYPE_TWO)));
            put("certificateType", new SelectorConfig(0, 9, new String[]{"身份证", "其他"}));
            put("workerType", new SelectorConfig(0, 10, new String[]{"管理人员", "安全管理人员", "工人"}));
            put("isSpecialWorker", new SelectorConfig(0, 11, new String[]{"是", "否"}));
            put("subcontractUnit", new SelectorConfig(0, 12, subcontractorNames));
        }};

        configMap.forEach((key, config) ->
                selectorList.add(createSelector(config))
        );

        return selectorList;
    }

}
