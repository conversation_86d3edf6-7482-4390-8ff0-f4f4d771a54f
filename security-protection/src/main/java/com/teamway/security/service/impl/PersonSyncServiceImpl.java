package com.teamway.security.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.teamway.personnel.constant.ApprovalStatus;
import com.teamway.personnel.entity.HmBasicInfo;
import com.teamway.personnel.service.BasicInfoService;
import com.teamway.security.dto.securityConfiguration.person.PersonAddByApprovalDto;
import com.teamway.security.dto.sync.PersonSyncBatchResultDto;
import com.teamway.security.dto.sync.PersonSyncFailDetail;
import com.teamway.security.dto.sync.PersonSyncResultDto;
import com.teamway.security.dto.sync.SyncLogInfo;
import com.teamway.security.entity.SecurityConfiguration.Person;
import com.teamway.security.service.PersonService;
import com.teamway.security.service.PersonSyncService;
import com.teamway.security.util.PersonDataConverter;
import com.teamway.security.util.PersonSyncResultManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClossName PersonSyncServiceImpl
 * @Description 人员数据同步服务实现类，负责将审批服务数据同步到安防平台
 * 
 * 重复检查逻辑说明：
 * 1. 身份证号重复：直接报错，不进行同步
 * 2. 手机号重复（身份证号不重复）：直接报错，不进行同步  
 * 3. 身份证号和手机号都重复：报告组合错误信息
 * 4. 错误代码自动匹配：根据错误信息内容自动设置对应的错误代码
 * 
 * <AUTHOR>
 * @createDate 2025/5/29
 * @version 1.0
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class PersonSyncServiceImpl implements PersonSyncService {

    private final BasicInfoService basicInfoService;
    private final PersonService personService;
    private final PersonDataConverter personDataConverter;

    @Value("${person.sync.batchSize:100}")
    private Integer batchSize;

    @Value("${person.sync.enableDuplicateCheck:true}")
    private Boolean enableDuplicateCheck;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PersonSyncResultDto syncApprovedPersons() {
        try {
            log.info("开始从审批服务同步已审批通过的人员数据");
            
            // 获取审批服务所有已审批人员数据
            List<HmBasicInfo> hrPersonList = getApprovedPersonList();
            if (CollUtil.isEmpty(hrPersonList)) {
                log.warn("审批服务中没有找到人员数据");
                return buildSyncResult(0, 0, 0, 0, null, null);
            }

            log.info("从审批服务获取到{}条人员数据", hrPersonList.size());

            // 过滤有效数据
            List<HmBasicInfo> validPersonList = filterValidPersonData(hrPersonList);
            log.info("过滤后有效人员数据{}条", validPersonList.size());

            if (CollUtil.isEmpty(validPersonList)) {
                log.warn("没有有效的人员数据需要同步");
                return buildSyncResult(hrPersonList.size(), 0, 0, hrPersonList.size(), null, null);
            }

            // 检查重复数据并生成错误信息
            DuplicateCheckResult duplicateResult = checkDuplicatePersonData(validPersonList);
            List<HmBasicInfo> needSyncPersonList = duplicateResult.getValidPersonList();
            List<PersonSyncFailDetail> duplicateFailDetails = duplicateResult.getDuplicateFailDetails();
            
            int skipCount = validPersonList.size() - needSyncPersonList.size() - duplicateFailDetails.size();
            log.info("去重后需要同步的人员数据{}条，跳过{}条，重复错误{}条", needSyncPersonList.size(), skipCount, duplicateFailDetails.size());

            // 如果没有需要同步的人员，直接返回结果
            if (CollUtil.isEmpty(needSyncPersonList)) {
                return handleEmptySync(hrPersonList.size(), skipCount, duplicateFailDetails);
            }

            // 批量同步数据
            PersonSyncBatchResultDto batchResult = batchSyncPersonData(needSyncPersonList);
            
            // 合并重复检查的失败详情和同步失败的详情
            List<PersonSyncFailDetail> allFailDetails = mergeFailDetails(duplicateFailDetails, batchResult.getFailDetails());
            int totalFailCount = allFailDetails.size();
            
            SyncLogInfo syncSummary = SyncLogInfo.createSummary(hrPersonList.size(), batchResult.getSuccessCount(), skipCount, totalFailCount);
            log.info("已审批人员数据同步完成：{}", JSONUtil.toJsonStr(syncSummary));

            return buildSyncResult(hrPersonList.size(), batchResult.getSuccessCount(), skipCount, totalFailCount, allFailDetails, batchResult.getSuccessList());

        } catch (Exception e) {
            SyncLogInfo errorLog = SyncLogInfo.createError(e.getMessage());
            log.error("已审批人员数据同步失败：{}", JSONUtil.toJsonStr(errorLog), e);
            return buildSyncResult(0, 0, 0, 0, null, null);
        }
    }

    /**
     * 获取已审批人员列表
     */
    private List<HmBasicInfo> getApprovedPersonList() {
        List<HmBasicInfo> hrPersonList = basicInfoService.list(
            new LambdaQueryWrapper<HmBasicInfo>()
                .eq(HmBasicInfo::getOnDutyStatus, ApprovalStatus.ENTERED_PENDING_CARD.getCode())
        );
//        hrPersonList = hrPersonList.stream().filter(hmBasicInfo -> hmBasicInfo.getIdCardNumber().equals("******************")).collect(Collectors.toList());
//        hrPersonList.get(0).setIdCardNumber("******************");
        return hrPersonList;
    }

    /**
     * 处理空同步情况
     */
    private PersonSyncResultDto handleEmptySync(int totalCount, int skipCount, List<PersonSyncFailDetail> duplicateFailDetails) {
        if (CollUtil.isNotEmpty(duplicateFailDetails)) {
            log.info("所有人员数据都存在重复问题，无法同步");
            return buildSyncResult(totalCount, 0, skipCount, duplicateFailDetails.size(), duplicateFailDetails, null);
        } else {
            log.info("所有人员数据已存在，无需同步");
            return buildSyncResult(totalCount, 0, skipCount, 0, null, null);
        }
    }

    /**
     * 合并失败详情
     */
    private List<PersonSyncFailDetail> mergeFailDetails(List<PersonSyncFailDetail> duplicateFailDetails, 
                                                       List<PersonSyncFailDetail> batchFailDetails) {
        List<PersonSyncFailDetail> allFailDetails = new ArrayList<>();
        Optional.ofNullable(duplicateFailDetails).ifPresent(allFailDetails::addAll);
        Optional.ofNullable(batchFailDetails).ifPresent(allFailDetails::addAll);
        return allFailDetails;
    }

    /**
     * 过滤有效的人员数据
     */
    private List<HmBasicInfo> filterValidPersonData(List<HmBasicInfo> hrPersonList) {
        return hrPersonList.stream()
            .filter(person -> StrUtil.isNotBlank(person.getName()))
            .filter(person -> StrUtil.isNotBlank(person.getIdCardNumber()))
            .collect(Collectors.toList());
    }

    /**
     * 检查重复的人员数据并生成错误信息
     */
    private DuplicateCheckResult checkDuplicatePersonData(List<HmBasicInfo> validPersonList) {
        if (!enableDuplicateCheck) {
            return new DuplicateCheckResult(validPersonList, new ArrayList<>());
        }

        // 获取所有身份证号和手机号
        Set<String> idCardNumbers = extractIdCardNumbers(validPersonList);
        Set<String> phoneNumbers = extractPhoneNumbers(validPersonList);

        // 查询已存在的人员
        List<Person> existingPersons = queryExistingPersons(idCardNumbers, phoneNumbers);
        if (CollUtil.isEmpty(existingPersons)) {
            return new DuplicateCheckResult(validPersonList, new ArrayList<>());
        }
        
        Set<String> existingIdCards = extractExistingIdCards(existingPersons);
        Set<String> existingPhones = extractExistingPhones(existingPersons);

        // 分别检查每个人员的重复情况
        return checkPersonDuplicates(validPersonList, existingIdCards, existingPhones);
    }

    /**
     * 提取身份证号集合
     */
    private Set<String> extractIdCardNumbers(List<HmBasicInfo> validPersonList) {
        return validPersonList.stream()
            .map(HmBasicInfo::getIdCardNumber)
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toSet());
    }

    /**
     * 提取手机号集合
     */
    private Set<String> extractPhoneNumbers(List<HmBasicInfo> validPersonList) {
        return validPersonList.stream()
            .map(HmBasicInfo::getMobilePhoneNumber)
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toSet());
    }

    /**
     * 查询已存在的人员
     */
    private List<Person> queryExistingPersons(Set<String> idCardNumbers, Set<String> phoneNumbers) {
        LambdaQueryWrapper<Person> queryWrapper = new LambdaQueryWrapper<>();
        
        // 只有当集合不为空时才添加查询条件
        boolean hasIdCards = CollUtil.isNotEmpty(idCardNumbers);
        boolean hasPhones = CollUtil.isNotEmpty(phoneNumbers);
        
        if (hasIdCards && hasPhones) {
            queryWrapper.and(wrapper -> 
                wrapper.in(Person::getPersonCertificateNum, idCardNumbers)
                    .or()
                    .in(Person::getPersonPhone, phoneNumbers)
            );
        } else if (hasIdCards) {
            queryWrapper.in(Person::getPersonCertificateNum, idCardNumbers);
        } else if (hasPhones) {
            queryWrapper.in(Person::getPersonPhone, phoneNumbers);
        } else {
            return new ArrayList<>();
        }

        return personService.list(queryWrapper);
    }

    /**
     * 提取已存在的身份证号
     */
    private Set<String> extractExistingIdCards(List<Person> existingPersons) {
        return existingPersons.stream()
            .map(Person::getPersonCertificateNum)
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toSet());
    }

    /**
     * 提取已存在的手机号
     */
    private Set<String> extractExistingPhones(List<Person> existingPersons) {
        return existingPersons.stream()
            .map(Person::getPersonPhone)
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toSet());
    }

    /**
     * 检查人员重复情况
     */
    private DuplicateCheckResult checkPersonDuplicates(List<HmBasicInfo> validPersonList, 
                                                      Set<String> existingIdCards, 
                                                      Set<String> existingPhones) {
        List<HmBasicInfo> needSyncPersonList = new ArrayList<>();
        List<PersonSyncFailDetail> duplicateFailDetails = new ArrayList<>();
        
        for (HmBasicInfo person : validPersonList) {
            boolean hasIdCardDuplicate = existingIdCards.contains(person.getIdCardNumber());
            boolean hasPhoneDuplicate = StrUtil.isNotBlank(person.getMobilePhoneNumber()) && 
                existingPhones.contains(person.getMobilePhoneNumber());
            
            if (hasIdCardDuplicate || hasPhoneDuplicate) {
                // 生成错误信息
                String errorMessage = generateDuplicateErrorMessage(hasIdCardDuplicate, hasPhoneDuplicate);
                
                PersonSyncFailDetail failDetail = PersonSyncFailDetail.of(
                    person.getName(), 
                    person.getIdCardNumber(), 
                    person.getMobilePhoneNumber(), 
                    errorMessage
                );
                duplicateFailDetails.add(failDetail);
                
                SyncLogInfo duplicateLog = SyncLogInfo.createPersonOperation(
                    person.getName() + "-" + person.getIdCardNumber(), 
                    "人员重复检查失败", 
                    errorMessage
                );
                log.warn("发现重复人员：{}", JSONUtil.toJsonStr(duplicateLog));
            } else {
                // 无重复，可以同步
                needSyncPersonList.add(person);
            }
        }

        return new DuplicateCheckResult(needSyncPersonList, duplicateFailDetails);
    }

    /**
     * 生成重复错误信息
     */
    private String generateDuplicateErrorMessage(boolean hasIdCardDuplicate, boolean hasPhoneDuplicate) {
        if (hasIdCardDuplicate && hasPhoneDuplicate) {
            return "身份证号和手机号都已存在";
        } else if (hasIdCardDuplicate) {
            return "身份证号已存在";
        } else {
            return "手机号已存在";
        }
    }

    /**
     * 批量同步人员数据
     */
    private PersonSyncBatchResultDto batchSyncPersonData(List<HmBasicInfo> needSyncPersonList) {
        List<PersonSyncBatchResultDto> batchResults = new ArrayList<>();
        
        // 分批处理
        List<List<HmBasicInfo>> batches = CollUtil.split(needSyncPersonList, batchSize);
        
        for (List<HmBasicInfo> batch : batches) {
            try {
                List<PersonAddByApprovalDto> personDtoList = convertToPersonAddDtoList(batch);

                log.info("开始同步批次数据，数量：{}", personDtoList.size());
                log.debug("批次数据详情：{}", JSONUtil.toJsonStr(personDtoList));

                // 调用批量添加并获取详细结果
                PersonSyncBatchResultDto batchResult = personService.addByApprovalBatchWithResult(personDtoList);
                batchResults.add(batchResult);
                
                logBatchResult(batchResult);
            } catch (Exception e) {
                // 创建异常批次的失败结果
                PersonSyncBatchResultDto failedBatchResult = PersonSyncResultManager.createFailureResult(batch.size());
                batchResults.add(failedBatchResult);
                
                SyncLogInfo exceptionLog = SyncLogInfo.createBatchException(batch.size(), e.getMessage());
                log.error("批次同步异常：{}", JSONUtil.toJsonStr(exceptionLog), e);
            }
        }
        
        // 合并所有批次结果
        return PersonSyncResultManager.mergeResults(batchResults);
    }

    /**
     * 转换为PersonAddByApprovalDto列表
     */
    private List<PersonAddByApprovalDto> convertToPersonAddDtoList(List<HmBasicInfo> batch) {
        return batch.stream()
            .map(personDataConverter::convertToPersonAddDto)
            .collect(Collectors.toList());
    }

    /**
     * 记录批次结果日志
     */
    private void logBatchResult(PersonSyncBatchResultDto batchResult) {
        if (PersonSyncResultManager.isCompleteSuccess(batchResult)) {
            SyncLogInfo successLog = SyncLogInfo.createBatchSuccess(batchResult.getSuccessCount());
            log.info("批次同步完全成功：{}", JSONUtil.toJsonStr(successLog));
        } else if (PersonSyncResultManager.hasPartialFailure(batchResult)) {
            SyncLogInfo partialFailLog = SyncLogInfo.createBatchPartialFailure(
                batchResult.getSuccessCount(), 
                batchResult.getFailCount(), 
                JSONUtil.toJsonStr(batchResult.getFailDetails())
            );
            log.warn("批次同步部分失败：{}", JSONUtil.toJsonStr(partialFailLog));
        } else {
            SyncLogInfo completeFailLog = SyncLogInfo.createBatchCompleteFailure(
                "批次同步完全失败", 
                JSONUtil.toJsonStr(batchResult.getFailDetails())
            );
            log.error("批次同步完全失败：{}", JSONUtil.toJsonStr(completeFailLog));
        }
    }

    /**
     * 构建同步结果
     */
    private PersonSyncResultDto buildSyncResult(Integer totalCount, Integer successCount, 
                                              Integer skipCount, Integer failCount,
                                              List<PersonSyncFailDetail> failDetails,
                                              List<String> successList) {
        // 根据失败数量判断是否成功：没有失败则为成功
        boolean success = Optional.ofNullable(failCount).orElse(0) == 0;
        
        return PersonSyncResultDto.builder()
            .totalCount(totalCount)
            .successCount(successCount)
            .skipCount(skipCount)
            .failCount(failCount)
            .success(success)
            .failDetails(failDetails)
            .successList(successList)
            .build();
    }

    /**
     * 重复检查结果内部类
     */
    private static class DuplicateCheckResult {
        private final List<HmBasicInfo> validPersonList;
        private final List<PersonSyncFailDetail> duplicateFailDetails;

        public DuplicateCheckResult(List<HmBasicInfo> validPersonList, List<PersonSyncFailDetail> duplicateFailDetails) {
            this.validPersonList = Optional.ofNullable(validPersonList).orElse(new ArrayList<>());
            this.duplicateFailDetails = Optional.ofNullable(duplicateFailDetails).orElse(new ArrayList<>());
        }

        public List<HmBasicInfo> getValidPersonList() {
            return validPersonList;
        }

        public List<PersonSyncFailDetail> getDuplicateFailDetails() {
            return duplicateFailDetails;
        }
    }
} 