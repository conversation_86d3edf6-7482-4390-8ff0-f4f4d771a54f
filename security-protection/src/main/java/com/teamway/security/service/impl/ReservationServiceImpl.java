package com.teamway.security.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.batch.MybatisBatch;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.entity.CommonType;
import com.teamway.base.service.CommonTypeService;
import com.teamway.common.constant.CommonConstant;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.util.DateUtils;
import com.teamway.common.util.PhoneNumberUtils;
import com.teamway.exception.ServiceException;
import com.teamway.security.common.BatchExecuteResult;
import com.teamway.security.common.constant.DockingConstant;
import com.teamway.security.common.constant.VariousConstant;
import com.teamway.security.common.enums.VisitorSexEnum;
import com.teamway.security.common.properties.SecurityComponentProperties;
import com.teamway.security.common.transfer.SecurityConfiguration.CarTransfer;
import com.teamway.security.common.transfer.accessManagement.ReservationTransfer;
import com.teamway.security.dto.accessManagement.visitor.*;
import com.teamway.security.dto.securityConfiguration.CarDelReqDto;
import com.teamway.security.dto.securityConfiguration.car.CarAddAndUpdateReqDto;
import com.teamway.security.dto.securityConfiguration.person.*;
import com.teamway.security.entity.SecurityConfiguration.Car;
import com.teamway.security.entity.SecurityConfiguration.Card;
import com.teamway.security.entity.SecurityConfiguration.Person;
import com.teamway.security.entity.accessManagement.visitorReservationManage.*;
import com.teamway.security.mapper.ReservationMapper;
import com.teamway.security.service.*;
import com.teamway.security.util.SecurityComponentUtil;
import com.teamway.security.util.SecurityRedisUtil;
import com.teamway.security.vo.accessManagement.visitor.ReservationSynMsgVo;
import com.teamway.security.vo.securityConfiguration.securityDevice.Sync4aPersonVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/16
 */
@Service
@Slf4j
public class ReservationServiceImpl extends ServiceImpl<ReservationMapper, Reservation> implements ReservationService {

    private CarService carService;
    private CardService cardService;
    private PersonService personService;
    private SecurityParkService securityParkService;
    private CarTransfer carTransfer;
    private SecurityDeviceDoorService securityDeviceDoorService;
    private ReservationTransfer reservationTransfer;
    private SecurityComponentProperties securityComponentProperties;
    private SecurityComponentUtil securityComponentUtil;
    private SecurityRedisUtil securityRedisUtil;

    @Value("${visitorNumberPrefix}")
    private String visitorNumberPrefix;

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Autowired
    CommonTypeService commonTypeService;

    @Value("${misUrl}")
    private String misUrl;

    @Autowired
    public void setCarService(CarService carService) {
        this.carService = carService;
    }

    @Autowired
    public void setCardService(CardService cardService) {
        this.cardService = cardService;
    }

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setSecurityParkService(SecurityParkService securityParkService) {
        this.securityParkService = securityParkService;
    }

    @Autowired
    public void setCarTransfer(CarTransfer carTransfer) {
        this.carTransfer = carTransfer;
    }

    @Autowired
    public void setSecurityDeviceDoorService(SecurityDeviceDoorService securityDeviceDoorService) {
        this.securityDeviceDoorService = securityDeviceDoorService;
    }

    @Autowired
    public void setReservationTransfer(ReservationTransfer reservationTransfer) {
        this.reservationTransfer = reservationTransfer;
    }

    @Autowired
    public void setSecurityComponentProperties(SecurityComponentProperties securityComponentProperties) {
        this.securityComponentProperties = securityComponentProperties;
    }

    @Autowired
    public void setSecurityComponentUtil(SecurityComponentUtil securityComponentUtil) {
        this.securityComponentUtil = securityComponentUtil;
    }

    @Autowired
    public void setSecurityRedisUtil(SecurityRedisUtil securityRedisUtil) {
        this.securityRedisUtil = securityRedisUtil;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addVisitorReservation(VisitorReservationAddQueryDto dto) {
        log.info("预约流程-接收到预约信息-进行保存预约信息流程，预约信息：{}", JSONUtil.toJsonStr(dto));
        checkVisitorReservationAddQueryDto(dto);
        // 保存预约信息
        Reservation reservation = reservationTransfer.dto2Entity(dto);
        reservation.setReservationState(CommonConstant.UNDER_APPROVAL);
        reservation.setSource(CommonConstant.PLATFORM);
        reservation.setReservationStartTime(LocalDateTimeUtil.parse(dto.getReservationStartTime(), DateUtils.DATETIME_FORMATTER));
        reservation.setReservationEndTime(LocalDateTimeUtil.parse(dto.getReservationEndTime(), DateUtils.DATETIME_FORMATTER));
//        reservation.setPersonCarryItems(dto.getPersonCarryItems());
//        reservation.setTransportGoods(dto.getTransportGoods());
//        reservation.setCarBrand(dto.getCarBrand());
        updateReservationState(reservation, dto);
        List<Reservation> reservationList = new ArrayList<>();
        reservationList.add(reservation);
        for (AccompanyingPerson accompanyingPerson : dto.getAccompanyingPersonList()) {
            Reservation reservation1 = reservationTransfer.acc2Entity(accompanyingPerson);
            reservation1.setVisitorUnit(dto.getVisitorUnit());
            reservation1.setSource(CommonConstant.PLATFORM);
            reservation1.setReservationState(CommonConstant.UNDER_APPROVAL);
            reservation1.setReceptionistName(dto.getReceptionistName());
            reservation1.setVisitorReason(dto.getVisitorReason());
            reservation1.setReservationStartTime(LocalDateTimeUtil.parse(dto.getReservationStartTime(), DateUtils.DATETIME_FORMATTER));
            reservation1.setReservationEndTime(LocalDateTimeUtil.parse(dto.getReservationEndTime(), DateUtils.DATETIME_FORMATTER));
            reservation1.setReceptionistPhone(dto.getReceptionistPhone());
            reservation1.setReceptionistDepartment(dto.getReceptionistDepartment());
            reservation1.setPersonFaceUrl(accompanyingPerson.getPersonFaceUrl());
            reservation1.setPersonCarryItems(accompanyingPerson.getPersonCarryItems());
            reservation1.setTransportGoods(accompanyingPerson.getTransportGoods());
            reservation1.setCarBrand(accompanyingPerson.getCarBrand());
            updateReservationState(reservation1, dto);
            reservationList.add(reservation1);
        }
        boolean save = saveBatch(reservationList);
        List<String> certificationList = reservationList.stream().map(Reservation::getVisitorCertification).collect(Collectors.toList());
        LambdaQueryWrapper<Reservation> reservationLambdaQueryWrapper = new LambdaQueryWrapper<>();
        reservationLambdaQueryWrapper.in(Reservation::getVisitorCertification, certificationList);
        List<Reservation> list = list(reservationLambdaQueryWrapper);
        for (Reservation res : list) {
            //添加人员
            Person person = reservationAddPerson(res);
            if (StrUtil.isNotEmpty(res.getVisitorCarNo())) {
                // 添加车辆
                reservationAddCar(res, person.getId());
            }
        }
        log.info("预约流程-接收到预约信息-开始保存预约信息，保存结果：{}", save ? "保存成功" : "保存失败");
        // todo: 将多条预约记录，发起请求大唐先一审批
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addVisitorReservationByController(VisitorReservationAddQueryDto dto) {
        log.info("预约流程-接收到预约信息-进行保存预约信息流程，预约信息：{}", JSONUtil.toJsonStr(dto));
        checkVisitorReservationAddQueryDto(dto);
        // 保存预约信息
        Reservation reservation = reservationTransfer.dto2Entity(dto);
        reservation.setReservationState(CommonConstant.PASS);
        reservation.setSource(CommonConstant.PLATFORM);
        reservation.setReservationStartTime(LocalDateTimeUtil.parse(dto.getReservationStartTime(), DateUtils.DATETIME_FORMATTER));
        reservation.setReservationEndTime(LocalDateTimeUtil.parse(dto.getReservationEndTime(), DateUtils.DATETIME_FORMATTER));
//        reservation.setPersonCarryItems(dto.getPersonCarryItems());
//        reservation.setTransportGoods(dto.getTransportGoods());
//        reservation.setCarBrand(dto.getCarBrand());
        updateReservationState(reservation, dto);
        List<Reservation> reservationList = new ArrayList<>();
        reservationList.add(reservation);
        for (AccompanyingPerson accompanyingPerson : dto.getAccompanyingPersonList()) {
            Reservation reservation1 = reservationTransfer.acc2Entity(accompanyingPerson);
            reservation1.setVisitorUnit(dto.getVisitorUnit());
            reservation1.setSource(CommonConstant.PLATFORM);
            reservation1.setReservationState(CommonConstant.PASS);
            reservation1.setReceptionistName(dto.getReceptionistName());
            reservation1.setVisitorReason(dto.getVisitorReason());
            reservation1.setReservationStartTime(LocalDateTimeUtil.parse(dto.getReservationStartTime(), DateUtils.DATETIME_FORMATTER));
            reservation1.setReservationEndTime(LocalDateTimeUtil.parse(dto.getReservationEndTime(), DateUtils.DATETIME_FORMATTER));
            reservation1.setReceptionistPhone(dto.getReceptionistPhone());
            reservation1.setReceptionistDepartment(dto.getReceptionistDepartment());
            reservation1.setPersonFaceUrl(accompanyingPerson.getPersonFaceUrl());
            reservation1.setPersonCarryItems(accompanyingPerson.getPersonCarryItems());
            reservation1.setTransportGoods(accompanyingPerson.getTransportGoods());
            reservation1.setCarBrand(accompanyingPerson.getCarBrand());
            updateReservationState(reservation1, dto);
            reservationList.add(reservation1);
        }
        boolean save = saveBatch(reservationList);
        List<String> certificationList = reservationList.stream().map(Reservation::getVisitorCertification).collect(Collectors.toList());
        LambdaQueryWrapper<Reservation> reservationLambdaQueryWrapper = new LambdaQueryWrapper<>();
        reservationLambdaQueryWrapper.in(Reservation::getVisitorCertification, certificationList);
        List<Reservation> list = list(reservationLambdaQueryWrapper);
        for (Reservation res : list) {
            //添加人员
            Person person = reservationAddPerson(res);
            if (StrUtil.isNotEmpty(res.getVisitorCarNo())) {
                // 添加车辆
                reservationAddCar(res, person.getId());
            }
        }
        log.info("预约流程-接收到预约信息-开始保存预约信息，保存结果：{}", save ? "保存成功" : "保存失败");
        // todo: 将多条预约记录，发起请求大唐先一审批
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVisitorReservation(VisitorReservationAddQueryDto dto) {
        Reservation reservation = getById(dto.getId());
        LambdaQueryWrapper<Person> personLambdaQueryWrapper = new LambdaQueryWrapper<>();
        personLambdaQueryWrapper.eq(Person::getPersonCertificateNum, reservation.getVisitorCertification());
        Person one = personService.getOne(personLambdaQueryWrapper);
        // 有授权且授权没有过期就直接return
        List<PersonAuthedDeviceDto> personAuthedDevice = getPersonAuthedDevice(dto.getId());
        LocalDateTime now = LocalDateTime.now();
        if (CollUtil.isNotEmpty(personAuthedDevice) && one.getPersonType().equals(CommonConstant.COMMON)) {
            personAuthedDevice.forEach(item -> {
                // 如果开始时间和结束时间都为空，则认为是长期有效的授权
                if (StrUtil.isAllBlank(item.getStartTime(), item.getEndTime())) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE,
                            "更新失败，该人员已存在安防平台，且授权长期有效");
                }

                // 如果只有一个时间为空，认为是异常数据
                if (StrUtil.isBlank(item.getStartTime()) || StrUtil.isBlank(item.getEndTime())) {
                    log.warn("授权信息异常：开始或结束时间只有一个为空, startTime={}, endTime={}",
                            item.getStartTime(), item.getEndTime());
                    return; // 跳过这条异常数据，继续检查其他授权
                }

                try {
                    // 解析 startTime 和 endTime
                    LocalDateTime startTime = LocalDateTime.parse(item.getStartTime(), DateUtils.DATETIME_FORMATTER);
                    LocalDateTime endTime = LocalDateTime.parse(item.getEndTime(), DateUtils.DATETIME_FORMATTER);

                    // 检查授权是否有效
                    if (now.isAfter(startTime) && now.isBefore(endTime)) {
                        throw new ServiceException(ResultCode.OPERATION_FAILURE,
                                "更新失败，该人员已存在安防平台，且授权未过期");
                    }
                } catch (Exception e) {
                    log.warn("解析授权时间异常: startTime={}, endTime={}", item.getStartTime(), item.getEndTime(), e);
                    // 时间格式解析异常，跳过这条数据
                    return;
                }
            });
        }
        if (Objects.isNull(reservation)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "预约");
        }

        Person person = Person.builder()
                .personName(dto.getVisitorName())
                .personSex(dto.getVisitorSex())
                .personPhone(generateRandomPhone())
                .personCertificateType(CommonConstant.ID_CARD)
                .personCertificateNum(dto.getVisitorCertification())
                .personFaceUrl(dto.getPersonFaceUrl())
                .build();
        //校验访客信息
        //checkVisitorPerson(person, dto.getVisitorStartTime(), dto.getVisitorEndTime());
        //更新数据
        Reservation updateReservation = JSON.parseObject(JSON.toJSONString(dto), new TypeReference<Reservation>() {
        });
        updateById(updateReservation);
        person.setId(one.getId());
        PersonAddOrUpdateQueryDto personAddOrUpdateQueryDto = new PersonAddOrUpdateQueryDto();
        BeanUtils.copyProperties(person, personAddOrUpdateQueryDto);
        personAddOrUpdateQueryDto.setPersonType(CommonConstant.VISITOR);
        //更新安防人员信息
        personService.updatePerson(personAddOrUpdateQueryDto);
        //更新车辆信息
        Car car = carService.lambdaQuery().eq(Car::getPersonId, one.getId()).one();
        if (StrUtil.isNotBlank(dto.getVisitorCarNo()) && StrUtil.isNotBlank(dto.getVisitorCarType())) {
            CarAddAndUpdateReqDto carUpdateReqDto = new CarAddAndUpdateReqDto();
            carUpdateReqDto.setCarNo(dto.getVisitorCarNo());
            carUpdateReqDto.setCarType(Integer.parseInt(dto.getVisitorCarType()));
            carUpdateReqDto.setPersonId(person.getId().toString());
            if (Objects.isNull(car)) {
                // 参数有车辆信息 访客刚开始没车，需添加车辆
                carService.addCar(carUpdateReqDto);
            } else {
                //参数有车辆信息 访客刚开始有车，需更新车辆信息
                carUpdateReqDto.setId(car.getId());
                carService.updateCar(carUpdateReqDto);
            }
        } else {
            //参数没有车辆信息 访客刚开始有车 需删除车辆信息
            if (ObjectUtil.isNotEmpty(car)) {
                CarDelReqDto carDelReqDto = new CarDelReqDto();
                carDelReqDto.setIds(CollectionUtil.toList(car.getId().toString()));
                carService.delCar(carDelReqDto);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVisitorReservation(List<String> ids) {
        List<Reservation> reservations = listByIds(ids);
        List<String> certifications = reservations.stream().map(Reservation::getVisitorCertification).filter(Objects::nonNull).collect(Collectors.toList());
        if (!certifications.isEmpty()) {
            LambdaUpdateWrapper<Person> qw = new LambdaUpdateWrapper<>();
            qw.in(Person::getPersonCertificateNum, certifications);
            List<Person> persons = personService.list(qw);
            if (!persons.isEmpty()) {
                List<Long> personIds = persons.stream().map(Person::getId).collect(Collectors.toList());
                PersonBatchDelQueryDto delParam = new PersonBatchDelQueryDto();
                String[] array = ArrayUtil.toArray(personIds.stream().map(String::valueOf).collect(Collectors.toList()), String.class);
                delParam.setPersonIdList(array);
                personService.batchDelPerson(delParam);
                //删除车辆信息
                List<String> carIds = carService.lambdaQuery().in(Car::getPersonId, personIds).list().stream().map(Car::getId).map(String::valueOf).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(carIds)) {
                    CarDelReqDto carDelReqDto = new CarDelReqDto();
                    carDelReqDto.setIds(carIds);
                    carService.delCar(carDelReqDto);
                }
            }
        }
        removeBatchByIds(ids);
    }


    private void updateReservationState(Reservation reservation, VisitorReservationAddQueryDto dto) {
        reservation.setPersonAddFlag(CommonConstant.RESERVATION_FAIL_STATE);
        reservation.setCardAddFlag(CommonConstant.RESERVATION_FAIL_STATE);
        reservation.setPersonAuthFlag(CommonConstant.RESERVATION_FAIL_STATE);
        if (StrUtil.isNotEmpty(dto.getVisitorCarNo())) {
            reservation.setCardAddFlag(CommonConstant.RESERVATION_FAIL_STATE);
            reservation.setCarAuthFlag(CommonConstant.RESERVATION_FAIL_STATE);
        }
    }


    /**
     * 校验访客人员信息
     * @param person 人员信息
     * @param startTime 来访时间
     * @param endTime 离开时间
     */
    private void checkVisitorPerson(Person person, String startTime, String endTime) {
        Person person1 = personService.lambdaQuery().eq(Person::getPersonCertificateNum, person.getPersonCertificateNum()).eq(Person::getPersonCertificateType, person.getPersonCertificateType()).one();
        if (ObjectUtil.isNotEmpty(person1) && !person1.getPersonName().equals(person.getPersonName())) {
            throw new ServiceException(ResultCode.EXIST, "身份证 " + person.getPersonCertificateNum());
        }
        if (ObjectUtil.isNotEmpty(person1) && CommonConstant.COMMON.equals(person1.getPersonType())) {
            throw new ServiceException(ResultCode.EXIST, person1.getPersonName());
        }
        Person person2 = personService.lambdaQuery().eq(Person::getPersonPhone, person.getPersonPhone()).ne(Person::getPersonPhone, DockingConstant.DEFAULT_PHONE).one();
        if (ObjectUtil.isNotEmpty(person2) && !person2.getPersonName().equals(person.getPersonName()) && DockingConstant.DEFAULT_PHONE.equals(person.getPersonPhone())) {
            throw new ServiceException(ResultCode.EXIST, "手机号 " + person.getPersonPhone());
        }
        // 校验该访客是否重复预约,根据身份证号判断访客是否预约过，如果预约过则判断预约开始时间和结束时间是否和历史记录重叠，如果重叠，则提示访客已经预约过
        List<Reservation> reservationList = lambdaQuery().eq(Reservation::getVisitorCertification, person.getPersonCertificateNum()).eq(Reservation::getReservationState, CommonConstant.UNDER_APPROVAL).list();
        if (CollectionUtil.isNotEmpty(reservationList)) {
            for (Reservation reservation : reservationList) {
                LocalDateTime reservationStartTime = reservation.getReservationStartTime();
                LocalDateTime reservationEndTime = reservation.getReservationEndTime();
                LocalDateTime visitorStartTime = LocalDateTimeUtil.parse(startTime, DateUtils.DATETIME_FORMATTER);
                LocalDateTime visitorEndTime = LocalDateTimeUtil.parse(endTime, DateUtils.DATETIME_FORMATTER);
                // 如果时间相同，则提示访客已经预约过
                // 预约开始时间在历史记录之后，预约结束时间在历史记录之前
                boolean b1 = (visitorStartTime.isEqual(reservationStartTime) || visitorStartTime.isAfter(reservationStartTime)) && (visitorEndTime.isEqual(reservationEndTime) || visitorStartTime.isBefore(reservationEndTime));
                if (b1) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "该访客已经预约过，预约时间为：" + reservationStartTime + "-" + reservationEndTime);
                }
                // 预约开始时间在历史记录之前，预约结束时间在历史记录之后
                boolean b2 = (visitorStartTime.isEqual(reservationStartTime) || visitorEndTime.isAfter(reservationStartTime)) && (visitorEndTime.isEqual(reservationEndTime) || visitorEndTime.isBefore(reservationEndTime));
                if (b2) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "该访客已经预约过，预约时间为：" + reservationStartTime + "-" + reservationEndTime);
                }
                // 预约开始时间在历史记录之前，预约结束时间在历史记录之后
                boolean b3 = (visitorStartTime.isEqual(reservationStartTime) || visitorStartTime.isBefore(reservationStartTime)) && (visitorEndTime.isEqual(reservationEndTime) || visitorEndTime.isAfter(reservationEndTime));
                if (b3) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "该访客已经预约过，预约时间为：" + reservationStartTime + "-" + reservationEndTime);
                }
            }
        }
        personService.checkImageUrlIsUseful(person.getPersonFaceUrl());
    }


    /**
     * 校验参数
     * @param dto 预约信息
     */
    private void checkVisitorReservationAddQueryDto(VisitorReservationAddQueryDto dto) {
        String visitorCertification = dto.getVisitorCertification();
        List<String> certificationList = dto.getAccompanyingPersonList().stream().map(AccompanyingPerson::getCertification).collect(Collectors.toList());
        certificationList.add(visitorCertification);
        HashSet<String> certificationSet = new HashSet<>(certificationList);
        // 判断输入的证件号是否重复
        if (certificationSet.size() != certificationList.size()) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "访客身份证号重复");
        }
        // 判断输入的证件号是否和库里的重复
        List<Person> personList = new ArrayList<>();
        List<Car> carList = new ArrayList<>();
        for (AccompanyingPerson accompanyingPerson : dto.getAccompanyingPersonList()) {
            // 将名称和身份证号提取成人员，到人员list中
            Person person = new Person();
            person.setPersonCertificateType("111");
            person.setPersonCertificateNum(accompanyingPerson.getCertification());
            person.setPersonName(accompanyingPerson.getName());
            person.setPersonPhone(dto.getVisitorPhone());
            person.setPersonFaceUrl(accompanyingPerson.getPersonFaceUrl());
            personList.add(person);
            carList.add(Car.builder().carNo(accompanyingPerson.getCarNo()).carType(StrUtil.isNotBlank(accompanyingPerson.getCarType()) ? Integer.parseInt(accompanyingPerson.getCarType()) : null).build());
        }
        carList.add(Car.builder().carNo(dto.getVisitorCarNo()).carType(StrUtil.isNotBlank(dto.getVisitorCarType()) ? Integer.parseInt(dto.getVisitorCarType()) : null).build());
        for (Car car : carList) {
            // 校验访客车辆信息 车牌号和车辆类型要么都为空，要么都不为空
            String visitorCarNum = car.getCarNo();
            Integer visitorCarType = car.getCarType();
            boolean checkCarMessageFlag = (StrUtil.isBlank(visitorCarNum) && ObjectUtil.isNotEmpty(visitorCarType)) || (StrUtil.isNotBlank(visitorCarNum) && ObjectUtil.isEmpty(visitorCarType));
            if (checkCarMessageFlag) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "访客车辆信息不完整");
            }
        }
        personList.add(
                Person.builder()
                        .personCertificateType("111")
                        .personName(dto.getVisitorName())
                        .personPhone(dto.getVisitorPhone())
                        .personFaceUrl(dto.getPersonFaceUrl())
                        .personCertificateNum(visitorCertification)
                        .build());
        for (Person person : personList) {
            Person person1 = personService.lambdaQuery().eq(Person::getPersonCertificateNum, person.getPersonCertificateNum()).eq(Person::getPersonCertificateType, person.getPersonCertificateType()).one();
            if (ObjectUtil.isNotEmpty(person1) && !person1.getPersonName().equals(person.getPersonName())) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "身份证 " + person.getPersonCertificateNum() + " 已存在");
            }
            if (ObjectUtil.isNotEmpty(person1) && "1".equals(person1.getPersonType())) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, person1.getPersonName() + " 属于普通用户，已存在系统中");
            }
            Person person2 = personService.lambdaQuery().eq(Person::getPersonPhone, person.getPersonPhone()).ne(Person::getPersonPhone, DockingConstant.DEFAULT_PHONE).one();
            if (ObjectUtil.isNotEmpty(person2) && !person2.getPersonName().equals(person.getPersonName()) && DockingConstant.DEFAULT_PHONE.equals(person.getPersonPhone())) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "手机号 " + person.getPersonPhone() + " 已存在");
            }
            // 校验该访客是否重复预约,根据身份证号判断访客是否预约过，如果预约过则判断预约开始时间和结束时间是否和历史记录重叠，如果重叠，则提示访客已经预约过
            List<Reservation> reservationList = lambdaQuery().eq(Reservation::getVisitorCertification, person.getPersonCertificateNum()).eq(Reservation::getReservationState, "1").list();
            if (CollectionUtil.isNotEmpty(reservationList)) {
                for (Reservation reservation : reservationList) {
                    LocalDateTime reservationStartTime = reservation.getReservationStartTime();
                    LocalDateTime reservationEndTime = reservation.getReservationEndTime();
                    LocalDateTime visitorStartTime = LocalDateTimeUtil.parse(dto.getReservationStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    LocalDateTime visitorEndTime = LocalDateTimeUtil.parse(dto.getReservationEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    // 如果时间相同，则提示访客已经预约过
                    // 预约开始时间在历史记录之后，预约结束时间在历史记录之前
                    boolean b1 = (visitorStartTime.isEqual(reservationStartTime) || visitorStartTime.isAfter(reservationStartTime)) && (visitorEndTime.isEqual(reservationEndTime) || visitorStartTime.isBefore(reservationEndTime));
                    if (b1) {
                        throw new ServiceException(ResultCode.OPERATION_FAILURE, "该访客已经预约过，预约时间为：" + reservationStartTime + "-" + reservationEndTime);
                    }
                    // 预约开始时间在历史记录之前，预约结束时间在历史记录之后
                    boolean b2 = (visitorStartTime.isEqual(reservationStartTime) || visitorEndTime.isAfter(reservationStartTime)) && (visitorEndTime.isEqual(reservationEndTime) || visitorEndTime.isBefore(reservationEndTime));
                    if (b2) {
                        throw new ServiceException(ResultCode.OPERATION_FAILURE, "该访客已经预约过，预约时间为：" + reservationStartTime + "-" + reservationEndTime);
                    }
                    // 预约开始时间在历史记录之前，预约结束时间在历史记录之后
                    boolean b3 = (visitorStartTime.isEqual(reservationStartTime) || visitorStartTime.isBefore(reservationStartTime)) && (visitorEndTime.isEqual(reservationEndTime) || visitorEndTime.isAfter(reservationEndTime));
                    if (b3) {
                        throw new ServiceException(ResultCode.OPERATION_FAILURE, "该访客已经预约过，预约时间为：" + reservationStartTime + "-" + reservationEndTime);
                    }
                }
            }
            personService.checkImageUrlIsUseful(person.getPersonFaceUrl());
        }
        // 校验访客预约开始时间和结束时间
        LocalDateTime visitorStartTime = LocalDateTimeUtil.parse(dto.getReservationStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime visitorEndTime = LocalDateTimeUtil.parse(dto.getReservationEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        if (visitorStartTime.isAfter(visitorEndTime)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "访客预约开始时间不能大于结束时间");
        }
        // 校验随行人的信息，如果随行人不为空，则卡片编号与卡片类型必填
        List<AccompanyingPerson> accompanyingPersonList = dto.getAccompanyingPersonList();
        for (AccompanyingPerson accompanyingPerson : accompanyingPersonList) {
            if (ObjectUtil.isNotEmpty(accompanyingPerson.getName())) {
                boolean flag = (ObjectUtil.isNotEmpty(accompanyingPerson.getCarNo()) && ObjectUtil.isEmpty(accompanyingPerson.getCarType())) || (ObjectUtil.isEmpty(accompanyingPerson.getCarNo()) && ObjectUtil.isNotEmpty(accompanyingPerson.getCarType()));
                if (flag) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "随行人车辆信息不完整");
                }
            }
        }
    }

    @Override
    public void notifyTheApprovalResult(ApprovalResultNotificationQueryDto dto) {
        log.info("预约流程-接收到预约审批结果-接收信息：{}", JSONUtil.toJsonStr(dto));
        // 校验参数
        List<ApprovalResultNotificationDto> approvalResult = dto.getApprovalResult();
        Reservation reservation = null;
        for (ApprovalResultNotificationDto approvalResultNotificationDto : approvalResult) {
            String reservationId = approvalResultNotificationDto.getReservationId();
            reservation = getById(reservationId);
            if (ObjectUtil.isEmpty(reservation)) {
                throw new ServiceException(ResultCode.NOT_EXIST, "预约记录");
            }
        }
        // 更新状态
        for (ApprovalResultNotificationDto approvalResultNotificationDto : approvalResult) {
            String reservationId = approvalResultNotificationDto.getReservationId();
            LambdaUpdateWrapper<Reservation> updateWrapper = new LambdaUpdateWrapper<Reservation>()
                    .eq(Reservation::getId, reservationId);
            if (VariousConstant.APPROVE.equals(approvalResultNotificationDto.getResult())) {
                updateWrapper.set(Reservation::getReservationState, CommonConstant.PASS);
            } else {
                updateWrapper.set(Reservation::getReservationState, CommonConstant.REFUSE);
            }
            boolean update = update(updateWrapper);
            //添加人员以及车辆
            Person person = reservationAddPerson(reservation);
            log.info("添加人员成功，人员id：{}", person.getId());
            if (StrUtil.isNotEmpty(reservation.getVisitorCarNo())) {
                // 添加车辆
                Car car = reservationAddCar(reservation, person.getId());
                log.info("添加车辆成功，车辆id：{}", car.getId());
            }
            log.info("预约流程-更新预约信息记录ID：{}，审批状态为：{}，更新结果：{}", reservationId, VariousConstant.APPROVE.equals(approvalResultNotificationDto.getResult()) ? "3" : "2", update ? "更新成功" : "更新失败");

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cardOpen(CardOpenQueryDto dto) {
        log.info("预约流程-接收到开卡请求-接收信息：{}，操作共 6 步", JSONUtil.toJsonStr(dto));
        // 添加人员
        Reservation reservation = checkCardOpenQueryDto(dto);
        this.lambdaUpdate().set(Reservation::getReservationState, CommonConstant.OPENING_CARD).eq(Reservation::getId, reservation.getId()).update();

        Person person = reservationAddPerson(reservation);
        // 绑定卡片
        reservationBindCard(reservation, dto.getCardNo(), person);
        // 人员授权
        reservationAuthorization(reservation, dto.getSecurityDeviceIdList(), person.getId());
        if (StrUtil.isNotEmpty(reservation.getVisitorCarNo())) {
            // 添加车辆
            Car car = reservationAddCar(reservation, person.getId());
            // 车辆授权
            reservationAuthorizationCar(reservation, car, dto.getParkIdList());
        }
        // 更改预约状态：开卡状态
        reservationUpdateState(reservation.getId());
        securityRedisUtil.deleteObject("reservationOpenCardNo-" + person.getId().toString());
    }

    /**
     * 开卡-更新开卡操作结果状态
     * @param reservationId 预约ID
     */
    private void reservationUpdateState(Long reservationId) {
        boolean update = this.lambdaUpdate().set(Reservation::getReservationState, "4").eq(Reservation::getId, reservationId).update();
        if (!update) {
            log.info("开卡操作-更改卡状态失败，卡ID：{}", reservationId);
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "更改开卡状态失败");
        }
        log.info("6.开卡流程-更改预约状态成功");
    }

    /**
     * 开卡-车辆授权
     * @param reservation 预约信息
     * @param car 车辆信息
     * @param parkIdList 车场ID列表
     */
    private void reservationAuthorizationCar(Reservation reservation, Car car, String[] parkIdList) {
        carService.authorizeWithPark(
                parkIdList,
                car.getId(),
                LocalDateTimeUtil.format(reservation.getReservationStartTime(), "yyyy-MM-dd HH:mm:ss"),
                LocalDateTimeUtil.format(reservation.getReservationEndTime(), "yyyy-MM-dd HH:mm:ss")
        );
        updateReservationOpenCardState(reservation, "5", "1");
        log.info("5.开卡流程-车辆授权成功");
    }

    /**
     * 开卡-添加车辆
     * @param reservation 预约信息
     * @param personId 人员ID
     * @return 车辆信息
     */
    private Car reservationAddCar(Reservation reservation, Long personId) {
        log.info("访客填写了车辆信息，{}", reservation.getVisitorCarNo());
        Car car = carService.lambdaQuery().eq(Car::getCarNo, reservation.getVisitorCarNo()).one();
        if (ObjectUtil.isEmpty(car)) {
            // 车辆不存在，添加车辆
            CarAddAndUpdateReqDto build = CarAddAndUpdateReqDto.builder()
                    .carNo(reservation.getVisitorCarNo())
                    .carType(NumberUtil.parseInt(reservation.getVisitorCarType(), 0))  // 使用Hutool的NumberUtil
                    .personId(personId.toString())
                    .build();
            carService.addCar(build);
        }
        updateReservationOpenCardState(reservation, CommonConstant.ADD_CAR, VariousConstant.RESERVATION_SUCCESS_STATE);
        log.info("4.开卡流程-添加车辆成功");
        return car;
    }

    /**
     * 开卡-人员授权
     * @param reservation
     * @param securityDeviceIdList
     * @param personId
     */
    private void reservationAuthorization(Reservation reservation, Long[] securityDeviceIdList, Long personId) {
        // 人员授权
        PersonAuthorizationQueryDto authQueryDto = PersonAuthorizationQueryDto.builder()
                .personIdList(new Long[]{personId})
                .doorDeviceIdList(securityDeviceIdList)
                .startTime(LocalDateTimeUtil.format(reservation.getReservationStartTime(), "yyyy-MM-dd HH:mm:ss"))
                .endTime(LocalDateTimeUtil.format(reservation.getReservationEndTime(), "yyyy-MM-dd HH:mm:ss"))
                .build();
        personService.personAuthorization(authQueryDto);
        updateReservationOpenCardState(reservation, "3", "1");
        log.info("3.开卡流程-人员授权成功");
    }

    /**
     * 开卡-绑定卡片
     * @param reservation
     * @param cardNo
     * @param person
     */
    private void reservationBindCard(Reservation reservation, String cardNo, Person person) {
        // 绑定卡片
        if (!securityRedisUtil.hasKey(VariousConstant.RESERVATION_CARD_ADD_FLAG + person.getId().toString())) {
            Card card = cardService.lambdaQuery().eq(Card::getPersonId, person.getId()).eq(Card::getCardNo, cardNo).eq(Card::getCardType, CommonConstant.CARD_TEMP).one();
            if (ObjectUtil.isEmpty(card)) {
                card = Card.builder()
                        .cardType(CommonConstant.CARD_TEMP)
                        .cardState(CommonConstant.CARD_NORMAL)
                        .cardNo(cardNo)
                        .personId(person.getId()).build();
                ArrayList<Card> cards = CollUtil.newArrayList(card);
                cardService.bindingCard(cards, person.getPersonSourceIndex());
                securityRedisUtil.setCacheObject("reservationOpenCardNo-" + person.getId().toString(), cardNo);
            }
        }
        updateReservationOpenCardState(reservation, CommonConstant.BIND_CARD, VariousConstant.RESERVATION_SUCCESS_STATE);
        log.info("2.开卡流程-绑定卡片成功");
    }

    /**
     * 开卡-添加人员
     * @param reservation
     * @return
     */
    private Person reservationAddPerson(Reservation reservation) {
        Person person = personService.lambdaQuery()
                .eq(Person::getPersonCertificateType, CommonConstant.ID_CARD)
//                .eq(Person::getPersonName, reservation.getVisitorName())
                .eq(Person::getPersonCertificateNum, reservation.getVisitorCertification()).one();
        if (ObjectUtil.isEmpty(person)) {
            // 人员不存在，添加人员
            PersonAddOrUpdateQueryDto personAddOrUpdateQueryDto = PersonAddOrUpdateQueryDto.builder()
                    .personType(CommonConstant.VISITOR)
                    .personCertificateType(CommonConstant.ID_CARD)
                    .personSex(reservation.getVisitorSex())
                    .personName(reservation.getVisitorName())
                    .personUnit(reservation.getVisitorUnit())
                    .personPhone(reservation.getVisitorPhone())
                    .personFaceUrl(reservation.getPersonFaceUrl())
                    .personCertificateNum(reservation.getVisitorCertification()).build();
            personService.addPerson(personAddOrUpdateQueryDto);
            person = personService.lambdaQuery()
                    .eq(Person::getPersonCertificateType, CommonConstant.ID_CARD)
                    .eq(Person::getPersonName, reservation.getVisitorName())
                    .eq(Person::getPersonCertificateNum, reservation.getVisitorCertification()).one();
        }
        updateReservationOpenCardState(reservation, CommonConstant.ADD_PERSON, VariousConstant.RESERVATION_SUCCESS_STATE);
        log.info("1.开卡流程-添加人员成功");
        return person;
    }

    /**
     * 更新预约开卡状态
     * @param reservation 预约信息
     * @param updateType 更新类型 1：添加人员 2：绑定卡片 3：人员授权 4：添加车辆 5：车辆授权
     * @param state 状态 1：已完成 0：未完成
     */
    private void updateReservationOpenCardState(Reservation reservation, String updateType, String state) {
        switch (updateType) {
            case CommonConstant.ADD_PERSON:
                if (this.lambdaUpdate().set(Reservation::getPersonAddFlag, state).eq(Reservation::getId, reservation.getId()).update()) {
                    log.info("开卡操作-更改预约开卡状态-添加人员成功，预约ID：{}", reservation.getId());
                } else {
                    log.info("开卡操作-更改预约开卡状态-添加人员失败，预约ID：{}", reservation.getId());
                }
                break;
            case CommonConstant.BIND_CARD:
                if (this.lambdaUpdate().set(Reservation::getCardAddFlag, state).eq(Reservation::getId, reservation.getId()).update()) {
                    log.info("开卡操作-更改预约开卡状态-绑定卡片成功，预约ID：{}", reservation.getId());
                } else {
                    log.info("开卡操作-更改预约开卡状态-绑定卡片失败，预约ID：{}", reservation.getId());
                }
                break;
            case CommonConstant.PERSON_AUTH:
                if (this.lambdaUpdate().set(Reservation::getPersonAuthFlag, state).eq(Reservation::getId, reservation.getId()).update()) {
                    log.info("开卡操作-更改预约开卡状态-人员授权成功，预约ID：{}", reservation.getId());
                } else {
                    log.info("开卡操作-更改预约开卡状态-人员授权失败，预约ID：{}", reservation.getId());
                }
                break;
            case CommonConstant.ADD_CAR:
                if (this.lambdaUpdate().set(Reservation::getCarAddFlag, state).eq(Reservation::getId, reservation.getId()).update()) {
                    log.info("开卡操作-更改预约开卡状态-添加车辆成功，预约ID：{}", reservation.getId());
                } else {
                    log.info("开卡操作-更改预约开卡状态-添加车辆失败，预约ID：{}", reservation.getId());
                }
                break;
            case CommonConstant.CAR_AUTH:
                if (this.lambdaUpdate().set(Reservation::getCarAuthFlag, state).eq(Reservation::getId, reservation.getId()).update()) {
                    log.info("开卡操作-更改预约开卡状态-车辆授权成功，预约ID：{}", reservation.getId());
                } else {
                    log.info("开卡操作-更改预约开卡状态-车辆授权失败，预约ID：{}", reservation.getId());
                }
                break;
            case CommonConstant.RESERVATION_STATE:
                if (this.lambdaUpdate().set(Reservation::getReservationState, state).eq(Reservation::getId, reservation.getId()).update()) {
                    log.info("开卡操作-更改预约开卡状态-开卡成功，预约ID：{}", reservation.getId());
                } else {
                    log.info("开卡操作-更改预约开卡状态-开卡失败，预约ID：{}", reservation.getId());
                }
            default:
                break;
        }
    }

    @Override
    public IPage<Reservation> listReservation(ReservationListQueryDto dto) {
        LambdaQueryWrapper<Reservation> queryWrapper = new LambdaQueryWrapper<Reservation>()
                .like(StrUtil.isNotBlank(dto.getVisitorName()), Reservation::getVisitorName, dto.getVisitorName())
                .like(StrUtil.isNotBlank(dto.getVisitorPhone()), Reservation::getVisitorPhone, dto.getVisitorPhone())
                .like(StrUtil.isNotBlank(dto.getVisitorCertification()), Reservation::getVisitorCertification, dto.getVisitorCertification())
                .like(StrUtil.isNotBlank(dto.getVisitorCarNo()), Reservation::getVisitorCarNo, dto.getVisitorCarNo())
                .like(StrUtil.isNotBlank(dto.getVisitorUnit()), Reservation::getVisitorUnit, dto.getVisitorUnit())
                .like(StrUtil.isNotBlank(dto.getReceptionistName()), Reservation::getReceptionistName, dto.getReceptionistName())
                .eq(StrUtil.isNotBlank(dto.getReservationState()), Reservation::getReservationState, dto.getReservationState())
                .eq(StrUtil.isNotBlank(dto.getPersonAuthFlag()), Reservation::getPersonAuthFlag, dto.getPersonAuthFlag())
                .ge(StrUtil.isNotBlank(dto.getReservationStartTime()), Reservation::getReservationStartTime, dto.getReservationStartTime())
                .le(StrUtil.isNotBlank(dto.getReservationEndTime()), Reservation::getReservationStartTime, dto.getReservationEndTime())
                .orderByDesc(Reservation::getCreateTime)
                .orderByAsc(Reservation::getId);
        Page<Reservation> reservationPage = baseMapper.selectPage(new Page<>(dto.getPageIndex(), dto.getPageSize()), queryWrapper);
        reservationPage.getRecords().forEach(res -> res.setVisitorNumber(visitorNumberPrefix + generateReservationNumber(res.getCreateTime(), res.getSerialNumber())));
        return reservationPage;
    }

    /**
     * 生成预约编号
     * @param createTime 创建时间
     * @param serialNumber 自增序号
     * @return 格式：YYMM + 5位序号，例如：250100001
     */
    public String generateReservationNumber(LocalDateTime createTime, Long serialNumber) {
        if (createTime == null) {
            createTime = LocalDateTime.now();
        }

        // 获取年月，格式为YYMM
        String yearMonth = createTime.format(DateTimeFormatter.ofPattern("yyMM"));

        // 将序号格式化为5位数，不足前面补0
        String formattedSerial = String.format("%05d", serialNumber % 100000);

        return yearMonth + formattedSerial;
    }

    private Reservation checkCardOpenQueryDto(CardOpenQueryDto dto) {
        String reservationId = dto.getReservationId();
        Reservation reservation = getById(reservationId);
        Person person = personService.lambdaQuery()
                .eq(Person::getPersonCertificateType, "111")
                .eq(Person::getPersonName, reservation.getVisitorName())
                .eq(Person::getPersonCertificateNum, reservation.getVisitorCertification()).one();
        if (ObjectUtil.isEmpty(reservation)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "预约记录不存在");
        }
        if (VariousConstant.APPROVE_CONFUSED.equals(reservation.getReservationState())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "该预约审批未通过");
        }
        if (VariousConstant.ALREADY_OPEN_CARD.equals(reservation.getReservationState())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "该预约已完成开卡");
        }
        if (VariousConstant.APPROVING.equals(reservation.getReservationState())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "该预约审批中");
        }
        String cardNo = dto.getCardNo();
        String personId = "";
        if (ObjectUtil.isNotEmpty(person)) {
            personId = person.getId().toString();
        }
        if (!securityRedisUtil.hasKey(VariousConstant.RESERVATION_CARD_ADD_FLAG + personId)) {
            Card card = cardService.lambdaQuery().eq(Card::getCardNo, cardNo).one();
            if (ObjectUtil.isNotEmpty(card)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "卡号已存在");
            }
        }
        for (Long deviceId : dto.getSecurityDeviceIdList()) {
            if (ObjectUtil.isEmpty(securityDeviceDoorService.getById(deviceId))) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "设备不存在");
            }
        }
        if (ArrayUtil.isNotEmpty(dto.getParkIdList())) {
            for (String parkId : dto.getParkIdList()) {
                if (ObjectUtil.isEmpty(securityParkService.getById(parkId))) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "停车场不存在");
                }
            }
        }
        return reservation;
    }

    @Override
    public void reservationAddPerson(CardOpenQueryDto dto) {
        Reservation reservation = this.getById(dto.getReservationId());
        reservationAddPerson(reservation);
        String cardAddFlag = reservation.getCardAddFlag();
        String personAuthFlag = reservation.getPersonAuthFlag();
        String carAddFlag = reservation.getCarAddFlag();
        String carAuthFlag = reservation.getCarAuthFlag();
        if (VariousConstant.RESERVATION_SUCCESS_STATE.equals(cardAddFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(personAuthFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(carAddFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(carAuthFlag)) {
            updateReservationOpenCardState(reservation, CommonConstant.RESERVATION_STATE, CommonConstant.OPENED_CARD);
        }
    }

    @Override
    public void reservationAddCard(CardOpenQueryDto dto) {
        Reservation reservation = this.getById(dto.getReservationId());
        Person person = personService.lambdaQuery()
                .eq(Person::getPersonCertificateType, "111")
                .eq(Person::getPersonName, reservation.getVisitorName())
                .eq(Person::getPersonCertificateNum, reservation.getVisitorCertification()).one();
        if (ObjectUtil.isEmpty(reservation)) {
            throw new ServiceException(ResultCode.NOT_EXIST, "预约记录");
        }
        if (ObjectUtil.isEmpty(person)) {
            throw new ServiceException(ResultCode.NEED_CREATE_PERSON_FIRST);
        }
        reservationBindCard(reservation, dto.getCardNo(), person);
        String personAddFlag = reservation.getPersonAddFlag();
        String personAuthFlag = reservation.getPersonAuthFlag();
        String carAddFlag = reservation.getCarAddFlag();
        String carAuthFlag = reservation.getCarAuthFlag();
        if (VariousConstant.RESERVATION_SUCCESS_STATE.equals(personAddFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(personAuthFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(carAddFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(carAuthFlag)) {
            updateReservationOpenCardState(reservation, "6", "4");
        }
    }

    @Override
    public void reservationPersonAuth(CardOpenQueryDto dto) {
        Reservation reservation = this.getById(dto.getReservationId());
        Person person = personService.lambdaQuery()
                .eq(Person::getPersonCertificateType, "111")
                .eq(Person::getPersonName, reservation.getVisitorName())
                .eq(Person::getPersonCertificateNum, reservation.getVisitorCertification()).one();
        if (ObjectUtil.isEmpty(reservation)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "预约记录不存在");
        }
        if (ObjectUtil.isEmpty(person)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "请先创建人员");
        }
        reservationAuthorization(reservation, dto.getSecurityDeviceIdList(), person.getId());
        String personAddFlag = reservation.getPersonAddFlag();
        String cardAddFlag = reservation.getCardAddFlag();
        String carAddFlag = reservation.getCarAddFlag();
        String carAuthFlag = reservation.getCarAuthFlag();
        if (VariousConstant.RESERVATION_SUCCESS_STATE.equals(personAddFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(cardAddFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(carAddFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(carAuthFlag)) {
            updateReservationOpenCardState(reservation, "6", "4");
        }
    }

    @Override
    public void reservationCarAdd(CardOpenQueryDto dto) {
        Reservation reservation = this.getById(dto.getReservationId());
        Person person = personService.lambdaQuery()
                .eq(Person::getPersonCertificateType, "111")
                .eq(Person::getPersonName, reservation.getVisitorName())
                .eq(Person::getPersonCertificateNum, reservation.getVisitorCertification()).one();
        if (ObjectUtil.isEmpty(reservation)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "预约记录不存在");
        }
        if (ObjectUtil.isEmpty(person)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "请先创建人员");
        }
        reservationAddCar(reservation, person.getId());
        String personAddFlag = reservation.getPersonAddFlag();
        String cardAddFlag = reservation.getCardAddFlag();
        String personAuthFlag = reservation.getPersonAuthFlag();
        String carAuthFlag = reservation.getCarAuthFlag();
        if (VariousConstant.RESERVATION_SUCCESS_STATE.equals(personAddFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(cardAddFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(personAuthFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(carAuthFlag)) {
            updateReservationOpenCardState(reservation, "6", "4");
        }
    }

    @Override
    public void reservationCarAuth(CardOpenQueryDto dto) {
        Reservation reservation = this.getById(dto.getReservationId());
        Person person = personService.lambdaQuery()
                .eq(Person::getPersonCertificateType, "111")
                .eq(Person::getPersonName, reservation.getVisitorName())
                .eq(Person::getPersonCertificateNum, reservation.getVisitorCertification()).one();
        if (ObjectUtil.isEmpty(reservation)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "预约记录不存在");
        }
        if (ObjectUtil.isEmpty(person)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "请先创建人员");
        }
        Car car = carService.lambdaQuery().eq(Car::getCarNo, reservation.getVisitorCarNo()).one();
        if (ObjectUtil.isEmpty(car)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "请先创建车辆");
        }
        reservationAuthorizationCar(reservation, car, dto.getParkIdList());
        String personAddFlag = reservation.getPersonAddFlag();
        String cardAddFlag = reservation.getCardAddFlag();
        String personAuthFlag = reservation.getPersonAuthFlag();
        String carAddFlag = reservation.getCarAddFlag();
        if (VariousConstant.RESERVATION_SUCCESS_STATE.equals(personAddFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(cardAddFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(personAuthFlag)
                && VariousConstant.RESERVATION_SUCCESS_STATE.equals(carAddFlag)) {
            updateReservationOpenCardState(reservation, "6", "4");
        }
    }

    @Override
    public void deleteVisitorReservation(ReservationBatchDelQueryDto dto) {
        //删除预约信息
        this.removeBatchByIds(Arrays.asList(dto.getReservationIdList()));
    }

    //访客数据增量同步
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReservationSynMsgVo synchronizingVisitorData() {
        // 1. 获取MIS系统访客数据
        MisVisitorListDTO misVisitorListDTO = getMisVisitorData();
        if (misVisitorListDTO == null || misVisitorListDTO.getData() == null) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "获取访客数据为空");
        }

        // 2. 转换为Reservation列表
        List<Reservation> newDataList = convertToReservations(misVisitorListDTO.getData());

        // 3. 增量同步数据
        return syncReservations(newDataList);
    }

    /**
     * 获取MIS系统访客数据
     */
    private MisVisitorListDTO getMisVisitorData() {
        try {
            MisVisitorListDTO misVisitorListDTO;
            String jsonResult = HttpUtil.get(misUrl + "/visitorinfo/listall", 6000);
//            String jsonResult = JsonFileReader.readJsonFile("E:\\project\\haimen\\business-server\\security-protection\\src\\main\\resources\\data.json");

            misVisitorListDTO = JSONObject.parseObject(jsonResult, MisVisitorListDTO.class);
            log.info("MIS系统访客数据条数：{} 条", misVisitorListDTO.getData().size());
            if (misVisitorListDTO.getCode() != 200) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "调用mis基建系统获取数据失败");
            }
            return misVisitorListDTO;
        } catch (Exception e) {
            log.error("调用mis基建系统获取访客数据失败", e);
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "调用mis基建系统获取访客数据失败");
        }
    }

    /**
     * 将MIS访客数据转换为Reservation列表
     */
    private List<Reservation> convertToReservations(List<MisVisitorMainDTO> visitorMains) {
        //车辆类型中文转对应的code
        typeCodeMapping(visitorMains);

        return visitorMains.stream()
                .flatMap(visitorMain -> {
                    // 获取所有访客信息
                    List<MisVisitorInfoDTO> visitorInfoList = visitorMain.getVisitorInfoList();
                    if (CollUtil.isEmpty(visitorInfoList)) {
                        // 如果没有访客信息，创建一个基本预约记录
                        return Stream.of(createReservation(visitorMain, null));
                    }

                    // 为每个访客信息创建预约记录
                    return visitorInfoList.stream()
                            .map(visitorInfo -> createReservation(visitorMain, visitorInfo));
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 创建单个预约记录
     */
    private Reservation createReservation(MisVisitorMainDTO visitorMain, MisVisitorInfoDTO visitorInfo) {
        // 生成唯一的reservationId
        String uniqueReservationId = visitorMain.getId();
        if (ObjectUtil.isNotEmpty(visitorInfo)) {
            // 或者使用其他唯一标识组合
            uniqueReservationId = uniqueReservationId + "_" + visitorInfo.getVisitorCertification();
        }
        // 性别转换
        String sex = null;
        if (ObjectUtil.isNotEmpty(visitorInfo) && visitorInfo.getVisitorSex() != null) {
            VisitorSexEnum sexEnum = VisitorSexEnum.fromDescription(visitorInfo.getVisitorSex());
            if (sexEnum != null) {
                sex = sexEnum.getCode();
            }
        }

        // 使用Builder构建预约记录
        Reservation reservation = Reservation.builder()
                .reservationId(uniqueReservationId)
                // 新增系统相关字段
                .sysPostId(visitorMain.getSysPostId())
                .sysDivisionId(visitorMain.getSysDivisionId())
                .sysOrgId(visitorMain.getSysOrgId())
                .sysDeleteflag(visitorMain.getSysDeleteflag())

                // 访客基本信息
                .visitorName(visitorInfo != null ? visitorInfo.getVisitorName() : null)
                .visitorSex(sex)
                .visitorPhone(visitorInfo != null ? visitorInfo.getVisitorPhone() : null)
                .visitorCertification(visitorInfo != null ? visitorInfo.getVisitorCertification() : null)
                .visitorCarNo(visitorInfo != null ? visitorInfo.getVisitorCarNo() : null)
                .visitorCarType(visitorInfo != null ? visitorInfo.getVisitorCarType() : null)

                // 访客单位和部门信息
                .visitorUnit(visitorMain.getVisitComp())
                .source(visitorMain.getSource())
                .receptionistName(visitorMain.getTfrPerson())
                .receptionistPhone(visitorMain.getPhoneNumber())
                .receptionistDepartment(visitorMain.getTfrComp())
                .tfrPersonId(visitorMain.getTfrPersonId())
                .tfrCompId(visitorMain.getTfrCompId())
                .visitorReason(visitorMain.getVisitReason())

                // 预约时间
                .reservationStartTime(visitorMain.getVisitStartTime() != null ?
                        LocalDateTime.ofInstant(visitorMain.getVisitStartTime().toInstant(), ZoneId.systemDefault()) : null)
                .reservationEndTime(visitorMain.getVisitEndTime() != null ?
                        LocalDateTime.ofInstant(visitorMain.getVisitEndTime().toInstant(), ZoneId.systemDefault()) : null)

                // 流程相关字段
                .flowInstanceId(visitorMain.getFlowInstanceId())
                .flowId(visitorMain.getFlowId())
                .flowStartflag(visitorMain.getFlowStartflag())
                .reservationState(convertFlowBizstateToReservationState(visitorMain.getFlowBizstate()))

                // 携带物品信息
                .personCarryItems(visitorInfo != null ? visitorInfo.getVisitorBelongings() : null)
                .transportGoods(visitorInfo != null ? visitorInfo.getVisitorCarrythings() : null)
                .build();

        // 设置BaseEntity的字段
        reservation.setCreateBy(visitorMain.getSysCreateby());
        reservation.setUpdateBy(visitorMain.getSysUpdateby());
        reservation.setCreateTime(visitorMain.getSysCreatetime() != null ?
                LocalDateTime.ofInstant(visitorMain.getSysCreatetime().toInstant(), ZoneId.systemDefault()) : null);
        reservation.setUpdateTime(visitorMain.getSysUpdatetime() != null ?
                LocalDateTime.ofInstant(visitorMain.getSysUpdatetime().toInstant(), ZoneId.systemDefault()) : null);

        return reservation;
    }

    /**
     * 车辆类型中文转对应的code
     * @param visitorMains
     */
    private void typeCodeMapping(List<MisVisitorMainDTO> visitorMains) {
        // 获取车辆类型的映射关系 ( name -> typeCode)
        Map<String, String> typeNameMap = commonTypeService.list().stream()
                .filter(type -> CommonConstant.TYPE_SIX.equals(type.getCategory()))  // 6是车辆类型
                .collect(Collectors.toMap(
                        CommonType::getName,
                        CommonType::getTypeCode,
                        (v1, v2) -> v1
                ));

        // 设置类型名称
        visitorMains.forEach(vo -> {
            vo.getVisitorInfoList().forEach(info -> {
                String typeCode = typeNameMap.get(info.getVisitorCarType());
                if (StrUtil.isNotEmpty(typeCode)) {
                    info.setVisitorCarType(typeCode);
                }
            });
        });
    }


    /**
     * 同步Reservation数据
     */
    private ReservationSynMsgVo syncReservations(List<Reservation> newDataList) {
        // 1. 获取未删除的ID列表
        List<String> ids = newDataList.stream()
                .filter(data -> data.getSysDeleteflag() != null && !data.getSysDeleteflag())
                .map(Reservation::getReservationId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(ids)) {
            log.warn("No valid reservation IDs found in new data");
            return ReservationSynMsgVo.builder()
                    .message("没有有效的预约数据")
                    .build();
        }

        // 2. 查询已存在的数据
        List<Reservation> existingList = this.lambdaQuery().in(Reservation::getReservationId, ids).list();

        // 3. 将已存在的数据转为Map，方便获取ID
        Map<String, Reservation> existingMap = existingList.stream()
                .collect(Collectors.toMap(Reservation::getReservationId, Function.identity()));

        // 4. 分类处理：新增和更新
        List<Reservation> toInsert = new ArrayList<>();
        List<Reservation> toUpdate = new ArrayList<>();

        for (Reservation newData : newDataList) {
            String reservationId = newData.getReservationId();
            if (reservationId == null) {
                log.warn("Found reservation with null ID: {}", newData);
                continue;
            }

            Reservation existingData = existingMap.get(reservationId);
            if (existingData != null) {
                // 已存在，设置ID后更新
                newData.setId(existingData.getId());  // 设置主键ID
                toUpdate.add(newData);
            } else {
                // 不存在，新增
                toInsert.add(newData);
            }
        }

        // 5. 批量处理
        BatchExecuteResult<Reservation> insertResult = new BatchExecuteResult<>();
        BatchExecuteResult<Reservation> updateResult = new BatchExecuteResult<>();

        // 处理新增
        if (!toInsert.isEmpty()) {
            log.info("Processing {} records for insert", toInsert.size());
            insertResult = executeBatch(toInsert, sqlSessionFactory, true);
            for (Reservation res : insertResult.getSuccessList()) {
                res.setVisitorPhone(PhoneNumberUtils.generateRandomPhoneNumber());
                Person person = reservationAddPerson(res);
                if (StrUtil.isNotEmpty(res.getVisitorCarNo())) {
                    reservationAddCar(res, person.getId());
                }
            }
        }

        // 处理更新
        if (!toUpdate.isEmpty()) {
            log.info("Processing {} records for update", toUpdate.size());
            updateResult = executeBatch(toUpdate, sqlSessionFactory, false);
        }

        // 6. 构建返回结果
        return ReservationSynMsgVo.builder()
                .insertSuccessNum(insertResult.getSuccessCount())
                .insertFailNum(insertResult.getFailCount())
                .updateSuccessNum(updateResult.getSuccessCount())
                .updateFailNum(updateResult.getFailCount())
                .insertOrUpdateFailList(Stream.concat(
                        insertResult.getFailList().stream(),
                        updateResult.getFailList().stream()
                ).map(e -> e.getReservationId() + " " + e.getVisitorName()).collect(Collectors.toList()))
                .message("同步完成")
                .build();
    }

    /**
     * 将MIS系统流程状态转换为预约状态
     */
    private String convertFlowBizstateToReservationState(String flowBizstate) {
        if (StrUtil.isBlank(flowBizstate)) {
            return CommonConstant.UN_APPROVED;  // 未审核
        }
        return switch (flowBizstate) {
            case CommonConstant.FLOWSTATE_GOING -> CommonConstant.UNDER_APPROVAL;   // 审核中
            case CommonConstant.FLOWSTATE_FINISH -> CommonConstant.PASS;  // 审核通过
            default -> CommonConstant.UN_APPROVED;  // 其他情况默认为未审核
        };
    }

    /**
     * 复用人员服务 人员授权
     * @param dto
     * @return
     */
    @Override
    public Map<String, AuthResultDto> personAuthorization(PersonAuthorizationQueryDto dto) {
        //根据访客信息id 查对应安防人员id然后替换
        List<Reservation> list = this.lambdaQuery().in(Reservation::getId, dto.getPersonIdList()).list();
        List<String> certificationList = list.stream().map(Reservation::getVisitorCertification).collect(Collectors.toList());
        List<Person> personList = personService.lambdaQuery().in(Person::getPersonCertificateNum, certificationList).list();
        Long[] personIdList = personList.stream().map(Person::getId).toArray(Long[]::new);
        dto.setPersonIdList(personIdList);
        //授权
        Map<String, AuthResultDto> stringAuthResultDtoMap = personService.personAuthorization(dto);
        //更新访客授权标记
        stringAuthResultDtoMap.forEach((k, v) -> {
            List<String> succeed = v.getSucceed();
            if (CollectionUtils.isNotEmpty(v.getSucceed())) {
                String personId = k.substring(k.lastIndexOf('-') + 1);
                for (Person person : personList) {
                    if (person.getId().toString().equals(personId)) {
                        Reservation reservation = list.stream()
                                .filter(r -> r.getVisitorCertification().equals(person.getPersonCertificateNum()))
                                .findFirst()
                                .orElse(null);
                        if (person.getPersonCertificateNum().equals(reservation.getVisitorCertification())) {
                            this.lambdaUpdate().set(Reservation::getPersonAuthFlag, VariousConstant.RESERVATION_SUCCESS_STATE).eq(Reservation::getVisitorCertification, reservation.getVisitorCertification()).update();
                        }
                    }
                }
            }
        });
        return stringAuthResultDtoMap;
    }

    @Override
    public Sync4aPersonVo editPersonAuthorization(EditPersonAuthorizationQueryDto dto) {
        //根据访客信息id 查对应安防人员id然后替换
        Reservation reservation = this.getById(dto.getPersonId());
        Person one = personService.lambdaQuery().in(Person::getPersonCertificateNum, reservation.getVisitorCertification()).one();
        dto.setPersonId(one.getId());
        Sync4aPersonVo sync4aPersonVo = personService.editPersonAuthorization(dto);
        return sync4aPersonVo;
    }

    @Override
    public List<PersonAuthedDeviceDto> getPersonAuthedDevice(Long personId) {
        //根据访客信息id 查对应安防人员id然后替换
        Reservation reservation = this.getById(personId);
        Person one = personService.lambdaQuery().in(Person::getPersonCertificateNum, reservation.getVisitorCertification()).one();
        List<?> rawList = personService.getPersonAuthedDevice(one.getId());

        // 使用JSON转换解决类型问题
        if (CollUtil.isNotEmpty(rawList)) {
            try {
                String jsonString = JSON.toJSONString(rawList);
                return JSON.parseArray(jsonString, PersonAuthedDeviceDto.class);
            } catch (Exception e) {
                log.error("转换PersonAuthedDeviceDto列表失败: {}", e.getMessage());
            }
        }
        return new ArrayList<>();
    }

    @Override
    public void exportPerson(ReservationExportRequestDto dto, HttpServletResponse response) {
        if (CollUtil.isEmpty(dto.getIds())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "请选择需要导出的访客");
        }

        // 根据ID查询访客信息
        List<Reservation> records = this.lambdaQuery()
                .in(Reservation::getId, dto.getIds())
                .list();

        if (CollUtil.isEmpty(records)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "未找到需要导出的访客信息");
        }

        try {
            // 1. 获取所有类型并创建映射
            List<CommonType> types = commonTypeService.list();
            Map<String, String> carTypeMap = getTypeNameMap(types, CommonConstant.TYPE_SIX);

            // 2. 设置导出参数
            ExportParams exportParams = new ExportParams("访客数据", "访客数据");
            exportParams.setType(ExcelType.XSSF);

            // 3. 准备导出数据
            List<ReservationExportDto> exportDtos = records.stream()
                    .map(reservation -> {
                        ReservationExportDto exportDto = new ReservationExportDto();
                        BeanUtils.copyProperties(reservation, exportDto);

                        // 处理时间格式
                        if (reservation.getReservationStartTime() != null) {
                            exportDto.setReservationStartTime(reservation.getReservationStartTime()
                                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        }
                        if (reservation.getReservationEndTime() != null) {
                            exportDto.setReservationEndTime(reservation.getReservationEndTime()
                                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        }

                        // 映射车辆类型
                        if (StrUtil.isNotBlank(reservation.getVisitorCarType())) {
                            exportDto.setVisitorCarType(carTypeMap.getOrDefault(
                                    reservation.getVisitorCarType(),
                                    reservation.getVisitorCarType()
                            ));
                        }

                        return exportDto;
                    })
                    .collect(Collectors.toList());

            // 4. 导出Excel
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, ReservationExportDto.class, exportDtos);

            // 5. 下载文件
            downLoadExcel("访客数据", response, workbook);

            log.info("访客数据导出完成，共导出 {} 条记录", records.size());

        } catch (Exception e) {
            log.error("导出访客数据失败: ", e);
            throw new ServiceException(ResultCode.EXPORT_FAIL, e);
        }
    }

    @Override
    public List<Long> getAllExportDataIds(ReservationListQueryDto dto) {
        dto.setPageIndex(1);
        dto.setPageSize(Integer.MAX_VALUE);
        List<Long> collect = listReservation(dto).getRecords().stream().map(reservation -> reservation.getId()).collect(Collectors.toList());
        return collect;
    }

    /**
     * 创建code->name的映射
     */
    private Map<String, String> getTypeNameMap(List<CommonType> types, String category) {
        return types.stream()
                .filter(type -> category.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode, // key是code
                        CommonType::getName,     // value是name
                        (v1, v2) -> v1
                ));
    }

    /**
     * 下载Excel
     */
    private void downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String encodedFileName = URLEncoder.encode(fileName + ".xlsx", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + encodedFileName);
            workbook.write(response.getOutputStream());
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    @Override
    public Sync4aPersonVo deletePersonAuthorization(DeletePersonAuthorizationQueryDto dto) {
        //根据访客信息id 查对应安防人员id然后替换
        Reservation reservation = this.getById(dto.getPersonId());
        Person one = personService.lambdaQuery().in(Person::getPersonCertificateNum, reservation.getVisitorCertification()).one();
        dto.setPersonId(one.getId());
        Sync4aPersonVo sync4aPersonVo = personService.deletePersonAuthorization(dto);
        this.lambdaUpdate().set(Reservation::getPersonAuthFlag, CommonConstant.TYPE_ZEOR).eq(Reservation::getId, reservation.getId()).update();
        return sync4aPersonVo;
    }


    /**
     * 执行批量操作
     */
    public BatchExecuteResult<Reservation> executeBatch(List<Reservation> dataList,
                                                        SqlSessionFactory sqlSessionFactory, boolean isInsert) {
        if (CollUtil.isEmpty(dataList)) {
            return new BatchExecuteResult<>();
        }

        String operationType = isInsert ? "新增" : "更新";
        log.info("开始批量{}数据，数量：{}", operationType, dataList.size());

        MybatisBatch<Reservation> mybatisBatch = new MybatisBatch<>(sqlSessionFactory, dataList);
        MybatisBatch.Method<Reservation> method = new MybatisBatch.Method<>(ReservationMapper.class);
        List<BatchResult> results = mybatisBatch.execute(
                isInsert ? method.insert() : method.updateById()
        );

        return processResults(dataList, results, operationType);
    }

    /**
     * 处理批量操作结果
     */
    private BatchExecuteResult<Reservation> processResults(List<Reservation> dataList,
                                                           List<BatchResult> results, String operationType) {
        BatchExecuteResult<Reservation> executeResult = new BatchExecuteResult<>();
        executeResult.setTotalCount(dataList.size());

        int dataIndex = 0;
        for (BatchResult result : results) {
            for (int count : result.getUpdateCounts()) {
                Reservation reservation = dataList.get(dataIndex);
                if (count == 1) {
                    executeResult.getSuccessList().add(reservation);
                    executeResult.setSuccessCount(executeResult.getSuccessCount() + 1);
                    log.info("记录{}成功，ID: {}", operationType, reservation.getId());
                } else {
                    executeResult.getFailList().add(reservation);
                    executeResult.setFailCount(executeResult.getFailCount() + 1);
                    log.error("记录{}失败，ID: {}", operationType, reservation.getId());
                }
                dataIndex++;
            }
        }

        return executeResult;
    }

    /**
     * 生成随机手机号
     * 号段参考:
     * 移动: 134,135,136,137,138,139,150,151,152,157,158,159,182,183,184,187,188,147,178
     * 联通: 130,131,132,155,156,185,186,145,176
     * 电信: 133,153,180,181,189,177
     */
    public String generateRandomPhone() {
        // 手机号前三位的号段
        String[] phonePrefix = {
                "130", "131", "132", "133", "134", "135", "136", "137", "138", "139",
                "150", "151", "152", "153", "155", "156", "157", "158", "159",
                "176", "177", "178",
                "180", "181", "182", "183", "184", "185", "186", "187", "188", "189"
        };

        // 随机选择号段
        Random random = new Random();
        String prefix = phonePrefix[random.nextInt(phonePrefix.length)];

        // 生成后8位随机数
        StringBuilder builder = new StringBuilder(prefix);
        for (int i = 0; i < 8; i++) {
            builder.append(random.nextInt(10));
        }

        return builder.toString();
    }
}
