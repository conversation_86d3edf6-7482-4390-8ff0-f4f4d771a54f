package com.teamway.security.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.common.constant.CommonConstant;
import com.teamway.security.entity.ScreenDataMaintenanceConfig;
import com.teamway.security.mapper.ScreenDataMaintenanceConfigMapper;
import com.teamway.security.service.ScreenDataMaintenanceConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClossName ScreenDataMaintenanceConfigServiceImpl
 * @Description 大屏数据维护配置表，存储大屏数据配置及维护信息 服务实现类
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
@Service
public class ScreenDataMaintenanceConfigServiceImpl extends ServiceImpl<ScreenDataMaintenanceConfigMapper, ScreenDataMaintenanceConfig> implements ScreenDataMaintenanceConfigService {

    @Autowired
    private ScreenDataMaintenanceConfigMapper screenDataMaintenanceConfigMapper;

    @Override
    public Boolean insertOrUpdate(ScreenDataMaintenanceConfig screenDataMaintenanceConfigVo) {
        return screenDataMaintenanceConfigMapper.insertOrUpdate(screenDataMaintenanceConfigVo);
    }

    @Override
    public ScreenDataMaintenanceConfig selectConfig() {
        return screenDataMaintenanceConfigMapper.selectOne(new LambdaQueryWrapper<ScreenDataMaintenanceConfig>().eq(ScreenDataMaintenanceConfig::getConfigVersion, CommonConstant.FIRST_VERSION));
    }
}
