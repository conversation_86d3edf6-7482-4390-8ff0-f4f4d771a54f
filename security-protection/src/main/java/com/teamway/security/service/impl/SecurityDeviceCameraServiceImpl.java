package com.teamway.security.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CameraPreset;
import com.teamway.base.entity.CommonType;
import com.teamway.base.entity.Region;
import com.teamway.base.service.CameraPresetService;
import com.teamway.base.service.CameraService;
import com.teamway.base.service.CommonTypeService;
import com.teamway.base.service.RegionService;
import com.teamway.center.entity.AlarmCenter;
import com.teamway.common.constant.CommonConstant;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.util.DateUtils;
import com.teamway.exception.ServiceException;
import com.teamway.security.common.enums.PersonIdentityTypeEnum;
import com.teamway.security.dto.accessManagement.car.EventListCarDto;
import com.teamway.security.dto.accessManagement.person.AccessRegionPersonListQueryDto;
import com.teamway.security.dto.accessManagement.person.EventListPersonDto;
import com.teamway.security.dto.accessManagement.person.EventListPersonExportDto;
import com.teamway.security.dto.accessManagement.person.EventListPersonMisDto;
import com.teamway.security.dto.accessManagement.securityDevice.AccessStatisticsQueryDto;
import com.teamway.security.dto.securityConfiguration.BandingCameraQueryDto;
import com.teamway.security.dto.securityConfiguration.CameraPresetBandingDto;
import com.teamway.security.entity.SecurityConfiguration.*;
import com.teamway.security.mapper.SecurityDeviceCameraMapper;
import com.teamway.security.mapper.SecurityDeviceRecordMapper;
import com.teamway.security.service.*;
import com.teamway.security.vo.accessManagement.car.EventListCarVo;
import com.teamway.security.vo.accessManagement.person.EventListPersonVo;
import com.teamway.security.vo.accessManagement.securityDevice.*;
import com.teamway.security.common.enums.EventTypeEnum;
import com.teamway.security.common.enums.AuthTypeEnum;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.DecimalFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.Comparator;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.teamway.common.util.ExcelUtils;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/8
 */
@Service
@Slf4j
public class SecurityDeviceCameraServiceImpl extends ServiceImpl<SecurityDeviceCameraMapper, SecurityDeviceCamera> implements SecurityDeviceCameraService {

    private SecurityDeviceRecordService securityDeviceRecordService;
    private SecurityDeviceCameraMapper securityDeviceCameraMapper;
    private SecurityDeviceDoorService securityDeviceDoorService;
    private SecurityDeviceService securityDeviceService;
    private CameraPresetService cameraPresetService;
    private CameraService cameraService;
    private RegionService regionService;
    private PersonService personService;
    private WorkGroupService workGroupService;
    private SecurityDeviceRecordMapper securityDeviceRecordMapper;
    @Autowired
    CommonTypeService commonTypeService;


    @Value("${timeLimit.accessListByPersonAll}")
    private Integer timeLimit;

    @Autowired
    @Lazy
    public void setSecurityDeviceRecordService(SecurityDeviceRecordService securityDeviceRecordService) {
        this.securityDeviceRecordService = securityDeviceRecordService;
    }

    @Autowired
    public void setSecurityDeviceCameraMapper(SecurityDeviceCameraMapper securityDeviceCameraMapper) {
        this.securityDeviceCameraMapper = securityDeviceCameraMapper;
    }

    @Autowired
    public void setSecurityDeviceDoorService(SecurityDeviceDoorService securityDeviceDoorService) {
        this.securityDeviceDoorService = securityDeviceDoorService;
    }

    @Autowired
    @Lazy
    public void setSecurityDeviceService(SecurityDeviceService securityDeviceService) {
        this.securityDeviceService = securityDeviceService;
    }

    @Autowired
    public void setCameraPresetService(CameraPresetService cameraPresetService) {
        this.cameraPresetService = cameraPresetService;
    }

    @Autowired
    public void setCameraService(CameraService cameraService) {
        this.cameraService = cameraService;
    }

    @Autowired
    public void setRegionService(RegionService regionService) {
        this.regionService = regionService;
    }

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setWorkGroupService(WorkGroupService workGroupService) {
        this.workGroupService = workGroupService;
    }

    @Autowired
    public void setSecurityDeviceRecordMapper(SecurityDeviceRecordMapper securityDeviceRecordMapper) {
        this.securityDeviceRecordMapper = securityDeviceRecordMapper;
    }

    @Override
    public BandingCameraQueryDto getBandedCamera(Long securityDeviceId) {
        if (ObjectUtil.isEmpty(securityDeviceService.getById(securityDeviceId))) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "安防设备不存在");
        }
        BandingCameraQueryDto cameraBySecurityDeviceId = new BandingCameraQueryDto();
        BandingCameraQueryDto cameraBySecurityDevice = securityDeviceCameraMapper.getCameraBySecurityDeviceId(securityDeviceId);
        if (ObjectUtil.isEmpty(cameraBySecurityDevice)) {
            ;
            cameraBySecurityDeviceId.setSecurityDeviceId(securityDeviceId);
            cameraBySecurityDeviceId.setCameraList(new ArrayList<>(0));
            return cameraBySecurityDeviceId;
        } else {
            List<Long> delCameraIds = new ArrayList<>();
            List<CameraPresetBandingDto> cameraList = cameraBySecurityDevice.getCameraList();

            cameraList = cameraList.stream()
                    .filter(cameraPresetBandingDto -> {
                        Camera camera = cameraService.getById(cameraPresetBandingDto.getCameraId());
                        if (ObjectUtil.isNotEmpty(camera)) {
                            cameraPresetBandingDto.setCameraName(camera.getName());
                            return true;
                        } else {
                            delCameraIds.add(cameraPresetBandingDto.getCameraId());
                            return false;
                        }
                    })
                    .collect(Collectors.toList());

            // 删除不存在的摄像机
            cameraList.removeIf(cameraPresetBandingDto -> delCameraIds.contains(cameraPresetBandingDto.getCameraId()));
            return cameraBySecurityDevice;
        }
    }

    @Override
    public BandedCameraVo getBandedCameraWithCameraInfo(Long securityDeviceId) {
        BandedCameraVo cameraInfoBySecurityDeviceId = securityDeviceCameraMapper.getCameraInfoBySecurityDeviceId(securityDeviceId);
        if (ObjectUtil.isEmpty(cameraInfoBySecurityDeviceId)) {
            BandedCameraVo bandedCameraVo = new BandedCameraVo();
            bandedCameraVo.setSecurityDeviceId(securityDeviceId);
            bandedCameraVo.setCameraList(new ArrayList<>(0));
            return bandedCameraVo;
        }
        List<CameraBandingVo> cameraList = cameraInfoBySecurityDeviceId.getCameraList();
        for (CameraBandingVo cameraBandingVo : cameraList) {
            if (!"-1".equals(cameraBandingVo.getPresetId())) {
                CameraPreset cameraPreset = cameraPresetService.getById(cameraBandingVo.getPresetId());
                cameraBandingVo.setPresetId(ObjectUtil.isEmpty(cameraPreset) ? "-1" : cameraPreset.getNo());
            }
        }
        return cameraInfoBySecurityDeviceId;
    }

    @Override
    public AccessStatisticsResultVo accessManagementStatistics(AccessStatisticsQueryDto queryDto) {

        String securityDeviceId = queryDto.getSecurityDeviceId();

        Long inNum = 0L;
        Long outNum = 0L;

        List<Long> inSecurityDeviceIds = new ArrayList<>();
        List<Long> outSecurityDeviceIds = new ArrayList<>();
        if (StrUtil.isNotEmpty(securityDeviceId)) {
            // 如果请求参数是查询指定设备的进出记录
            SecurityDevice securityDevice = securityDeviceService.getById(securityDeviceId);
            if (ObjectUtil.isEmpty(securityDevice)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "安防设备不存在");
            }

            if ("1".equals(securityDevice.getSecurityDeviceType())) {
                // 如果是门禁
                List<Long> securityDeviceDoorIdList = securityDeviceDoorService.lambdaQuery()
                        .eq(SecurityDeviceDoor::getSecurityDeviceId, securityDevice.getId())
                        .list()
                        .stream()
                        .map(SecurityDeviceDoor::getId)
                        .collect(Collectors.toList());
                inNum = securityDeviceRecordService.lambdaQuery().in(SecurityDeviceRecord::getDeviceId, securityDeviceDoorIdList)
                        .eq(SecurityDeviceRecord::getState, "1")
                        .notIn(SecurityDeviceRecord::getOpenType, "-1", "4")
                        .ge(SecurityDeviceRecord::getDate, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))
                        .le(SecurityDeviceRecord::getDate, LocalDateTimeUtil.endOfDay(LocalDateTime.now()))
                        .count();
                outNum = securityDeviceRecordService.lambdaQuery().in(SecurityDeviceRecord::getDeviceId, securityDeviceDoorIdList)
                        .eq(SecurityDeviceRecord::getState, "2")
                        .notIn(SecurityDeviceRecord::getOpenType, "-1", "4")
                        .ge(SecurityDeviceRecord::getDate, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))
                        .le(SecurityDeviceRecord::getDate, LocalDateTimeUtil.endOfDay(LocalDateTime.now()))
                        .count();

            } else if ("2".equals(securityDevice.getSecurityDeviceType())) {
                // 如果是道闸
                List<SecurityDeviceDoor> securityDeviceDoorList = securityDeviceDoorService.lambdaQuery()
                        .eq(SecurityDeviceDoor::getSecurityDeviceId, securityDevice.getId())
                        .list();
                Map<String, List<Long>> securityIdMap = securityDeviceDoorList.stream()
                        .collect(Collectors.groupingBy(SecurityDeviceDoor::getType,
                                Collectors.mapping(SecurityDeviceDoor::getId, Collectors.toList())));
                inSecurityDeviceIds = securityIdMap.getOrDefault("1", Collections.emptyList());
                outSecurityDeviceIds = securityIdMap.getOrDefault("2", Collections.emptyList());
                inNum = securityDeviceRecordService.lambdaQuery()
                        .eq(SecurityDeviceRecord::getState, "1")
                        .notIn(SecurityDeviceRecord::getOpenType, "-1", "4")
                        .in(SecurityDeviceRecord::getDeviceId, inSecurityDeviceIds)
                        .ge(SecurityDeviceRecord::getDate, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))
                        .le(SecurityDeviceRecord::getDate, LocalDateTimeUtil.endOfDay(LocalDateTime.now()))
                        .count();
                outNum = securityDeviceRecordService.lambdaQuery()
                        .eq(SecurityDeviceRecord::getState, "2")
                        .notIn(SecurityDeviceRecord::getOpenType, "-1", "4")
                        .in(SecurityDeviceRecord::getDeviceId, outSecurityDeviceIds)
                        .ge(SecurityDeviceRecord::getDate, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))
                        .le(SecurityDeviceRecord::getDate, LocalDateTimeUtil.endOfDay(LocalDateTime.now()))
                        .count();
            }

        } else {
            // 如果请求参数是查询所有设备的进出记录，门禁的和道闸的分别处理，最后相加
            // 处理门禁的
            List<SecurityDevice> securityDeviceList = securityDeviceService.lambdaQuery().eq(SecurityDevice::getSecurityDeviceType, "1").list();
            List<Long> securityDeviceIdList = securityDeviceList.stream().map(SecurityDevice::getId).collect(Collectors.toList());
            List<Long> securityDeviceDoorIdList = securityDeviceDoorService.lambdaQuery()
                    .in(SecurityDeviceDoor::getSecurityDeviceId, securityDeviceIdList)
                    .list()
                    .stream()
                    .map(SecurityDeviceDoor::getId)
                    .collect(Collectors.toList());
            Long doorInNum = securityDeviceRecordService.lambdaQuery().in(SecurityDeviceRecord::getDeviceId, securityDeviceDoorIdList)
                    .eq(SecurityDeviceRecord::getState, "1")
                    .notIn(SecurityDeviceRecord::getOpenType, "-1", "4")
                    .ge(SecurityDeviceRecord::getDate, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))
                    .le(SecurityDeviceRecord::getDate, LocalDateTimeUtil.endOfDay(LocalDateTime.now()))
                    .count();
            Long doorOutNum = securityDeviceRecordService.lambdaQuery().in(SecurityDeviceRecord::getDeviceId, securityDeviceDoorIdList)
                    .eq(SecurityDeviceRecord::getState, "2")
                    .notIn(SecurityDeviceRecord::getOpenType, "-1", "4")
                    .ge(SecurityDeviceRecord::getDate, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))
                    .le(SecurityDeviceRecord::getDate, LocalDateTimeUtil.endOfDay(LocalDateTime.now()))
                    .count();
            // 处理道闸的
            List<SecurityDevice> securityDeviceList1 = securityDeviceService.lambdaQuery().eq(SecurityDevice::getSecurityDeviceType, "2").list();
            List<Long> securityDeviceIdList1 = securityDeviceList1.stream().map(SecurityDevice::getId).collect(Collectors.toList());
            List<Long> inIdList = securityDeviceDoorService.lambdaQuery().in(SecurityDeviceDoor::getSecurityDeviceId, securityDeviceIdList1)
                    .eq(SecurityDeviceDoor::getType, "1")
                    .list()
                    .stream()
                    .map(SecurityDeviceDoor::getId)
                    .collect(Collectors.toList());
            List<Long> outIdList = securityDeviceDoorService.lambdaQuery().in(SecurityDeviceDoor::getSecurityDeviceId, securityDeviceIdList1)
                    .eq(SecurityDeviceDoor::getType, "2")
                    .list()
                    .stream()
                    .map(SecurityDeviceDoor::getId)
                    .collect(Collectors.toList());

            Long gateInNum = securityDeviceRecordService.lambdaQuery()
                    .eq(SecurityDeviceRecord::getState, "1")
                    .notIn(SecurityDeviceRecord::getOpenType, "-1", "4")
                    .in(SecurityDeviceRecord::getDeviceId, inIdList)
                    .ge(SecurityDeviceRecord::getDate, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))
                    .le(SecurityDeviceRecord::getDate, LocalDateTimeUtil.endOfDay(LocalDateTime.now()))
                    .count();

            Long gateOutNum = securityDeviceRecordService.lambdaQuery()
                    .eq(SecurityDeviceRecord::getState, "2")
                    .notIn(SecurityDeviceRecord::getOpenType, "-1", "4")
                    .in(SecurityDeviceRecord::getDeviceId, outIdList)
                    .ge(SecurityDeviceRecord::getDate, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))
                    .le(SecurityDeviceRecord::getDate, LocalDateTimeUtil.endOfDay(LocalDateTime.now()))
                    .count();

            inNum = doorInNum + gateInNum;
            outNum = doorOutNum + gateOutNum;
        }
        String inNumStr = padZeros(inNum);
        String outNumStr = padZeros(outNum);

        return new AccessStatisticsResultVo(inNumStr, outNumStr);
    }

    public String padZeros(Long number) {
        if (number < 10) {
            return "00" + number;
        } else if (number < 100) {
            return "0" + number;
        } else {
            return String.valueOf(number);
        }
    }

    @Override
    public IPage<EventListPersonVo> eventListByPerson(EventListPersonDto queryDto) {
        if (StrUtil.isNotBlank(queryDto.getEventTime())) {
            queryDto.setStartTime(DateUtils.dateSearchFormat("1", queryDto.getEventTime()));
            queryDto.setEndTime(DateUtils.dateSearchFormat("2", queryDto.getEventTime()));
        }
        IPage<EventListPersonVo> eventListPersonVoIPage;
        if (queryDto.getPageIndex() == null || queryDto.getPageSize() == null) {
            //校验时间
            isTimeRangeExceedSevenDays(queryDto.getStartTime(), queryDto.getEndTime());
            // 创建不分页的Page对象
            Page<EventListPersonVo> page = new Page<>(1, Integer.MAX_VALUE);
            // 查询数据
            eventListPersonVoIPage = securityDeviceRecordMapper.eventListByPerson(queryDto, page);
        } else {
            Page<EventListPersonVo> page = new Page<>(queryDto.getPageIndex(), queryDto.getPageSize());
            eventListPersonVoIPage = securityDeviceRecordMapper.eventListByPerson(queryDto, page);
        }

        // 获取所有分类数据
        List<CommonType> commonTypeList = commonTypeService.list();

        // 构建三个映射关系：进出方向、工种、工作单位
        Map<String, String> directionMap = commonTypeList.stream()
                .filter(type -> CommonConstant.TYPE_SEVEN.equals(type.getCategory()))  // 7人员进出记录类型
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));

        // 工种映射 (TYPE_ONE)
        Map<String, String> personTypeMap = commonTypeList.stream()
                .filter(type -> CommonConstant.TYPE_ONE.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));

        // 工作单位映射 (TYPE_FOUR)
        Map<String, String> workUnitMap = commonTypeList.stream()
                .filter(type -> CommonConstant.TYPE_FOUR.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));

        // 设置所有描述字段
        eventListPersonVoIPage.getRecords().forEach(vo -> {
            // 设置进出方向描述
            vo.setDirectionDesc(directionMap.getOrDefault(vo.getDirection(), vo.getDirection()));
            
            // 设置工种描述
            vo.setPersonTypeDesc(personTypeMap.getOrDefault(vo.getPersonType(), vo.getPersonType()));
            
            // 设置工作单位描述
            vo.setWorkUnitDesc(workUnitMap.getOrDefault(vo.getWorkUnit(), vo.getWorkUnit()));
            
            // 设置总包单位名称
            vo.setContractorUnitName(workUnitMap.getOrDefault(vo.getContractorUnitCode(), vo.getContractorUnitCode()));
        });

        return eventListPersonVoIPage;
    }

    @Override
    public List<EventListPersonVo> eventListByPersonMis(EventListPersonMisDto queryDto) {
        // 1) 若 eventTime 不为空，设置开始结束时间
        if (StrUtil.isNotBlank(queryDto.getEventTime())) {
            queryDto.setStartTime(DateUtils.dateSearchFormat("1", queryDto.getEventTime()));
            queryDto.setEndTime(DateUtils.dateSearchFormat("2", queryDto.getEventTime()));
        }

        isTimeRangeExceedSevenDays(queryDto.getStartTime(), queryDto.getEndTime());

        // 3) 查询数据
        List<EventListPersonVo> eventListPersonVoList =
                securityDeviceRecordMapper.eventListByPersonMis(queryDto);

        // 4) 构建分类映射表
        List<CommonType> commonTypeList = commonTypeService.list();
        // a) 7 -> directionDesc (人员进出记录类型)
        Map<String, String> directionMap = buildMap(commonTypeList, CommonConstant.TYPE_SEVEN);
        // b) 1 -> personType (工种)
        Map<String, String> personTypeMap = buildMap(commonTypeList, CommonConstant.TYPE_ONE);
        // c) 4 -> workUnit (单位)
        Map<String, String> unitMap = buildMap(commonTypeList, CommonConstant.TYPE_FOUR);

        // 5) 替换编码为对应名称
        eventListPersonVoList.forEach(vo -> {
            // directionDesc
            vo.setDirectionDesc(directionMap.getOrDefault(vo.getDirection(), vo.getDirection()));
            // personType
            vo.setPersonType(personTypeMap.getOrDefault(vo.getPersonType(), vo.getPersonType()));
            // workUnit
            vo.setWorkUnit(unitMap.getOrDefault(vo.getWorkUnit(), vo.getWorkUnit()));
        });

        // 6) 返回结果
        return eventListPersonVoList;
    }

    /**
     * 构建指定 category 的映射: (typeCode -> name)
     */
    private Map<String, String> buildMap(List<CommonType> commonTypeList, String category) {
        return commonTypeList.stream()
                .filter(ct -> category.equals(ct.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));
    }

    /**
     * 检查时间范围是否超过timeLimit天
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void isTimeRangeExceedSevenDays(String startTime, String endTime) {
        if (StrUtil.isBlank(startTime) || StrUtil.isBlank(endTime)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "开始时间和结束时间不能为空");
        }
        // 解析时间字符串
        LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 计算时间差
        Duration duration = Duration.between(start, end);
        if (duration.toDays() > timeLimit) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "查询时间范围不能超过" + timeLimit + "天");
        }
    }

    @Override
    public IPage<EventListCarVo> eventListByCar(EventListCarDto queryDto) {
        if (StrUtil.isNotBlank(queryDto.getEventTime())) {
            queryDto.setStartTime(DateUtils.dateSearchFormat("1", queryDto.getEventTime()));
            queryDto.setEndTime(DateUtils.dateSearchFormat("2", queryDto.getEventTime()));
        }
        Page<EventListCarVo> page = new Page<>(queryDto.getPageIndex(), queryDto.getPageSize());
        IPage<EventListCarVo> eventListCarVoIPage = securityDeviceRecordMapper.eventListByCar(queryDto, page);
        return eventListCarVoIPage;
    }

    private void statisticsAllCavePerson(LocalDateTime todayStart, LocalDateTime todayEnd, IndexStatisticsNumVo allCavePerson) {
        List<Region> regionList = regionService.list();
        List<SecurityDeviceRecord> securityDeviceRecordList = new ArrayList<>();

        if (CollUtil.isNotEmpty(regionList)) {
            securityDeviceRecordList = securityDeviceRecordService.lambdaQuery()
                    .eq(SecurityDeviceRecord::getRecordType, "0")
                    .ne(SecurityDeviceRecord::getOpenType, "-1")
                    .in(SecurityDeviceRecord::getRegionId, regionList.stream().map(Region::getId).collect(Collectors.toList()))
                    .orderByAsc(SecurityDeviceRecord::getDate)
                    .between(SecurityDeviceRecord::getDate, todayStart, todayEnd)
                    .list();
        }

        Map<String, Integer> netPersonnelCountByPersonIdentityType = new HashMap<>();
        Map<String, Set<String>> enteredCarByPersonIdentityType = new HashMap<>();

        for (PersonIdentityTypeEnum typeEnum : PersonIdentityTypeEnum.values()) {
            netPersonnelCountByPersonIdentityType.put(typeEnum.getCode(), 0);
            enteredCarByPersonIdentityType.put(typeEnum.getCode(), new HashSet<>());
        }

        int currentNetPersonnelCount = 0;
        String currentPersonIdentityType = null;

        for (SecurityDeviceRecord record : securityDeviceRecordList) {
            String personIndex = record.getPersonIndex();
            Person person = personService.getById(personIndex);

            if (person == null) {
                continue;
            }

            String personIdentityType = person.getPersonIdentityType();

            if (!Objects.equals(currentPersonIdentityType, personIdentityType)) {
                currentPersonIdentityType = personIdentityType;
                Integer num = netPersonnelCountByPersonIdentityType.get(currentPersonIdentityType);
                if (num != 0) {
                    currentNetPersonnelCount = num;
                } else {
                    currentNetPersonnelCount = 0;
                }
            }

            Set<String> enteredPersons = enteredCarByPersonIdentityType.get(currentPersonIdentityType);

            if (record.getState() == 1) {
                // 处理进场记录
                if (enteredPersons.contains(personIndex)) {
                    currentNetPersonnelCount--;
                }
                currentNetPersonnelCount++;
                enteredPersons.add(personIndex);
            } else if (record.getState() == 2) {
                // 处理出场记录
                if (enteredPersons.contains(personIndex)) {
                    currentNetPersonnelCount--;
                    enteredPersons.remove(personIndex);
                }
            }

            // 忽略连续的出场记录导致净人员数量为0的情况
            currentNetPersonnelCount = Math.max(0, currentNetPersonnelCount);

            // 更新当前区域的净人员数量
            netPersonnelCountByPersonIdentityType.put(currentPersonIdentityType, currentNetPersonnelCount);
        }

        List<IndexNumVo> indexNumVoList = new ArrayList<>();
        int total = netPersonnelCountByPersonIdentityType.values().stream().mapToInt(Integer::intValue).sum();

        netPersonnelCountByPersonIdentityType.forEach((key, value) -> {
            indexNumVoList.add(IndexNumVo.builder()
                    .typeName(PersonIdentityTypeEnum.getByCode(key).getDesc())
                    .typeNum(padZeros(Long.valueOf(value)))
                    .build());
        });

        allCavePerson.setRegionNumList(indexNumVoList);
        allCavePerson.setTotal(padZeros((long) total));
    }


    /**
     * 设置结果
     *
     * @param allCavePerson
     * @param netPersonnelCountByPersonIdentityType
     */
    private void setResult(IndexStatisticsNumVo allCavePerson, Map<String, Integer> netPersonnelCountByPersonIdentityType) {
        Map<Long, Region> regionMap = regionService.lambdaQuery().ne(Region::getPid, 0).list().stream().collect(Collectors.toMap(Region::getId, region -> region));
        Map<Long, Integer> map = mergeNetPersonnelCountByRegion(netPersonnelCountByPersonIdentityType, regionMap);
        List<IndexNumVo> indexNumVoList = new ArrayList<>();
        AtomicReference<Integer> total = new AtomicReference<>(0);
        map.forEach((key, value) -> {
            Region region = regionService.getById(key);
            indexNumVoList.add(IndexNumVo.builder()
                    .typeName(region.getName())
                    .typeNum(padZeros(Long.valueOf(value)))
                    .build());
            total.updateAndGet(v -> v + value);
        });
        allCavePerson.setRegionNumList(indexNumVoList);
        Integer i = total.get();
        allCavePerson.setTotal(padZeros(Long.valueOf(i)));
    }

    public Map<Long, Integer> mergeNetPersonnelCountByRegion(Map<String, Integer> netPersonnelCountByRegion,
                                                             Map<Long, Region> regionMap) {
        Map<Long, Integer> mergedNetPersonnelCountByRegion = new HashMap<>();

        for (Map.Entry<String, Integer> entry : netPersonnelCountByRegion.entrySet()) {
            String regionIdStr = entry.getKey();
            int netPersonnelCount = entry.getValue();
            // 将区域ID字符串转换为Long类型
            Long regionId = Long.parseLong(regionIdStr);
            // 获取区域对象
            Region region = regionMap.get(regionId);
            // 确保区域对象存在
            if (region != null) {
                // 找到对应的二级区域ID
                Long secondLevelRegionId = getSecondLevelRegionId(region, regionMap);
                // 累加净人员数量到二级区域
                mergedNetPersonnelCountByRegion.merge(secondLevelRegionId, netPersonnelCount, Integer::sum);
            }
        }

        return mergedNetPersonnelCountByRegion;
    }

    private static Long getSecondLevelRegionId(Region region, Map<Long, Region> regionMap) {
        Long parentId = region.getPid();
        Long currentId = region.getId();

        // 递归向上查找，直到找到根区域的下一级或pid为0的区域
        while (parentId != null && !parentId.equals(Region.ROOT_REGION_ID)) {
            Region parentRegion = regionMap.get(parentId);
            if (parentRegion != null) {
                currentId = parentId;
                parentId = parentRegion.getPid();
            } else {
                // 处理不存在的情况，防止死循环
                break;
            }
        }

        return currentId;
    }


    private void statisticsCarInCave(List<Region> regionList, LocalDateTime todayStart, LocalDateTime todayEnd, IndexStatisticsNumVo inCaveCar) {
        List<SecurityDeviceRecord> securityDeviceRecordList = new ArrayList<>();
        if (CollUtil.isNotEmpty(regionList)) {
            securityDeviceRecordList = securityDeviceRecordService.lambdaQuery()
                    .eq(SecurityDeviceRecord::getRecordType, "1")
                    .ne(SecurityDeviceRecord::getOpenType, "-1")
                    .in(SecurityDeviceRecord::getRegionId, regionList.stream().map(Region::getId).collect(Collectors.toList()))
                    .orderByAsc(SecurityDeviceRecord::getDate)
                    .between(SecurityDeviceRecord::getDate, todayStart, todayEnd)
                    .list();
        }

        Map<String, Integer> netCarCountByRegion = new HashMap<>();
        for (Region region : regionList) {
            netCarCountByRegion.put(region.getId().toString(), 0);
        }
        Map<String, Set<String>> enteredCarByRegion = new HashMap<>();
        String currentRegion = null;
        int currentNetCarCount = 0;

        for (SecurityDeviceRecord record : securityDeviceRecordList) {
            // 如果区域发生变化，更新当前区域和净车辆数量
            if (!record.getRegionId().equals(currentRegion)) {
                currentRegion = record.getRegionId();
                Integer num = netCarCountByRegion.get(record.getRegionId());
                if (num != 0) {
                    currentNetCarCount = num;
                } else {
                    currentNetCarCount = 0;
                }
                enteredCarByRegion.computeIfAbsent(currentRegion, k -> new HashSet<>());
            }

            // 处理进场记录
            if (record.getState() == 1) {
                String plateNo = record.getPlateNo();
                // 如果该人员已经进入但还没有出去，重新统计该人员的进出记录
                if (enteredCarByRegion.get(currentRegion).contains(plateNo)) {
                    currentNetCarCount--;
                }
                currentNetCarCount++;
                enteredCarByRegion.get(currentRegion).add(plateNo);
            }
            // 处理出场记录
            else if (record.getState() == 2) {
                String plateNo = record.getPlateNo();
                // 如果该车辆已经进入，重新统计该车辆的进出记录
                String containRegion = "";
                boolean contains = false;
                for (Map.Entry<String, Set<String>> stringSetEntry : enteredCarByRegion.entrySet()) {
                    Set<String> value = stringSetEntry.getValue();
                    if (value.contains(plateNo)) {
                        containRegion = stringSetEntry.getKey();
                        contains = true;
                        break;
                    }
                }
                if (enteredCarByRegion.get(currentRegion).contains(plateNo)) {
                    currentNetCarCount--;
                    enteredCarByRegion.get(currentRegion).remove(plateNo);
                } else {
                    if (contains) {
                        Integer i = netCarCountByRegion.get(containRegion);
                        i--;
                        netCarCountByRegion.put(containRegion, i);
                        enteredCarByRegion.get(containRegion).remove(plateNo);
                    }
                }
            }

            // 忽略连续的出场记录导致净车辆数量为0的情况
            if (currentNetCarCount < 0) {
                currentNetCarCount = 0;
            }

            // 更新当前区域的净车辆数量
            netCarCountByRegion.put(currentRegion, currentNetCarCount);
        }

        setResult(inCaveCar, netCarCountByRegion);
    }

    private void statisticsPersonInCave(List<Region> regionList, LocalDateTime todayStart, LocalDateTime todayEnd, IndexStatisticsNumVo inCavePerson) {
        List<SecurityDeviceRecord> securityDeviceRecordList = new ArrayList<>();
        if (CollUtil.isNotEmpty(regionList)) {
            securityDeviceRecordList = securityDeviceRecordService.lambdaQuery()
                    .eq(SecurityDeviceRecord::getRecordType, "0")
                    .eq(SecurityDeviceRecord::getOpenType, "1")
                    .in(SecurityDeviceRecord::getRegionId, regionList.stream().map(Region::getId).collect(Collectors.toList()))
                    .orderByAsc(SecurityDeviceRecord::getDate)
                    .between(SecurityDeviceRecord::getDate, todayStart, todayEnd)
                    .list();
        }

        Map<String, Integer> netPersonnelCountByRegion = new HashMap<>();
        for (Region region : regionList) {
            netPersonnelCountByRegion.put(region.getId().toString(), 0);
        }
        Map<String, Set<String>> enteredPersonnelByRegion = new HashMap<>();
        String currentRegion = null;
        int currentNetPersonnelCount = 0;

        for (SecurityDeviceRecord record : securityDeviceRecordList) {
            // 如果区域发生变化，更新当前区域和净人员数量
            if (!Objects.equals(currentRegion, record.getRegionId())) {
                currentRegion = record.getRegionId();
                Integer num = netPersonnelCountByRegion.get(currentRegion);
                if (num != 0) {
                    currentNetPersonnelCount = num;
                } else {
                    currentNetPersonnelCount = 0;
                }
                enteredPersonnelByRegion.computeIfAbsent(currentRegion, k -> new HashSet<>());
            }

            // 处理进场记录
            if (record.getState() == 1) {
                String cardNo = record.getCardNo();
                // 如果该人员已经进入但还没有出去，重新统计该人员的进出记录
                if (enteredPersonnelByRegion.get(currentRegion).contains(cardNo)) {
                    currentNetPersonnelCount--;
                }
                currentNetPersonnelCount++;
                enteredPersonnelByRegion.get(currentRegion).add(cardNo);
            }
            // 处理出场记录
            else if (record.getState() == 2) {
                String cardNo = record.getCardNo();
                // 如果该人员已经进入，重新统计该人员的进出记录
                String containRegion = "";
                boolean contains = false;
                for (Map.Entry<String, Set<String>> stringSetEntry : enteredPersonnelByRegion.entrySet()) {
                    Set<String> value = stringSetEntry.getValue();
                    if (value.contains(cardNo)) {
                        containRegion = stringSetEntry.getKey();
                        contains = true;
                        break;
                    }
                }
                if (enteredPersonnelByRegion.get(currentRegion).contains(cardNo)) {
                    currentNetPersonnelCount--;
                    enteredPersonnelByRegion.get(currentRegion).remove(cardNo);
                } else {
                    if (contains) {
                        Integer i = netPersonnelCountByRegion.get(containRegion);
                        i--;
                        netPersonnelCountByRegion.put(containRegion, i);
                        enteredPersonnelByRegion.get(containRegion).remove(cardNo);
                    }
                }
            }

            // 忽略连续的出场记录导致净人员数量为0的情况
            if (currentNetPersonnelCount < 0) {
                currentNetPersonnelCount = 0;
            }

            // 更新当前区域的净人员数量
            netPersonnelCountByRegion.put(currentRegion, currentNetPersonnelCount);
        }

        setResult(inCavePerson, netPersonnelCountByRegion);
    }


    public static void main(String[] args) {
        double totalTimeInHours = 100000.0;
        double second = NumberUtil.div(totalTimeInHours, 1000, 1);
        double minutes = NumberUtil.div(second, 60, 2);
        double hours = NumberUtil.div(minutes, 60, 2);
        DecimalFormat df = new DecimalFormat("#0.0");
        String format = df.format(hours);
        System.out.println(format);

    }

    private List<SecurityDeviceRecord> getSecurityDeviceRecords(AccessRegionPersonListQueryDto queryDto, String startTime, String endTime) {
        MPJLambdaWrapper<SecurityDeviceRecord> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(
                        SecurityDeviceRecord::getId,
                        SecurityDeviceRecord::getDate,
                        SecurityDeviceRecord::getDeviceName,
                        SecurityDeviceRecord::getState,
                        SecurityDeviceRecord::getPersonName,
                        SecurityDeviceRecord::getPersonIndex,
                        SecurityDeviceRecord::getWorkUnit,
                        SecurityDeviceRecord::getPlateNo,
                        SecurityDeviceRecord::getCarIndex,
                        SecurityDeviceRecord::getPersonType,
                        SecurityDeviceRecord::getCarType,
                        SecurityDeviceRecord::getCarType,
                        SecurityDeviceRecord::getAuthType,
                        SecurityDeviceRecord::getPicUrl,
                        SecurityDeviceRecord::getRecordType,
                        SecurityDeviceRecord::getOpenType,
                        SecurityDeviceRecord::getRegionId,
                        SecurityDeviceRecord::getRegionName,
                        SecurityDeviceRecord::getCardNo)
                .selectAs(SecurityDevice::getId, SecurityDeviceRecord::getDeviceId)
                .innerJoin(Person.class, Person::getId, SecurityDeviceRecord::getPersonIndex)
                .innerJoin(SecurityDeviceDoor.class, SecurityDeviceDoor::getId, SecurityDeviceRecord::getDeviceId)
                .innerJoin(SecurityDevice.class, SecurityDevice::getId, SecurityDeviceDoor::getSecurityDeviceId)
                .like(StrUtil.isNotBlank(queryDto.getPersonName()), Person::getPersonName, queryDto.getPersonName())
                .eq(StrUtil.isNotBlank(queryDto.getPersonIdentityType()), Person::getPersonIdentityType, queryDto.getPersonIdentityType())
                .eq(StrUtil.isNotBlank(queryDto.getWorkGroupId()), Person::getPersonUnitId, queryDto.getWorkGroupId())
                .eq(StrUtil.isNotBlank(queryDto.getRegionId()), SecurityDeviceRecord::getRegionId, queryDto.getRegionId())
                .ge(StrUtil.isNotBlank(startTime), SecurityDeviceRecord::getDate, startTime)
                .le(StrUtil.isNotBlank(endTime), SecurityDeviceRecord::getDate, endTime)
                .eq(SecurityDeviceRecord::getRecordType, "0")
                .in(SecurityDeviceRecord::getState, "1", "2")
                .orderByAsc(SecurityDeviceRecord::getDate);
        List<SecurityDeviceRecord> list = securityDeviceRecordMapper.selectJoinList(SecurityDeviceRecord.class, wrapper);
        return list;
    }

    // 缓存设备记录列表
    private List<SecurityDeviceRecord> getCachedSecurityDeviceRecords(String personId, String securityDeviceId, List<SecurityDeviceRecord> list, String plateNo, String type) {
        if ("1".equals(type)) {
            return list.stream()
                    .filter(securityDeviceRecord -> securityDeviceRecord.getPersonIndex().equals(personId))
                    .filter(securityDeviceRecord -> securityDeviceRecord.getDeviceId().equals(securityDeviceId))
                    .filter(securityDeviceRecord -> securityDeviceRecord.getRecordType() == 0)
                    .filter(securityDeviceRecord -> securityDeviceRecord.getState() == 1 || securityDeviceRecord.getState() == 2)
                    .collect(Collectors.toList());
        } else {
            return list.stream()
                    .filter(securityDeviceRecord -> securityDeviceRecord.getPlateNo().equals(plateNo))
                    .filter(securityDeviceRecord -> securityDeviceRecord.getDeviceId().equals(securityDeviceId))
                    .filter(securityDeviceRecord -> securityDeviceRecord.getRecordType() == 1)
                    .filter(securityDeviceRecord -> securityDeviceRecord.getState() == 1 || securityDeviceRecord.getState() == 2)
                    .collect(Collectors.toList());
        }
    }

    // 计算总时间
    private long calculateTotalTime(List<SecurityDeviceRecord> securityDeviceRecordList) {
        boolean inside = false;
        Date enterTime = null;
        long totalTime = 0;

        for (SecurityDeviceRecord record : securityDeviceRecordList) {
            if (record.getState() == 1) { // 进入
                if (!inside) { // 如果之前不是进入状态，表示新的进入周期开始
                    inside = true;
                    enterTime = record.getDate();
                }
            } else if (record.getState() == 2 && inside) { // 出去
                inside = false;
                totalTime += record.getDate().getTime() - enterTime.getTime();
                enterTime = null;
            }
        }

        return totalTime;
    }

    // 格式化总时间
    private String formatTotalTime(double totalTimeInHours) {
        DecimalFormat df = new DecimalFormat("#0.00");
        return df.format(totalTimeInHours);
    }


    private List<SecurityDeviceRecord> getSecurityDeviceRecordsByCars(AccessRegionPersonListQueryDto queryDto, String startTime, String endTime) {
        MPJLambdaWrapper<SecurityDeviceRecord> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(SecurityDeviceRecord::getId,
                        SecurityDeviceRecord::getDate,
                        SecurityDeviceRecord::getDeviceName,
                        SecurityDeviceRecord::getState,
                        SecurityDeviceRecord::getPersonName,
                        SecurityDeviceRecord::getPersonIndex,
                        SecurityDeviceRecord::getWorkUnit,
                        SecurityDeviceRecord::getPlateNo,
                        SecurityDeviceRecord::getCarIndex,
                        SecurityDeviceRecord::getPersonType,
                        SecurityDeviceRecord::getCarType,
                        SecurityDeviceRecord::getCarType,
                        SecurityDeviceRecord::getAuthType,
                        SecurityDeviceRecord::getPicUrl,
                        SecurityDeviceRecord::getRecordType,
                        SecurityDeviceRecord::getOpenType,
                        SecurityDeviceRecord::getRegionId,
                        SecurityDeviceRecord::getRegionName,
                        SecurityDeviceRecord::getCardNo)
                .selectAs(SecurityDevice::getId, SecurityDeviceRecord::getDeviceId)
//                .innerJoin(Car.class, Car::getId, SecurityDeviceRecord::getCarIndex)
                .innerJoin(SecurityDeviceDoor.class, SecurityDeviceDoor::getId, SecurityDeviceRecord::getDeviceId)
                .innerJoin(SecurityDevice.class, SecurityDevice::getId, SecurityDeviceDoor::getSecurityDeviceId)
                .like(StrUtil.isNotBlank(queryDto.getCarNo()), Car::getCarNo, queryDto.getCarNo())
                .eq(StrUtil.isNotBlank(queryDto.getCarType()), Car::getCarType, queryDto.getCarType())
                .eq(StrUtil.isNotBlank(queryDto.getRegionId()), SecurityDeviceRecord::getRegionId, queryDto.getRegionId())
                .ge(StrUtil.isNotBlank(startTime), SecurityDeviceRecord::getDate, startTime)
                .le(StrUtil.isNotBlank(endTime), SecurityDeviceRecord::getDate, endTime)
                .eq(SecurityDeviceRecord::getRecordType, "1")
                .in(SecurityDeviceRecord::getState, "1", "2")
                .orderByAsc(SecurityDeviceRecord::getDate);
        List<SecurityDeviceRecord> list = securityDeviceRecordMapper.selectJoinList(SecurityDeviceRecord.class, wrapper);
        return list;
    }

    @Override
    public List<SecurityDeviceDoor> getSecurityDeviceDoorByDeviceId(Long deviceId) {
        SecurityDevice securityDevice = securityDeviceService.getById(deviceId);
        if (ObjectUtil.isEmpty(securityDevice)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "设备不存在");
        }
        return securityDeviceDoorService.lambdaQuery().eq(SecurityDeviceDoor::getSecurityDeviceId, deviceId).list();
    }

    @Override
    public void exportEventListByPerson(EventListPersonDto queryDto, HttpServletResponse response) {
        // 设置不分页查询，获取所有数据
        queryDto.setPageIndex(1);
        queryDto.setPageSize(Integer.MAX_VALUE);
        
        IPage<EventListPersonVo> eventListPersonVoIPage = this.eventListByPerson(queryDto);
        List<EventListPersonVo> records = eventListPersonVoIPage.getRecords();
        
        if (CollUtil.isEmpty(records)) {
            throw new ServiceException(ResultCode.NOT_NULL, "导出的数据");
        }

        // 转换为导出DTO
        List<EventListPersonExportDto> exportDtoList = records.stream().map(vo -> {
            // 使用枚举映射事件类型
            String eventTypeDesc = vo.getEventType();
            EventTypeEnum eventTypeEnum = EventTypeEnum.getByCode(vo.getEventType());
            if (eventTypeEnum != null) {
                eventTypeDesc = eventTypeEnum.getDesc();
            }

            // 使用枚举映射授权类型
            String authTypeDesc = vo.getAuthType();
            AuthTypeEnum authTypeEnum = AuthTypeEnum.getByCode(vo.getAuthType());
            if (authTypeEnum != null) {
                authTypeDesc = authTypeEnum.getDesc();
            }

            return EventListPersonExportDto.builder()
                    .personName(vo.getPersonName())
                    .time(vo.getTime())
                    .securityDeviceDoorName(vo.getSecurityDeviceDoorName())
                    .directionDesc(vo.getDirectionDesc())
                    .eventTypeDesc(eventTypeDesc)
                    .workUnitDesc(vo.getWorkUnitDesc())
                    .personTypeDesc(vo.getPersonTypeDesc())
                    .authType(authTypeDesc)
                    .subcontractUnitName(vo.getSubcontractUnitName())
                    .personPost(vo.getPersonPost())
                    .build();
        }).collect(Collectors.toList());

        // 按时间倒序排序
        exportDtoList.sort(Comparator.comparing(EventListPersonExportDto::getTime).reversed());

        // 导出Excel
        ExportParams exportParams = new ExportParams("门禁进出记录", "门禁进出记录", ExcelType.HSSF);
        
        try {
            ExcelUtils.exportExcel(exportDtoList, EventListPersonExportDto.class, "门禁进出记录", exportParams, response);
        } catch (IOException e) {
            throw new ServiceException(ResultCode.EXPORT_FAIL, e);
        }
    }
}
