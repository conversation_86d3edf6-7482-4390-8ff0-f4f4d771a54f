package com.teamway.security.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONReader;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.common.constant.AlarmLevelConstant;
import com.teamway.base.entity.CommonType;
import com.teamway.base.entity.Region;
import com.teamway.base.service.CommonTypeService;
import com.teamway.base.service.RegionService;
import com.teamway.common.constant.AlarmCenterAlarmType;
import com.teamway.common.constant.CommonConstant;
import com.teamway.common.constant.DeviceEnum;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.event.AlarmCenterEvent;
import com.teamway.common.util.DateUtils;
import com.teamway.common.util.JsonFileReader;
import com.teamway.common.util.SnowflakeUtils;
import com.teamway.exception.ServiceException;
import com.teamway.mqtt.MqttUtils;
import com.teamway.security.common.enums.*;
import com.teamway.security.config.UnitPriorityConfig;
import com.teamway.security.dto.AccessRecordQueryDto;
import com.teamway.security.dto.mis.DeliveryPlanQueryDTO;
import com.teamway.security.dto.mqttdto.accessManageMent.person.MqttDataCarReportDto;
import com.teamway.security.dto.mqttdto.accessManageMent.person.MqttDataPersonReportDto;
import com.teamway.security.dto.mqttdto.accessManageMent.person.MqttDataReportDto;
import com.teamway.security.dto.mqttdto.accessManageMent.person.MqttRecordReportDto;
import com.teamway.security.dto.mqttdto.index.MqttToAccessControlDto;
import com.teamway.security.dto.mqttdto.index.MqttToIndexCommonEventDto;
import com.teamway.security.dto.securityConfiguration.person.PersonBlackListAddQueryDto;
import com.teamway.security.dto.statistics.FactoryPersonDetailQueryDto;
import com.teamway.security.entity.AccessAlertConfig;
import com.teamway.security.entity.PersonUnitCount;
import com.teamway.security.entity.SecurityConfiguration.*;
import com.teamway.security.mapper.PersonMapper;
import com.teamway.security.mapper.SecurityDeviceRecordMapper;
import com.teamway.security.service.*;
import com.teamway.security.vo.DeviceAccStatVo;
import com.teamway.security.vo.mis.DeliveryPlanStatVO;
import com.teamway.security.vo.mis.DeliveryPlanVO;
import com.teamway.security.vo.statistics.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.teamway.security.common.constant.DatePatternConstant.NORM_TIME_PATTERN_HOUR_MINUTE_FORMAT;

/**
 * @ClossName SecurityDeviceRecordServiceImpl
 * @Description 安防设备记录信息 服务实现类
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class SecurityDeviceRecordServiceImpl extends ServiceImpl<SecurityDeviceRecordMapper, SecurityDeviceRecord> implements SecurityDeviceRecordService {
    private PersonService personService;
    private CarService carService;
    private RegionService regionService;
    private SecurityDeviceDoorService securityDeviceDoorService;
    private SecurityDeviceService securityDeviceService;
    private InPlantRecordService inPlantRecordService;
    private WorkGroupService workGroupService;
    private ApplicationContext applicationContext;
    @Autowired
    SecurityDeviceRecordMapper securityDeviceRecordMapper;
    @Autowired
    CommonTypeService commonTypeService;
    @Value("${misUrl}")
    private String misUrl;
    @Value("${security.device.record.expansion.area:扩建区}")
    private String expansionAreaName;

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    @Lazy
    public void setCarService(CarService carService) {
        this.carService = carService;
    }

    @Autowired
    public void setRegionService(RegionService regionService) {
        this.regionService = regionService;
    }

    @Autowired
    public void setSecurityDeviceDoorService(SecurityDeviceDoorService securityDeviceDoorService) {
        this.securityDeviceDoorService = securityDeviceDoorService;
    }

    @Autowired
    public void setSecurityDeviceService(SecurityDeviceService securityDeviceService) {
        this.securityDeviceService = securityDeviceService;
    }

    @Autowired
    @Lazy
    public void setInPlantRecordService(InPlantRecordService inPlantRecordService) {
        this.inPlantRecordService = inPlantRecordService;
    }

    @Autowired
    public void setWorkGroupService(WorkGroupService workGroupService) {
        this.workGroupService = workGroupService;
    }

    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Autowired
    private AccessAlertConfigService accessAlertConfigService;

    @Value("${deviceOfflineTime.door:60}")
    private int doorOfflineTime;

    @Autowired
    private UnitPriorityConfig unitPriorityConfig;

    private Map<String, Integer> getUnitPriorityMap() {
        Map<String, Integer> priorityMap = new HashMap<>();
        if (unitPriorityConfig.getList() != null) {
            for (UnitPriorityConfig.UnitPriority priority : unitPriorityConfig.getList()) {
                priorityMap.put(priority.getName(), priority.getPriority());
            }
        }
        return priorityMap;
    }

    @Override
    public void securityRecordReport(String message, String topic) {
        MqttRecordReportDto reportDto = null;
        try {
            reportDto = JSON.parseObject(message, MqttRecordReportDto.class);
        } catch (Exception e) {
            log.debug("接收来自IOT组件的事件上报(告警和通知)，json转实体类失败，失败原因：{}，上报json：{}", e.getMessage(), message);
        }
        if (ObjectUtil.isNull(reportDto)
                || StrUtil.isEmpty(reportDto.getDeviceId())
                || StrUtil.isEmpty(reportDto.getEventType())
                || StrUtil.isEmpty(reportDto.getSubType())
                || StrUtil.isEmpty(reportDto.getEventTime())
        ) {
            log.info("接收来自IOT组件的事件上报(告警和通知)，上报信息不完整");
            return;
        }

        if ("NotificationEvent".equals(reportDto.getEventType())) {
            // 通知事件
            if ("personRecord".equals(reportDto.getSubType())) {
                // 人员进出通知
                log.info("接受用户上报记录信息：{}", message);
                personRecordProcess(reportDto);
            } else if ("carRecord".equals(reportDto.getSubType())) {
                log.info("接受车辆上报记录信息：{}", message);
                carRecordProcess(reportDto);
            } else if ("online".equals(reportDto.getSubType())) {
                log.info("接受设备上线上报信息：{}", message);
                acceptDeviceNotification(reportDto);
            } else if ("sub_online".equals(reportDto.getSubType())) {
                log.info("接受子设备上线上报信息：{}", message);
                acceptSubDeviceNotification(reportDto);
            }
        } else if ("AlarmEvent".equals(reportDto.getEventType())) {
            // 告警事件
            if ("offline".equals(reportDto.getSubType())) {
                log.info("接受设备离线上报信息：{}", message);
                acceptDeviceAlarm(reportDto);
            } else if ("sub_offline".equals(reportDto.getSubType())) {
                log.info("接受子设备离线上报信息：{}", message);
                acceptSubDeviceAlarm(reportDto);
            }
        }
    }

    private void acceptSubDeviceAlarm(MqttRecordReportDto reportDto) {
        String deviceId = reportDto.getDeviceId();
        String regionName = "";
        Integer type = null;
        Integer level = null;
        String alarmTime = "";
        String deviceName = "";
        String alarmDesc = "";
        String value = "";
        List<SecurityDeviceDoor> securityDeviceDoorList = securityDeviceDoorService.lambdaQuery().eq(SecurityDeviceDoor::getDoorId, deviceId).list();
        if (CollUtil.isNotEmpty(securityDeviceDoorList)) {
            SecurityDeviceDoor securityDeviceDoor = securityDeviceDoorList.get(0);
            log.info("查询出属于安防门禁点设备，设备名称：{}", securityDeviceDoor.getDoorName());
            boolean update = securityDeviceDoorService.lambdaUpdate().eq(SecurityDeviceDoor::getId, securityDeviceDoor.getId()).set(SecurityDeviceDoor::getOnlineStatus, "0").update();
            if (update) {
                log.info("更新安防门禁点设备状态成功");
            } else {
                log.info("更新安防门禁点设备状态失败");
            }
            SecurityDevice securityDevice = securityDeviceService.getById(securityDeviceDoor.getSecurityDeviceId());
            if (StrUtil.isNotEmpty(securityDevice.getRegionId())) {
                Region region = regionService.getById(securityDevice.getRegionId());
                regionName = region.getName();
            }
            deviceName = securityDeviceDoor.getDoorName();
            type = 3;
            level = 1;
            alarmTime = reportDto.getEventTime();
            alarmDesc = (SecutityDeviceTypeEnum.MENJIN.getCode().equals(securityDevice.getSecurityDeviceType()) ? "门禁" : "道闸") + "设备：" + securityDeviceDoor.getDoorName() + " 于 " + alarmTime + " 掉线";
            value = "安防设备掉线";
        }
        if (StrUtil.isBlank(deviceName)) {
            log.info("未找到对应类型设备，设备ID:{}", deviceId);
            return;
        }
        AlarmCenterEvent alarmCenterEvent = new AlarmCenterEvent(this);
        alarmCenterEvent.setRegionName(StrUtil.isNotEmpty(regionName) ? regionName : "未知区域");
        alarmCenterEvent.setType(type);
        alarmCenterEvent.setValue(value);
        alarmCenterEvent.setLevel(level);
        alarmCenterEvent.setAlarmTime(alarmTime);
        alarmCenterEvent.setDeviceName(deviceName);
        alarmCenterEvent.setAlarmDesc(alarmDesc);
        alarmCenterEvent.setState(0);
        applicationContext.publishEvent(alarmCenterEvent);
    }

    /**
     * 接收设备离线上报，目前只是针对动环主机设备
     * @param reportDto
     */
    private void acceptDeviceAlarm(MqttRecordReportDto reportDto) {
        log.info("门禁{}离线，进入告警推送判断", reportDto.getDeviceId());
        AlarmCenterEvent event = new AlarmCenterEvent(this);
        event.setType(AlarmCenterAlarmType.DEVICE_OFFLINE_ALARM);
        event.setLevel(Integer.valueOf(AlarmLevelConstant.GENERAL));
        SecurityDevice securityDevice = securityDeviceService.list(new LambdaQueryWrapper<SecurityDevice>().eq(SecurityDevice::getDeviceSourceIndex, reportDto.getDeviceId())).stream().findFirst().orElse(null);
        if (ObjectUtil.isEmpty(securityDevice)) {
            return;
        }

        //记录缓存当中一小时内相同ip的摄像机状态变化将不会推送告警中心
        if (StrUtil.isNotEmpty(securityDevice.getOffLineTime())) {
            String offLineTime = securityDevice.getOffLineTime();
            LocalDateTime parse = LocalDateTime.parse(offLineTime, DateUtils.DATETIME_FORMATTER);
            Duration between = Duration.between(parse, LocalDateTime.now());
            long minutes = between.toMinutes();
            if (minutes >= 0 && minutes < doorOfflineTime) {
                log.info("id为{}的门禁{}分钟内已存在告警推送", reportDto.getDeviceId(), doorOfflineTime);
                return;
            }
        }

        //查询门禁对应区域
        Region region = regionService.getById(securityDevice.getRegionId());
        if (ObjectUtil.isNotEmpty(region)) {
            event.setRegionName(region.getAbsolutePath());
        }
        event.setDeviceName(securityDevice.getSecurityDeviceName());
        event.setValue(DeviceEnum.DOOR.getStatusDesc());
        event.setAlarmDesc(DeviceEnum.DOOR.getDesc());
        event.setAlarmTime(LocalDateTime.now().format(DateUtils.DATETIME_FORMATTER));
        event.setState(0);
        //同步门禁最后离线时间
        securityDevice.setOffLineTime(LocalDateTime.now().format(DateUtils.DATETIME_FORMATTER));
        securityDeviceService.updateById(securityDevice);
        //发送告警中心
        log.info("门禁{}离线，推送消息到告警中心", reportDto.getDeviceId());
        applicationContext.publishEvent(event);
    }

    /**
     * 接收子设备上线上报，如门禁点等设备上报
     * @param reportDto
     */
    private void acceptSubDeviceNotification(MqttRecordReportDto reportDto) {
        String deviceId = reportDto.getDeviceId();
        List<SecurityDeviceDoor> securityDeviceDoorList = securityDeviceDoorService.lambdaQuery().eq(SecurityDeviceDoor::getDoorId, deviceId).list();
        if (CollUtil.isNotEmpty(securityDeviceDoorList)) {
            SecurityDeviceDoor securityDeviceDoor = securityDeviceDoorList.get(0);
            log.info("查询出属于门禁点设备，设备名称：{}", securityDeviceDoor.getDoorName());
            boolean update = securityDeviceDoorService.lambdaUpdate().eq(SecurityDeviceDoor::getId, securityDeviceDoor.getId()).set(SecurityDeviceDoor::getOnlineStatus, 1).update();
            if (update) {
                log.info("更新门禁点设备状态为上线成功");
            } else {
                log.info("更新门禁点设备状态为上线失败");
            }
        }
    }

    /**
     * 接收设备上线上报
     * @param reportDto
     */
    private void acceptDeviceNotification(MqttRecordReportDto reportDto) {
        // todo 具体设备接收来自IOT组件的上报，根据项目来定
    }

    private void carRecordProcess(MqttRecordReportDto reportDto1) {
        MqttDataReportDto mqttDataReportDto = reportDto1.getData();
        log.info("上报车辆信息：{}", JSONUtil.toJsonStr(mqttDataReportDto));
        if (ObjectUtil.isEmpty(mqttDataReportDto)) {
            log.info("上报车辆信息为空");
            return;
        }
        MqttDataCarReportDto carRecord = mqttDataReportDto.getCarRecord();

        String deviceId = "";
        String securityDeviceName = "未知道闸";
        Region region = null;
        String imageUrl = carRecord.getPicUrl();
        String carIndex = "";
        String plateNo = carRecord.getPlateId();
        Integer carType = null;
        Integer openType = 6;
        Integer state = 0;

        SecurityDevice securityDevice = securityDeviceService.lambdaQuery().eq(SecurityDevice::getDeviceSourceIndex, carRecord.getDeviceId()).one();
        if (ObjectUtil.isNotEmpty(securityDevice)) {
            deviceId = securityDevice.getId().toString();
            securityDeviceName = securityDevice.getSecurityDeviceName();
            state = carRecord.getSign();
            region = regionService.getById(securityDevice.getRegionId());
        }

        Car car = carService.lambdaQuery().eq(Car::getCarNo, carRecord.getPlateId()).one();
//        if(ObjectUtil.isEmpty(car)){
//            return;
//        }
        if (ObjectUtil.isNotEmpty(car)) {
            carIndex = car.getId().toString();
            plateNo = car.getCarNo();
            carType = car.getCarType();
        }

        SecurityDeviceRecord deviceRecord = SecurityDeviceRecord.builder()
                .id(SnowflakeUtils.getSnowflakeId())
                .date(DateUtils.formatDate(carRecord.getDate()))
                .deviceName(securityDeviceName)
                .state(state)
                .carIndex(carIndex)
                .plateNo(plateNo)
                .carType(carType)
                .picUrl(imageUrl)
                .recordType(OperateType.ServiceType.CAR.getCode())
                .deviceId(deviceId)
                .openType(openType)
                .regionId(ObjectUtil.isNotEmpty(region) ? region.getId().toString() : null)
                .regionName(ObjectUtil.isNotEmpty(region) ? region.getName() : "")
                .personName("")
                .build();
        if (this.save(deviceRecord)) {
            log.info("车辆上报记录保存成功：{}", JSONUtil.parse(deviceRecord));
        }
        inPlantRecordService.saveInPlantCarRecord(deviceRecord);
        sendMqttToAccessControl(deviceRecord);
    }

    private void personRecordProcess(MqttRecordReportDto reportDto1) {
        MqttDataReportDto mqttDataReportDto = reportDto1.getData();
        log.info("上报人员进出记录信息：{}", JSONUtil.toJsonStr(mqttDataReportDto));

        if (ObjectUtil.isNull(mqttDataReportDto)) {
            log.info("上报数据为空");
            return;
        }
        MqttDataPersonReportDto personRecord = mqttDataReportDto.getPersonRecord();
        Person person = getPerson(personRecord);
        if (ObjectUtil.isEmpty(person)) {
            log.info("人员不存在与系统中，接受到的人员唯一索引：{}", personRecord.getPersonNo());
            // 人员不存在时，仍然创建记录但使用上报数据中的信息
            createDeviceRecordForUnknownPerson(personRecord, "", personRecord.getSecurityDeviceDoorName(), personRecord.getSecurityDeviceDoorName(), null, "");
            return;
        }
        // 获取设备名称
        String deviceId = "";
        String securityDeviceName = "未知门禁";
        String securityDeviceDoorName = "未知门禁点";
        Region region = null;
        String cardNo = personRecord.getCardNo();

        String reportDeviceId = StrUtil.isNotBlank(personRecord.getDoorId()) ? personRecord.getDoorId() : StrUtil.isNotEmpty(personRecord.getDeviceId()) ? personRecord.getDeviceId() : null;

        if (StrUtil.isNotEmpty(reportDeviceId)) {
            SecurityDeviceDoor securityDeviceDoor = securityDeviceDoorService.lambdaQuery().eq(SecurityDeviceDoor::getDoorId, reportDeviceId).one();
            if (ObjectUtil.isEmpty(securityDeviceDoor)) {
                log.info("设备不存在与系统中，接受到的设备唯一索引：{}", reportDeviceId);
                return;
            }
            deviceId = securityDeviceDoor.getId().toString();
            securityDeviceDoorName = securityDeviceDoor.getDoorName();
            SecurityDevice securityDevice = securityDeviceService.getById(securityDeviceDoor.getSecurityDeviceId());
            securityDeviceName = securityDevice.getSecurityDeviceName();
            region = regionService.getById(securityDevice.getRegionId());
        }


        if (personRecord.getEventCode().equals(OpenDoorTypeEnum.NOT_SURE.getCode())) {
            return;
        }
        String imageUrl = getPersonRecordImageUrl(personRecord, person);
        String personName = getPersonRecordName(personRecord, person);
        String idCard = StrUtil.isNotBlank(personRecord.getIdCard()) ? personRecord.getIdCard() : null;
        String workUnit = Optional.ofNullable(person).map(Person::getPersonUnit).filter(StrUtil::isNotEmpty).orElse("");
        Integer personType = Optional.ofNullable(person).map(Person::getPersonJobType).filter(StrUtil::isNotEmpty).map(Integer::parseInt).orElse(null);
        Integer state = getPersonRecordState(personRecord);

        SecurityDeviceRecord deviceRecord = SecurityDeviceRecord.builder()
                .date(DateUtils.formatDate(personRecord.getDate()))
                .deviceId(deviceId)
                .deviceName(securityDeviceName)
                .securityDeviceDoorName(securityDeviceDoorName)
                .state(state)
                .personIndex(ObjectUtil.isNotEmpty(person) ? person.getId().toString() : "")
                .personName(personName)
                .idCard(idCard)
                .workUnit(workUnit)
                .personType(personType)
                .authType(ObjectUtil.isNotEmpty(person) ? StrUtil.isNotBlank(person.getPersonType()) ? Integer.parseInt(person.getPersonType()) : null : null)
                .recordType(OperateType.ServiceType.PERSON.getCode())
                .openType(Integer.parseInt(personRecord.getEventCode()))
                .picUrl(imageUrl)
                .regionId(ObjectUtil.isNotEmpty(region) ? region.getId().toString() : null)
                .regionName(ObjectUtil.isNotEmpty(region) ? region.getName() : "")
                .id(SnowflakeUtils.getSnowflakeId())
                .cardNo(cardNo)
                .build();
        if (this.save(deviceRecord)) {
            log.info("人员上报记录保存成功：{}", JSONUtil.parse(deviceRecord));
        }
        //1、非工作时间告警
        AccessAlertConfig accessAlertConfig = accessAlertConfigService.getAccessAlertConfig();
        String isAlarm = accessAlertConfig.getIsAlarm();
        Integer maxAccessCount = Integer.valueOf(accessAlertConfig.getMaxAccessCount());
        Date date = deviceRecord.getDate();
        //是人员开门
        if (!StrUtil.isEmpty(deviceRecord.getPersonIndex())) {
            Instant instant = date.toInstant();
            LocalDateTime localDateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalTime recordTime = localDateTime.toLocalTime();
            String startWorkTime = accessAlertConfig.getStartWorkTime();
            String endWorkTime = accessAlertConfig.getEndWorkTime();
            DateTimeFormatter dateTimeFormatter = NORM_TIME_PATTERN_HOUR_MINUTE_FORMAT.getDateTimeFormatter();
            LocalTime startTime = LocalTime.parse(startWorkTime, dateTimeFormatter);
            LocalTime endTime = LocalTime.parse(endWorkTime, dateTimeFormatter);
            Integer count = securityDeviceRecordMapper.selectTodyRecordCountByPersonId(deviceRecord.getPersonIndex());
            PersonBlackListAddQueryDto queryDto = new PersonBlackListAddQueryDto();
            //非工作时间作业告警加黑名单
            String regionName = ObjectUtil.isNotEmpty(region) ? region.getName() : "";
            queryDto.setIds(List.of(deviceRecord.getPersonIndex()));
            if (isAlarm.equals(IsAlarmEnum.ON.getCode()) && (recordTime.isBefore(startTime) || recordTime.isAfter(endTime))) {
                AlarmCenterEvent event = new AlarmCenterEvent(this);
                event.setType(AlarmCenterAlarmType.SECURITY_ALARM);
                event.setLevel(Integer.valueOf(AlarmLevelConstant.GENERAL));
                event.setRegionName(regionName);
                event.setTerminalDeviceName(securityDeviceDoorName);
                event.setValue("非法作业");
                event.setAlarmDesc(personName + "人员在" + regionName + "区域非法作业，请注意！");
                event.setAlarmTime(LocalDateTime.now().format(DateUtils.DATETIME_FORMATTER));
                event.setAlarmId(deviceRecord.getPersonIndex());
                event.setState(0);
                log.info("非工作时间作业告警，推送消息到告警中心,门禁：{}，人员：{}，区域：{}", deviceId, personName, regionName);
                applicationContext.publishEvent(event);
                log.info("非工作时间作业告警PersonBlackListAddQueryDto：{}", JSON.toJSONString(queryDto));
//                try {
//                    personService.addBlackList(queryDto);
//                } catch (Exception e) {
//                    log.info("加入黑名单失败", e);
//                }
            }

            //频繁进出超过上限次数告警加黑名单
            if (count != null && count > maxAccessCount) {
                AlarmCenterEvent event = new AlarmCenterEvent(this);
                event.setType(AlarmCenterAlarmType.SECURITY_ALARM);
                event.setLevel(Integer.valueOf(AlarmLevelConstant.GENERAL));
                event.setRegionName(regionName);
                event.setTerminalDeviceName(securityDeviceDoorName);
                event.setValue("频繁进出");
                event.setAlarmDesc(personName + "人员频繁出入！出入次数已超过" + maxAccessCount + "次！");
                event.setAlarmTime(LocalDateTime.now().format(DateUtils.DATETIME_FORMATTER));
                event.setAlarmId(deviceRecord.getPersonIndex());
                event.setState(0);
                log.info("频繁进出超过上限次数告警，推送消息到告警中心，门禁：{}，人员：{}，区域：{}", deviceId, personName, regionName);
                applicationContext.publishEvent(event);
                log.info("频繁进出超过上限次数告警PersonBlackListAddQueryDto：{}", JSON.toJSONString(queryDto));
//                try {
//                    personService.addBlackList(queryDto);
//                } catch (Exception e) {
//                    log.info("加入黑名单失败", e);
//                }
            }
            inPlantRecordService.saveInPlantPersonRecord(deviceRecord);
            // 出入口管理-门禁控制页面-mqtt推送
            sendMqttToAccessControl(deviceRecord);
        }
    }

    /**
     * 为未知人员创建设备记录
     */
    private void createDeviceRecordForUnknownPerson(MqttDataPersonReportDto personRecord, String deviceId,
                                                    String securityDeviceName, String securityDeviceDoorName,
                                                    Region region, String cardNo) {
        if (personRecord.getEventCode().equals(OpenDoorTypeEnum.NOT_SURE.getCode())) {
            return;
        }

        // 从上报数据中获取人员信息，设置默认值
        String personName = StrUtil.isNotBlank(personRecord.getPersonName()) ? personRecord.getPersonName() : "未知人员";
        String workUnit = StrUtil.isNotBlank(personRecord.getCompany()) ? personRecord.getCompany() : "未知单位";
        String idCard = StrUtil.isNotBlank(personRecord.getIdCard()) ? personRecord.getIdCard() : null;

        // 获取人员单位的映射关系 (typeCode -> name)
        Map<String, String> typeNameMap = commonTypeService.list().stream()
                .filter(type -> CommonConstant.TYPE_FOUR.equals(type.getCategory()))  // 4是单位
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));

        // 处理workUnit格式：截取最后一个/后面的数据，并映射到typeCode
        String workUnitTypeCode = processWorkUnitToTypeCode(workUnit, typeNameMap);

        String imageUrl = StrUtil.isNotBlank(personRecord.getPicUrl()) ? personRecord.getPicUrl() : "";
        Integer state = getPersonRecordState(personRecord);

        // 根据身份证号查询人员信息
        Integer personType = null; // 普工 默认人员类型
        if (StrUtil.isNotBlank(personRecord.getIdCard())) {
            try {
                Person person = personService.lambdaQuery()
                        .eq(Person::getPersonCertificateNum, personRecord.getIdCard())
                        .one();
                if (ObjectUtil.isNotEmpty(person) && StrUtil.isNotBlank(person.getPersonJobType())) {
                    try {
                        personType = Integer.parseInt(person.getPersonJobType());
                    } catch (NumberFormatException e) {
                        log.warn("人员工种类型转换失败: {}, 使用默认值1", person.getPersonJobType());
                        personType = null;
                    }
                }
            } catch (Exception e) {
                log.warn("根据身份证号查询人员信息失败: {}", personRecord.getIdCard(), e);
            }
        }

        SecurityDeviceRecord deviceRecord = SecurityDeviceRecord.builder()
                .date(DateUtils.formatDate(personRecord.getDate()))
                .deviceId(deviceId)
                .deviceName(securityDeviceName)
                .securityDeviceDoorName(securityDeviceDoorName)
                .state(state)
                .personName(personName)
                .idCard(idCard)
                .workUnit(workUnitTypeCode) // 使用处理后的typeCode
                .personType(personType) // 使用查询到的人员工种类型或默认值
                .authType(1) // 默认授权类型为普通人员
                .recordType(OperateType.ServiceType.PERSON.getCode())
                .openType(Integer.parseInt(personRecord.getEventCode()))
                .picUrl(imageUrl)
                .regionId(ObjectUtil.isNotEmpty(region) ? region.getId().toString() : null)
                .regionName(ObjectUtil.isNotEmpty(region) ? region.getName() : "")
                .id(SnowflakeUtils.getSnowflakeId())
                .cardNo(cardNo)
                .build();

        if (this.save(deviceRecord)) {
            log.info("未知人员上报记录保存成功：{}", JSONUtil.parse(deviceRecord));
        }

        // 为未知人员也推送门禁控制消息
        sendMqttToAccessControl(deviceRecord);
    }

    /**
     * 处理workUnit格式，截取最后一个/后面的数据并映射到typeCode
     *
     * @param workUnit 原始workUnit字符串，格式如："华能海门电厂/扩建区/江西水电工程"
     * @param typeNameMap 单位名称到typeCode的映射
     * @return 对应的typeCode，如果找不到匹配则返回原始字符串
     */
    private String processWorkUnitToTypeCode(String workUnit, Map<String, String> typeNameMap) {
        if (StrUtil.isBlank(workUnit)) {
            return "未知单位";
        }

        // 截取最后一个/后面的数据
        String unitName = workUnit;
        if (workUnit.contains("/")) {
            unitName = workUnit.substring(workUnit.lastIndexOf("/") + 1);
        }

        // 从typeNameMap中查找匹配的typeCode
        for (Map.Entry<String, String> entry : typeNameMap.entrySet()) {
            if (unitName.equals(entry.getValue())) {
                return entry.getKey(); // 返回typeCode
            }
        }

        // 如果找不到匹配，记录日志并返回原始字符串
        log.warn("未找到匹配的单位typeCode，单位名称: {}, 原始workUnit: {}", unitName, workUnit);
        return unitName;
    }

    /**
     * 获取出入标志
     * @param reportDto
     * @return
     */
    private Integer getPersonRecordState(MqttDataPersonReportDto reportDto) {
        Integer state = reportDto.getSign();
        if (OpenDoorTypeEnum.REMOTE.getCode().equals(reportDto.getEventCode())) {
            state = 2;
        }
        return state;
    }

    /**
     * 获取人员工作单位
     *
     * @param person
     * @return
     */
    private String getPersonRecordWorkUnit(Person person) {
        if (ObjectUtil.isNotEmpty(person)) {
            WorkGroup workGroup = workGroupService.getById(person.getPersonUnitId());
            return ObjectUtil.isNotEmpty(workGroup) ? workGroup.getName() : "";
        }
        return "";
    }

    private static String getPersonRecordName(MqttDataPersonReportDto personRecord, Person person) {
        String personName = "未知人员";
        if ("4".equals(personRecord.getEventCode())) {
            personName = "";
        }
        if (ObjectUtil.isNotEmpty(person)) {
            personName = person.getPersonName();
        }
        return personName;
    }

    private static String getPersonRecordImageUrl(MqttDataPersonReportDto personRecord, Person person) {
        String imageUrl;
        if (StrUtil.isNotEmpty(personRecord.getPicUrl())) {
            imageUrl = personRecord.getPicUrl();
        } else {
            if (ObjectUtil.isNotEmpty(person)) {
                imageUrl = person.getPersonFaceUrl();
            } else {
                imageUrl = "";
            }
        }
        return imageUrl;
    }

    /**
     * 出入口管理-门禁控制页面-mqtt推送
     *
     * @param deviceRecord
     */
    private void sendMqttToAccessControl(SecurityDeviceRecord deviceRecord) {
        MqttToAccessControlDto mqtt = null;
        if (deviceRecord.getRecordType() == 0) {
            mqtt = MqttToAccessControlDto.builder()
                    .securityDeviceName(deviceRecord.getDeviceName())
                    .personName(deviceRecord.getPersonName())
                    .workUnit(deviceRecord.getWorkUnit())
                    .cardNo(deviceRecord.getCardNo())
                    .eventType(deviceRecord.getOpenType().toString())
                    .imageUrl(deviceRecord.getPicUrl())
                    .time(DateUtil.format(deviceRecord.getDate(), "yyyy-MM-dd HH:mm:ss"))
                    .type("1")
                    .build();
            MqttUtils.send(mqtt, "accessControl");
            log.info("出入口管理-门禁控制页面-人员模块-mqtt推送完成：{}", JSONUtil.parse(mqtt));
        } else if (deviceRecord.getRecordType() == 1) {
            Car car = carService.getById(deviceRecord.getCarIndex());
            mqtt = MqttToAccessControlDto.builder()
                    .carNo(ObjectUtil.isNotEmpty(car) ? car.getCarNo() : deviceRecord.getCardNo())
                    .carType(ObjectUtil.isNotEmpty(car) ? car.getCarType().toString() : "")
                    .carColor(ObjectUtil.isNotEmpty(car) ? car.getColor() : "")
                    .eventType(deviceRecord.getOpenType().toString())
                    .securityDeviceName(deviceRecord.getDeviceName())
                    .imageUrl(deviceRecord.getPicUrl())
                    .time(DateUtil.format(deviceRecord.getDate(), "yyyy-MM-dd HH:mm:ss"))
                    .type("2")
                    .build();
            MqttUtils.send(mqtt, "accessControl");
            log.info("出入口管理-门禁控制页面-车辆模块-mqtt推送完成：{}", JSONUtil.parse(mqtt));
        }
    }


    private void sendMqttToIndex(SecurityDeviceRecord deviceRecord) {
        MqttToIndexCommonEventDto mqtt = new MqttToIndexCommonEventDto();
        mqtt.setEventTime(DateUtil.format(deviceRecord.getDate(), "yyyy-MM-dd HH:mm:ss"));
        Integer recordType = deviceRecord.getRecordType();
        if (ObjectUtil.isNotEmpty(recordType) && recordType == 0) {
            mqtt.setEventContent("人员：" + deviceRecord.getPersonName() + " 通过：" + deviceRecord.getDeviceName() + " 结果：" + EventTypeEnum.getByCode(deviceRecord.getOpenType().toString()).getDesc());
        } else if (ObjectUtil.isNotEmpty(recordType) && recordType == 1) {
            String accessType = "通过";
            if (deviceRecord.getState() == 1) {
                accessType = "进入";
            } else if (deviceRecord.getState() == 2) {
                accessType = "离开";
            }
            mqtt.setEventContent("车辆：" + deviceRecord.getPlateNo() + accessType + deviceRecord.getDeviceName());
        } else if (ObjectUtil.isNotEmpty(deviceRecord.getOpenType()) && deviceRecord.getOpenType() == 7) {
            SecurityDeviceDoor securityDeviceDoor = securityDeviceDoorService.getById(deviceRecord.getDeviceId());
            SecurityDevice securityDevice = securityDeviceService.getById(securityDeviceDoor.getSecurityDeviceId());
            String type = "1".equals(securityDevice.getSecurityDeviceType()) ? "门禁：" : "2".equals(securityDevice.getSecurityDeviceType()) ? "道闸：" : "";
            mqtt.setEventContent(type + deviceRecord.getDeviceName() + "恢复上线");
        }
        if (!"陌生人员".equals(deviceRecord.getPersonName())) {
            MqttUtils.send(mqtt, "indexCommon");
            log.info("首页-事件-mqtt推送完成：{}", JSONUtil.parse(mqtt));
        }
    }

    /**
     * 获取人员实体
     *
     * @param reportDto
     * @return
     */
    private Person getPerson(MqttDataPersonReportDto reportDto) {
        String personNo = reportDto.getPersonNo();
        return personService.lambdaQuery().eq(Person::getPersonSourceIndex, personNo).one();
    }


    @Override
    public Page<SecurityDeviceRecord> getAccessRecordPage(Integer type, AccessRecordQueryDto request) {

        log.info("分页获取设备记录：分页参数：{}", request);

        return lambdaQuery()
                .like(StringUtils.isNotBlank(request.getDeviceName()), SecurityDeviceRecord::getDeviceName,
                        request.getDeviceName())
                .eq(SecurityDeviceRecord::getRecordType, type)
                .like(!org.springframework.util.StringUtils.isEmpty(request.getPersonName()),
                        SecurityDeviceRecord::getPersonName, request.getPersonName())
                .like(!org.springframework.util.StringUtils.isEmpty(request.getPlateNo()), SecurityDeviceRecord::getPlateNo,
                        request.getPlateNo())
                .eq(!org.springframework.util.StringUtils.isEmpty(request.getAuthType()),
                        SecurityDeviceRecord::getAuthType, request.getAuthType())
                .eq(!org.springframework.util.StringUtils.isEmpty(request.getCarType()),
                        SecurityDeviceRecord::getCarType, request.getCarType())
                .ge(!org.springframework.util.StringUtils.isEmpty(request.getStartTime()),
                        SecurityDeviceRecord::getDate, request.getStartTime())
                .le(!org.springframework.util.StringUtils.isEmpty(request.getEndTime()), SecurityDeviceRecord::getDate,
                        request.getEndTime())
                .last("ORDER BY date DESC")
                .page(new Page<SecurityDeviceRecord>(request.getPageIndex(), request.getPageSize()));
    }

    @Override
    public DeviceAccStatVo getDeviceAccStat(Integer type) {
        DeviceAccStatVo result = new DeviceAccStatVo();
        if(type.equals(CommonConstant.INT_TYPE_ONE)){
            PersonPieVO personPieStatisticsNoLogin = getPersonPieStatisticsNoLogin();
            result.setInCount(Math.toIntExact(personPieStatisticsNoLogin.getEnterNumber()));
            result.setOutCount(Math.toIntExact(personPieStatisticsNoLogin.getExitNumber()));
            result.setInPlaceCount(Math.toIntExact(personPieStatisticsNoLogin.getPresentNumber()));
            result.setVisitorCount(Math.toIntExact(personPieStatisticsNoLogin.getVisitorPresentnumber()));
            return result;
        }
        List<SecurityDeviceRecord> todayDeviceRecord = getFilteredDeviceRecords(type);

        if (todayDeviceRecord.isEmpty()) {
            return result;
        }

        Map<String, List<SecurityDeviceRecord>> groups = groupDeviceRecordsByIndex(type, todayDeviceRecord);

        if (groups.isEmpty()) {
            return result;
        }

        DeviceAccStatVo deviceAccStat = calculateDeviceAccStatistics(groups);
        return deviceAccStat;
    }

    @Override
    public List<SecurityDeviceRecord> getLatestAccessControlRecord() {
        List<SecurityDeviceRecord> securityDeviceRecordList = securityDeviceRecordMapper.getLatestAccessControlRecord();
        return securityDeviceRecordList;
    }

    @Override
    public List<String> getZombieRecord(String isZombieTime) {
        // 创建QueryWrapper来构建查询条件
        QueryWrapper<SecurityDeviceRecord> queryWrapper = new QueryWrapper<>();
        // 第一个子查询：获取不在最近`isZombieTime`天内的记录的`person_index`
        QueryWrapper<SecurityDeviceRecord> subQueryWrapper = new QueryWrapper<>();
        subQueryWrapper.select("DISTINCT person_index")
                .ge("date", LocalDate.now().minusDays(Long.valueOf(isZombieTime))) // 当前日期减去isZombieTime天
                .eq("record_type", SecRecordTypeEnum.REN.getCode());

        // 第二个条件：查询主表，排除那些在第一个子查询中包含的`person_index`
        queryWrapper.select("DISTINCT person_index")
                .eq("record_type", SecRecordTypeEnum.REN.getCode())
                .isNotNull("person_index")
                .ne("person_index", "");
        List<String> subPersonIndexList = securityDeviceRecordMapper.selectList(subQueryWrapper)
                .stream()
                .map(SecurityDeviceRecord::getPersonIndex)
                .collect(Collectors.toList());
        // 执行查询并返回结果
        List<String> personIndexList = securityDeviceRecordMapper.selectList(queryWrapper)
                .stream()
                .map(SecurityDeviceRecord::getPersonIndex)
                .collect(Collectors.toList());
        List<String> list = new ArrayList<>();
        for (String s : personIndexList) {
            if (!subPersonIndexList.contains(s)) {
                list.add(s);
            }
        }
        return list;
    }

    @Override
    public List<CarTypeCountVO> getCarStatistics() {
        // 执行查询
        List<CarTypeCountVO> result = securityDeviceRecordMapper.getCarStatistics();
        // 获取车辆类型的映射关系 (typeCode -> name)
        Map<String, String> typeNameMap = commonTypeService.list().stream()
                .filter(type -> CommonConstant.TYPE_SIX.equals(type.getCategory()))  // 6是车辆类型
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));

        // 设置类型名称
        result.forEach(vo -> {
            String typeName = typeNameMap.get(vo.getCarType().toString());
            vo.setCarTypeName(typeName);
        });
        return result;
    }

    @Override
    public PersonPieVO getPersonPieStatistics() {
        // 查询今日人员进出记录
        List<SecurityDeviceRecord> recordList = getTodayPersonRecords();

        // 获取扩建区工作组信息
        List<WorkGroup> expansionAreaGroups = getExpansionAreaGroups();
        if (CollUtil.isEmpty(expansionAreaGroups)) {
            log.warn("No expansion area work groups found with simpleName: {}", expansionAreaName);
            return new PersonPieVO(0L, 0L, 0L, new ArrayList<>());
        }

        // 获取单位名称映射
        Map<String, String> typeNameMap = getUnitTypeNameMap();

        // 筛选扩建区记录
        recordList = filterExpansionAreaRecords(recordList, expansionAreaGroups, typeNameMap);
        if (CollUtil.isEmpty(recordList)) {
            return new PersonPieVO(0L, 0L, 0L, new ArrayList<>());
        }

        // 计算进场人数统计 - 根据idCard去重
        long enterNumber = calculateDistinctEnterCount(recordList);

        // 计算在场人数（与getPersonColStatistics保持一致）
        long presentNumber = calculatePresentPersonCountByUnit(recordList);

        // 计算出场人数（入场人数减去在场人数）
        long exitNumber = enterNumber - presentNumber;

        // 访客在场人数统计
        long visitorPresentNumber = calculateVisitorPresentCount(recordList);

        // 计算工种分布统计
        List<PersonTypeCount> personTypeCountList = calculatePersonTypeCountList(recordList);

        // 构建返回结果
        PersonPieVO personPieVO = new PersonPieVO();
        personPieVO.setEnterNumber(enterNumber);
        personPieVO.setExitNumber(exitNumber);
        personPieVO.setPresentNumber(presentNumber);
        personPieVO.setVisitorPresentnumber(visitorPresentNumber);
        personPieVO.setPersonTypeCountList(personTypeCountList);

        // 设置当前时间刻度
        String currentTimeScale = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:00"));
        personPieVO.setTimeScale(currentTimeScale);

        return personPieVO;
    }

    @Override
    public PersonColVO getPersonColStatistics() {
        // 查询今日人员进出记录
        List<SecurityDeviceRecord> recordList = getTodayPersonRecords();

        // 获取扩建区工作组信息
        List<WorkGroup> expansionAreaGroups = getExpansionAreaGroups();
        if (CollUtil.isEmpty(expansionAreaGroups)) {
            log.warn("No expansion area work groups found with simpleName: {}", expansionAreaName);
            return createEmptyPersonColVO();
        }

        // 获取单位名称映射
        Map<String, String> typeNameMap = getUnitTypeNameMap();

        // 筛选扩建区记录
        recordList = filterExpansionAreaRecords(recordList, expansionAreaGroups, typeNameMap);
        if (CollUtil.isEmpty(recordList)) {
            return createEmptyPersonColVO();
        }

        // 查询扩建区注册人员
        List<Person> registeredPersons = getRegisteredPersons(expansionAreaGroups);

        // 计算统计数据
        List<PersonUnitCount> personUnitCountList = calculatePersonUnitCountList(recordList, registeredPersons, typeNameMap);
        List<PersonWorkerTypeCount> workerTypeCounts = calculatePersonWorkerTypeCountList(recordList, typeNameMap);

        // 构建返回结果
        return buildPersonColVO(registeredPersons.size(), personUnitCountList, workerTypeCounts);
    }

    @Override
    public PersonColVO getPersonColStatisticsNoLogin() {
        // 查询今日人员进出记录
        List<SecurityDeviceRecord> recordList = getTodayPersonRecords();
        
        // 获取扩建区工作组信息
        List<WorkGroup> expansionAreaGroups = getExpansionAreaGroups();
        if (CollUtil.isEmpty(expansionAreaGroups)) {
            log.warn("No expansion area work groups found with simpleName: {}", expansionAreaName);
            return createEmptyPersonColVO();
        }

        // 获取单位名称映射
        Map<String, String> typeNameMap = getUnitTypeNameMap();
        
        // 筛选扩建区记录
        recordList = filterExpansionAreaRecords(recordList, expansionAreaGroups, typeNameMap);
        if (CollUtil.isEmpty(recordList)) {
            return createEmptyPersonColVO();
        }

        // 查询扩建区注册人员
        List<Person> registeredPersons = getRegisteredPersons(expansionAreaGroups);
        
        // 计算统计数据
        List<PersonUnitCount> personUnitCountList = calculatePersonUnitCountList(recordList, registeredPersons, typeNameMap);
        List<PersonWorkerTypeCount> workerTypeCounts = calculatePersonWorkerTypeCountList(recordList, typeNameMap);
        
        // 构建返回结果
        return buildPersonColVO(registeredPersons.size(), personUnitCountList, workerTypeCounts);
    }

    /**
     * 查询今日人员进出记录
     */
    private List<SecurityDeviceRecord> getTodayPersonRecords() {
        String today = DateUtils.getLocalDate().format(DateUtils.DATE_FORMATTER);
        return lambdaQuery()
                .eq(SecurityDeviceRecord::getRecordType, CommonConstant.ACCESS_RECORD_PERSON)
                .in(SecurityDeviceRecord::getState, CommonConstant.ACCESS_IN, CommonConstant.ACCESS_OUT)
                .likeRight(SecurityDeviceRecord::getDate, today)
                .list();
    }

    /**
     * 获取扩建区工作组信息
     */
    private List<WorkGroup> getExpansionAreaGroups() {
        List<WorkGroup> allGroups = workGroupService.getWorkGroupTree();
        return findExpansionAreaAndChildGroupIds(allGroups);
    }

    /**
     * 获取单位名称映射
     */
    private Map<String, String> getUnitTypeNameMap() {
        return commonTypeService.list().stream()
                .filter(type -> CommonConstant.TYPE_FOUR.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));
    }

    /**
     * 筛选扩建区记录
     */
    private List<SecurityDeviceRecord> filterExpansionAreaRecords(List<SecurityDeviceRecord> recordList, 
                                                                  List<WorkGroup> expansionAreaGroups, 
                                                                  Map<String, String> typeNameMap) {
        Set<String> expansionAreaSimpleNames = expansionAreaGroups.stream()
                .map(WorkGroup::getSimpleName)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        return recordList.stream()
                .filter(record -> {
                    String workUnitCode = record.getWorkUnit();
                    if (StrUtil.isBlank(workUnitCode)) {
                        return false;
                    }
                    String unitName = typeNameMap.get(workUnitCode);
                    return StrUtil.isNotBlank(unitName) && expansionAreaSimpleNames.contains(unitName);
                })
                .collect(Collectors.toList());
    }

    /**
     * 查询扩建区注册人员
     */
    private List<Person> getRegisteredPersons(List<WorkGroup> expansionAreaGroups) {
        List<Long> expansionAreaGroupIds = expansionAreaGroups.stream()
                .map(WorkGroup::getId)
                .collect(Collectors.toList());

        return personService.lambdaQuery()
                .eq(Person::getPersonType, CommonConstant.COMMON)
                .in(Person::getPersonUnitId, expansionAreaGroupIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList()))
                .list();
    }

    /**
     * 创建空的PersonColVO对象
     */
    private PersonColVO createEmptyPersonColVO() {
            PersonColVO defaultVO = new PersonColVO();
            defaultVO.setRegisterTotal(0L);
            defaultVO.setWorkingTotal(0L);
            defaultVO.setPersonUnitCountList(new ArrayList<>());
            defaultVO.setPersonWorkerTypeCountList(new ArrayList<>());
            return defaultVO;
        }

    /**
     * 构建PersonColVO对象
     */
    private PersonColVO buildPersonColVO(long registerCount, 
                                        List<PersonUnitCount> personUnitCountList, 
                                        List<PersonWorkerTypeCount> workerTypeCounts) {
        long workingTotal = personUnitCountList.stream()
                .mapToLong(PersonUnitCount::getWorkingCount)
                .sum();

        PersonColVO personColVO = new PersonColVO();
        personColVO.setRegisterTotal(registerCount);
        personColVO.setWorkingTotal(workingTotal);
        personColVO.setPersonUnitCountList(personUnitCountList);
        personColVO.setPersonWorkerTypeCountList(workerTypeCounts);
        return personColVO;
    }

    private List<SecurityDeviceRecord> getFilteredDeviceRecords(Integer type) {
        return this.lambdaQuery()
                .between(SecurityDeviceRecord::getDate, DateUtils.dateSearchFormat(DateUtils.DATE_FORMAT_TYPE_ONE, DateUtils.getLocalDateString()), DateUtils.dateSearchFormat(DateUtils.DATE_FORMAT_TYPE_TWO, DateUtils.getLocalDateString()))
                .ne(SecurityDeviceRecord::getOpenType, OperateType.OpenType.UNKNOWN.getValue())
                .ne(SecurityDeviceRecord::getOpenType, OperateType.OpenType.REMOTE.getValue())  // 使用枚举值排除远程开门
                .orderByAsc(SecurityDeviceRecord::getDate)
                .list()
                .stream()
                .filter(record -> isRecordRelevant(type, record))
                .peek(record -> setEmptyIndexToBlank(type, record))
                .collect(Collectors.toList());
    }

    private boolean isRecordRelevant(Integer type, SecurityDeviceRecord record) {
        if (type.equals(DeviceType.GUARD.getValue())) {
            return ObjectUtil.isNotNull(record.getRecordType())
                    && record.getRecordType() == 0 && (record.getState() == 1 || record.getState() == 2);
        } else if (type.equals(DeviceType.GATE.getValue())) {
            return ObjectUtil.isNotNull(record.getRecordType())
                    && record.getRecordType() == 1 && (record.getState() == 1 || record.getState() == 2);
        }
        return false;
    }

    private void setEmptyIndexToBlank(Integer type, SecurityDeviceRecord record) {
        if (type.equals(DeviceType.GUARD.getValue()) && StrUtil.isEmpty(record.getPersonIndex())) {
            record.setPersonIndex("");
        } else if (type.equals(DeviceType.GATE.getValue()) && StrUtil.isEmpty(record.getCarIndex())) {
            record.setCarIndex("");
        }
    }

    private Map<String, List<SecurityDeviceRecord>> groupDeviceRecordsByIndex(Integer type, List<SecurityDeviceRecord> records) {
        return records.stream()
                .collect(Collectors.groupingBy(record -> type.equals(DeviceType.GUARD.getValue()) ? record.getPersonIndex() : record.getCarIndex()));
    }

    private DeviceAccStatVo calculateDeviceAccStatistics(Map<String, List<SecurityDeviceRecord>> groups) {
        int inCount = 0;
        int outCount = 0;
        Set<String> visitorCount = new HashSet<>();
        Set<Integer> carTypes = new HashSet<>();
        int inPlaceCount = 0;

        for (List<SecurityDeviceRecord> recordList : groups.values()) {
            int curCount = 0;
            boolean isIn = false;

            for (SecurityDeviceRecord record : recordList) {
                if (record.getState() == 1) {
                    if (!isIn) {
                        isIn = true;
                        curCount++;
                    }
                    inCount++;
                } else if (record.getState() == 2) {
                    if (isIn) {
                        curCount--;
                        isIn = false;
                    }
                    outCount++;
                }

                if (record.getAuthType() != null && record.getAuthType() == 2 && record.getState() == 1) {
                    visitorCount.add(record.getPersonIndex());
                }

                if (record.getCarType() != null && !carTypes.contains(record.getCarType())) {
                    carTypes.add(record.getCarType());
                }
            }
            inPlaceCount += curCount;
        }

        return new DeviceAccStatVo(inCount, outCount, visitorCount.size(), carTypes.size(), inPlaceCount);
    }


    @Override
    public List<DeliveryPlanStatVO> getDeliveryPlanStatList(DeliveryPlanQueryDTO queryDTO) {
        // 1. 获取原始图纸交付数据
        List<DeliveryPlanVO> allPlans = getDeliveryPlanList(queryDTO);
        if (CollUtil.isEmpty(allPlans)) {
            return new ArrayList<>();
        }

        // 2. 按专业名称分组并统计
        return allPlans.stream()
                .collect(Collectors.groupingBy(DeliveryPlanVO::getMajorName))
                .entrySet().stream()
                .map(entry -> {
                    DeliveryPlanStatVO statVO = new DeliveryPlanStatVO();
                    statVO.setMajorName(entry.getKey());

                    // 计算计划交付数量（有计划交付时间的数量）
                    long planCount = entry.getValue().stream()
                            .filter(plan -> StrUtil.isNotBlank(plan.getPlanDeliveryTime()))
                            .count();
                    statVO.setPlanCount((int) planCount);

                    // 计算实际交付数量（有实际交付时间的数量）
                    long actualCount = entry.getValue().stream()
                            .filter(plan -> StrUtil.isNotBlank(plan.getActualDeliveryTime()))
                            .count();
                    statVO.setActualCount((int) actualCount);

                    return statVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取原始图纸交付计划列表
     */
    private List<DeliveryPlanVO> getDeliveryPlanList(DeliveryPlanQueryDTO queryDTO) {
        List<DeliveryPlanVO> deliveryPlanList = new ArrayList<>();

        try {
            log.info("开始获取图纸交付计划列表，查询参数：{}", JSONUtil.toJsonStr(queryDTO));
            // 调用接口获取数据
            String result = HttpUtil.post(misUrl + "/constrdeliveryplan/list", JSONUtil.toJsonStr(queryDTO), 6000);
            if (StrUtil.isBlank(result)) {
                log.warn("图纸交付计划接口返回数据为空");
                return deliveryPlanList;
            }

            // 解析返回的JSON数据
            JSONObject jsonObject = JSONUtil.parseObj(result);
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null) {
                log.warn("图纸交付计划接口返回数据格式异常，data为空");
                return deliveryPlanList;
            }

            // 直接获取data数组
            JSONArray planArray = (JSONArray) data.get("data");
            if (CollUtil.isEmpty(planArray)) {
                log.info("图纸交付计划列表为空");
                return deliveryPlanList;
            }

            // 遍历JSON数组并转换为DeliveryPlanVO对象
            for (int i = 0; i < planArray.size(); i++) {
                JSONObject planJson = planArray.getJSONObject(i);
                // 使用Hutool的BeanUtil转换
                DeliveryPlanVO planVO = JSONUtil.toBean(planJson, DeliveryPlanVO.class);
                // 处理特殊字段
                planVO.setMajorName(StrUtil.isBlank(planVO.getMajorName()) ? planVO.getMajor() : planVO.getMajorName());
                deliveryPlanList.add(planVO);
            }
        } catch (Exception e) {
            log.error("获取图纸交付计划列表失败：", e);
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "获取图纸交付计划列表失败");
        }
        return deliveryPlanList;
    }

    private List<WorkGroup> findExpansionAreaGroups(List<WorkGroup> groups) {
        List<WorkGroup> expansionGroups = new ArrayList<>();
        for (WorkGroup group : groups) {
            // 检查当前工作组是否是扩建区
            if (StrUtil.equals(group.getSimpleName(), expansionAreaName)) {
                expansionGroups.add(group);
            }
            // 递归检查子工作组
            if (CollUtil.isNotEmpty(group.getChildren())) {
                expansionGroups.addAll(findExpansionAreaGroups(group.getChildren()));
            }
        }
        return expansionGroups;
    }

    private List<Long> getAllChildWorkGroupIds(List<WorkGroup> allGroups, Long parentId) {
        List<Long> childIds = new ArrayList<>();
        for (WorkGroup group : allGroups) {
            if (group.getPid().equals(parentId)) {
                childIds.add(group.getId());
                // 递归获取子工作组的ID
                childIds.addAll(getAllChildWorkGroupIds(allGroups, group.getId()));
            }
        }
        return childIds;
    }

    /**
     * 递归查找扩建区工作组及其所有子工作组
     * @param groups 要搜索的工作组列表
     * @return 扩建区及其所有子工作组列表
     */
    private List<WorkGroup> findExpansionAreaAndChildGroupIds(List<WorkGroup> groups) {
        List<WorkGroup> expansionGroups = new ArrayList<>();
        if (CollUtil.isEmpty(groups)) {
            return expansionGroups;
        }

        for (WorkGroup group : groups) {
            // 检查当前工作组是否是扩建区
            if (StrUtil.equals(group.getSimpleName(), expansionAreaName)) {
                // 添加扩建区自身
                expansionGroups.add(group);
                // 递归添加其所有子工作组
                addAllChildGroups(group.getChildren(), expansionGroups);
            } else if (CollUtil.isNotEmpty(group.getChildren())) {
                // 如果当前工作组不是扩建区，继续递归查找其子工作组
                expansionGroups.addAll(findExpansionAreaAndChildGroupIds(group.getChildren()));
            }
        }
        return expansionGroups;
    }

    /**
     * 递归添加所有子工作组
     * @param children 子工作组列表
     * @param expansionGroups 存储工作组的列表
     */
    private void addAllChildGroups(List<WorkGroup> children, List<WorkGroup> expansionGroups) {
        if (CollUtil.isEmpty(children)) {
            return;
        }
        for (WorkGroup child : children) {
            expansionGroups.add(child);
            if (CollUtil.isNotEmpty(child.getChildren())) {
                addAllChildGroups(child.getChildren(), expansionGroups);
            }
        }
    }

    @Override
    public PersonPieVO getPersonPieStatisticsNoLogin() {
        // 查询今日人员进出记录
        List<SecurityDeviceRecord> recordList = getTodayPersonRecords();
        
        // 获取扩建区工作组信息
        List<WorkGroup> expansionAreaGroups = getExpansionAreaGroups();
        if (CollUtil.isEmpty(expansionAreaGroups)) {
            log.warn("No expansion area work groups found with simpleName: {}", expansionAreaName);
            return new PersonPieVO(0L, 0L, 0L, new ArrayList<>());
        }

        // 获取单位名称映射
        Map<String, String> typeNameMap = getUnitTypeNameMap();
        
        // 筛选扩建区记录
        recordList = filterExpansionAreaRecords(recordList, expansionAreaGroups, typeNameMap);
        if (CollUtil.isEmpty(recordList)) {
            return new PersonPieVO(0L, 0L, 0L,0L, new ArrayList<>());
        }

        // 计算进场人数统计 - 根据idCard去重
        long enterNumber = calculateDistinctEnterCount(recordList);
        
        // 计算在场人数（与getPersonColStatistics保持一致）
        long presentNumber = calculatePresentPersonCountByUnit(recordList);
        
        // 计算出场人数（入场人数减去在场人数）
        long exitNumber = enterNumber - presentNumber;
        
        // 访客在场人数统计
        long visitorPresentNumber = calculateVisitorPresentCount(recordList);
        
        // 计算工种分布统计
        List<PersonTypeCount> personTypeCountList = calculatePersonTypeCountList(recordList);
        
        // 构建返回结果
        PersonPieVO personPieVO = new PersonPieVO();
        personPieVO.setEnterNumber(enterNumber);
        personPieVO.setExitNumber(exitNumber);
        personPieVO.setPresentNumber(presentNumber);
        personPieVO.setVisitorPresentnumber(visitorPresentNumber);
        personPieVO.setPersonTypeCountList(personTypeCountList);
        
        // 设置当前时间刻度
        String currentTimeScale = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:00"));
        personPieVO.setTimeScale(currentTimeScale);

        return personPieVO;
    }

    /**
     * 计算在场人数（基于最新记录判断）
     */
    private long calculatePresentPersonCount(List<SecurityDeviceRecord> recordList) {
        // 按idCard分组，取每个人最新（date最大）的一条记录
        Map<String, SecurityDeviceRecord> latestRecordMap = new HashMap<>();
        for (SecurityDeviceRecord record : recordList) {
            String personKey = record.getIdCard();
            if (StrUtil.isBlank(personKey)) {
                continue;
            }
            SecurityDeviceRecord old = latestRecordMap.get(personKey);
            if (old == null || (record.getDate() != null && old.getDate() != null && record.getDate().after(old.getDate()))) {
                latestRecordMap.put(personKey, record);
            }
        }

        // 只统计最新记录为进场的人员
        return latestRecordMap.values().stream()
                .filter(r -> r.getState().equals(CommonConstant.ACCESS_IN))
                .count();
    }

    /**
     * 计算在场人数（与getPersonColStatistics保持一致，按单位统计）
     */
    private long calculatePresentPersonCountByUnit(List<SecurityDeviceRecord> recordList) {
        // 按单位分组统计在场人员（与calculatePersonUnitCountList保持一致）
        Map<String, List<SecurityDeviceRecord>> unitRecordsMap = recordList.stream()
                .filter(record -> StrUtil.isNotBlank(record.getWorkUnit()))
                .collect(Collectors.groupingBy(SecurityDeviceRecord::getWorkUnit));

        long totalPresentCount = 0;

        for (List<SecurityDeviceRecord> unitRecords : unitRecordsMap.values()) {
            // 先按idCard分组，取每个人最新（date最大）的一条记录
            Map<String, SecurityDeviceRecord> latestRecordMap = new HashMap<>();
            for (SecurityDeviceRecord record : unitRecords) {
                String personKey = record.getIdCard();
                if (StrUtil.isBlank(personKey)) {
                    continue;
                }
                SecurityDeviceRecord old = latestRecordMap.get(personKey);
                if (old == null || (record.getDate() != null && old.getDate() != null && record.getDate().after(old.getDate()))) {
                    latestRecordMap.put(personKey, record);
                }
            }

            // 只保留最新记录为进场的人员
            Set<String> presentPersonKeys = latestRecordMap.values().stream()
                    .filter(r -> r.getState().equals(CommonConstant.ACCESS_IN))
                    .map(SecurityDeviceRecord::getIdCard)
                .collect(Collectors.toSet());

            totalPresentCount += presentPersonKeys.size();
        }

        return totalPresentCount;
    }

    /**
     * 计算访客在场人数（基于最新记录判断）
     */
    private long calculateVisitorPresentCount(List<SecurityDeviceRecord> recordList) {
        // 按idCard分组，取每个人最新（date最大）的一条记录
        Map<String, SecurityDeviceRecord> latestRecordMap = new HashMap<>();
        for (SecurityDeviceRecord record : recordList) {
            String personKey = record.getIdCard();
            if (StrUtil.isBlank(personKey)) {
                continue;
            }
            SecurityDeviceRecord old = latestRecordMap.get(personKey);
            if (old == null || (record.getDate() != null && old.getDate() != null && record.getDate().after(old.getDate()))) {
                latestRecordMap.put(personKey, record);
            }
        }

        // 只统计最新记录为进场的访客
        return latestRecordMap.values().stream()
                .filter(r -> r.getState().equals(CommonConstant.ACCESS_IN) && 
                           CommonConstant.VISITOR.equals(r.getAuthType()))
                .count();
    }

    /**
     * 计算工种分布统计（基于最新记录判断）
     */
    private List<PersonTypeCount> calculatePersonTypeCountList(List<SecurityDeviceRecord> recordList) {
        // 按idCard分组，取每个人最新（date最大）的一条记录
        Map<String, SecurityDeviceRecord> latestRecordMap = new HashMap<>();
        for (SecurityDeviceRecord record : recordList) {
            String personKey = record.getIdCard();
            if (StrUtil.isBlank(personKey)) {
                continue;
            }
            SecurityDeviceRecord old = latestRecordMap.get(personKey);
            if (old == null || (record.getDate() != null && old.getDate() != null && record.getDate().after(old.getDate()))) {
                latestRecordMap.put(personKey, record);
            }
        }

        // 统计每种工种的在场人数
        Map<Integer, Long> personTypeCountMap = latestRecordMap.values().stream()
                .filter(record -> record.getState().equals(CommonConstant.ACCESS_IN) && record.getPersonType() != null)
                .collect(Collectors.groupingBy(
                        SecurityDeviceRecord::getPersonType,
                        Collectors.counting()
                ));

        // 转换为List<PersonTypeCount>
        List<PersonTypeCount> result = personTypeCountMap.entrySet().stream()
                .map(entry -> new PersonTypeCount(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

        // 获取工种名称映射并设置
        Map<String, String> jobTypeNameMap = commonTypeService.list().stream()
                .filter(type -> CommonConstant.TYPE_ONE.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));

        result.forEach(vo -> {
            String typeName = jobTypeNameMap.get(vo.getPersonType().toString());
            vo.setPersonTypeName(typeName);
        });

        return result;
    }

    /**
     * 计算单个单位的在场人数（基于最新记录判断）
     */
    private long calculateUnitPresentCount(List<SecurityDeviceRecord> unitRecords) {
        // 先按idCard分组，取每个人最新（date最大）的一条记录
        Map<String, SecurityDeviceRecord> latestRecordMap = new HashMap<>();
        for (SecurityDeviceRecord record : unitRecords) {
            String personKey = record.getIdCard();
            if (StrUtil.isBlank(personKey)) {
                continue;
            }
            SecurityDeviceRecord old = latestRecordMap.get(personKey);
            if (old == null || (record.getDate() != null && old.getDate() != null && record.getDate().after(old.getDate()))) {
                latestRecordMap.put(personKey, record);
            }
        }

        // 只统计最新记录为进场的人员
        return latestRecordMap.values().stream()
                .filter(r -> r.getState().equals(CommonConstant.ACCESS_IN))
                .count();
    }

    /**
     * 计算单个单位的工种统计（基于最新记录判断）
     */
    private List<WorkerTypeStatVO> calculateUnitWorkerTypeStats(List<SecurityDeviceRecord> unitRecords, Map<String, String> workerTypeNameMap) {
        // 先按idCard分组，取每个人最新（date最大）的一条记录
        Map<String, SecurityDeviceRecord> latestRecordMap = new HashMap<>();
        for (SecurityDeviceRecord record : unitRecords) {
            String personKey = record.getIdCard();
            if (StrUtil.isBlank(personKey)) {
                continue;
            }
            SecurityDeviceRecord old = latestRecordMap.get(personKey);
            if (old == null || (record.getDate() != null && old.getDate() != null && record.getDate().after(old.getDate()))) {
                latestRecordMap.put(personKey, record);
            }
        }

        // 统计每种工种的在场人数
        Map<Integer, Long> workerTypeCountMap = latestRecordMap.values().stream()
                .filter(record -> record.getState().equals(CommonConstant.ACCESS_IN) && record.getPersonType() != null)
                .collect(Collectors.groupingBy(
                        SecurityDeviceRecord::getPersonType,
                        Collectors.counting()
                ));

        return workerTypeCountMap.entrySet().stream()
                .map(typeEntry -> WorkerTypeStatVO.builder()
                        .workerType(typeEntry.getKey())
                        .workerTypeName(workerTypeNameMap.get(typeEntry.getKey().toString()))
                        .count(typeEntry.getValue().intValue())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<PersonPieVO> getPersonTrendStatisticsNoLogin(Integer timeRange, String currentTime) {
        // 如果未指定时间范围，默认为10小时
        timeRange = timeRange == null || timeRange <= 0 ? 10 : timeRange;

        // 获取当前时间（从参数获取或使用系统时间）
        LocalDateTime now = getCurrentTime(currentTime);

        // 获取当天的开始时间（00:00:00）
        LocalDateTime todayStart = now.toLocalDate().atStartOfDay();

        // 修改起始时间计算，多减去一个小时以腾出空间给当前时间
        LocalDateTime startTime = now.truncatedTo(ChronoUnit.HOURS).minusHours(timeRange);

        // 生成时间刻度（最后一个时间点使用当前分钟）
        List<String> timeScales = generateTimeScales(startTime, now, timeRange);

        // 查询今日所有记录（从当天开始）
        List<SecurityDeviceRecord> allRecords = getTimeRangeRecords(todayStart, now);

        // 获取扩建区工作组信息
        List<WorkGroup> expansionAreaGroups = getExpansionAreaGroups();
        if (CollUtil.isEmpty(expansionAreaGroups)) {
            log.warn("No expansion area work groups found with simpleName: {}", expansionAreaName);
            return new ArrayList<>();
        }

        // 获取单位名称映射
        Map<String, String> typeNameMap = getUnitTypeNameMap();

        // 筛选扩建区记录
        List<SecurityDeviceRecord> filteredRecords = filterExpansionAreaRecords(allRecords, expansionAreaGroups, typeNameMap);

        // 获取工种名称映射（只需获取一次）
        Map<String, String> jobTypeNameMap = getJobTypeNameMap();

        // 收集每个时间点的统计数据
        List<PersonPieVO> trendData = new ArrayList<>();

        // 按时间刻度处理数据
        for (int i = 0; i < timeRange; i++) {
            LocalDateTime currentTimeScale = startTime.plusHours(i + 1);
            // 最后一个时间点使用当前时间，其他使用整点
            LocalDateTime endTime = i == timeRange - 1 ? now : currentTimeScale;
            String timeScale = timeScales.get(i);

            // 筛选从当天开始到当前时间刻度的记录
            List<SecurityDeviceRecord> hourRecords = filterRecordsByTimeRange(filteredRecords, todayStart, endTime);

            if (CollUtil.isEmpty(hourRecords)) {
                PersonPieVO emptyData = createEmptyPersonPieVO(timeScale);
                trendData.add(emptyData);
                continue;
            }

            // 计算统计数据
            PersonPieVO hourData = calculateHourStatistics(hourRecords, jobTypeNameMap, timeScale);
            trendData.add(hourData);
        }

        // 按时间刻度排序（从早到晚）
        trendData.sort((a, b) -> {
            LocalTime timeA = LocalTime.parse(a.getTimeScale(), DateTimeFormatter.ofPattern("HH:mm"));
            LocalTime timeB = LocalTime.parse(b.getTimeScale(), DateTimeFormatter.ofPattern("HH:mm"));
            return timeA.compareTo(timeB);
        });

        // 筛选当天有效的时间刻度数据
        LocalDateTime finalNow = now;
        return trendData.stream()
                .filter(data -> {
                    // 解析时间刻度
                    LocalTime scaleTime = LocalTime.parse(data.getTimeScale(), DateTimeFormatter.ofPattern("HH:mm"));
                    // 只保留不超过当前时间的数据
                    return !scaleTime.isAfter(LocalTime.of(finalNow.getHour(), finalNow.getMinute()));
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取当前时间
     */
    private LocalDateTime getCurrentTime(String currentTime) {
        if (StringUtils.isNotBlank(currentTime)) {
            try {
                return LocalDateTime.parse(currentTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
            } catch (Exception e) {
                log.warn("Invalid currentTime format: {}, using system time instead", currentTime);
                return LocalDateTime.now();
            }
        } else {
            return LocalDateTime.now();
        }
    }

    /**
     * 生成时间刻度
     */
    private List<String> generateTimeScales(LocalDateTime startTime, LocalDateTime now, int timeRange) {
        List<String> timeScales = new ArrayList<>();
        for (int i = 0; i < timeRange - 1; i++) {
            timeScales.add(startTime.plusHours(i + 1).format(DateTimeFormatter.ofPattern("HH:00")));
        }
        // 添加当前时间（包含分钟）
        timeScales.add(now.format(DateTimeFormatter.ofPattern("HH:mm")));
        return timeScales;
    }

    /**
     * 查询时间范围内的记录
     */
    private List<SecurityDeviceRecord> getTimeRangeRecords(LocalDateTime startTime, LocalDateTime endTime) {
        return lambdaQuery()
                .eq(SecurityDeviceRecord::getRecordType, CommonConstant.ACCESS_RECORD_PERSON)
                .in(SecurityDeviceRecord::getState, CommonConstant.ACCESS_IN, CommonConstant.ACCESS_OUT)
                .ge(SecurityDeviceRecord::getDate, startTime)
                .le(SecurityDeviceRecord::getDate, endTime)
                .list();
    }

    /**
     * 获取工种名称映射
     */
    private Map<String, String> getJobTypeNameMap() {
        return commonTypeService.list().stream()
                .filter(type -> CommonConstant.TYPE_ONE.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));
    }

    /**
     * 按时间范围筛选记录
     */
    private List<SecurityDeviceRecord> filterRecordsByTimeRange(List<SecurityDeviceRecord> records, 
                                                               LocalDateTime startTime, 
                                                               LocalDateTime endTime) {
        return records.stream()
                .filter(record -> {
                    LocalDateTime recordTime = LocalDateTime.ofInstant(
                            record.getDate().toInstant(),
                            ZoneId.systemDefault());
                    // 只统计从当天0点到当前时间刻度的数据
                    return !recordTime.isBefore(startTime) && !recordTime.isAfter(endTime);
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建空的PersonPieVO对象
     */
    private PersonPieVO createEmptyPersonPieVO(String timeScale) {
        PersonPieVO emptyData = new PersonPieVO();
        emptyData.setEnterNumber(0L);
        emptyData.setExitNumber(0L);
        emptyData.setPresentNumber(0L);
        emptyData.setPersonTypeCountList(new ArrayList<>());
        emptyData.setTimeScale(timeScale);
        return emptyData;
    }

    /**
     * 计算小时统计数据
     */
    private PersonPieVO calculateHourStatistics(List<SecurityDeviceRecord> hourRecords, 
                                               Map<String, String> jobTypeNameMap, 
                                               String timeScale) {
        // 计算进场人数统计 - 根据idCard去重
        long enterNumber = calculateDistinctEnterCount(hourRecords);

        // 计算在场人数（与getPersonColStatisticsNoLogin保持一致，按单位统计）
        long presentNumber = calculatePresentPersonCountByUnit(hourRecords);

        // 计算出场人数（入场人数减去在场人数）
        long exitNumber = enterNumber - presentNumber;

        // 计算工种分布统计（基于最新记录判断）
        List<PersonTypeCount> personTypeCountList = calculatePersonTypeCountList(hourRecords);

        // 创建并添加该时间点的统计数据
        PersonPieVO hourData = new PersonPieVO();
        hourData.setEnterNumber(enterNumber);
        hourData.setExitNumber(exitNumber);
        hourData.setPresentNumber(presentNumber);
        hourData.setPersonTypeCountList(personTypeCountList);
        hourData.setTimeScale(timeScale);
        return hourData;
    }

    @Override
    public List<UnitStatisticsVO> getUnitStatistics() {
        // 查询今日人员进出记录
        List<SecurityDeviceRecord> recordList = getTodayPersonRecords();

        if (CollUtil.isEmpty(recordList)) {
            return new ArrayList<>();
        }

        // 获取扩建区工作组信息
        List<WorkGroup> expansionAreaGroups = getExpansionAreaGroups();
        if (CollUtil.isEmpty(expansionAreaGroups)) {
            log.warn("No expansion area work groups found with simpleName: {}", expansionAreaName);
            return new ArrayList<>();
        }

        // 获取单位名称映射
        Map<String, String> typeNameMap = getUnitTypeNameMap();

        // 筛选扩建区记录
        recordList = filterExpansionAreaRecords(recordList, expansionAreaGroups, typeNameMap);
        if (CollUtil.isEmpty(recordList)) {
            return new ArrayList<>();
        }

        // 获取工种名称映射
        Map<String, String> workerTypeNameMap = commonTypeService.list().stream()
                .filter(type -> CommonConstant.TYPE_ONE.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));

        // 按单位分组统计
        Map<String, List<SecurityDeviceRecord>> unitRecordsMap = recordList.stream()
                .collect(Collectors.groupingBy(SecurityDeviceRecord::getWorkUnit));

        // 构建统计结果
        return unitRecordsMap.entrySet().stream()
                .map(entry -> {
                    String unitCode = entry.getKey();
                    List<SecurityDeviceRecord> unitRecords = entry.getValue();

                    // 统计进场人数（根据身份证号码去重）
                    int enterCount = (int) unitRecords.stream()
                            .filter(r -> r.getState().equals(CommonConstant.ACCESS_IN))
                            .map(SecurityDeviceRecord::getIdCard)
                            .filter(StrUtil::isNotEmpty)
                            .distinct()
                            .count();

                    // 计算在场人数（基于最新记录判断）
                    int presentCount = (int) calculateUnitPresentCount(unitRecords);

                    // 计算出场人数（进场人数减去在场人数）
                    int exitCount = enterCount - presentCount;

                    // 按工种统计在场人数（基于最新记录判断）
                    List<WorkerTypeStatVO> workerTypeStats = calculateUnitWorkerTypeStats(unitRecords, workerTypeNameMap);

                    return UnitStatisticsVO.builder()
                            .unitCode(unitCode)
                            .unitName(typeNameMap.getOrDefault(unitCode, unitCode))
                            .enterCount(enterCount)
                            .presentCount(presentCount)
                            .exitCount(exitCount)
                            .workerTypeStats(workerTypeStats)
                            .build();
                })
                .sorted((unit1, unit2) -> Integer.compare(unit2.getEnterCount(), unit1.getEnterCount())) // 按进场人数降序排序
                .collect(Collectors.toList());
    }

    @Override
    public List<WorkerTypeStatVO> getWorkerTypeStatistics(String timeRange) {
        // 获取时间范围
        LocalDateTime startTime = getStartTimeByRange(timeRange);
        LocalDateTime endTime = LocalDateTime.now();

        // 查询时间范围内的所有记录
        LambdaQueryWrapper<SecurityDeviceRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecurityDeviceRecord::getRecordType, CommonConstant.ACCESS_RECORD_PERSON)
                .ge(SecurityDeviceRecord::getDate, startTime)
                .le(SecurityDeviceRecord::getDate, endTime);
        List<SecurityDeviceRecord> records = list(wrapper);

        // 获取所有相关人员
        List<String> personIds = records.stream()
                .map(SecurityDeviceRecord::getPersonIndex)
                .filter(StrUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(personIds)) {
            return new ArrayList<>();
        }

        // 查询人员详细信息
        List<Person> persons = personService.lambdaQuery()
                .in(Person::getId, personIds)
                .list();

        // 按单位分组统计
        Map<String, WorkerTypeStatVO.WorkerTypeStatVOBuilder> unitStatsMap = new HashMap<>();

        for (Person person : persons) {
            String unitCode = person.getPersonUnit();
            String workerType = person.getWorkerType();

            WorkerTypeStatVO.WorkerTypeStatVOBuilder builder = unitStatsMap.computeIfAbsent(unitCode,
                    k -> WorkerTypeStatVO.builder()
                            .unitCode(k)
                            .managerCount(0)
                            .safetyManagerCount(0)
                            .workerCount(0));

            // 根据工人类型进行统计
            if (WorkerTypeEnum.MANAGER.getCode().equals(workerType)) {
                builder.managerCount(builder.build().getManagerCount() + 1);
            } else if (WorkerTypeEnum.CONSTRUCTION_WORKER.getCode().equals(workerType)) {
                builder.safetyManagerCount(builder.build().getSafetyManagerCount() + 1);
            } else if (WorkerTypeEnum.Worker.getCode().equals(workerType)) {
                builder.workerCount(builder.build().getWorkerCount() + 1);
            }
        }

        // 获取单位名称映射
        Map<String, String> unitNameMap = commonTypeService.list().stream()
                .filter(type -> CommonConstant.TYPE_FOUR.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));

        // 构建最终结果
        return unitStatsMap.values().stream()
                .map(builder -> {
                    WorkerTypeStatVO stat = builder.build();
                    stat.setUnitName(unitNameMap.getOrDefault(stat.getUnitCode(), stat.getUnitCode()));
                    return stat;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<JobTypeStatVO> getWorkerJobStatistics(String timeRange) {
        // 获取时间范围
        LocalDateTime startTime = getStartTimeByRange(timeRange);
        LocalDateTime endTime = LocalDateTime.now();

        // 查询时间范围内的所有记录
        LambdaQueryWrapper<SecurityDeviceRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecurityDeviceRecord::getRecordType, CommonConstant.ACCESS_RECORD_PERSON)
                .ge(SecurityDeviceRecord::getDate, startTime)
                .le(SecurityDeviceRecord::getDate, endTime);
        List<SecurityDeviceRecord> records = list(wrapper);

        // 获取所有相关人员ID
        List<String> personIds = records.stream()
                .map(SecurityDeviceRecord::getPersonIndex)
                .filter(StrUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(personIds)) {
            return new ArrayList<>();
        }

        // 查询人员详细信息
        List<Person> persons = personService.lambdaQuery()
                .in(Person::getId, personIds)
                .list();

        // 按工种类型分组统计
        Map<String, Long> jobTypeCountMap = persons.stream()
                .filter(person -> StringUtils.isNotBlank(person.getPersonJobType()))
                .collect(Collectors.groupingBy(
                        Person::getPersonJobType,
                        Collectors.counting()
                ));

        // 获取工种类型名称映射
        Map<String, String> jobTypeNameMap = commonTypeService.list().stream()
                .filter(type -> CommonConstant.TYPE_ONE.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));

        // 构建统计结果
        return jobTypeCountMap.entrySet().stream()
                .map(entry -> JobTypeStatVO.builder()
                        .jobTypeCode(entry.getKey())
                        .jobTypeName(jobTypeNameMap.getOrDefault(entry.getKey(), entry.getKey()))
                        .count(entry.getValue())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<SpecialWorkerStatVO> getSpecialWorkerStatistics(String timeRange) {
        // 获取时间范围
        LocalDateTime startTime = getStartTimeByRange(timeRange);
        LocalDateTime endTime = LocalDateTime.now();

        // 查询时间范围内的所有记录
        LambdaQueryWrapper<SecurityDeviceRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecurityDeviceRecord::getRecordType, CommonConstant.ACCESS_RECORD_PERSON)
                .ge(SecurityDeviceRecord::getDate, startTime)
                .le(SecurityDeviceRecord::getDate, endTime);
        List<SecurityDeviceRecord> records = list(wrapper);

        // 获取所有相关人员ID
        List<String> personIds = records.stream()
                .map(SecurityDeviceRecord::getPersonIndex)
                .filter(StrUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(personIds)) {
            return new ArrayList<>();
        }

        // 查询人员详细信息
        List<Person> persons = personService.lambdaQuery()
                .in(Person::getId, personIds)
                .list();

        // 按工种分组统计特殊工种人数
        Map<String, Long> jobTypeSpecialWorkerCountMap = persons.stream()
                .filter(person -> "1".equals(person.getIsSpecialWorker()))
                .filter(person -> StringUtils.isNotBlank(person.getPersonJobType()))
                .collect(Collectors.groupingBy(
                        Person::getPersonJobType,
                        Collectors.counting()
                ));

        // 获取工种名称映射
        Map<String, String> jobTypeNameMap = commonTypeService.list().stream()
                .filter(type -> CommonConstant.TYPE_ONE.equals(type.getCategory()))
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,
                        CommonType::getName,
                        (v1, v2) -> v1
                ));

        // 构建统计结果
        return jobTypeSpecialWorkerCountMap.entrySet().stream()
                .map(entry -> SpecialWorkerStatVO.builder()
                        .specialJobTypeCode(entry.getKey())
                        .specialJobTypeName(jobTypeNameMap.getOrDefault(entry.getKey(), entry.getKey()))
                        .count(entry.getValue())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 根据时间范围计算开始时间
     * 支持的时间范围：
     * - day: 当天零点
     * - week: 一周前
     * - month: 一个月前
     *
     * @param timeRange 时间范围代码
     * @return 计算后的开始时间
     * @throws ServiceException 当时间范围无效时抛出异常
     */
    private LocalDateTime getStartTimeByRange(String timeRange) {
        try {
            return TimeRangeEnum.getByCode(timeRange).calculateStartTime(LocalDateTime.now());
        } catch (IllegalArgumentException e) {
            throw new ServiceException(ResultCode.PARAM_ERROR, e.getMessage());
        }
    }

    /**
     * 计算不同单位的不同工人类型数量统计
     * @param recordList 设备记录列表
     * @param typeNameMap 单位名称映射
     * @return 工人类型统计列表
     */
    private List<PersonWorkerTypeCount> calculatePersonWorkerTypeCountList(List<SecurityDeviceRecord> recordList, Map<String, String> typeNameMap) {
        // 按单位分组统计在场人员
        Map<String, List<SecurityDeviceRecord>> unitRecordsMap = recordList.stream()
                .filter(record -> StrUtil.isNotBlank(record.getWorkUnit()))
                .collect(Collectors.groupingBy(SecurityDeviceRecord::getWorkUnit));

        // 统计每个单位的工人类型数量
        Map<String, PersonWorkerTypeCount> unitWorkerTypeMap = new HashMap<>();

        for (Map.Entry<String, List<SecurityDeviceRecord>> entry : unitRecordsMap.entrySet()) {
            String unitCode = entry.getKey();
            List<SecurityDeviceRecord> unitRecords = entry.getValue();

            // 先按idCard分组，取每个人最新（date最大）的一条记录
            Map<String, SecurityDeviceRecord> latestRecordMap = new HashMap<>();
            for (SecurityDeviceRecord record : unitRecords) {
                String personKey = record.getIdCard();
                if (StrUtil.isBlank(personKey)) {
                    continue;
                }
                SecurityDeviceRecord old = latestRecordMap.get(personKey);
                if (old == null || (record.getDate() != null && old.getDate() != null && record.getDate().after(old.getDate()))) {
                    latestRecordMap.put(personKey, record);
                }
            }

            // 只保留最新记录为进场的人员
            Set<String> presentPersonKeys = latestRecordMap.values().stream()
                    .filter(r -> r.getState().equals(CommonConstant.ACCESS_IN))
                    .map(SecurityDeviceRecord::getIdCard)
                    .collect(Collectors.toSet());

            if (presentPersonKeys.isEmpty()) {
                continue;
            }

            // 查询在场人员的详细信息，只用idCard
            List<Person> presentPersons = new ArrayList<>();
            try {
                List<String> idCards = presentPersonKeys.stream()
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.toList());
                if (!idCards.isEmpty()) {
                    List<Person> personsByIdCard = personService.lambdaQuery()
                            .in(Person::getPersonCertificateNum, idCards)
                            .list();
                    presentPersons.addAll(personsByIdCard);
                }
            } catch (Exception e) {
                log.warn("查询在场人员信息失败", e);
            }

            // 统计该单位的不同工人类型
            PersonWorkerTypeCount typeCount = new PersonWorkerTypeCount();
            typeCount.setPersonUnit(unitCode);
            typeCount.setPersonUnitName(typeNameMap.getOrDefault(unitCode, unitCode));
            typeCount.setManagerCount(0);
            typeCount.setBuilderCount(0);
            typeCount.setSpecialWorkerCount(0);
            typeCount.setOtherWorkerCount(0);
            typeCount.setTotal(0);

            for (Person person : presentPersons) {
                boolean isSpecialWorker = SpecialWorkerEnum.YES.getCode().equals(person.getIsSpecialWorker());
                WorkerTypeEnum workerType = WorkerTypeEnum.getByCode(person.getWorkerType());

                if (isSpecialWorker) {
                    // 如果是特殊工种，只计入特殊工种，不再计入其他类型
                    typeCount.setSpecialWorkerCount(typeCount.getSpecialWorkerCount() + 1);
                } else {
                    // 如果不是特殊工种，才按工种类型分类计入
                    if (workerType == WorkerTypeEnum.MANAGER || workerType == WorkerTypeEnum.CONSTRUCTION_WORKER) {
                        typeCount.setManagerCount(typeCount.getManagerCount() + 1);
                    } else if (workerType == WorkerTypeEnum.Worker) {
                        typeCount.setBuilderCount(typeCount.getBuilderCount() + 1);
                    }
                }
            }

            // 计算总数（实际在场人数，避免重叠计算）
            typeCount.setTotal((Integer) presentPersonKeys.size());
            
            // 计算其他工人数量（总数减去管理者和施工者）
            typeCount.setOtherWorkerCount(typeCount.getTotal() - typeCount.getManagerCount() - typeCount.getBuilderCount());

            unitWorkerTypeMap.put(unitCode, typeCount);
        }

        // 获取单位优先级配置并排序
        Map<String, Integer> unitPriorityMap = getUnitPriorityMap();
        List<PersonWorkerTypeCount> result = new ArrayList<>();

        // 为每个优先级配置的单位创建统计对象
        unitPriorityMap.forEach((unitName, priority) -> {
            PersonWorkerTypeCount typeCount = new PersonWorkerTypeCount();
            typeCount.setPersonUnitName(unitName);
            typeCount.setManagerCount(0);
            typeCount.setBuilderCount(0);
            typeCount.setSpecialWorkerCount(0);
            typeCount.setOtherWorkerCount(0);
            typeCount.setTotal(0);

            // 从统计结果中查找对应单位的数据
            Optional<PersonWorkerTypeCount> matchingCount = unitWorkerTypeMap.values().stream()
                    .filter(count -> unitName.equals(count.getPersonUnitName()))
                    .findFirst();

            if (matchingCount.isPresent()) {
                PersonWorkerTypeCount existingCount = matchingCount.get();
                typeCount.setPersonUnit(existingCount.getPersonUnit());
                typeCount.setManagerCount(existingCount.getManagerCount());
                typeCount.setBuilderCount(existingCount.getBuilderCount());
                typeCount.setSpecialWorkerCount(existingCount.getSpecialWorkerCount());
                typeCount.setOtherWorkerCount(existingCount.getOtherWorkerCount());
                typeCount.setTotal(existingCount.getTotal());
            }

            result.add(typeCount);
        });

        // 按优先级排序
        result.sort((o1, o2) -> {
            Integer priority1 = unitPriorityMap.get(o1.getPersonUnitName());
            Integer priority2 = unitPriorityMap.get(o2.getPersonUnitName());
            return priority1.compareTo(priority2);
        });

        return result;
    }

    /**
     * 计算不同单位的注册人数和施工人数统计
     * @param recordList 设备记录列表
     * @param registeredPersons 注册人员列表
     * @param typeNameMap 单位名称映射
     * @return 单位统计列表
     */
    private List<PersonUnitCount> calculatePersonUnitCountList(List<SecurityDeviceRecord> recordList, 
                                                              List<Person> registeredPersons, 
                                                              Map<String, String> typeNameMap) {
        //单位注册人数
        Map<String, Long> unitRegisterMap = registeredPersons.stream()
                .collect(Collectors.groupingBy(Person::getPersonUnit, Collectors.counting()));

        // 按单位分组统计在场人员（与calculatePersonWorkerTypeCountList保持一致）
        Map<String, List<SecurityDeviceRecord>> unitRecordsMap = recordList.stream()
                .filter(record -> StrUtil.isNotBlank(record.getWorkUnit()))
                .collect(Collectors.groupingBy(SecurityDeviceRecord::getWorkUnit));

        // 统计每个单位的在场人数
        Map<String, Long> unitPresentCountMap = new HashMap<>();

        for (Map.Entry<String, List<SecurityDeviceRecord>> entry : unitRecordsMap.entrySet()) {
            String unitCode = entry.getKey();
            List<SecurityDeviceRecord> unitRecords = entry.getValue();

            // 先按idCard分组，取每个人最新（date最大）的一条记录
            Map<String, SecurityDeviceRecord> latestRecordMap = new HashMap<>();
            for (SecurityDeviceRecord record : unitRecords) {
                String personKey = record.getIdCard();
                if (StrUtil.isBlank(personKey)) {
                    continue;
                }
                SecurityDeviceRecord old = latestRecordMap.get(personKey);
                if (old == null || (record.getDate() != null && old.getDate() != null && record.getDate().after(old.getDate()))) {
                    latestRecordMap.put(personKey, record);
                }
            }

            // 只保留最新记录为进场的人员
            Set<String> presentPersonKeys = latestRecordMap.values().stream()
                    .filter(r -> r.getState().equals(CommonConstant.ACCESS_IN))
                    .map(SecurityDeviceRecord::getIdCard)
                    .collect(Collectors.toSet());

            unitPresentCountMap.put(unitCode, (long) presentPersonKeys.size());
        }

        List<PersonUnitCount> personUnitCountList = new ArrayList<>();
        unitRegisterMap.forEach((registerKey, registerValue) -> {
            PersonUnitCount personUnitCount = new PersonUnitCount();
            personUnitCount.setPersonUnit(registerKey);
            personUnitCount.setRegisterCount(registerValue);
            personUnitCount.setWorkingCount(unitPresentCountMap.getOrDefault(registerKey, 0L));
            personUnitCountList.add(personUnitCount);
        });

        // 设置类型名称
        personUnitCountList.forEach(vo -> {
            String typeName = typeNameMap.get(vo.getPersonUnit().toString());
            if (StrUtil.isNotEmpty(typeName)) {
                vo.setPersonUnitName(typeName);
            } else {
                vo.setPersonUnitName(vo.getPersonUnit());
            }
        });

        // 过滤掉施工人数为0或null的记录
        return personUnitCountList.stream()
                .filter(item -> item.getWorkingCount() != null && item.getWorkingCount() > 0)
                .collect(Collectors.toList());
    }

    @Override
    public FactoryPersonStatisticsVO getFactoryPersonStatisticsNoLogin() {
        // 查询今日人员进出记录
        List<SecurityDeviceRecord> recordList = getTodayPersonRecords();
        
        // 获取扩建区工作组信息
        List<WorkGroup> expansionAreaGroups = getExpansionAreaGroups();
        if (CollUtil.isEmpty(expansionAreaGroups)) {
            log.warn("No expansion area work groups found with simpleName: {}", expansionAreaName);
            return createEmptyFactoryPersonStatisticsVO();
        }

        // 获取单位名称映射
        Map<String, String> typeNameMap = getUnitTypeNameMap();
        
        // 筛选扩建区记录
        recordList = filterExpansionAreaRecords(recordList, expansionAreaGroups, typeNameMap);
        if (CollUtil.isEmpty(recordList)) {
            return createEmptyFactoryPersonStatisticsVO();
        }

        // 计算统计数据 - 根据idCard去重统计进场人数
        int enterNumber = (int) recordList.stream()
                .filter(e -> e.getState().equals(CommonConstant.ACCESS_IN))
                .map(SecurityDeviceRecord::getIdCard)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .count();

        // 计算在场人数（与getPersonColStatisticsNoLogin保持一致）
        int presentNumber = (int) calculatePresentPersonCountByUnit(recordList);

        // 计算出场人数（入场人数减去在场人数），确保不为负数
        int exitNumber = Math.max(enterNumber - presentNumber, 0);

        // 计算在场人员分类统计
        Map<String, Integer> workerTypeCounts = calculateFactoryWorkerTypeCounts(recordList);
        
        // 获取各类型人员数量，确保不为负数
        int managerCount = Math.max(workerTypeCounts.getOrDefault(WorkerTypeEnum.MANAGER.getCode(), 0), 0);
        int builderCount = Math.max(workerTypeCounts.getOrDefault(WorkerTypeEnum.Worker.getCode(), 0), 0);
        int safetyManagerCount = Math.max(workerTypeCounts.getOrDefault(WorkerTypeEnum.CONSTRUCTION_WORKER.getCode(), 0), 0);
        
        // 改造其他人员计算逻辑：其他人员 = 在场人员数量 - 管理人员 - 安全管理人员 - 建筑工人
        int otherCount = presentNumber - managerCount - safetyManagerCount - builderCount;
        // 确保其他人员数量不为负数
        otherCount = Math.max(otherCount, 0);

        return FactoryPersonStatisticsVO.builder()
                .enterNumber(enterNumber)
                .presentNumber(presentNumber)
                .exitNumber(exitNumber)
                .managerCount(managerCount)
                .builderCount(builderCount)
                .safetyManagerCount(safetyManagerCount)
                .otherCount(otherCount)
                .build();
    }

    /**
     * 创建空的工厂人员统计VO
     */
    private FactoryPersonStatisticsVO createEmptyFactoryPersonStatisticsVO() {
        return FactoryPersonStatisticsVO.builder()
                .enterNumber(0)
                .presentNumber(0)
                .exitNumber(0)
                .managerCount(0)
                .builderCount(0)
                .safetyManagerCount(0)
                .otherCount(0)
                .build();
    }

    /**
     * 计算工厂工人类型统计数量
     * @param recordList 设备记录列表
     * @return 工人类型统计映射
     */
    private Map<String, Integer> calculateFactoryWorkerTypeCounts(List<SecurityDeviceRecord> recordList) {
        // 先按idCard分组，取每个人最新（date最大）的一条记录
        Map<String, SecurityDeviceRecord> latestRecordMap = new HashMap<>();
        for (SecurityDeviceRecord record : recordList) {
            String personKey = record.getIdCard();
            if (StrUtil.isBlank(personKey)) {
                continue;
            }
            SecurityDeviceRecord old = latestRecordMap.get(personKey);
            if (old == null || (record.getDate() != null && old.getDate() != null && record.getDate().after(old.getDate()))) {
                latestRecordMap.put(personKey, record);
            }
        }

        // 只保留最新记录为进场的人员
        Set<String> presentPersonKeys = latestRecordMap.values().stream()
                .filter(r -> r.getState().equals(CommonConstant.ACCESS_IN))
                .map(SecurityDeviceRecord::getIdCard)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(presentPersonKeys)) {
            return Map.of(
                    WorkerTypeEnum.MANAGER.getCode(), 0,
                    WorkerTypeEnum.Worker.getCode(), 0,
                    WorkerTypeEnum.CONSTRUCTION_WORKER.getCode(), 0,
                    WorkerTypeEnum.OTHER.getCode(), 0
            );
        }

        // 查询在场人员信息
        List<Person> presentPersons = personService.lambdaQuery()
                .in(Person::getPersonCertificateNum, presentPersonKeys)
                .list();

        // 统计各类型人员数量
        int managerCount = 0;
        int builderCount = 0;
        int safetyManagerCount = 0;
        int otherCount = 0;

        for (Person person : presentPersons) {
            // 根据工种类型统计，使用WorkerTypeEnum枚举
            WorkerTypeEnum workerType = WorkerTypeEnum.getByCode(person.getWorkerType());
            
            // 优化其他人员计算逻辑：只要它不是管理人员、安全管理人员、工人，那他就是其他人员
            if (WorkerTypeEnum.MANAGER.equals(workerType)) {
                // 管理人员
                managerCount++;
            } else if (WorkerTypeEnum.CONSTRUCTION_WORKER.equals(workerType)) {
                // 安全管理人员
                safetyManagerCount++;
            } else if (WorkerTypeEnum.Worker.equals(workerType)) {
                // 工人（建筑工人）
                builderCount++;
            } else {
                // 其他人员：包括OTHER枚举值以及其他任何不属于上述三种类型的人员
                otherCount++;
            }
        }

        return Map.of(
                WorkerTypeEnum.MANAGER.getCode(), managerCount,
                WorkerTypeEnum.Worker.getCode(), builderCount,
                WorkerTypeEnum.CONSTRUCTION_WORKER.getCode(), safetyManagerCount,
                WorkerTypeEnum.OTHER.getCode(), otherCount
        );
    }

    @Override
    public List<ContractorWorkerTypeStatVO> getContractorWorkerTypeStatistics(String unitCode) {
        // 查询今日人员进出记录
        List<SecurityDeviceRecord> recordList = getTodayPersonRecords();
        
        // 获取扩建区工作组信息
        List<WorkGroup> expansionAreaGroups = getExpansionAreaGroups();
        if (CollUtil.isEmpty(expansionAreaGroups)) {
            log.warn("No expansion area work groups found with simpleName: {}", expansionAreaName);
            return new ArrayList<>();
        }

        // 获取单位名称映射
        Map<String, String> typeNameMap = getUnitTypeNameMap();
        
        // 筛选扩建区记录
        recordList = filterExpansionAreaRecords(recordList, expansionAreaGroups, typeNameMap);
        if (CollUtil.isEmpty(recordList)) {
            return new ArrayList<>();
        }

        // 获取在场人员信息
        List<Person> presentPersons = getPresentPersons(recordList);
        
        // 根据总包单位编码过滤人员
        if (StrUtil.isNotBlank(unitCode)) {
            presentPersons = presentPersons.stream()
                    .filter(person -> unitCode.equals(person.getPersonUnit()))
                    .collect(Collectors.toList());
        }
        
        // 获取工种名称映射
        Map<String, String> jobTypeNameMap = getJobTypeNameMap();
        
        // 获取工人类型名称映射
        Map<String, String> workerTypeNameMap = getWorkerTypeNameMap();

        // 按总包单位、分包单位、工人类型、工种、岗位分组统计
        Map<String, List<Person>> groupedPersons = presentPersons.stream()
                .collect(Collectors.groupingBy(person -> {
                    String contractorUnit = StrUtil.isNotBlank(person.getPersonUnit()) ? person.getPersonUnit() : "";
                    String subcontractUnitId = StrUtil.isNotBlank(person.getSubcontractUnitId()) ? person.getSubcontractUnitId() : "";
                    String workerType = StrUtil.isNotBlank(person.getWorkerType()) ? person.getWorkerType() : "";
                    String jobType = StrUtil.isNotBlank(person.getPersonJobType()) ? person.getPersonJobType() : "";
                    String personPost = StrUtil.isNotBlank(person.getPersonPost()) ? person.getPersonPost() : "";
                    
                    return String.join("|", contractorUnit, subcontractUnitId, workerType, jobType, personPost);
                }));

        // 构建统计结果
        List<ContractorWorkerTypeStatVO> result = new ArrayList<>();
        for (Map.Entry<String, List<Person>> entry : groupedPersons.entrySet()) {
            String[] keys = entry.getKey().split("\\|", -1); // 使用-1确保包含所有空字符串
            // 安全地获取数组元素，避免数组越界
            String contractorUnit = keys[0];
            String subcontractUnitId = keys[1];
            String workerType = keys[2];
            String jobType = keys[3];
            String personPost = keys[4];

            // 从第一个人员记录中获取分包单位名称
            String subcontractUnitName = "";
            if (!entry.getValue().isEmpty()) {
                Person firstPerson = entry.getValue().get(0);
                subcontractUnitName = StrUtil.isNotBlank(firstPerson.getSubcontractUnit()) ? firstPerson.getSubcontractUnit() : "";
            }

            ContractorWorkerTypeStatVO vo = ContractorWorkerTypeStatVO.builder()
                    .contractorUnitCode(contractorUnit)
                    .contractorUnitName(typeNameMap.getOrDefault(contractorUnit, contractorUnit))
                    .subcontractUnitCode(subcontractUnitId)
                    .subcontractUnitName(subcontractUnitName)
                    .workerTypeCode(workerType)
                    .workerTypeName(workerTypeNameMap.getOrDefault(workerType, workerType))
                    .jobTypeCode(jobType)
                    .jobTypeName(jobTypeNameMap.getOrDefault(jobType, jobType))
                    .personPost(personPost)
                    .presentCount(entry.getValue().size())
                    .build();
            
            result.add(vo);
        }
//        String str = JsonFileReader.readJsonFile("E:\\project\\haimen\\business-server\\security-protection\\src\\main\\java\\com\\teamway\\security\\service\\impl\\data.json");


//        List<ContractorWorkerTypeStatVO> insUserMangerVoList = JSON.parseObject(str, new TypeReference<List<ContractorWorkerTypeStatVO>>() {});

        // 按subcontractUnitName、workerTypeName、jobTypeName、personPost进行排序
        // 将空值排在最后
        result.sort(Comparator.comparing(ContractorWorkerTypeStatVO::getSubcontractUnitName,
                        (s1, s2) -> {
                            boolean blank1 = StrUtil.isBlank(s1);
                            boolean blank2 = StrUtil.isBlank(s2);
                            if (blank1 && !blank2) return 1;  // 空值排在后面
                            if (!blank1 && blank2) return -1; // 非空值排在前面
                            if (blank1 && blank2) return 0;   // 都是空值，相等
                            return s1.compareTo(s2); // 都不是空值，正常比较
                        })
                .thenComparing(ContractorWorkerTypeStatVO::getWorkerTypeName,
                        (s1, s2) -> {
                            boolean blank1 = StrUtil.isBlank(s1);
                            boolean blank2 = StrUtil.isBlank(s2);
                            if (blank1 && !blank2) return 1;
                            if (!blank1 && blank2) return -1;
                            if (blank1 && blank2) return 0;
                            return s1.compareTo(s2);
                        })
                .thenComparing(ContractorWorkerTypeStatVO::getJobTypeName,
                        (s1, s2) -> {
                            boolean blank1 = StrUtil.isBlank(s1);
                            boolean blank2 = StrUtil.isBlank(s2);
                            if (blank1 && !blank2) return 1;
                            if (!blank1 && blank2) return -1;
                            if (blank1 && blank2) return 0;
                            return s1.compareTo(s2);
                        })
                .thenComparing(ContractorWorkerTypeStatVO::getPersonPost,
                        (s1, s2) -> {
                            boolean blank1 = StrUtil.isBlank(s1);
                            boolean blank2 = StrUtil.isBlank(s2);
                            if (blank1 && !blank2) return 1;
                            if (!blank1 && blank2) return -1;
                            if (blank1 && blank2) return 0;
                            return s1.compareTo(s2);
                        }));

        return result;
    }

    @Override
    public IPage<FactoryPersonDetailVO> getFactoryPersonDetailPage(FactoryPersonDetailQueryDto queryDto) {
        // 查询今日人员进出记录
        List<SecurityDeviceRecord> recordList = getTodayPersonRecords();
        
        // 获取扩建区工作组信息
        List<WorkGroup> expansionAreaGroups = getExpansionAreaGroups();
        if (CollUtil.isEmpty(expansionAreaGroups)) {
            log.warn("No expansion area work groups found with simpleName: {}", expansionAreaName);
            return new Page<>();
        }

        // 获取单位名称映射
        Map<String, String> typeNameMap = getUnitTypeNameMap();
        
        // 筛选扩建区记录
        recordList = filterExpansionAreaRecords(recordList, expansionAreaGroups, typeNameMap);
        if (CollUtil.isEmpty(recordList)) {
            return new Page<>();
        }

        // 获取在场人员身份证号列表
        Set<String> presentPersonIds = getPresentPersonIds(recordList);
        if (CollUtil.isEmpty(presentPersonIds)) {
            return new Page<>();
        }

        // 使用 MyBatis Plus 分页查询
        Page<Person> page = new Page<>(queryDto.getPageIndex(), queryDto.getPageSize());
        
        // 调用 Mapper 进行分页查询，传递身份证号列表
        IPage<Person> personPage = ((PersonMapper) personService.getBaseMapper()).selectFactoryPersonDetailPage(
                page,
                new ArrayList<>(presentPersonIds),
                queryDto.getPersonName(),
                queryDto.getContractorUnitName(),
                queryDto.getSubcontractUnitName(),
                queryDto.getJobTypeName(),
                queryDto.getPersonPost()
        );

        // 获取工种名称映射
        Map<String, String> jobTypeNameMap = getJobTypeNameMap();

        // 构建详情列表
        List<FactoryPersonDetailVO> detailList = new ArrayList<>();
        List<Person> personList = personPage.getRecords();
        for (int i = 0; i < personList.size(); i++) {
            Person person = personList.get(i);
            FactoryPersonDetailVO vo = FactoryPersonDetailVO.builder()
                    .serialNumber((int) ((personPage.getCurrent() - 1) * personPage.getSize() + i + 1))
                    .personName(person.getPersonName())
                    .idCard(person.getPersonCertificateNum())
                    .contractorUnitCode(person.getPersonUnit())
                    .contractorUnitName(typeNameMap.getOrDefault(person.getPersonUnit(), person.getPersonUnit()))
                    .subcontractUnitCode(person.getSubcontractUnitId())
                    .subcontractUnitName(StrUtil.isNotBlank(person.getSubcontractUnit()) ? person.getSubcontractUnit() : "")
                    .jobTypeCode(person.getPersonJobType())
                    .jobTypeName(jobTypeNameMap.getOrDefault(person.getPersonJobType(), person.getPersonJobType()))
                    .personPost(StrUtil.isNotBlank(person.getPersonPost()) ? person.getPersonPost() : "")
                    .personFaceUrl(person.getPersonFaceUrl())
                    .build();
            
            detailList.add(vo);
        }

        // 创建新的分页结果，使用 IPage 格式
        Page<FactoryPersonDetailVO> resultPage = new Page<>(personPage.getCurrent(), personPage.getSize());
        resultPage.setRecords(detailList);
        resultPage.setTotal(personPage.getTotal());
        resultPage.setPages(personPage.getPages());
        
        return resultPage;
    }

    /**
     * 获取在场人员信息
     */
    private List<Person> getPresentPersons(List<SecurityDeviceRecord> recordList) {
        // 按身份证号分组，取每个人最新（date最大）的一条记录
        Map<String, SecurityDeviceRecord> latestRecordMap = new HashMap<>();
        for (SecurityDeviceRecord record : recordList) {
            String personKey = record.getIdCard();
            if (StrUtil.isBlank(personKey)) {
                continue;
            }
            SecurityDeviceRecord old = latestRecordMap.get(personKey);
            if (old == null || (record.getDate() != null && old.getDate() != null && record.getDate().after(old.getDate()))) {
                latestRecordMap.put(personKey, record);
            }
        }

        // 只保留最新记录为进场的人员，统一使用身份证号
        Set<String> presentPersonIds = latestRecordMap.values().stream()
                .filter(r -> r.getState().equals(CommonConstant.ACCESS_IN))
                .map(SecurityDeviceRecord::getIdCard)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(presentPersonIds)) {
            return new ArrayList<>();
        }

        // 查询人员详细信息，使用身份证号查询
        return personService.lambdaQuery()
                .in(Person::getPersonCertificateNum, presentPersonIds)
                .list();
    }

    /**
     * 获取在场人员身份证号列表
     */
    private Set<String> getPresentPersonIds(List<SecurityDeviceRecord> recordList) {
        // 按身份证号分组，取每个人最新（date最大）的一条记录
        Map<String, SecurityDeviceRecord> latestRecordMap = new HashMap<>();
        for (SecurityDeviceRecord record : recordList) {
            String personKey = record.getIdCard();
            if (StrUtil.isBlank(personKey)) {
                continue;
            }
            SecurityDeviceRecord old = latestRecordMap.get(personKey);
            if (old == null || (record.getDate() != null && old.getDate() != null && record.getDate().after(old.getDate()))) {
                latestRecordMap.put(personKey, record);
            }
        }

        // 只保留最新记录为进场的人员，统一使用身份证号
        return latestRecordMap.values().stream()
                .filter(r -> r.getState().equals(CommonConstant.ACCESS_IN))
                .map(SecurityDeviceRecord::getIdCard)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());
    }



    /**
     * 获取工人类型名称映射
     */
    private Map<String, String> getWorkerTypeNameMap() {
        return Arrays.stream(WorkerTypeEnum.values())
                .collect(Collectors.toMap(
                        WorkerTypeEnum::getCode,
                        WorkerTypeEnum::getDesc,
                        (v1, v2) -> v1
                ));
    }

    /**
     * 计算去重后的进场人数
     * 根据idCard去重统计进场人数，避免同一个人多次进场被重复计算
     * 
     * @param recordList 设备记录列表
     * @return 去重后的进场人数
     */
    private long calculateDistinctEnterCount(List<SecurityDeviceRecord> recordList) {
        return recordList.stream()
                .filter(e -> e.getState().equals(CommonConstant.ACCESS_IN))
                .map(SecurityDeviceRecord::getIdCard)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .count();
    }

    @Override
    public List<SubcontractDropdownVO> getSubcontractDropdownList(String contractorUnitCode) {
        // 调用现有方法获取总包单位工人类型统计数据
        List<ContractorWorkerTypeStatVO> contractorStats = getContractorWorkerTypeStatistics(contractorUnitCode);
        
        if (CollUtil.isEmpty(contractorStats)) {
            return new ArrayList<>();
        }

        // 获取总包单位名称
        Map<String, String> typeNameMap = getUnitTypeNameMap();
        String contractorUnitName = typeNameMap.getOrDefault(contractorUnitCode, contractorUnitCode);

        // 按分包单位分组，收集所有分包单位
        Set<String> subcontractUnitKeys = contractorStats.stream()
                .filter(stat -> StrUtil.isNotBlank(stat.getSubcontractUnitCode()))
                .map(stat -> {
                    String subcontractCode = stat.getSubcontractUnitCode();
                    String subcontractName = stat.getSubcontractUnitName();
                    return subcontractCode + "|" + subcontractName;
                })
                .collect(Collectors.toSet());

        List<SubcontractDropdownVO> dropdownList = new ArrayList<>();
        
        // 添加分包单位到下拉列表
        List<String> sortedSubcontractKeys = subcontractUnitKeys.stream()
                .sorted()
                .collect(Collectors.toList());
        
        for (String subcontractKey : sortedSubcontractKeys) {
            String[] keyParts = subcontractKey.split("\\|", -1);
            String subcontractUnitCode = keyParts.length > 0 ? keyParts[0] : "";
            String subcontractUnitName = keyParts.length > 1 ? keyParts[1] : "";
            
            SubcontractDropdownVO subcontractItem = SubcontractDropdownVO.builder()
                    .unitCode(subcontractUnitCode)
                    .unitName(subcontractUnitName)
                    .build();
            dropdownList.add(subcontractItem);
        }
        
        return dropdownList;
    }

    @Override
    public JobPostStatVO getJobPostStatistics(String contractorUnitCode, String targetUnitCode) {
        // 调用现有方法获取总包单位工人类型统计数据
        List<ContractorWorkerTypeStatVO> contractorStats = getContractorWorkerTypeStatistics(contractorUnitCode);
        
        if (CollUtil.isEmpty(contractorStats)) {
            return null;
        }

        // 获取单位名称映射
        Map<String, String> typeNameMap = getUnitTypeNameMap();
        
        // 优化逻辑：targetUnitCode只会传分包编码过来，不传就是使用所有数据
        String targetUnitName;
        String resultUnitCode;
        List<ContractorWorkerTypeStatVO> filteredStats;
        
        if (StrUtil.isBlank(targetUnitCode)) {
            // 不传targetUnitCode，使用所有数据（总包维度）
            targetUnitName = typeNameMap.getOrDefault(contractorUnitCode, contractorUnitCode);
            resultUnitCode = contractorUnitCode;
            filteredStats = contractorStats;
        } else {
            // 传了targetUnitCode，只使用该分包的数据
            resultUnitCode = targetUnitCode;
            filteredStats = contractorStats.stream()
                    .filter(stat -> targetUnitCode.equals(stat.getSubcontractUnitCode()))
                    .collect(Collectors.toList());
            
            // 从过滤的统计数据中获取分包单位名称
            targetUnitName = filteredStats.stream()
                    .findFirst()
                    .map(ContractorWorkerTypeStatVO::getSubcontractUnitName)
                    .filter(StrUtil::isNotBlank)
                    .orElse("未知分包单位");
        }

        if (CollUtil.isEmpty(filteredStats)) {
            return JobPostStatVO.builder()
                    .unitCode(resultUnitCode)
                    .unitName(targetUnitName)
                    .totalCount(0)
                    .jobPostItems(new ArrayList<>())
                    .build();
        }

        // 计算总人数
        int totalCount = filteredStats.stream()
                .mapToInt(ContractorWorkerTypeStatVO::getPresentCount)
                .sum();

        // 统计工种人数（按工种编码分组求和）
        Map<String, JobPostStatVO.JobPostItem> jobItemMap = new HashMap<>();
        // 统计岗位人数（按岗位名称分组求和）
        Map<String, JobPostStatVO.JobPostItem> postItemMap = new HashMap<>();
        
        for (ContractorWorkerTypeStatVO stat : filteredStats) {
            // 工种统计
            if (StrUtil.isNotBlank(stat.getJobTypeCode())) {
                String jobTypeKey = stat.getJobTypeCode();
                JobPostStatVO.JobPostItem jobItem = jobItemMap.computeIfAbsent(jobTypeKey, 
                        k -> JobPostStatVO.JobPostItem.builder()
                                .itemCode(stat.getJobTypeCode())
                                .itemName(stat.getJobTypeName())
                                .itemType("job")
                                .count(0)
                                .build());
                jobItem.setCount(jobItem.getCount() + stat.getPresentCount());
            }
            
            // 岗位统计
            if (StrUtil.isNotBlank(stat.getPersonPost())) {
                String postKey = stat.getPersonPost();
                JobPostStatVO.JobPostItem postItem = postItemMap.computeIfAbsent(postKey, 
                        k -> JobPostStatVO.JobPostItem.builder()
                                .itemCode(null) // 岗位没有编码
                                .itemName(stat.getPersonPost())
                                .itemType("post")
                                .count(0)
                                .build());
                postItem.setCount(postItem.getCount() + stat.getPresentCount());
            }
        }

        // 合并工种和岗位统计，并按人数降序排列
        List<JobPostStatVO.JobPostItem> jobPostItems = new ArrayList<>();
        jobPostItems.addAll(jobItemMap.values());
        jobPostItems.addAll(postItemMap.values());
        jobPostItems.sort((a, b) -> b.getCount().compareTo(a.getCount()));

        JobPostStatVO result = JobPostStatVO.builder()
                .unitCode(resultUnitCode)
                .unitName(targetUnitName)
                .totalCount(totalCount)
                .jobPostItems(jobPostItems)
                .build();
        
        return result;
    }

    @Override
    public CarInOutStatVO getCarInOutStatistics() {
        // 获取当日车辆进出记录
        List<Map<String, Object>> recordList = securityDeviceRecordMapper.getCarInOutStatistics();
        
        if (CollUtil.isEmpty(recordList)) {
            CarInOutStatVO emptyResult = new CarInOutStatVO();
            emptyResult.setInCount(0);
            emptyResult.setOutCount(0);
            emptyResult.setPresentCount(0);
            return emptyResult;
        }
        
        // 按车牌号分组统计进出次数
        Map<String, List<Map<String, Object>>> plateRecordMap = recordList.stream()
                .collect(Collectors.groupingBy(record -> (String) record.get("plateNo")));
        
        int totalInCount = 0;
        int totalOutCount = 0;
        int presentCount = 0;
        
        // 遍历每个车牌的记录
        for (Map.Entry<String, List<Map<String, Object>>> entry : plateRecordMap.entrySet()) {
            String plateNo = entry.getKey();
            List<Map<String, Object>> plateRecords = entry.getValue();
            
            // 统计该车牌的进出次数
            int inCount = (int) plateRecords.stream()
                    .mapToLong(record -> {
                        Integer state = (Integer) record.get("state");
                        return state != null && state == 1 ? 1L : 0L;
                    })
                    .sum();
            
            int outCount = (int) plateRecords.stream()
                    .mapToLong(record -> {
                        Integer state = (Integer) record.get("state");
                        return state != null && state == 2 ? 1L : 0L;
                    })
                    .sum();
            
            // 累计总进出次数
            totalInCount += inCount;
            totalOutCount += outCount;
            
            // 判断该车辆是否在场（进场次数大于出场次数）
            if (inCount > outCount) {
                presentCount++;
            }
            
            log.debug("车牌：{}，进场次数：{}，出场次数：{}，是否在场：{}", 
                    plateNo, inCount, outCount, inCount > outCount);
        }
        
        // 数据校验：出场数量不能超过入场数量
        if (totalOutCount > totalInCount) {
            log.warn("车辆出场数量({})超过入场数量({})，数据异常", totalOutCount, totalInCount);
            totalOutCount = totalInCount;
        }
        
        // 构建返回结果
        CarInOutStatVO statistics = new CarInOutStatVO();
        statistics.setInCount(totalInCount);
        statistics.setOutCount(totalOutCount);
        statistics.setPresentCount(presentCount);
        
        log.info("车辆进出场统计结果：{}", JSONUtil.toJsonStr(statistics));
        return statistics;
    }

}