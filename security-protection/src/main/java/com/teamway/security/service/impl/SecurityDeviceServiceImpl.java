package com.teamway.security.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.base.entity.Camera;
import com.teamway.base.entity.CameraPreset;
import com.teamway.base.entity.Region;
import com.teamway.base.service.CameraPresetService;
import com.teamway.base.service.CameraService;
import com.teamway.base.service.RegionService;
import com.teamway.common.constant.SecurityDeviceStateConstant;
import com.teamway.common.constant.SecurityDeviceTypeConstant;
import com.teamway.common.entity.BaseEntity;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.util.SnowflakeUtils;
import com.teamway.exception.ServiceException;
import com.teamway.mqtt.MqttUtils;
import com.teamway.security.common.constant.CacheConstant;
import com.teamway.security.common.constant.SecurityDeviceConstant;
import com.teamway.security.common.constant.VariousConstant;
import com.teamway.security.common.enums.ManufacturerEnum;
import com.teamway.security.common.enums.SecurityDeviceTypeEnum;
import com.teamway.security.common.enums.SecurityTopicEnum;
import com.teamway.security.common.enums.SyncDeviceTypeEnum;
import com.teamway.security.common.properties.SecurityComponentProperties;
import com.teamway.security.common.transfer.SecurityConfiguration.SecurityDeviceTransfer;
import com.teamway.security.common.tree.TreeData;
import com.teamway.security.dto.ResponseDto;
import com.teamway.security.dto.accessManagement.securityDevice.SecurityDeviceTreeBuildedQueryDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.SecurityDevice.MqttComponentTemplateDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.SecurityDevice.MqttSecurityDelQueryDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.SecurityDevice.MqttSecuritySyncQueryDto;
import com.teamway.security.dto.mqttdto.securityConfiguration.SecurityDevice.MqttSecuritySyncResultDto;
import com.teamway.security.dto.securityConfiguration.*;
import com.teamway.security.dto.securityConfiguration.securityDevice.SecurityDeviceListForAuth;
import com.teamway.security.entity.SecurityConfiguration.*;
import com.teamway.security.mapper.SecurityDeviceDoorMapper;
import com.teamway.security.mapper.SecurityDeviceMapper;
import com.teamway.security.service.*;
import com.teamway.security.util.SecurityComponentUtil;
import com.teamway.security.vo.securityConfiguration.securityDevice.SyncDeviceVo;
import com.teamway.security.vo.statistics.AccessStatisticsVo;
import com.teamway.security.vo.statistics.BarriersStatisticsVo;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @ClossName SecurityDeviceServiceImpl
 * @Description 安防设备信息 服务实现类
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
@Service
@Slf4j
public class SecurityDeviceServiceImpl extends ServiceImpl<SecurityDeviceMapper, SecurityDevice> implements SecurityDeviceService {

    private CameraService cameraService;
    private RegionService regionService;
    private CameraPresetService cameraPresetService;
    private SecurityDeviceCameraService securityDeviceCameraService;
    private SecurityDeviceDoorService securityDeviceDoorService;
    private DeviceFunctionService deviceFunctionService;
    private SecurityDeviceFunctionService securityDeviceFunctionService;
    private SecurityDevicePersonService securityDevicePersonService;
    private SecurityDeviceTransfer securityDeviceTransfer;
    private SecurityComponentUtil securityComponentUtil;
    private SecurityDeviceMapper securityDeviceMapper;
    private SecurityDeviceDoorMapper securityDeviceDoorMapper;
    private SecurityComponentProperties securityComponentProperties;
    private StringRedisTemplate redisTemplate;
    @Autowired
    public void setCameraService(CameraService cameraService) {
        this.cameraService = cameraService;
    }
    @Autowired
    public void setRegionService(RegionService regionService) {
        this.regionService = regionService;
    }
    @Autowired
    public void setCameraPresetService(CameraPresetService cameraPresetService) {
        this.cameraPresetService = cameraPresetService;
    }
    @Autowired
    public void setSecurityDeviceCameraService(SecurityDeviceCameraService securityDeviceCameraService) {
        this.securityDeviceCameraService = securityDeviceCameraService;
    }
    @Autowired
    public void setSecurityDeviceDoorService(SecurityDeviceDoorService securityDeviceDoorService) {
        this.securityDeviceDoorService = securityDeviceDoorService;
    }
    @Autowired
    public void setDeviceFunctionService(DeviceFunctionService deviceFunctionService) {
        this.deviceFunctionService = deviceFunctionService;
    }
    @Autowired
    public void setSecurityDeviceFunctionService(SecurityDeviceFunctionService securityDeviceFunctionService) {
        this.securityDeviceFunctionService = securityDeviceFunctionService;
    }
    @Autowired
    public void setSecurityDevicePersonService(SecurityDevicePersonService securityDevicePersonService) {
        this.securityDevicePersonService = securityDevicePersonService;
    }
    @Autowired
    public void setSecurityDeviceTransfer(SecurityDeviceTransfer securityDeviceTransfer) {
        this.securityDeviceTransfer = securityDeviceTransfer;
    }
    @Autowired
    public void setSecurityComponentUtil(SecurityComponentUtil securityComponentUtil) {
        this.securityComponentUtil = securityComponentUtil;
    }
    @Autowired
    public void setSecurityDeviceMapper(SecurityDeviceMapper securityDeviceMapper) {
        this.securityDeviceMapper = securityDeviceMapper;
    }
    @Autowired
    public void setSecurityDeviceDoorMapper(SecurityDeviceDoorMapper securityDeviceDoorMapper) {
        this.securityDeviceDoorMapper = securityDeviceDoorMapper;
    }
    @Autowired
    public void setSecurityComponentProperties(SecurityComponentProperties securityComponentProperties) {
        this.securityComponentProperties = securityComponentProperties;
    }
    @Autowired
    public void setRedisTemplate(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void addSecurityDevice(SecurityDeviceAddOrUpdateQueryDto dto) {
        checkSecurityDevice(dto);

        SecurityDevice securityDevice = securityDeviceTransfer.addDto2Entity(dto);
        sendSecurityDeviceSyncDto(securityDevice);

        if (!save(securityDevice)) {
            log.info("安防设备保存失败，保存实体：{}", JSON.toJSON(securityDevice));
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "安防设备保存失败");
        }
        batchSaveSecurityDeviceDoor(securityDevice.getSecurityDevicePointList(), securityDevice.getId());
        // 保存设备关联功能
        saveDeviceFunction(securityDevice.getId(), dto.getFunction());
    }

    /**
     * 保存设备功能
     *
     * @param id       设备id
     * @param function 功能集合
     */
    private void saveDeviceFunction(Long id, List<String> function) {
        securityDeviceFunctionService.lambdaUpdate().eq(SecurityDeviceFunction::getSecurityDeviceId, id).remove();
        List<SecurityDeviceFunction> securityDeviceFunctionList = new ArrayList<>();
        for (String entity : function) {
            securityDeviceFunctionList.add(new SecurityDeviceFunction(id, Long.parseLong(entity)));
        }
        securityDeviceFunctionService.saveBatch(securityDeviceFunctionList);
    }

    /**
     * 保存安防设备到组件
     *
     * @param mqttComponentTemplateDto
     */
    private String saveSecurityDeviceToComponent(MqttComponentTemplateDto<MqttSecuritySyncQueryDto> mqttComponentTemplateDto) {
        ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(mqttComponentTemplateDto, "device", "sync", CacheConstant.syncDeviceTopicKey, securityComponentProperties.getComponentVersion(), mqttComponentTemplateDto.getSerialId(), "1");
        if (responseDto.getCode() != 0) {
            log.info("安防设备保存失败，请求安防设备组件-保存失败，返回信息：{}", responseDto.getMessage());
            throw new ServiceException(ResultCode.OPERATION_FAILURE, responseDto.getMessage());
        }
        if (ObjectUtil.isEmpty(mqttComponentTemplateDto.getParams().getDeviceId())) {
            MqttSecuritySyncResultDto result = JSONObject.parseObject(responseDto.getData().toString(), MqttSecuritySyncResultDto.class);
            return result.getDeviceId();
        }
        return "";
    }

    /**
     * 构建请求实体param
     *
     * @param securityDeviceDoor
     * @param deviceType
     * @return
     */
    private MqttSecuritySyncQueryDto buildSecurityDeviceSyncParamDto(SecurityDeviceDoor securityDeviceDoor, String deviceType, List<String> function) {
        return MqttSecuritySyncQueryDto.builder()
                .deviceType(Integer.parseInt(deviceType))
                .ip(securityDeviceDoor.getIp())
                .name(securityDeviceDoor.getDoorName())
                .password(securityDeviceDoor.getPassword())
                .port(securityDeviceDoor.getPort())
                .username(securityDeviceDoor.getUsername())
                .function(function)
                .deviceId(securityDeviceDoor.getDeviceSourceIndex())
                .build();
    }

    @Override
    public IPage<SecurityDevice> listSecurityDevices(SecurityDeviceListQueryDto dto) {
        Page<SecurityDevice> page = new Page<>(dto.getPageIndex(), dto.getPageSize());
        securityDeviceMapper.selectPage(dto,page);
        // 获取所有 SecurityDevice 对应的 SecurityDeviceDoor 信息
        List<Long> securityDeviceIds = page.getRecords().stream().map(SecurityDevice::getId).collect(Collectors.toList());
        Map<Long, List<SecurityDeviceDoor>> doorMap = securityDeviceDoorService.lambdaQuery()
                .in(SecurityDeviceDoor::getSecurityDeviceId, securityDeviceIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(SecurityDeviceDoor::getSecurityDeviceId));

        // 获取所有 SecurityDevice 对应的 SecurityDeviceFunction 信息
        Map<Long, List<String>> functionMap = securityDeviceFunctionService.lambdaQuery()
                .in(SecurityDeviceFunction::getSecurityDeviceId, securityDeviceIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(SecurityDeviceFunction::getSecurityDeviceId,
                        Collectors.mapping(securityDeviceFunction -> String.valueOf(securityDeviceFunction.getDeviceFunctionId()), Collectors.toList())));

        // 将信息设置到 SecurityDevice 对象中
        for (SecurityDevice securityDevice : page.getRecords()) {
            securityDevice.setSecurityDevicePointList(doorMap.getOrDefault(securityDevice.getId(), Collections.emptyList()));
            securityDevice.setDeviceTypeCode(SecurityDeviceTypeEnum.getDescByCode(securityDevice.getDeviceTypeCode()));
            securityDevice.setFunction(functionMap.getOrDefault(securityDevice.getId(), Collections.emptyList()));
        }
        return page;
    }

    @Override
    public IPage<SecurityDevice> selectPage(SecurityDeviceListQueryDto dto) {
        checkQueryDto(dto);

        // 单表分页查询 SecurityDevice
        LambdaQueryWrapper<SecurityDevice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .select(SecurityDevice::getId,
                        SecurityDevice::getSecurityDeviceName,
                        SecurityDevice::getSecurityDeviceType,
                        SecurityDevice::getManufacturer,
                        SecurityDevice::getRegionId,
                        SecurityDevice::getDeviceSourceIndex,
                        SecurityDevice::getDeviceTypeCode,
                        SecurityDevice::getCreateTime,
                        SecurityDevice::getOnline)
                .like(StrUtil.isNotBlank(dto.getSecurityDeviceName()), SecurityDevice::getSecurityDeviceName, dto.getSecurityDeviceName())
                .eq(StrUtil.isNotBlank(dto.getManufacturer()), SecurityDevice::getManufacturer, dto.getManufacturer())
                .eq(ObjectUtil.isNotNull(dto.getRegionId()), SecurityDevice::getRegionId, dto.getRegionId())
                .eq(StrUtil.isNotBlank(dto.getType()), SecurityDevice::getSecurityDeviceType, dto.getType())
                .orderByDesc(SecurityDevice::getCreateTime)
                .orderByAsc(SecurityDevice::getId);

        Page<SecurityDevice> page = new Page<>(dto.getPageIndex(), dto.getPageSize());
        securityDeviceMapper.selectPage(page, queryWrapper);

        // 获取 SecurityDevice 的 ID 列表
        List<Long> securityDeviceIds = page.getRecords().stream().map(SecurityDevice::getId).collect(Collectors.toList());

        // 根据 SecurityDevice ID 查询 SecurityDeviceDoor 信息
        Map<Long, List<SecurityDeviceDoor>> doorMap = securityDeviceDoorService.lambdaQuery()
                .in(CollUtil.isNotEmpty(securityDeviceIds), SecurityDeviceDoor::getSecurityDeviceId, securityDeviceIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(SecurityDeviceDoor::getSecurityDeviceId));

        // 设置 SecurityDevice 对应的 SecurityDeviceDoor 信息
        for (SecurityDevice securityDevice : page.getRecords()) {
            securityDevice.setSecurityDevicePointList(doorMap.getOrDefault(securityDevice.getId(), Collections.emptyList()));
            securityDevice.setDeviceTypeCode(SecurityDeviceTypeEnum.getDescByCode(securityDevice.getDeviceTypeCode()));
        }

        return page;
    }


    @Override
    public List<DeviceFunction> getAllFunction() {
        List<DeviceFunction> list = deviceFunctionService.list();
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException(ResultCode.NOT_INIT, "设备功能配置");
        }
        return list;
    }

    @Override
    public List<TreeData> getRegCameraTree(SecurityDeviceTreeBuildedQueryDto dto) {
        //1.构建区域树
        List<TreeData> regionTreeVos = buildRegionTree(Region.ROOT_REGION_ID, regionService.list());
        //2. 遍历区域树，查询每一个区域下的安防设备，将安防设备构建成TreeData，然后添加到区域的children中
        addSecurityDeviceInRegionTree(regionTreeVos, dto.getType());
        return regionTreeVos;
    }

    @Override
    public List<SecurityDeviceListForAuth> listForAuthSecurityDevices(SecurityDeviceListQueryDto dto) {
        List<SecurityDeviceListForAuth> securityDeviceListForAuths = new ArrayList<>();

        // 获取区域下的安防设备
        List<SecurityDevice> securityDeviceList = this.lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(dto.getRegionId()), SecurityDevice::getRegionId, dto.getRegionId())
                .eq(SecurityDevice::getSecurityDeviceType, SecurityDeviceConstant.DOOR_TYPE)
                .list();

        // 收集所有安防设备ID
        Set<Long> securityDeviceIds = securityDeviceList.stream()
                .map(SecurityDevice::getId)
                .collect(Collectors.toSet());

        // 如果安防设备ID为空，则直接返回空列表
        if (securityDeviceIds.isEmpty()) {
            return securityDeviceListForAuths;
        }

        // 如果带了人员参数，则查询人员授权的安防设备
        Long personId = dto.getPersonId();
        List<Long> personDoorIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(personId)){
            List<SecurityDevicePerson> securityDevicePersonList = securityDevicePersonService.lambdaQuery()
                    .eq(SecurityDevicePerson::getPersonId, personId).list();
            personDoorIds = securityDevicePersonList.stream().map(SecurityDevicePerson::getDeviceId).collect(Collectors.toList());
        }

        // 批量查询安防设备门信息
        List<SecurityDeviceDoor> securityDeviceDoors = securityDeviceDoorService.lambdaQuery()
                .notIn(CollUtil.isNotEmpty(personDoorIds),SecurityDeviceDoor::getId,personDoorIds)
                .in(SecurityDeviceDoor::getSecurityDeviceId, securityDeviceIds)
                .list();

        // 构建安防设备授权列表
        securityDeviceList.forEach(securityDevice -> {
            securityDeviceDoors.stream()
                    .filter(securityDeviceDoor -> securityDeviceDoor.getSecurityDeviceId().equals(securityDevice.getId()))
                    .forEach(securityDeviceDoor -> securityDeviceListForAuths.add(SecurityDeviceListForAuth.builder()
                            .securityDeviceId(securityDevice.getId())
                            .securityDeviceName(securityDevice.getSecurityDeviceName())
                            .securityDeviceDoorId(securityDeviceDoor.getId())
                            .securityDeviceDoorName(securityDeviceDoor.getDoorName())
                            .build()));
        });

        return securityDeviceListForAuths;
    }

    @Override
    public String getSecurityDeviceIndexByType(String type){
        List<SecurityDevice> securityDevice = this.lambdaQuery().eq(SecurityDevice::getSecurityDeviceType, type).list();
        if (CollUtil.isEmpty(securityDevice)){
            throw new ServiceException(ResultCode.OPERATION_FAILURE,"请先初始化安防设备信息，然后再执行此操作");
        }
        return securityDevice.get(0).getDeviceSourceIndex();
    }

    @Override
    public AccessStatisticsVo getAccessStatistics() {
        AccessStatisticsVo accessStatisticsVo = new AccessStatisticsVo();
        List<String> onlineList = this.listObjs(new LambdaQueryWrapper<SecurityDevice>()
                .eq(SecurityDevice::getSecurityDeviceType, SecurityDeviceTypeConstant.ACCESS)
                .select(SecurityDevice::getOnline));
        //在线数
        long online = onlineList.stream().filter(e -> e.equals(SecurityDeviceStateConstant.ON_LINE.toString())).count();
        //离线数
        long offline = onlineList.stream().filter(e -> e.equals(SecurityDeviceStateConstant.OFF_LINE.toString())).count();
        //总数
        long total = online + offline;
        //在线率
        double onlineRate = (double) online / total * 100;
        accessStatisticsVo.setOnline(online);
        accessStatisticsVo.setOffline(offline);
        accessStatisticsVo.setTotal(total);
        accessStatisticsVo.setOnlineRate(onlineRate);
        return accessStatisticsVo;
    }

    @Override
    public BarriersStatisticsVo getBarriersStatistics() {
        BarriersStatisticsVo barriersStatisticsVo = new BarriersStatisticsVo();
        List<String> onlineList = this.listObjs(new LambdaQueryWrapper<SecurityDevice>()
                .eq(SecurityDevice::getSecurityDeviceType, SecurityDeviceTypeConstant.BARRIERS)
                .select(SecurityDevice::getOnline));
        //在线数
        long online = onlineList.stream().filter(e -> e.equals(SecurityDeviceStateConstant.ON_LINE.toString())).count();
        //离线数
        long offline = onlineList.stream().filter(e -> e.equals(SecurityDeviceStateConstant.OFF_LINE.toString())).count();
        //总数
        long total = online + offline;
        //在线率
        double onlineRate = (double) online / total * 100;
        barriersStatisticsVo.setOnline(online);
        barriersStatisticsVo.setOffline(offline);
        barriersStatisticsVo.setTotal(total);
        barriersStatisticsVo.setOnlineRate(onlineRate);
        return barriersStatisticsVo;
    }

    private List<TreeData> buildRegionTree(Long pId, List<Region> list) {
        // 给定的父节点PID，和所有的区域列表，构建一个区域树
        // 1.找到所有的子区域
        List<Region> children = list.stream().filter(region -> region.getPid().equals(pId)).collect(Collectors.toList());
        // 2.将子区域转换成TreeData
        List<TreeData> treeDataList = children.stream().map(TreeData::convertRegion).collect(Collectors.toList());
        // 3.递归调用，将子区域的子区域添加到子区域的children中
        treeDataList.forEach(treeData -> {
            treeData.setChildren(buildRegionTree(treeData.getId(), list));
        });
        if (CollUtil.isNotEmpty(treeDataList)) {
            treeDataList.sort(Comparator.comparing(TreeData::getSort));
        }
        // 4.返回
        return treeDataList;
    }

    private void addSecurityDeviceInRegionTree(List<TreeData> regionTreeVos, String type) {
        // 1.遍历区域树
        regionTreeVos.forEach(treeData -> {
            // 2.1 查询区域下的安防设备
            List<SecurityDevice> securityDeviceList = this.list(
                    new LambdaQueryWrapper<SecurityDevice>()
                            .eq(SecurityDevice::getRegionId, treeData.getId())
                            .eq(StrUtil.isNotBlank(type) && !"0".equals(type),SecurityDevice::getSecurityDeviceType,type)
                            .ne(SecurityDevice::getSecurityDeviceType,"3")
                            .orderByDesc(SecurityDevice::getUpdateTime)
                            .orderByAsc(SecurityDevice::getId));
            // 2.2 将安防设备构建成TreeData，然后添加到区域的children中
            List<TreeData> cameraTreeDataList = securityDeviceList.stream().map(TreeData::convertSecurityDevice).collect(Collectors.toList());
            // 取消门禁点层节点展示
            buildSecurityDeviceDoor(cameraTreeDataList);
            if (CollUtil.isNotEmpty(treeData.getChildren())) {
                treeData.getChildren().addAll(cameraTreeDataList);
                // 3.递归调用，将区域的children传入，继续构建区域树
                addSecurityDeviceInRegionTree(treeData.getChildren(),type);
            } else {
                treeData.setChildren(cameraTreeDataList);
            }
        });
    }

    /**
     * 将门禁设备下面的安防设备查出来，添加到树中
     * @param cameraTreeDataList
     */
    private void buildSecurityDeviceDoor(List<TreeData> cameraTreeDataList) {
        for (TreeData treeData : cameraTreeDataList) {
            List<SecurityDeviceDoor> securityDeviceDoorList = securityDeviceDoorService.lambdaQuery().eq(SecurityDeviceDoor::getSecurityDeviceId, treeData.getId()).list();
            if (CollUtil.isNotEmpty(securityDeviceDoorList)){
                List<TreeData> treeDataList = securityDeviceDoorList.stream().map(TreeData::convertSecurityDeviceDoor).collect(Collectors.toList());
                // 将treeDataList中的元素的pid属性设置为treeData.getId();
                treeDataList.forEach(treeData1 -> treeData1.setPid(treeData.getId()));
                // 根据treeDataList中元素的name属性进行排序
                treeDataList.sort(Comparator.comparing(TreeData::getName));
                treeData.setChildren(treeDataList);
            }
        }
    }

    private void checkQueryDto(SecurityDeviceListQueryDto dto) {
        if (ObjectUtil.isNotEmpty(dto.getRegionId())) {
            Region region = regionService.getById(dto.getRegionId());
            if (ObjectUtil.isEmpty(region)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "区域不存在");
            }
        }
    }

    @Override
    public void updateSecurityDevice(SecurityDeviceAddOrUpdateQueryDto dto) {
        Long id = dto.getId();
        if (ObjectUtil.isEmpty(id)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "安防设备ID不能为空");
        }
        if (dto.getManufacturer().equals(ManufacturerEnum.HK.getCode())) {
            SecurityDevice securityDevice = securityDeviceMapper.selectById(id);
            if (ObjectUtil.isEmpty(securityDevice)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "不存在该安防设备，请检查！！");
            }
            if (ObjectUtil.isEmpty(regionService.lambdaQuery().eq(Region::getId, dto.getRegionId()).one())) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "区域不存在");
            }
            securityDeviceMapper.update(null, new LambdaUpdateWrapper<SecurityDevice>()
                    .eq(BaseEntity::getId, id).set(SecurityDevice::getRegionId, dto.getRegionId()));
            return;
        }
        checkSecurityDevice(dto);
        SecurityDevice securityDevice = securityDeviceTransfer.addDto2Entity(dto);
        securityDevice.setId(id);
        for (SecurityDeviceDoor securityDeviceDoor : securityDevice.getSecurityDevicePointList()) {
            SecurityDeviceDoor securityDeviceDoor1 = securityDeviceDoorService.getById(securityDeviceDoor.getId());
            if (ObjectUtil.isEmpty(securityDeviceDoor1)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "安防设备下点位不存在");
            }
            securityDeviceDoor.setDeviceSourceIndex(securityDeviceDoor1.getDeviceSourceIndex());
        }
        // 请求组件
        sendSecurityDeviceSyncDto(securityDevice);
        if (!this.updateById(securityDevice)) {
            log.info("安防设备更新失败，更新实体：{}", JSON.toJSON(securityDevice));
        }
        if (!securityDeviceDoorService.updateBatchById(securityDevice.getSecurityDevicePointList())) {
            log.info("安防设备下点位更新失败，更新实体：{}", JSON.toJSON(securityDevice.getSecurityDevicePointList()));
        }
        saveDeviceFunction(securityDevice.getId(), dto.getFunction());
    }

    /**
     * 构造请求组件实体
     *
     * @param securityDevice
     */
    private void sendSecurityDeviceSyncDto(SecurityDevice securityDevice) {
        MqttComponentTemplateDto<MqttSecuritySyncQueryDto> mqttComponentTemplateDto = new MqttComponentTemplateDto<>(securityComponentProperties.getDeviceFactory());
        for (SecurityDeviceDoor securityDeviceDoor : securityDevice.getSecurityDevicePointList()) {
            mqttComponentTemplateDto.setParams(buildSecurityDeviceSyncParamDto(securityDeviceDoor, securityDevice.getSecurityDeviceType(), securityDevice.getFunction()));
            String sourceIndex = saveSecurityDeviceToComponent(mqttComponentTemplateDto);
            if (ObjectUtil.isEmpty(securityDeviceDoor.getDeviceSourceIndex())) {
                securityDeviceDoor.setDeviceSourceIndex(sourceIndex);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelSecurityDevice(SecurityDeviceDelQueryDto dto) {
        Long[] ids = dto.getIds();
        for (Long id : ids) {
            ArrayList<Long> longs = new ArrayList<>();
            SecurityDevice securityDevice = this.getById(id);
            if (ObjectUtil.isEmpty(securityDevice)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "安防设备不存在");
            }
            longs.add(id);
            List<SecurityDeviceDoor> securityDeviceDoorList = securityDeviceDoorService.lambdaQuery().eq(SecurityDeviceDoor::getSecurityDeviceId, id).list();
            for (SecurityDeviceDoor securityDeviceDoor : securityDeviceDoorList) {
                sendSecurityDeviceDelDto(securityDeviceDoor.getDeviceSourceIndex());
                log.info("删除安防设备-删除组件成功，设备组件唯一索引：{}", securityDeviceDoor.getDeviceSourceIndex());
                if (securityDeviceDoorService.removeById(securityDeviceDoor.getId())){
                    log.info("删除安防设备-删除数据库中设备点位成功，点位名称：{}", securityDeviceDoor.getDoorName());
                }
            }
            if (this.removeById(id)){
                log.info("删除安防设备-删除数据库成功，设备名称：{}", securityDevice.getSecurityDeviceName());
            }
        }
    }

    private void sendSecurityDeviceDelDto(String securityDeviceId) {
        MqttComponentTemplateDto<MqttSecurityDelQueryDto> queryDto = new MqttComponentTemplateDto<MqttSecurityDelQueryDto>(securityComponentProperties.getDeviceFactory());
        queryDto.setParams(new MqttSecurityDelQueryDto(securityDeviceId));
        ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(queryDto, "device", "del", CacheConstant.syncDeviceTopicKey, securityComponentProperties.getComponentVersion(), queryDto.getSerialId(), "1");
        if (responseDto.getCode() != 0) {
            log.info("安防设备删除失败，请求安防设备组件-删除失败，返回信息：{}", responseDto.getMessage());
            throw new ServiceException(ResultCode.OPERATION_FAILURE, responseDto.getMessage());
        }
    }

    @Override
    public boolean bandingCamera(BandingCameraQueryDto dto) {
        checkBandingCameraQueryDto(dto);
        boolean result = true;
        securityDeviceCameraService.remove(new LambdaUpdateWrapper<SecurityDeviceCamera>().eq(SecurityDeviceCamera::getSecurityDeviceId, dto.getSecurityDeviceId()));
        for (CameraPresetBandingDto cameraPresetBandingDto : dto.getCameraList()) {
            SecurityDeviceCamera securityDeviceCamera = SecurityDeviceCamera.builder()
                    .securityDeviceId(dto.getSecurityDeviceId())
                    .cameraId(cameraPresetBandingDto.getCameraId())
                    .presetId(ObjectUtil.isNotEmpty(cameraPresetBandingDto.getPresetId()) ? cameraPresetBandingDto.getPresetId() : -1L)
                    .build();
            result = securityDeviceCameraService.save(securityDeviceCamera);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SyncDeviceVo syncDevice(String syncDeviceType) {
        List<String> syncDeviceCode = Arrays.stream(SyncDeviceTypeEnum.values()).map(SyncDeviceTypeEnum::getCode)
                .collect(Collectors.toList());
        if (!syncDeviceCode.contains(syncDeviceType)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE,"没有此设备类别，请检查！！");
        }
        String serialId = UUID.randomUUID().toString().replaceAll("-", "");
        SyncDeviceInputDto syncDeviceInput = SyncDeviceInputDto.builder().serialId(serialId).params(SyncDeviceInputDto.ParamsDTO.builder()
                .category(syncDeviceType).pageNo(1).pageSize(999).build()).build();
        MqttUtils.sendAndBuildCache(syncDeviceInput, SecurityTopicEnum.SYNC_DEVICE.getTopic(), serialId);
        String cacheSyncDevice = redisTemplate.opsForValue().get(serialId);
        if (StrUtil.isEmpty(cacheSyncDevice)) {
            log.error("等待安防组件上的 mqtt 超时");
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "内部请求超时");
        }
        log.info("接收到安防组件上 mqtt 的数据：{}", cacheSyncDevice);
        SyncDeviceResponseDto syncDeviceResponseDto = JSON.parseObject(cacheSyncDevice, SyncDeviceResponseDto.class);
        if (syncDeviceResponseDto.getCode() != 0) {
            log.info("同步设备-请求安防设备组件-同步失败，返回信息：{}", cacheSyncDevice);
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "设备同步失败");
        }
        List<SyncDeviceResponseDto.DataDTO.RecordsDTO> syncDeviceList = syncDeviceResponseDto.getData().getRecords();
        List<String> newSourceIndexList = syncDeviceList.stream()
                .map(SyncDeviceResponseDto.DataDTO.RecordsDTO::getDeviceId).collect(Collectors.toList());
        List<SecurityDevice> oldSecurityDeviceList = securityDeviceMapper.selectList(null);
        List<String> oldSourceIndexList = oldSecurityDeviceList.stream().map(SecurityDevice::getDeviceSourceIndex)
                .collect(Collectors.toList());
        List<SecurityDeviceDoor> oldSecurityDeviceDoorList = securityDeviceDoorMapper.selectList(null);
        // 新增的数据
        Collection<String> insertSourceIndexList = CollectionUtils.subtract(newSourceIndexList, oldSourceIndexList);
        // 删除的数据
        Collection<String> deleteSourceIndexList = CollectionUtils.subtract(oldSourceIndexList, newSourceIndexList);
        // 可能更新的数据
        Collection<String> mayUpdateSourceIndexList = CollectionUtils.intersection(newSourceIndexList, oldSourceIndexList);
        saveSecurityDevice(insertSourceIndexList, syncDeviceList);
        deleteSecurityDevice(deleteSourceIndexList, oldSecurityDeviceList, oldSecurityDeviceDoorList);
        mayUpdateSecurityDevice(mayUpdateSourceIndexList, syncDeviceList, oldSecurityDeviceList, oldSecurityDeviceDoorList);
        // 提取名称
        List<String> insertDeviceNameList = insertSourceIndexList.stream().map(sourceIndex ->
                syncDeviceList.stream().filter(device -> device.getDeviceId().equals(sourceIndex)).findFirst().orElse(null)
                        .getDeviceName()).collect(Collectors.toList());
        List<String> deleteDeviceNameList = deleteSourceIndexList.stream().map(sourceIndex ->
                oldSecurityDeviceList.stream().filter(device -> device.getDeviceSourceIndex().equals(sourceIndex)).findFirst()
                        .orElse(null).getSecurityDeviceName()).collect(Collectors.toList());
        return SyncDeviceVo.builder().addedDeviceList(insertDeviceNameList).deletedDeviceList(deleteDeviceNameList).build();
    }

    /**
     * 可能更新的安防设备
     *
     * @param mayUpdateSourceIndexList 可能更新组件的唯一标识
     * @param syncDeviceList 组件的原始数据
     * @param oldSecurityDeviceList 安防设备数据
     * @param oldSecurityDeviceDoorList 安防设备点位数据
     */
    @Transactional
    public void mayUpdateSecurityDevice(Collection<String> mayUpdateSourceIndexList,
                                         List<SyncDeviceResponseDto.DataDTO.RecordsDTO> syncDeviceList,
                                         List<SecurityDevice> oldSecurityDeviceList, List<SecurityDeviceDoor> oldSecurityDeviceDoorList) {
        List<SecurityDeviceDoor> insertSecurityDeviceDoorList = new ArrayList<>();
        List<Long> deleteSecurityDeviceDoorIdList = new ArrayList<>();
        for (String mayUpdateSourceIndex : mayUpdateSourceIndexList) {
            SyncDeviceResponseDto.DataDTO.RecordsDTO syncDevice = syncDeviceList.stream().filter(device ->
                    device.getDeviceId().equals(mayUpdateSourceIndex)).findFirst().orElse(null);
            SecurityDevice securityDevice = oldSecurityDeviceList.stream().filter(device ->
                    device.getDeviceSourceIndex().equals(mayUpdateSourceIndex)).findFirst().orElse(null);
            if (syncDevice == null || securityDevice == null) {
                continue;
            }
            if (syncDevice.getFactory().equals(securityDevice.getManufacturer()) ||
                    !syncDevice.getDeviceName().equals(securityDevice.getSecurityDeviceName())) {
                securityDeviceMapper.update(null, new LambdaUpdateWrapper<SecurityDevice>()
                        .eq(SecurityDevice::getDeviceSourceIndex, mayUpdateSourceIndex)
                        .set(SecurityDevice::getManufacturer, ManufacturerEnum.getCode(syncDevice.getFactory()))
                        .set(SecurityDevice::getSecurityDeviceName, syncDevice.getDeviceName())
                        .set(SecurityDevice::getOnline,syncDevice.getOnline()));
            }
            List<SyncDeviceResponseDto.DataDTO.RecordsDTO.DoorListDTO> syncDoorList = syncDevice.getDoorList();
            List<SecurityDeviceDoor> securityDeviceDoorList = oldSecurityDeviceDoorList.stream().filter(deviceDoor ->
                    deviceDoor.getSecurityDeviceId().equals(securityDevice.getId())).collect(Collectors.toList());
            List<String> syncDoorIdList = syncDoorList.stream().map(SyncDeviceResponseDto.DataDTO.RecordsDTO.DoorListDTO::getDoorId).collect(Collectors.toList());
            List<String> securityDeviceDoorIdList = securityDeviceDoorList.stream().map(SecurityDeviceDoor::getDoorId).collect(Collectors.toList());
            Collection<String> insertDoorIdList = CollectionUtils.subtract(syncDoorIdList, securityDeviceDoorIdList);
            Collection<String> deleteDoorIdList = CollectionUtils.subtract(securityDeviceDoorIdList, syncDoorIdList);
            Collection<String> mayUpdateDoorIdList = CollectionUtils.intersection(securityDeviceDoorIdList, syncDoorIdList);
            for (String insertDoorId : insertDoorIdList) {
                SyncDeviceResponseDto.DataDTO.RecordsDTO.DoorListDTO syncDoor = syncDoorList.stream().filter(door ->
                        door.getDoorId().equals(insertDoorId)).findFirst().orElse(null);
                insertSecurityDeviceDoorList.add(SecurityDeviceDoor.builder().doorId(insertDoorId)
                        .doorName(syncDoor.getDoorName()).securityDeviceId(securityDevice.getId()).build());
            }
            for (String deleteDoorId : deleteDoorIdList) {
                SecurityDeviceDoor securityDeviceDoor = securityDeviceDoorList.stream().filter(door ->
                        door.getDoorId().equals(deleteDoorId)).findFirst().orElse(null);
                deleteSecurityDeviceDoorIdList.add(securityDeviceDoor.getId());
            }
            for (String mayUpdateDoorId : mayUpdateDoorIdList) {
                SyncDeviceResponseDto.DataDTO.RecordsDTO.DoorListDTO syncDoor = syncDoorList.stream()
                        .filter(door -> door.getDoorId().equals(mayUpdateDoorId)).findFirst().orElse(null);
                SecurityDeviceDoor securityDeviceDoor = securityDeviceDoorList.stream().filter(door ->
                        door.getDoorId().equals(mayUpdateDoorId)).findFirst().orElse(null);
                if (!syncDoor.getDoorName().equals(securityDeviceDoor.getDoorName())) {
                    securityDeviceDoorMapper.update(null, new LambdaUpdateWrapper<SecurityDeviceDoor>()
                            .eq(SecurityDeviceDoor::getDoorId, mayUpdateDoorId).set(SecurityDeviceDoor::getDoorName,
                                    syncDoor.getDoorName()));
                }
            }
        }
        securityDeviceDoorService.saveBatch(insertSecurityDeviceDoorList);
        if (CollectionUtil.isNotEmpty(deleteSecurityDeviceDoorIdList)) {
            securityDeviceDoorMapper.deleteBatchIds(deleteSecurityDeviceDoorIdList);
        }
    }

    /**
     * 删除安防设备
     *
     * @param deleteSourceIndexList 需要删除组件的唯一标识
     * @param oldSecurityDeviceList 安防设备数据
     * @param oldSecurityDeviceDoorList 安防设备点位数据
     */
    private void deleteSecurityDevice(Collection<String> deleteSourceIndexList,
                                      List<SecurityDevice> oldSecurityDeviceList, List<SecurityDeviceDoor> oldSecurityDeviceDoorList) {
        List<Long> deleteSecurityDeviceIdList = new ArrayList<>();
        List<Long> deleteSecurityDeviceDoorIdList = new ArrayList<>();
        for (String sourceIndex : deleteSourceIndexList) {
            SecurityDevice securityDevice = oldSecurityDeviceList.stream().filter(device -> device.getDeviceSourceIndex().equals(sourceIndex)).findFirst().orElse(null);
            if (securityDevice == null) {
                continue;
            }
            Long id = securityDevice.getId();
            deleteSecurityDeviceIdList.add(id);
            List<SecurityDeviceDoor> securityDeviceDoorList = oldSecurityDeviceDoorList.stream()
                    .filter(deviceDoor -> deviceDoor.getSecurityDeviceId().equals(id)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(securityDeviceDoorList)) {
                deleteSecurityDeviceDoorIdList.addAll(securityDeviceDoorList.stream()
                        .map(BaseEntity::getId).collect(Collectors.toList()));
            }
        }
        if (CollectionUtil.isNotEmpty(deleteSecurityDeviceIdList)) {
            securityDeviceMapper.deleteBatchIds(deleteSecurityDeviceIdList);
        }
        if (CollectionUtil.isNotEmpty(deleteSecurityDeviceDoorIdList)) {
            securityDeviceDoorMapper.deleteBatchIds(deleteSecurityDeviceDoorIdList);
        }
    }

    /**
     * 保存安防设备
     *
     * @param insertSourceIndexList 需要新增组件的唯一标识
     * @param syncDeviceList 组件的原始数据
     */
    @Transactional
    public void saveSecurityDevice(Collection<String> insertSourceIndexList, List<SyncDeviceResponseDto.DataDTO.RecordsDTO> syncDeviceList) {
        List<SecurityDevice> saveSecurityDeviceList = new ArrayList<>();
        List<SecurityDeviceDoor> saveSecurityDeviceDoorList = new ArrayList<>();
        for (String sourceIndex : insertSourceIndexList) {
            SyncDeviceResponseDto.DataDTO.RecordsDTO insertDevice = syncDeviceList.stream().filter(device ->
                    device.getDeviceId().equals(sourceIndex)).findFirst().orElse(null);
            if (insertDevice == null) {
                continue;
            }
            Long id = SnowflakeUtils.getSnowflakeId();
            SecurityDevice securityDevice = SecurityDevice.builder()
                    .manufacturer(ManufacturerEnum.getCode(insertDevice.getFactory()))
                    .securityDeviceType(Convert.toStr(insertDevice.getDeviceType()))
                    .securityDeviceName(insertDevice.getDeviceName())
                    .deviceSourceIndex(insertDevice.getDeviceId())
                    .online(insertDevice.getOnline())
                    .build();
            securityDevice.setId(id);
            saveSecurityDeviceList.add(securityDevice);
            List<SyncDeviceResponseDto.DataDTO.RecordsDTO.DoorListDTO> doorList = insertDevice.getDoorList();
            if (CollectionUtils.isEmpty(doorList)) {
                continue;
            }
            List<SecurityDeviceDoor> securityDeviceDoorList = doorList.stream().map(deviceDoor -> SecurityDeviceDoor.builder()
                    .doorId(deviceDoor.getDoorId()).doorName(deviceDoor.getDoorName())
                    .securityDeviceId(id).build()).collect(Collectors.toList());
            saveSecurityDeviceDoorList.addAll(securityDeviceDoorList);
        }
        this.saveBatch(saveSecurityDeviceList);
        securityDeviceDoorService.saveBatch(saveSecurityDeviceDoorList);
    }

    private void batchSaveSecurityDeviceDoor(List<SecurityDeviceDoor> securityDeviceDoorList, Long securityDeviceId) {
        securityDeviceDoorList = securityDeviceDoorList.stream()
                .peek(securityDeviceDoor -> securityDeviceDoor.setSecurityDeviceId(securityDeviceId))
                .collect(Collectors.toList());
        if (!securityDeviceDoorService.saveBatch(securityDeviceDoorList)) {
            log.info("安防设备门禁保存失败，保存实体：{}", JSON.toJSON(securityDeviceDoorList));
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "安防设备门禁保存失败");
        }
    }

    /**
     * 针对集合中的对象的某一个属性进行去重，得到去重后的集合
     *
     * @param keyExtractor
     * @return
     */
    Predicate<CameraPresetBandingDto> distinctByKey(Function<? super CameraPresetBandingDto, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    private void checkBandingCameraQueryDto(BandingCameraQueryDto dto) {
        Long securityDeviceId = dto.getSecurityDeviceId();
        List<CameraPresetBandingDto> cameraList = dto.getCameraList();
        SecurityDevice securityDevice = this.getById(securityDeviceId);
        if (ObjectUtil.isEmpty(securityDevice)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "安防设备不存在");
        }
        if (cameraList.size() > VariousConstant.SECURITY_DEVICE_BINDING_CAMERA_NUM) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "设备最多绑定5台摄像机");
        }
        if (CollUtil.isNotEmpty(cameraList)) {
            boolean isDistinct = cameraList.stream().filter(distinctByKey(CameraPresetBandingDto::getCameraId)).count() == cameraList.size();
            if (!isDistinct) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "一个设备只能绑定摄像机的一个预置位");
            }
        }
        for (CameraPresetBandingDto cameraPresetBandingDto : cameraList) {
            Long cameraId = cameraPresetBandingDto.getCameraId();
            Camera camera = cameraService.getById(cameraId);
            if (ObjectUtil.isEmpty(camera)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "摄像机不存在");
            }
            if (ObjectUtil.isNotEmpty(cameraPresetBandingDto.getPresetId())) {
                if (cameraPresetBandingDto.getPresetId() == -1L) {
                    continue;
                }
                List<CameraPreset> cameraPreset = cameraPresetService.lambdaQuery()
                        .eq(CameraPreset::getCameraId, cameraPresetBandingDto.getCameraId())
                        .eq(CameraPreset::getId, cameraPresetBandingDto.getPresetId()).list();
                if (CollUtil.isEmpty(cameraPreset)) {
                    throw new ServiceException(ResultCode.OPERATION_FAILURE, "预置位不存在");
                }
            }
        }
    }

    private void checkSecurityDevice(SecurityDeviceAddOrUpdateQueryDto dto) {
        String securityDeviceName = dto.getSecurityDeviceName();

        // 检查设备名称是否已存在
        SecurityDevice existingDevice = this.lambdaQuery()
                .eq(SecurityDevice::getSecurityDeviceName, securityDeviceName)
                .ne(ObjectUtil.isNotEmpty(dto.getId()), SecurityDevice::getId, dto.getId())
                .one();
        if (ObjectUtil.isNotEmpty(existingDevice)) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "设备名称已存在");
        }
        if (ObjectUtil.isEmpty(regionService.lambdaQuery().eq(Region::getId, dto.getRegionId()).one())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "区域不存在");
        }

        // 检查是否存在重复的IP地址
        List<String> uniqueIps = dto.getSecurityDevicePointList()
                .stream()
                .map(SecurityDeviceDoor::getIp)
                .distinct()
                .collect(Collectors.toList());
        if (uniqueIps.size() != dto.getSecurityDevicePointList().size()) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "设备IP重复");
        }

        // 检查IP地址是否已存在于其他设备中
        for (SecurityDeviceDoor securityDeviceDoor : dto.getSecurityDevicePointList()) {
            String ip = securityDeviceDoor.getIp();
            List<SecurityDeviceDoor> existingDoor = securityDeviceDoorService.lambdaQuery()
                    .eq(SecurityDeviceDoor::getIp, ip)
                    .ne(ObjectUtil.isNotEmpty(securityDeviceDoor.getId()), SecurityDeviceDoor::getSecurityDeviceId, dto.getId())
                    .list();
            if (CollUtil.isNotEmpty(existingDoor)) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "设备IP已存在");
            }
        }

        // 检查功能是否存在
        for (String functionId : dto.getFunction()) {
            if (ObjectUtil.isEmpty(deviceFunctionService.getById(functionId))) {
                throw new ServiceException(ResultCode.OPERATION_FAILURE, "功能不存在");
            }
        }
        setSecurityDeviceDoorName(dto.getSecurityDevicePointList(), securityDeviceName, dto.getSecurityDeviceType());
    }

    private void setSecurityDeviceDoorName(List<SecurityDeviceDoor> securityDevicePointList, String securityDeviceName, String securityDeviceType) {
        String type = "1".equals(securityDeviceType) ? "门" : "2".equals(securityDeviceType) ? "道闸" : "";

        int listSize = securityDevicePointList.size();
        for (int i = 0; i < listSize; i++) {
            SecurityDeviceDoor securityDeviceDoor = securityDevicePointList.get(i);
            if ("2".equals(securityDeviceType)){
                // 道闸
                switch (securityDeviceDoor.getType()) {
                    case "1":
                        securityDeviceDoor.setDoorName(buildDoorName(securityDeviceName, "", "进"));
                        break;
                    case "2":
                        securityDeviceDoor.setDoorName(buildDoorName(securityDeviceName, "", "出"));
                        break;
                    case "3":
                        securityDeviceDoor.setDoorName(securityDeviceName);
                        break;
                    case "0":
                        securityDeviceDoor.setDoorName(buildDoorName(securityDeviceName, type, Integer.toString(i + 1)));
                        break;
                    default:
                        break;
                }
            }else {
                // 其他
                switch (securityDeviceDoor.getType()) {
                    case "1":
                        securityDeviceDoor.setDoorName(buildDoorName(securityDeviceName, type, Integer.toString(i + 1)));
                        break;
                    case "2":
                        securityDeviceDoor.setDoorName(buildDoorName(securityDeviceName, type, Integer.toString(i + 1)));
                        break;
                    case "3":
                        securityDeviceDoor.setDoorName(securityDeviceName);
                        break;
                    case "0":
                        securityDeviceDoor.setDoorName(buildDoorName(securityDeviceName, type, Integer.toString(i + 1)));
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private String buildDoorName(String securityDeviceName, String type, String suffix) {
        return new StringBuilder()
                .append(securityDeviceName)
                .append("_")
                .append(type)
                .append(suffix)
                .toString();
    }
}
