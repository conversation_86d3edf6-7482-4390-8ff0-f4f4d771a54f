package com.teamway.security.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.common.entity.PageModel;
import com.teamway.common.entity.ResultCode;
import com.teamway.common.entity.ResultModel;
import com.teamway.common.util.SnowflakeUtils;
import com.teamway.exception.ServiceException;
import com.teamway.mqtt.MqttUtils;
import com.teamway.security.common.constant.CacheConstant;
import com.teamway.security.common.constant.SecurityDeviceConstant;
import com.teamway.security.common.enums.SecurityTopicEnum;
import com.teamway.security.core.event.BaseEvent;
import com.teamway.security.dto.*;
import com.teamway.security.dto.mqttdto.securityConfiguration.SecurityDevice.MqttComponentTemplateDto;
import com.teamway.security.dto.securityConfiguration.ParkRelationReqDto;
import com.teamway.security.dto.securityConfiguration.ParkRelationResDto;
import com.teamway.security.entity.SecurityConfiguration.SecurityDevice;
import com.teamway.security.entity.SecurityPark;
import com.teamway.security.entity.SecurityParkDevice;
import com.teamway.security.mapper.SecurityParkMapper;
import com.teamway.security.service.SecurityDeviceService;
import com.teamway.security.service.SecurityParkDeviceService;
import com.teamway.security.service.SecurityParkService;
import com.teamway.security.util.SecurityComponentUtil;
import com.teamway.security.util.SecurityRedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class SecurityParkServiceImpl extends ServiceImpl<SecurityParkMapper, SecurityPark> implements SecurityParkService {
    private SecurityParkDeviceService securityParkDeviceService;
    private SecurityComponentUtil securityComponentUtil;
    private SecurityDeviceService securityDeviceService;
    private SecurityRedisUtil securityRedisUtil;
    @Autowired
    public void setSecurityParkDeviceService(SecurityParkDeviceService securityParkDeviceService) {
        this.securityParkDeviceService = securityParkDeviceService;
    }
    @Autowired
    public void setSecurityComponentUtil(SecurityComponentUtil securityComponentUtil) {
        this.securityComponentUtil = securityComponentUtil;
    }
    @Autowired
    public void setSecurityDeviceService(SecurityDeviceService securityDeviceService) {
        this.securityDeviceService = securityDeviceService;
    }
    @Autowired
    public void setSecurityRedisUtil(SecurityRedisUtil securityRedisUtil) {
        this.securityRedisUtil = securityRedisUtil;
    }

    @Override
    public PageModel<List<SecurityPark>> findPageList(SecurityParkGetReqDto securityParkGetReqDto) {
        LambdaQueryWrapper<SecurityPark> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(securityParkGetReqDto.getName() != null, SecurityPark::getName, securityParkGetReqDto.getName());
        Page<SecurityPark> page = new Page<>(securityParkGetReqDto.getPageIndex(), securityParkGetReqDto.getPageSize());
        baseMapper.selectPage(page, lambdaQueryWrapper);
        List<SecurityPark> securityParks = page.getRecords();
        securityParks.forEach(securityPark -> {
            LambdaQueryWrapper<SecurityParkDevice> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SecurityParkDevice::getParkId, securityPark.getId());
            List<SecurityParkDevice> securityParkDevices = securityParkDeviceService.list(wrapper);
            for (SecurityParkDevice securityParkDevice : securityParkDevices) {
                SecurityDevice securityDevice = securityDeviceService.getById(securityParkDevice.getDeviceId());
                String barriers = securityPark.getBarriers();
                if (barriers != null && barriers.length() > 0) {
                    securityPark.setBarriers(barriers + "," + securityDevice.getSecurityDeviceName());
                } else {
                    securityPark.setBarriers(securityDevice.getSecurityDeviceName());
                }
            }
        });
        PageModel<List<SecurityPark>> pageModel = new PageModel<>();
        pageModel.setCount((int) page.getTotal());
        pageModel.setData(securityParks);
        return pageModel;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultModel<String> relateBarrier(SecurityParkRelateBarrierReqDto securityParkRelateBarrierReqDto) {
        SecurityPark securityPark = this.getById(securityParkRelateBarrierReqDto.getParkId());
        if (securityPark == null) {
            throw new ServiceException(ResultCode.NOT_EXIST,"停车场");
        }
        List<String> strings = securityParkRelateBarrierReqDto.getDeviceIds();
        if (strings != null && strings.size() > 0) {
            for (String deviceId : strings) {
                SecurityDevice securityDevice = securityDeviceService.getById(deviceId);
                if (securityDevice == null) {
                    throw new ServiceException(ResultCode.NOT_EXIST,"设备id:" + deviceId);
                }
                if (!"2".equals(securityDevice.getSecurityDeviceType())) {
                    throw new ServiceException(ResultCode.DEVICE_NOT_IS_GATE);
                }
                LambdaQueryWrapper<SecurityParkDevice> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(SecurityParkDevice::getDeviceId, deviceId);
                List<SecurityParkDevice> list = securityParkDeviceService.list(lambdaQueryWrapper);
                if (list != null && list.size() > 0) {
                    securityParkDeviceService.remove(lambdaQueryWrapper);
                }
                SecurityParkDevice securityParkDevice = new SecurityParkDevice();
                securityParkDevice.setParkId(Long.valueOf(securityParkRelateBarrierReqDto.getParkId()));
                securityParkDevice.setDeviceId(Long.valueOf(deviceId));
                securityParkDeviceService.save(securityParkDevice);
            }
            return ResultModel.success();
        } else {
            return ResultModel.fail();
        }
    }

    @Override
    public ResultModel<String> sync() {
        String topic = "/v1_0/guard/park/get/request/hk/" + SnowflakeUtils.getSnowflakeId();
        MqttUtils.sendAndBuildCache(new Object(), topic, Thread.currentThread());
        topic = topic.replace("request", "response");
        Object o = securityRedisUtil.getCacheMapValue(CacheConstant.syncParkTopicKey, topic);
        log.info("获取mqtt数据：{}。。。。。key:{}......topic：{}", o, CacheConstant.syncParkTopicKey, topic);
        if (o == null) {
            throw new ServiceException(ResultCode.SYNC_PARK_FAIL_NEED_AGAIN);
        }
        LinkedHashMap map = (LinkedHashMap) o;
        Object source = map.get("source");
        String topic1 = (String) map.get("topic");
        String message1 = (String) map.get("message");
        BaseEvent baseEvent = new BaseEvent(source, topic1, message1);
        SecurityParkSyncResDto securityParkSyncResDto = JSON.parseObject(baseEvent.getMessage(), SecurityParkSyncResDto.class);
        int size = this.list().size();
        List<Long> updateIds = new ArrayList<>();
        int add = 0;
        List<SecurityParkSyncDataResDto> needAdd = new ArrayList<>();
        for (SecurityParkSyncDataResDto datum : securityParkSyncResDto.getData()) {
            LambdaQueryWrapper<SecurityPark> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(SecurityPark::getParkSourceIndex, datum.getId());
            SecurityPark one = this.getOne(lambdaQueryWrapper);
            if (one != null) {
                one.setName(datum.getName());
                this.updateById(one);
                updateIds.add(one.getId());
            } else {
                needAdd.add(datum);
            }
        }
        LambdaQueryWrapper<SecurityPark> removeSecurityParkWrapper = new LambdaQueryWrapper<>();
        removeSecurityParkWrapper.notIn(updateIds.size() > 0, SecurityPark::getId, updateIds);
        this.remove(removeSecurityParkWrapper);
        LambdaQueryWrapper<SecurityParkDevice> removeSecurityParkDeviceWrapper = new LambdaQueryWrapper<>();
        removeSecurityParkDeviceWrapper.notIn(updateIds.size() > 0, SecurityParkDevice::getParkId, updateIds);
        securityParkDeviceService.remove(removeSecurityParkDeviceWrapper);
        int del = size - updateIds.size();
        for (SecurityParkSyncDataResDto securityParkSyncDataResDto : needAdd) {
            SecurityPark securityPark = new SecurityPark();
            securityPark.setName(securityParkSyncDataResDto.getName());
            securityPark.setParkSourceIndex(securityParkSyncDataResDto.getId());
            this.save(securityPark);
            add++;
        }
        String message = "新增 " + add + " ,删除 " + del;
        //查询停车场关联的道闸
        log.info("查询停车场关联的道闸开始..................");
        List<SecurityPark> list = this.list();
        if (list != null && list.size() > 0) {
            for (SecurityPark securityPark : list) {
                String topicRelation = "/v1_0/guard/park/relation/request/hk/" + SnowflakeUtils.getSnowflakeId();
                ParkRelationReqDto parkRelationReqDto = new ParkRelationReqDto();
                parkRelationReqDto.setParkId(securityPark.getParkSourceIndex());
                MqttUtils.sendAndBuildCache(parkRelationReqDto, topicRelation, Thread.currentThread());
                topicRelation = topicRelation.replace("request", "response");
                Object oRelation = securityRedisUtil.getCacheMapValue(CacheConstant.syncParkTopicKey, topicRelation);
                log.info("获取mqtt数据：{}。。。。。key:{}......topic：{}", oRelation, CacheConstant.syncParkTopicKey, topicRelation);
                if (oRelation == null) {
                    throw new ServiceException(ResultCode.SYNC_PARK_FAIL_NEED_AGAIN);
                }
                //删除之前的关联关系
                LambdaQueryWrapper<SecurityParkDevice> spdl = new LambdaQueryWrapper<>();
                spdl.eq(SecurityParkDevice::getParkId,securityPark.getId());
                securityParkDeviceService.remove(spdl);
                //删除后新增
                LinkedHashMap map1 = (LinkedHashMap) oRelation;
                Object source1 = map1.get("source");
                String topic11 = (String) map1.get("topic");
                String message11 = (String) map1.get("message");
                BaseEvent baseEvent1 = new BaseEvent(source1, topic11, message11);
                ParkRelationResDto parkRelationResDto = JSON.parseObject(baseEvent1.getMessage(), ParkRelationResDto.class);
                if (parkRelationResDto.getData() != null && parkRelationResDto.getData().size() > 0) {
                    for (String barrier : parkRelationResDto.getData()) {
                        //根据barrier查询数据设备表id
                        LambdaQueryWrapper<SecurityDevice> securityDeviceLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        securityDeviceLambdaQueryWrapper.eq(SecurityDevice::getDeviceSourceIndex, barrier);
                        List<SecurityDevice> securityDevices = securityDeviceService.list(securityDeviceLambdaQueryWrapper);
                        if (securityDevices != null && securityDevices.size() == 1) {
                            SecurityDevice securityDevice = securityDevices.get(0);
                            SecurityParkDevice securityParkDevice = new SecurityParkDevice();
                            securityParkDevice.setParkId(securityPark.getId());
                            securityParkDevice.setDeviceId(securityDevice.getId());
                            securityParkDeviceService.save(securityParkDevice);
                        }
                    }
                }
            }
        }
        log.info("查询停车场关联的道闸结束..................");
        return new ResultModel<>(0, message);
    }


    @Override
    public String getIdsByComponent() {
        MqttComponentTemplateDto<Map<String, Object>> mqttComponentTemplateDto = new MqttComponentTemplateDto<Map<String, Object>>(securityDeviceService.getSecurityDeviceIndexByType(SecurityDeviceConstant.BARRIER_TYPE));
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", 1);
        map.put("pageSize", 100);
        mqttComponentTemplateDto.setParams(map);
        ResponseDto responseDto = securityComponentUtil.sendMqttToSecurityComponent(
                mqttComponentTemplateDto,
                SecurityTopicEnum.PARK_LIST.getTopic(),
                CacheConstant.syncParkTopicKey,
                mqttComponentTemplateDto.getSerialId());
        if (!Objects.equals(ResultCode.SUCCESS.getCode(), responseDto.getCode())) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, responseDto.getMessage());
        }
        List<Map> array = JSON.parseArray(JSON.toJSONString(responseDto.getData()), Map.class);
        if (array.isEmpty()) {
            throw new ServiceException(ResultCode.OPERATION_FAILURE, "没有停车场");
        }
        List<String> ids = new ArrayList<>();
        for (Map m : array) {
            ids.add(m.get("id").toString());
        }
        return ids.get(0);
    }


}
