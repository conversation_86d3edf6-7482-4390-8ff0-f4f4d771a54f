package com.teamway.security.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.teamway.base.dto.other.CommonTypeDto;
import com.teamway.base.entity.*;
import com.teamway.base.service.*;
import com.teamway.base.vo.CameraVo;
import com.teamway.common.constant.CameraStateConstant;
import com.teamway.common.constant.CommonConstant;
import com.teamway.common.util.JsonFileReader;
import com.teamway.security.dto.mis.MilestonePlanQueryDTO;
import com.teamway.security.entity.PersonUnitCount;
import com.teamway.security.entity.ScreenDataMaintenanceConfig;
import com.teamway.security.service.ScreenDataMaintenanceConfigService;
import com.teamway.security.service.SecurityDeviceRecordService;
import com.teamway.security.service.SecurityDeviceService;
import com.teamway.security.service.SecurityStatisticsService;
import com.teamway.security.vo.ProjectStatusInfoVo;
import com.teamway.security.vo.mis.MilestonePlanVO;
import com.teamway.security.vo.statistics.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClossName StatisticsServiceImpl
 * @Description StatisticsService实现类
 * <AUTHOR>
 * @createDate 2024/12/23
 * @version 1.0
 **/
@Slf4j
@Service
public class SecurityStatisticsServiceImpl implements SecurityStatisticsService {

    @Autowired
    private RegionService regionService;
    @Autowired
    private CameraService cameraService;
    @Autowired
    private CameraStateRecordService cameraStateRecordService;
    @Autowired
    private ScreenDataMaintenanceConfigService screenDataMaintenanceConfigService;
    @Autowired
    private SecurityDeviceService securityDeviceService;
    @Autowired
    private HScreenService hScreenService;
    @Autowired
    private SecurityDeviceRecordService securityDeviceRecordService;
    @Autowired
    private CommonTypeService commonTypeService;

    @Value("${misUrl}")
    private String misUrl;

    @Value("${milestone.category:milestone}")
    private String category;

    @Value("${milestone.name:}")
    private String milestone;

    @Value("${milestone.parentId:1}")
    private String parentId;
    /**
     * 获取摄像机数量
     *
     * @return
     */
    @Override
    public CameraNumberVo getCameraStatistics() {
        CameraNumberVo cameraNumberVo = new CameraNumberVo();
        //在线数
        long online = cameraService.count(new LambdaQueryWrapper<Camera>().eq(Camera::getState, CameraStateConstant.ON_LINE));
        cameraNumberVo.setOnline((int) online);
        //离线数
        long offline = cameraService.count(new LambdaQueryWrapper<Camera>().eq(Camera::getState, CameraStateConstant.OFF_LINE));
        cameraNumberVo.setOffline((int) offline);
        //总数
        cameraNumberVo.setTotal(cameraNumberVo.getOnline() + cameraNumberVo.getOffline());
        //在线率
        cameraNumberVo.setOnlineRate((double) online / cameraNumberVo.getTotal() * 100);
        return cameraNumberVo;
    }

    /**
     * 获取摄像机离线数
     *
     * @param limit 行数
     * @return key摄像机名称 value离线数量
     */
    @Override
    public List<Map<String, Object>> getCameraOffLineNum(Integer limit) {
        QueryWrapper<CameraStateRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("camera_name cameraName, count(1) count");
        queryWrapper.eq("state", CameraStateConstant.OFF_LINE);
        queryWrapper.groupBy("camera_id");
        queryWrapper.orderByDesc("count");
        queryWrapper.last("limit " + limit);
        return cameraStateRecordService.listMaps(queryWrapper);
    }

    /**
     * 根据区域统计摄像机数量
     *
     * @param regionId
     * @return
     */
    @Override
    public List<RegionCameraNumVo> getRegionCameraNum(Long regionId) {
        //查询指定区域
        List<Region> regionList = regionService.list(new LambdaQueryWrapper<Region>().eq(Region::getPid, regionId));
        //如果没查到子级  就查本身
        if (CollUtil.isEmpty(regionList)) {
            regionList = regionService.list(new LambdaQueryWrapper<Region>().eq(Region::getId, regionId));
        }
        if (CollUtil.isEmpty(regionList)) {
            return Collections.emptyList();
        }
        //查询所有区域
        List<Region> allRegionList = regionService.list();
        List<RegionCameraNumVo> resultList = new ArrayList<>();
        for (Region region : regionList) {
            RegionCameraNumVo regionCameraNumVo = new RegionCameraNumVo();
            regionCameraNumVo.setRegionId(region.getId());
            regionCameraNumVo.setRegionName(region.getName());
            List<Long> regionIds = new ArrayList<>();
            regionIds.add(region.getId());
            //查询子级区域
            this.buildMap(region.getId(), allRegionList, regionIds);
            //在线数
            long onLineCount = cameraService.count(new LambdaQueryWrapper<Camera>().in(Camera::getRegionId, regionIds).eq(Camera::getState, CameraStateConstant.ON_LINE));
            regionCameraNumVo.setOnline((int) onLineCount);
            //离线数
            long offLineCount = cameraService.count(new LambdaQueryWrapper<Camera>().in(Camera::getRegionId, regionIds).eq(Camera::getState, CameraStateConstant.OFF_LINE));
            regionCameraNumVo.setOffline((int) offLineCount);
            resultList.add(regionCameraNumVo);
        }
        return resultList;
    }

    @Override
    public String getConstructionDays() {
        Date startTime = screenDataMaintenanceConfigService.selectConfig().getStartTime();
        long daysPassed = ChronoUnit.DAYS.between(startTime.toInstant(), new Date().toInstant());
        return daysPassed + "";
    }

    @Override
    public List<CameraVo> selectCarmeraByconfig() {
        LambdaQueryWrapper<ScreenDataMaintenanceConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenDataMaintenanceConfig::getConfigVersion, CommonConstant.FIRST_VERSION);
        ScreenDataMaintenanceConfig config = screenDataMaintenanceConfigService.getOne(queryWrapper);
        List<Long> cameraList = JSON.parseArray(config.getCameraIds(), Long.class);
        List<Camera> list = new ArrayList<>();
        Map<Long, Camera> cameraMap = cameraService.lambdaQuery()
                .in(Camera::getId, cameraList)
                .list()
                .stream()
                .collect(Collectors.toMap(Camera::getId, Function.identity()
                ));
        for (Long id : cameraList) {
            Camera camera = cameraMap.get(id);
            if (camera != null) {
                list.add(camera);
            }
        }
        List<CameraVo> cameraVoList = new ArrayList<>();
        for (Camera camera : list) {
            String jsonString = JSON.toJSONString(camera);
            CameraVo cameraVo = JSON.parseObject(jsonString, CameraVo.class);
            cameraVoList.add(cameraVo);
        }
        return cameraVoList;
    }

    @Override
    public ProjectStatusInfoVo getLiveProjectStatus() {
        String constructionDays = getConstructionDays();
        CameraNumberVo cameraStatistics = getCameraStatistics();
        AccessStatisticsVo accessStatistics = securityDeviceService.getAccessStatistics();
        BarriersStatisticsVo barriersStatistics = securityDeviceService.getBarriersStatistics();
        ProjectStatusInfoVo projectStatusInfoVo = new ProjectStatusInfoVo();
        projectStatusInfoVo.setConstructionDays(constructionDays);
        projectStatusInfoVo.setCameraNumberVo(cameraStatistics);
        projectStatusInfoVo.setAccessStatisticsVo(accessStatistics);
        projectStatusInfoVo.setBarriersStatisticsVo(barriersStatistics);
        return projectStatusInfoVo;
    }

    /**
     * 查询父区域下子区域
     *
     * @param pId
     * @param list
     * @return :
     * @date : 2022/8/5
     * <AUTHOR> zhangsai
     */
    private List<Long> buildMap(Long pId, List<Region> list, List<Long> regionIds) {
        for (Region region : list) {
            if (region.getPid().equals(pId)) {
                regionIds.add(region.getId());
                //递归
                buildMap(region.getId(), list, regionIds);
            }
        }
        return regionIds;
    }

    @Override
    public String contentStitching(Long id) {
        HScreen hScreenById = hScreenService.getHScreenById(id);
        StringBuilder contentStitching = new StringBuilder();
        CommonTypeDto commonTypeDto = new CommonTypeDto();
        commonTypeDto.setCategory(CommonConstant.TYPE_EIGHT);
        List<CommonType> typeListByCategory = commonTypeService.getTypeListByCategory(commonTypeDto);
        Map<String, CommonType> typeMap = typeListByCategory.stream()
                .collect(Collectors.toMap(
                        CommonType::getTypeCode,    // key映射函数
                        type -> type,               // value映射函数
                        (existing, replacement) -> existing  // 处理重复key的情况，保留第一个值
                ));
        hScreenById.getHScreenContentList().forEach(content -> {
            String serialNumber = content.getSerialNumber().toString();
            CommonType commonType = typeMap.get(serialNumber);
            switch (serialNumber) {
                case CommonConstant.TYPE_ZEOR:
                    //项目名称
                    if(content.getIsShow().equals(CommonConstant.TYPE_ONE)) {
                        contentStitching.append(commonType.getName() + commonType.getDescription());
                    }
                    break;
                case CommonConstant.TYPE_ONE:
                    //今日进场人数
                    if(content.getIsShow().equals(CommonConstant.TYPE_ONE)) {
                        PersonColVO personColStatistics = securityDeviceRecordService.getPersonColStatisticsNoLogin();
                        Long workingTotal = personColStatistics.getWorkingTotal();
                        contentStitching.append(commonType.getName() + "：" + workingTotal + "人；" + " ");
                    }
                    break;
                case CommonConstant.TYPE_TWO:
                    //单位
                    PersonColVO colStatisticsNoLogin = securityDeviceRecordService.getPersonColStatisticsNoLogin();
                    List<PersonUnitCount> personUnitCountList = colStatisticsNoLogin.getPersonUnitCountList();
                    personUnitCountList.forEach(personUnitCount -> contentStitching.append(personUnitCount.getPersonUnitName() + "：" + personUnitCount.getWorkingCount()+ "人；" + " "));
                    break;
                case CommonConstant.TYPE_THREE:
                    //工种
                    if(content.getIsShow().equals(CommonConstant.TYPE_ONE)) {
                        PersonPieVO personPieStatisticsNoLogin = securityDeviceRecordService.getPersonPieStatisticsNoLogin();
                        personPieStatisticsNoLogin.getPersonTypeCountList().forEach(personTypeCount -> contentStitching.append(personTypeCount.getPersonTypeName() + "：" + personTypeCount.getCount() + "人；" + " "));
                    }
                    break;
                case CommonConstant.TYPE_FOUR:
                    //项目在岗人数
                    break;
                case CommonConstant.TYPE_FIVE:
                    //班组数  /crewManagement/pageCrew
                    break;
                case CommonConstant.TYPE_SIX:
                    //今日出勤人数
                    break;
                case CommonConstant.TYPE_SEVEN:
                    //今日出场人数
                    break;
                case CommonConstant.TYPE_EIGHT:
                    //今日管理人数
                    break;
                case CommonConstant.TYPE_NINE:
                    //今日特种作业人数
                    break;
                default:
                    break;
            }
        });
        return contentStitching.toString();
    }


    @Override
    public List<MilestonePlanVO> getMilestonePlanList(MilestonePlanQueryDTO queryDTO) {
        queryDTO.setCategory(category);
        queryDTO.setMilestone(milestone);
        queryDTO.setParentId(parentId);
        log.info("里程碑计划列表，查询参数：{}", JSONUtil.toJsonStr(queryDTO));
        // 调用接口获取数据
        String jsonResult = HttpUtil.post(misUrl + "/misplustask/list", JSONUtil.toJsonStr(queryDTO), 6000);

        List<MilestonePlanVO> resultList = new ArrayList<>();

        if (StrUtil.isBlank(jsonResult)) {
            log.warn("接口返回数据为空");
            return resultList;
        }

        try {
            // 解析JSON数据
            JSONObject jsonObject = JSONUtil.parseObj(jsonResult);
            if (jsonObject == null) {
                log.warn("JSON解析结果为空");
                return resultList;
            }

            JSONObject outerData = jsonObject.getJSONObject("data");
            if (outerData == null) {
                log.warn("未找到外层data节点");
                return resultList;
            }

            JSONArray taskArray = outerData.getJSONArray("data");
            if (CollUtil.isEmpty(taskArray)) {
                log.info("任务列表为空");
                return resultList;
            }

            // 遍历任务数据
            for (int i = 0; i < taskArray.size(); i++) {
                JSONObject task = taskArray.getJSONObject(i);
                if (task == null) {
                    continue;
                }

                try {
                    MilestonePlanVO vo = buildMilestonePlanVO(task);
                    if (vo != null) {
                        resultList.add(vo);
                    }
                } catch (Exception e) {
                    log.error("处理任务数据失败, task: {}", task, e);
                }
            }
        } catch (Exception e) {
            log.error("解析里程碑计划数据失败, jsonResult: {}", jsonResult, e);
        }

        return resultList;
    }

    /**
     * 构建里程碑计划VO
     */
    private MilestonePlanVO buildMilestonePlanVO(JSONObject task) {
        if (task == null) {
            return null;
        }

        MilestonePlanVO vo = new MilestonePlanVO();

        // 设置作业任务名称
        String ayName = task.getStr("ayName");
        vo.setAyName(StrUtil.blankToDefault(ayName, ""));

        // 设置计划时间
        String finish = task.getStr("finish");
        vo.setPlanTime(formatDateRange(finish));

        // 设置实际时间
        String actualFinish = task.getStr("actualFinish");
        vo.setActualTime(formatDateRange(actualFinish));

        // 计算偏差天数
        vo.setDeviationDays(calculateDeviationDays(finish, actualFinish));

        return vo;
    }

    /**
     * 计算偏差天数（负数表示延期，正数表示提前）
     */
    private String calculateDeviationDays(String planFinish, String actualFinish) {
        // 只需要检查完成时间
        if (StrUtil.hasBlank(planFinish, actualFinish)) {
            return "";
        }

        try {
            // 转换为日期对象，只保留日期部分
            Date pFinish = DateUtil.parseDate(planFinish);
            Date aFinish = DateUtil.parseDate(actualFinish);

            // 验证日期对象
            if (pFinish == null || aFinish == null) {
                return "";
            }

            // 计算计划完成日期和实际完成日期的差值
            long diffDays = (pFinish.getTime() - aFinish.getTime()) / (1000 * 60 * 60 * 24);

            return String.valueOf(diffDays);
        } catch (Exception e) {
            log.error("计算偏差天数失败, planFinish: {}, actualFinish: {}",
                    planFinish, actualFinish, e);
            return "";
        }
    }

    /**
     * 格式化日期范围
     */
    private String formatDateRange(String endDate) {
        // 只显示完成时间
        if (StrUtil.isBlank(endDate)) {
            return "";
        }

        try {
            Date end = DateUtil.parseDate(endDate);
            if (end == null) {
                return "";
            }
            return DateUtil.format(end, "yyyy/MM/dd");
        } catch (Exception e) {
            log.error("日期格式化失败, endDate: {}", endDate, e);
            return "";
        }
    }
}
