package com.teamway.security.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teamway.common.entity.ResultCode;
import com.teamway.exception.ServiceException;
import com.teamway.security.common.constant.WorkGroupConstant;
import com.teamway.security.entity.SecurityConfiguration.WorkGroup;
import com.teamway.security.mapper.WorkGroupMapper;
import com.teamway.security.service.WorkGroupService;
import com.teamway.security.vo.WorkGroupPathVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/11/7
 */
@Service
public class WorkGroupServiceImpl extends ServiceImpl<WorkGroupMapper, WorkGroup> implements WorkGroupService {

    @Autowired
    private WorkGroupMapper workGroupMapper;
    @Override
    public boolean addWorkGroup(WorkGroup workGroup) {
        checkAddOrUpdate(workGroup);
        return save(workGroup);
    }

    private void checkAddOrUpdate(WorkGroup workGroup) {
        if (ObjectUtil.isNotEmpty(workGroup.getId()) && ObjectUtil.isEmpty(getById(workGroup.getId()))) {
            throw new ServiceException(ResultCode.NOT_EXIST,"工作组");
        }
        List<WorkGroup> list = this.lambdaQuery()
                .ne(ObjectUtil.isNotEmpty(workGroup.getId()), WorkGroup::getId, workGroup.getId())
                .eq(WorkGroup::getName, workGroup.getName()).list();
        if (CollUtil.isNotEmpty(list)) {
            throw new ServiceException(ResultCode.EXIST,"工作组名称");
        }
        // 校验父级工作组是否存在
        checkPidIsExist(workGroup.getPid());
    }

    @Override
    public boolean updateWorkGroup(WorkGroup workGroup) {
        checkAddOrUpdate(workGroup);
        return updateById(workGroup);
    }

    @Override
    public boolean delWorkGroup(Long id) {
        Optional.ofNullable(this.lambdaQuery().eq(WorkGroup::getPid, id).list()).ifPresent(workGroups -> {
            if (!workGroups.isEmpty()) {
                throw new ServiceException(ResultCode.WORKGROUP_HAD_CHILD_GROUP,"该工作组下存在子工作组，无法删除");
            }
        });
        return removeById(id);
    }

    @Override
    public List<Long> getWorkGroupIdListByWorkGroupId(long id) {
        List<Long> workGroupIdList = new ArrayList<>();
        workGroupIdList.add(id);
        this.buildList(id,this.list(),workGroupIdList);
        return workGroupIdList;
    }

    /**
     * 获取所有工作组，包含完整路径
     * @return 工作组列表，带完整路径
     */
    @Override
    public List<WorkGroupPathVO> listWithFullPath() {
        return workGroupMapper.selectWithFullPath();
    }

    private void buildList(long pId, List<WorkGroup> list, List<Long> workGroupIdList) {
        for (WorkGroup workGroup : list) {
            if(workGroup.getPid().equals(pId)){
                workGroupIdList.add(workGroup.getId());
                buildList(workGroup.getId(),list,workGroupIdList);
            }
        }
    }

    @Override
    public List<WorkGroup> getWorkGroupTree() {
        return buildWorkGroupTree(list());
    }

    /**
     * 构建工作组树
     *
     * @param workGroups
     * @return
     */
    private List<WorkGroup> buildWorkGroupTree(List<WorkGroup> workGroups) {
        // 创建一个根节点
        List<WorkGroup> rootNodes = new ArrayList<>();
        for (WorkGroup workGroup : workGroups) {
            if (workGroup.getPid() == 0) {
                rootNodes.add(workGroup);
            }
        }
        // 递归构建树
        for (WorkGroup rootNode : rootNodes) {
            buildChildren(rootNode, workGroups);
        }
        return rootNodes;
    }

    private void buildChildren(WorkGroup parentNode, List<WorkGroup> workGroups) {
        List<WorkGroup> children = new ArrayList<>();
        for (WorkGroup workGroup : workGroups) {
            if (workGroup.getPid().equals(parentNode.getId())) {
                children.add(workGroup);
                buildChildren(workGroup, workGroups);
            }
        }
        parentNode.setChildren(children);
    }

    /**
     * 校验父级工作组是否存在
     *
     * @param pid
     */
    private void checkPidIsExist(Long pid) {
        if (!WorkGroupConstant.ROOT_WORK_GROUP_ID.equals(pid)) {
            WorkGroup workGroup = this.getById(pid);
            if (workGroup == null) {
                throw new ServiceException(ResultCode.NOT_EXIST,"父级工作组");
            }
        }
    }
}
