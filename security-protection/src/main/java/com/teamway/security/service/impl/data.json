[{"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "", "workerTypeName": "", "jobTypeCode": "", "jobTypeName": "", "personPost": "", "presentCount": 8}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "2", "workerTypeName": "安全管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "安全员", "presentCount": 4}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "5", "jobTypeName": "5", "personPost": "", "presentCount": 11}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "12", "jobTypeName": "保温工", "personPost": "", "presentCount": 12}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "19", "jobTypeName": "其他工种", "personPost": "", "presentCount": 3}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "8", "jobTypeName": "支模工", "personPost": "", "presentCount": 3}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "1", "jobTypeName": "普工", "personPost": "", "presentCount": 141}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "17", "jobTypeName": "木工", "personPost": "", "presentCount": 62}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "4", "jobTypeName": "架子工", "personPost": "", "presentCount": 6}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "14", "jobTypeName": "泥工", "personPost": "", "presentCount": 3}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "15", "jobTypeName": "混凝土工", "personPost": "", "presentCount": 14}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "3", "jobTypeName": "焊工", "personPost": "", "presentCount": 10}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "2", "jobTypeName": "电工", "personPost": "", "presentCount": 38}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "7", "jobTypeName": "起重指挥", "personPost": "", "presentCount": 4}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "6", "jobTypeName": "起重机司机", "personPost": "", "presentCount": 3}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "10", "jobTypeName": "钢筋工", "personPost": "", "presentCount": 35}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "", "presentCount": 1}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "安全员", "presentCount": 5}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "总工程师", "presentCount": 1}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "执行经理", "presentCount": 1}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "技术员", "presentCount": 1}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "施工员", "presentCount": 9}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "材料员", "presentCount": 1}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "测量员", "presentCount": 2}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "生产经理", "presentCount": 3}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "管理员", "presentCount": 10}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "资料员", "presentCount": 3}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "项目副经理", "presentCount": 1}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "", "subcontractUnitName": "", "workerTypeCode": "1", "workerTypeName": "管理人员", "jobTypeCode": "", "jobTypeName": "", "personPost": "项目经理", "presentCount": 1}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "1927546526282858497", "subcontractUnitName": "广西凌通劳务有限公司", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "3", "jobTypeName": "焊工", "personPost": "", "presentCount": 1}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "1950119398603800577", "subcontractUnitName": "江西东坤建设有限公司", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "1", "jobTypeName": "普工", "personPost": "", "presentCount": 1}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "1941425084898000898", "subcontractUnitName": "江西春达建设有限公司", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "1", "jobTypeName": "普工", "personPost": "", "presentCount": 1}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "1930907437638926337", "subcontractUnitName": "江西省启胜建筑劳务服务有限公司", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "3", "jobTypeName": "焊工", "personPost": "", "presentCount": 1}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "1927558968811503618", "subcontractUnitName": "江西省宗睿建设工程有限公司", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "12", "jobTypeName": "保温工", "personPost": "", "presentCount": 4}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "1927558968811503618", "subcontractUnitName": "江西省宗睿建设工程有限公司", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "1", "jobTypeName": "普工", "personPost": "", "presentCount": 8}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "1927558968811503618", "subcontractUnitName": "江西省宗睿建设工程有限公司", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "3", "jobTypeName": "焊工", "personPost": "", "presentCount": 4}, {"contractorUnitCode": "105", "contractorUnitName": "江西水电工程", "subcontractUnitCode": "1927558968811503618", "subcontractUnitName": "江西省宗睿建设工程有限公司", "workerTypeCode": "3", "workerTypeName": "工人", "jobTypeCode": "10", "jobTypeName": "钢筋工", "personPost": "", "presentCount": 1}]