<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.security.mapper.AccessAlertConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teamway.security.entity.AccessAlertConfig">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="start_work_time" property="startWorkTime" />
        <result column="end_work_time" property="endWorkTime" />
        <result column="is_alarm" property="isAlarm" />
        <result column="is_zombie_time" property="isZombieTime" />
        <result column="max_access_count" property="maxAccessCount" />
        <result column="stay_alert_time" property="stayAlertTime" />
    </resultMap>

</mapper>
