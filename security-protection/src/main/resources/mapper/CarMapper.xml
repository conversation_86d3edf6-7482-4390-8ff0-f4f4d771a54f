<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.security.mapper.CarMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teamway.security.entity.SecurityConfiguration.Car">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="person_id" property="personId" />
        <result column="car_no" property="carNo" />
        <result column="car_type" property="carType" />
        <result column="remarks" property="remarks" />
        <result column="car_index" property="carIndex" />
        <result column="is_auth" property="isAuth" />
        <result column="auth_type" property="authType" />
        <result column="begin_time" property="beginTime" />
        <result column="end_time" property="endTime" />
        <result column="state" property="state" />
        <result column="black_index" property="blackIndex" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        create_by,
        update_by,
        person_id, car_no, car_type, remarks, car_index, is_auth, auth_type, begin_time, end_time ,state ,black_index
    </sql>

    <select id="findPageList"
            resultType="com.teamway.security.dto.securityConfiguration.CarGetResDataListDto"
            parameterType="com.teamway.security.dto.securityConfiguration.CarGetReqDto">
        SELECT
        c.id id,
        p.id personId,
        c.car_index carIndex,
        c.car_no carNo,
        c.car_type carType,
        c.color,
        c.car_image_url,
        p.person_name personName,
        p.person_phone personPhone,
        p.person_unit personUnit,
        p.person_type personType,
        p.person_certificate_num personCertificateNum,
        c.remarks remarks,
        c.update_by updateBy,
        c.update_time updateTime
        FROM
        b_sec_car c
        LEFT JOIN b_sec_person p ON c.person_id = p.id
        <where>
            <if test="dto.carNo !=null and dto.carNo != ''">
                and c.car_no like concat('%',#{dto.carNo},'%')
            </if>
            <if test="dto.carType !=null">
                and c.car_type = #{dto.carType}
            </if>
            <if test="dto.personName !=null and dto.personName != ''">
                and p.person_name like concat('%',#{dto.personName},'%')
            </if>
            <if test="dto.personPhone !=null and dto.personPhone != ''">
                and p.person_phone like concat('%',#{dto.personPhone},'%')
            </if>
            <if test="dto.state !=null">
                and c.state = #{dto.state}
            </if>
            order by c.update_time desc
        </where>
    </select>

    <resultMap id="maps" type="com.teamway.security.vo.securityConfiguration.person.PersonAuthedDeviceVo">
        <id property="securityDeviceId" column="securityDeviceId"/>
        <result property="securityDeviceName" column="securityDeviceName"/>
        <collection property="securityDeviceDoorList" ofType="com.teamway.security.entity.SecurityConfiguration.SecurityDeviceDoor">
            <id property="id" column="securityDeviceDoorId"/>
            <result property="doorName" column="doorName"/>
            <result property="securityDeviceId" column="securityDeviceId"/>
        </collection>
    </resultMap>

    <select id="getAuthedByCarId" resultMap="maps">
        SELECT
            sd.id securityDeviceId,
            sd.security_device_name securityDeviceName,
            sd.id securityDeviceId
        FROM
            b_sec_security_device sd
        WHERE
                sd.id IN (
                SELECT
                    sdc.device_id
                FROM
                    b_sec_car c
                        INNER JOIN b_sec_security_device_car sdc ON c.id = sdc.car_id
                WHERE
                    car_id = #{carId})
    </select>
    <select id="getCarPersonInfo" resultType="com.teamway.security.entity.SecurityConfiguration.Person">
        SELECT
            p.id AS id,
            p.person_name AS personName,
            p.person_certificate_num AS personCertificateNum,
            p.person_certificate_type AS personCertificateType,
            p.person_job_num AS personJobNum,
            p.person_job_type AS personJobType,
            p.person_phone AS personPhone,
            p.person_sex AS personSex,
            p.person_type AS personType,
            p.person_unit AS personUnit,
            p.person_face_url AS personFaceUrl,
            p.person_source_index AS personSourceIndex,
            p.source_type AS sourceType,
            p.state AS state,
            p.person_post AS personPost,
            p.person_department AS personDepartment,
            p.person_unit_id AS personUnitId,
            p.person_birth_date AS personBirthDate,
            p.person_identity_type AS personIdentityType,
            p.person_office_phone AS personOfficePhone,
            p.person_email AS personEmail,
            p.person_address AS personAddress,
            c.car_no AS carNo,
            c.car_type AS carType,
            c.color AS carColor,
            c.car_image_url AS carImageUrl
        FROM
            b_sec_car c
                LEFT JOIN
            b_sec_person p ON c.person_id = p.id
        WHERE
            c.car_no = #{licensePlate}
    </select>
</mapper>
