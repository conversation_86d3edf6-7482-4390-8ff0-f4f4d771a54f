<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.security.mapper.CardMapper">

    <select id="listCard" parameterType="com.teamway.security.dto.securityConfiguration.card.CardListQueryDto" resultType="com.teamway.security.vo.securityConfiguration.card.CardListVo">
        select c.id cardId,p.person_name, c.card_no, c.card_state, c.card_type, c.update_by, c.update_time
        from b_sec_card c
                 inner join b_sec_person p on c.person_id = p.id
        <where>
            <if test="dto.personName != null and dto.personName != ''">
                and p.person_name like concat('%',#{dto.personName},'%')
            </if>
            <if test="dto.cardNo != null and dto.cardNo != ''">
                and c.card_no like concat('%',#{dto.cardNo},'%')
            </if>
            <if test="dto.cardState != null and dto.cardState != ''">
                and c.card_state = #{dto.cardState}
            </if>
            <if test="dto.cardType != null and dto.cardType != ''">
                and c.card_type = #{dto.cardType}
            </if>
        </where>
        order by update_time desc
    </select>

</mapper>