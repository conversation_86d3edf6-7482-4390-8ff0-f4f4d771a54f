<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.security.mapper.PersonMapper">

    <resultMap id="maps" type="com.teamway.security.vo.securityConfiguration.person.PersonAuthedDeviceVo">
        <id property="securityDeviceId" column="securityDeviceId"/>
        <result property="securityDeviceName" column="securityDeviceName"/>
        <collection property="securityDeviceDoorList" ofType="com.teamway.security.entity.SecurityConfiguration.SecurityDeviceDoor">
            <id property="id" column="securityDeviceDoorId"/>
            <result property="doorName" column="doorName"/>
            <result property="securityDeviceId" column="securityDeviceId"/>
        </collection>
    </resultMap>

    <select id="getAuthedByPersonId" parameterType="long" resultMap="maps">
        select sd.id                   securityDeviceId,
               sd.security_device_name securityDeviceName,
               sdd.id                  securityDeviceDoorId,
               sdd.door_name           doorName,
               sd.id                   securityDeviceId
        from b_sec_security_device_door sdd
                 INNER JOIN b_sec_security_device sd on sdd.security_device_id = sd.id
        where sdd.id in (select sdp.device_id
                         from b_sec_person p
                                  inner join b_sec_security_device_person sdp on p.id = sdp.person_id
                         where person_id = #{personId})
    </select>
    <select id="getPersonAuthedDevice" resultType="com.teamway.security.dto.securityConfiguration.person.PersonAuthedDeviceDto" parameterType="long">
        SELECT
            sdd.id securityDeviceDoorId,
            sdd.door_name securityDeviceDoorName,
            sdp.start_time,
            sdp.end_time
        FROM
            b_sec_security_device_person sdp
                INNER JOIN b_sec_security_device_door sdd ON
                sdp.device_id = sdd.id
        WHERE
            sdp.person_id = #{personId}
    </select>

    <resultMap id="personListMap" type="com.teamway.security.vo.securityConfiguration.person.PersonListVo">
        <id property="id" column="id" />
        <result property="personCertificateNum" column="personCertificateNum"/>
        <result property="personCertificateType" column="personCertificateType" />
        <result property="personName" column="personName" />
        <result property="personPhone" column="personPhone" />
        <result property="personSex" column="personSex" />
        <result property="personFaceUrl" column="personFaceUrl" />
        <result property="personBirthDate" column="personBirthDate" />
        <result property="personIdentityType" column="personIdentityType" />
        <result property="personOfficePhone" column="personOfficePhone" />
        <result property="personEmail" column="personEmail" />
        <result property="personAddress" column="personAddress" />
        <result property="personUnitId" column="personUnitId" />
        <result property="personUnitName" column="personUnitName" />
        <collection property="personCardList" ofType="com.teamway.security.entity.SecurityConfiguration.Card">
            <id property="personId" column="personId"/>
            <result property="cardNo" column="cardNo" />
            <result property="cardType" column="cardType" />
            <result property="personId" column="personId" />
            <result property="cardState" column="cardState" />
        </collection>
    </resultMap>

    <select id="selectPersonList"
            parameterType="com.teamway.security.dto.securityConfiguration.person.PersonListQueryDto"
            resultType="com.teamway.security.vo.securityConfiguration.person.PersonListVo">
        WITH RECURSIVE work_group_path AS (
        -- 基础查询：获取所有工作组
        SELECT
        id,
        name,
        pid,
        CAST(name AS CHAR(1000)) AS full_path
        FROM b_sec_work_group
        WHERE pid IS NULL OR pid = 0

        UNION ALL

        -- 递归查询：获取子工作组并拼接路径
        SELECT
        wg.id,
        wg.name,
        wg.pid,
        CONCAT(wp.full_path, '/', wg.name)
        FROM b_sec_work_group wg
        INNER JOIN work_group_path wp ON wg.pid = wp.id
        )

        SELECT
        p.id AS id,
        p.person_certificate_num AS personCertificateNum,
        p.person_certificate_type AS personCertificateType,
        p.person_name AS personName,
        p.person_phone AS personPhone,
        p.person_sex AS personSex,
        p.person_face_url AS personFaceUrl,
        p.person_birth_date AS personBirthDate,
        p.person_identity_type AS personIdentityType,
        p.person_office_phone AS personOfficePhone,
        p.person_email AS personEmail,
        p.person_address AS personAddress,
        p.person_unit AS personUnit,
        p.person_job_type AS personJobType,
        p.person_job_num AS personJobNum,
        p.person_type AS personType,
        p.person_department AS personDepartment,
        p.person_post AS personPost,
        p.worker_type AS workerType,
        p.is_special_worker AS isSpecialWorker,
        p.subcontract_unit AS subcontractUnit,
        p.subcontract_unit_id AS subcontractUnitId,
        p.update_time AS updateTime,
        wg.id AS personUnitId,
        wgp.full_path AS personUnitName
        FROM b_sec_person p
        LEFT JOIN b_sec_work_group wg ON wg.id = p.person_unit_id
        LEFT JOIN work_group_path wgp ON wgp.id = wg.id
        LEFT JOIN b_sec_card bsc ON bsc.person_id = p.id
        <where>
            <!-- 动态条件 -->
            <if test="dto.personName != null and dto.personName != ''">
                AND p.person_name LIKE CONCAT('%', #{dto.personName}, '%')
            </if>
            <if test="dto.personJobNum != null and dto.personJobNum != ''">
                AND p.person_job_num LIKE CONCAT('%', #{dto.personJobNum}, '%')
            </if>
            <if test="dto.personPhone != null and dto.personPhone != ''">
                AND p.person_phone LIKE CONCAT('%', #{dto.personPhone}, '%')
            </if>
            <if test="dto.personUnit != null and dto.personUnit != ''">
                AND (
                CASE
                WHEN p.person_unit IS NOT NULL THEN
                EXISTS (
                SELECT 1
                FROM hm_common_type
                WHERE category = '4'
                AND type_code = p.person_unit
                AND status = 1
                AND name LIKE CONCAT('%', #{dto.personUnit}, '%')
                )
                ELSE FALSE
                END
                )
            </if>
            <if test="dto.personType != null and dto.personType != ''">
                AND p.person_type = #{dto.personType}
            </if>
            <if test="dto.personJobType != null and dto.personJobType != ''">
                AND p.person_job_type = #{dto.personJobType}
            </if>
            <if test="dto.blackListType != null and dto.blackListType != ''">
                AND p.state = #{dto.blackListType}
            </if>
            <if test="dto.cardId != null and dto.cardId != ''">
                AND bsc.card_no = #{dto.cardId}
            </if>
            <if test="dto.personCertificateNum != null and dto.personCertificateNum != ''">
                AND p.person_certificate_num LIKE CONCAT('%', #{dto.personCertificateNum}, '%')
            </if>
            <if test="dto.workerType != null and dto.workerType != ''">
                AND p.worker_type = #{dto.workerType}
            </if>
            <if test="dto.isSpecialWorker != null and dto.isSpecialWorker != ''">
                AND p.is_special_worker = #{dto.isSpecialWorker}
            </if>
            <!-- 分包单位模糊查询 -->
            <if test="dto.subcontractUnit != null and dto.subcontractUnit != ''">
                AND p.subcontract_unit LIKE CONCAT('%', #{dto.subcontractUnit}, '%')
            </if>
            <!-- 工种名称模糊查询 -->
            <if test="dto.personJobTypeName != null and dto.personJobTypeName != ''">
                AND (
                    CASE
                        WHEN p.person_job_type IS NOT NULL THEN
                            EXISTS (
                                SELECT 1
                                FROM hm_common_type
                                WHERE category = '1'
                                AND type_code = p.person_job_type
                                AND status = 1
                                AND name LIKE CONCAT('%', #{dto.personJobTypeName}, '%')
                            )
                        ELSE FALSE
                    END
                )
            </if>
            <!-- 岗位模糊查询 -->
            <if test="dto.personPost != null and dto.personPost != ''">
                AND p.person_post LIKE CONCAT('%', #{dto.personPost}, '%')
            </if>
            <!-- 工号模糊查询 -->
            <if test="dto.personJobNumLike != null and dto.personJobNumLike != ''">
                AND p.person_job_num LIKE CONCAT('%', #{dto.personJobNumLike}, '%')
            </if>
        </where>
        <!-- 排序逻辑 -->
        ORDER BY p.update_time DESC, p.id ASC
    </select>
    <select id="selectAlertRecord" resultType="com.teamway.security.vo.accessManagement.person.personAlarmVo">
        SELECT
        a.alarm_id AS alarmId,
        b.person_unit AS personUnit,
        b.person_type AS personType,
        b.person_name AS personName,
        b.person_job_type AS personJobType,
        b.person_phone AS personPhone,
        a.alarm_desc AS alarmDesc,
        a.alarm_time AS alarmTime
        FROM b_alarm_center a
        INNER JOIN b_sec_person b ON a.alarm_id = b.id
        AND b.person_name IS NOT NULL
        AND b.person_name != ''
        <where>
            <if test="dto.alarmType != null">
                AND a.type = #{dto.alarmType}
            </if>
            <if test="dto.personUnit != null and dto.personUnit != ''">
                AND (
                CASE
                WHEN b.person_unit IS NOT NULL THEN
                EXISTS (
                SELECT 1
                FROM hm_common_type
                WHERE category = '4'
                AND type_code = b.person_unit
                AND status = 1
                AND name LIKE CONCAT('%', #{dto.personUnit}, '%')
                )
                ELSE FALSE
                END
                )
            </if>
            <if test="dto.personName != null and dto.personName != ''">
                AND b.person_name LIKE CONCAT('%', #{dto.personName}, '%')
            </if>
        </where>
        ORDER BY a.alarm_time DESC
    </select>
    <select id="getPersonAuthedDeviceWithDetails" resultType="com.teamway.security.dto.securityConfiguration.person.PersonAuthedDeviceDto">
        SELECT
            sdp.device_id AS securityDeviceDoorId,
            sdd.door_name AS securityDeviceDoorName,
            sd.security_device_name AS securityDeviceName,
            r.name AS regionName,
            sdp.start_time AS startTime,
            sdp.end_time AS endTime
        FROM
            b_sec_security_device_person sdp
                LEFT JOIN
            b_sec_security_device_door sdd ON sdp.device_id = sdd.id
                LEFT JOIN
            b_sec_security_device sd ON sdd.security_device_id = sd.id
                LEFT JOIN
            base_region r ON sd.region_id = r.id
        WHERE
            sdp.person_id = #{personId}
    </select>

    <select id="selectFactoryPersonDetailPage" resultType="com.teamway.security.entity.SecurityConfiguration.Person">
        SELECT
            p.id,
            p.person_certificate_num AS personCertificateNum,
            p.person_certificate_type AS personCertificateType,
            p.person_job_num AS personJobNum,
            p.person_job_type AS personJobType,
            p.person_name AS personName,
            p.person_phone AS personPhone,
            p.person_sex AS personSex,
            p.person_type AS personType,
            p.person_unit AS personUnit,
            p.person_face_url AS personFaceUrl,
            p.person_source_index AS personSourceIndex,
            p.state,
            p.person_post AS personPost,
            p.person_department AS personDepartment,
            p.person_unit_id AS personUnitId,
            p.person_birth_date AS personBirthDate,
            p.person_identity_type AS personIdentityType,
            p.person_office_phone AS personOfficePhone,
            p.person_email AS personEmail,
            p.person_address AS personAddress,
            p.worker_type AS workerType,
            p.is_special_worker AS isSpecialWorker,
            p.subcontract_unit AS subcontractUnit
        FROM b_sec_person p
        <where>
            <!-- 人员身份证号列表过滤 -->
            <if test="personIds != null and personIds.size() > 0">
                AND p.person_certificate_num IN
                <foreach collection="personIds" item="personId" open="(" separator="," close=")">
                    #{personId}
                </foreach>
            </if>
            <!-- 人员姓名模糊查询 -->
            <if test="personName != null and personName != ''">
                AND p.person_name LIKE CONCAT('%', #{personName}, '%')
            </if>
            <!-- 总包单位模糊查询 -->
            <if test="contractorUnitName != null and contractorUnitName != ''">
                AND (
                    CASE
                        WHEN p.person_unit IS NOT NULL THEN
                            EXISTS (
                                SELECT 1
                                FROM hm_common_type
                                WHERE category = '4'
                                AND type_code = p.person_unit
                                AND status = 1
                                AND name LIKE CONCAT('%', #{contractorUnitName}, '%')
                            )
                        ELSE FALSE
                    END
                )
            </if>
            <!-- 分包单位模糊查询 -->
            <if test="subcontractUnitName != null and subcontractUnitName != ''">
                AND p.subcontract_unit LIKE CONCAT('%', #{subcontractUnitName}, '%')
            </if>
            <!-- 工种模糊查询 -->
            <if test="jobTypeName != null and jobTypeName != ''">
                AND (
                    CASE
                        WHEN p.person_job_type IS NOT NULL THEN
                            EXISTS (
                                SELECT 1
                                FROM hm_common_type
                                WHERE category = '1'
                                AND type_code = p.person_job_type
                                AND status = 1
                                AND name LIKE CONCAT('%', #{jobTypeName}, '%')
                            )
                        ELSE FALSE
                    END
                )
            </if>
            <!-- 岗位模糊查询 -->
            <if test="personPost != null and personPost != ''">
                AND p.person_post LIKE CONCAT('%', #{personPost}, '%')
            </if>
        </where>
        ORDER BY p.person_name ASC
    </select>
</mapper>