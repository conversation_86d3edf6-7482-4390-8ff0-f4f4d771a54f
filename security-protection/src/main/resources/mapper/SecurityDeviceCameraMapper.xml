<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.security.mapper.SecurityDeviceCameraMapper">

    <resultMap id="maps" type="com.teamway.security.dto.securityConfiguration.BandingCameraQueryDto">
        <id column="security_device_id" property="securityDeviceId"/>
        <collection property="cameraList" ofType="com.teamway.security.dto.securityConfiguration.CameraPresetBandingDto">
            <result property="cameraId" column="camera_id"/>
            <result property="presetId" column="preset_id"/>
        </collection>
    </resultMap>

    <resultMap id="cameraInfoMaps" type="com.teamway.security.vo.accessManagement.securityDevice.BandedCameraVo">
        <id column="security_device_id" property="securityDeviceId"/>
        <collection property="cameraList" ofType="com.teamway.security.vo.accessManagement.securityDevice.CameraBandingVo">
            <result property="cameraId" column="camera_id"/>
            <result property="cameraName" column="name"/>
            <result property="subId" column="sub_id"/>
            <result property="mainId" column="main_id"/>
            <result property="videoProtocol" column="video_protocol"/>
            <result property="presetId" column="preset_id"/>
        </collection>
    </resultMap>

    <select id="getCameraBySecurityDeviceId" resultMap="maps" parameterType="long">
        select * from b_sec_security_device_camera where security_device_id = #{id}
    </select>
    <select id="getCameraInfoBySecurityDeviceId" resultMap="cameraInfoMaps" parameterType="long">
        SELECT
            sdc.*,
            c.main_id,
            c.name,
            c.sub_id,
            c.video_protocol
        FROM
            b_sec_security_device_camera sdc
                INNER JOIN base_camera c ON sdc.camera_id = c.id
        WHERE
            sdc.security_device_id = #{id}
    </select>
</mapper>