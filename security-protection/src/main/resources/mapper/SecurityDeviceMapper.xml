<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.security.mapper.SecurityDeviceMapper">


    <select id="selectPage" resultType="com.teamway.security.entity.SecurityConfiguration.SecurityDevice">
        SELECT
        sd.id,
        sd.security_device_name,
        sd.security_device_type,
        sd.manufacturer,
        sd.region_id,
        sd.device_source_index,
        sd.device_type_code,
        sd.create_time
        FROM b_sec_security_device sd
        LEFT JOIN b_sec_security_device_door sdd ON sdd.security_device_id = sd.id
        WHERE 1=1
        <if test="dto.securityDeviceIp != null and dto.securityDeviceIp != ''">
            AND sdd.ip LIKE CONCAT('%', #{dto.securityDeviceIp}, '%')
        </if>
        <if test="dto.securityDeviceName != null and dto.securityDeviceName != ''">
            AND sd.security_device_name LIKE CONCAT('%', #{dto.securityDeviceName}, '%')
        </if>
        <if test="dto.manufacturer != null and dto.manufacturer != ''">
            AND sd.manufacturer = #{dto.manufacturer}
        </if>
        <if test="dto.regionId != null">
            AND sd.region_id = #{dto.regionId}
        </if>
        <if test="dto.type != null and dto.type != ''">
            AND sd.security_device_type = #{dto.type}
        </if>
        ORDER BY sd.create_time DESC, sd.id ASC
    </select>
</mapper>