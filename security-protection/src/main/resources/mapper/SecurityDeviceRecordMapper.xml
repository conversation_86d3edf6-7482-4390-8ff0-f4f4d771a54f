<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.security.mapper.SecurityDeviceRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teamway.security.entity.SecurityConfiguration.SecurityDeviceRecord">
        <result column="id" property="id" />
        <result column="date" property="date" />
        <result column="device_name" property="deviceName" />
        <result column="state" property="state" />
        <result column="person_index" property="personIndex" />
        <result column="person_name" property="personName" />
        <result column="work_unit" property="workUnit" />
        <result column="car_index" property="carIndex" />
        <result column="plate_no" property="plateNo" />
        <result column="person_type" property="personType" />
        <result column="car_type" property="carType" />
        <result column="auth_type" property="authType" />
        <result column="pic_url" property="picUrl" />
        <result column="record_type" property="recordType" />
        <result column="device_id" property="deviceId" />
        <result column="open_type" property="openType" />
        <result column="region_id" property="regionId" />
        <result column="region_name" property="regionName" />
        <result column="card_no" property="cardNo" />
        <result column="security_device_door_name" property="securityDeviceDoorName" />
    </resultMap>

    <select id="getAccessRecordPage"
            resultType="com.teamway.security.entity.SecurityConfiguration.SecurityDeviceRecord">



    </select>
    <select id="getRecordPersonIdList" resultType="java.lang.String">
        select distinct person_index
                from b_sec_security_device_record
                where person_index != ''
          and state in (1, 2)
          and record_type = 0
    </select>
    <select id="getRecordPersonDeviceIdList" resultType="java.lang.String" parameterType="string">
        select distinct device_id
        from b_sec_security_device_record
        where person_index = #{personIndex}
          and state in (1, 2)
          and record_type = 0
    </select>
    <select id="eventListByPerson" resultType="com.teamway.security.vo.accessManagement.person.EventListPersonVo">
        SELECT
        sdr.person_name AS personName,
        sdr.card_no AS cardNo,
        sdr.open_type AS eventType,
        wg.name AS workGroupName,
        sdr.device_name AS securityDeviceName,
        sdr.security_device_door_name AS securityDeviceDoorName,
        sdr.state AS direction,
        sdr.work_unit AS workUnit,
        sdr.person_type AS personType,
        p.person_type AS type,
        p.person_post As personPost,
        sdr.auth_type AS authType,
        sdr.date AS time,
        sdr.pic_url AS imageUrl,
        p.person_unit AS contractorUnitCode,
        p.subcontract_unit_id AS subcontractUnitCode,
        p.subcontract_unit AS subcontractUnitName
        FROM b_sec_security_device_record sdr
        LEFT JOIN b_sec_person p ON p.person_certificate_num = sdr.id_card
        LEFT JOIN b_sec_work_group wg ON wg.id = p.person_unit_id
        WHERE sdr.record_type = '0'

        <!-- 根据查询条件动态拼接SQL -->
        <if test="queryDto.personIdentityType != null and queryDto.personIdentityType != ''">
            AND p.person_identity_type = #{queryDto.personIdentityType}
        </if>
        <if test="queryDto.authType != null and queryDto.authType != ''">
            AND sdr.auth_type = #{queryDto.authType}
        </if>
        <if test="queryDto.eventType != null and queryDto.eventType != ''">
            AND sdr.open_type = #{queryDto.eventType}
        </if>
        <if test="queryDto.personName != null and queryDto.personName != ''">
            AND sdr.person_name LIKE CONCAT('%', #{queryDto.personName}, '%')
        </if>
        <if test="queryDto.workUnit != null and queryDto.workUnit != ''">
            AND (
            CASE
            WHEN sdr.auth_type = '1' AND sdr.work_unit IS NOT NULL THEN
            CASE
            WHEN #{queryDto.workUnit} regexp '^\\d+$' THEN
            EXISTS (
            SELECT 1
            FROM hm_common_type
            WHERE category = '4'
            AND type_code = sdr.work_unit
            AND status = 1
            AND type_code = #{queryDto.workUnit}
            )
            ELSE
            EXISTS (
            SELECT 1
            FROM hm_common_type
            WHERE category = '4'
            AND type_code = sdr.work_unit
            AND status = 1
            AND name LIKE CONCAT('%', #{queryDto.workUnit}, '%')
            )
            END
            WHEN sdr.work_unit IS NOT NULL THEN
            CASE
            WHEN #{queryDto.workUnit} regexp '^\\d+$' THEN
            sdr.work_unit = #{queryDto.workUnit}
            ELSE
            sdr.work_unit LIKE CONCAT('%', #{queryDto.workUnit}, '%')
            END
            ELSE FALSE
            END
            )
        </if>
        <if test="queryDto.startTime != null and queryDto.startTime != ''">
            AND sdr.date &gt;= #{queryDto.startTime}
        </if>
        <if test="queryDto.endTime != null and queryDto.endTime != ''">
            AND sdr.date &lt;= #{queryDto.endTime}
        </if>

        <!-- 如果有工作组ID，进行IN查询 -->
        <if test="queryDto.workGroupId != null and queryDto.workGroupId != ''">
            AND wg.id IN (
            SELECT wg_id
            FROM b_sec_work_group
            WHERE parent_id = #{queryDto.workGroupId}
            )
        </if>
        
        <!-- 分包单位筛选 -->
        <if test="queryDto.subcontractUnitCode != null and queryDto.subcontractUnitCode != ''">
            AND p.subcontract_unit_id = #{queryDto.subcontractUnitCode}
        </if>
        <if test="queryDto.subcontractUnitName != null and queryDto.subcontractUnitName != ''">
            AND p.subcontract_unit LIKE CONCAT('%', #{queryDto.subcontractUnitName}, '%')
        </if>
        
        <!-- 岗位筛选 -->
        <if test="queryDto.personPost != null and queryDto.personPost != ''">
            AND p.person_post LIKE CONCAT('%', #{queryDto.personPost}, '%')
        </if>
        
        <!-- 门禁点筛选 -->
        <if test="queryDto.securityDeviceDoorName != null and queryDto.securityDeviceDoorName != ''">
            AND sdr.security_device_door_name LIKE CONCAT('%', #{queryDto.securityDeviceDoorName}, '%')
        </if>
        
        <!-- 工种筛选 -->
        <if test="queryDto.personType != null and queryDto.personType != ''">
            AND (
            CASE
            WHEN #{queryDto.personType} regexp '^\\d+$' THEN
            sdr.person_type = #{queryDto.personType}
            ELSE
            EXISTS (
            SELECT 1
            FROM hm_common_type
            WHERE category = '1'
            AND type_code = sdr.person_type
            AND status = 1
            AND name LIKE CONCAT('%', #{queryDto.personType}, '%')
            )
            END
            )
        </if>
        
        <!-- 进场方向筛选 -->
        <if test="queryDto.direction != null and queryDto.direction != ''">
            AND sdr.state = #{queryDto.direction}
        </if>

        ORDER BY sdr.date DESC
    </select>

    <select id="eventListByPersonMis" resultType="com.teamway.security.vo.accessManagement.person.EventListPersonVo">
        SELECT
        sdr.person_name AS personName,
        sdr.card_no AS cardNo,
        sdr.open_type AS eventType,
        wg.name AS workGroupName,
        sdr.device_name AS securityDeviceName,
        sdr.security_device_door_name AS securityDeviceDoorName,
        sdr.state AS direction,
        sdr.work_unit AS workUnit,
        sdr.person_type AS personType,
        p.person_type AS type,
        p.person_post As personPost,
        sdr.auth_type AS authType,
        sdr.date AS time,
        sdr.pic_url AS imageUrl,
        p.person_unit AS contractorUnitCode,
        p.subcontract_unit_id AS subcontractUnitCode,
        p.subcontract_unit AS subcontractUnitName
        FROM b_sec_security_device_record sdr
        LEFT JOIN b_sec_person p ON p.person_certificate_num = sdr.id_card
        LEFT JOIN b_sec_work_group wg ON wg.id = p.person_unit_id
        WHERE sdr.record_type = '0'

        <!-- 根据查询条件动态拼接SQL -->
        <if test="queryDto.personIdentityType != null and queryDto.personIdentityType != ''">
            AND p.person_identity_type = #{queryDto.personIdentityType}
        </if>
        <if test="queryDto.authType != null and queryDto.authType != ''">
            AND sdr.auth_type = #{queryDto.authType}
        </if>
        <if test="queryDto.eventType != null and queryDto.eventType != ''">
            AND sdr.open_type = #{queryDto.eventType}
        </if>
        <if test="queryDto.personName != null and queryDto.personName != ''">
            AND sdr.person_name LIKE CONCAT('%', #{queryDto.personName}, '%')
        </if>
        <if test="queryDto.workUnit != null and queryDto.workUnit != ''">
            AND (
            CASE
            WHEN sdr.auth_type = '1' AND sdr.work_unit IS NOT NULL THEN
            CASE
            WHEN #{queryDto.workUnit} regexp '^\\d+$' THEN
            EXISTS (
            SELECT 1
            FROM hm_common_type
            WHERE category = '4'
            AND type_code = sdr.work_unit
            AND status = 1
            AND type_code = #{queryDto.workUnit}
            )
            ELSE
            EXISTS (
            SELECT 1
            FROM hm_common_type
            WHERE category = '4'
            AND type_code = sdr.work_unit
            AND status = 1
            AND name LIKE CONCAT('%', #{queryDto.workUnit}, '%')
            )
            END
            WHEN sdr.work_unit IS NOT NULL THEN
            CASE
            WHEN #{queryDto.workUnit} regexp '^\\d+$' THEN
            sdr.work_unit = #{queryDto.workUnit}
            ELSE
            sdr.work_unit LIKE CONCAT('%', #{queryDto.workUnit}, '%')
            END
            ELSE FALSE
            END
            )
        </if>
        <if test="queryDto.startTime != null and queryDto.startTime != ''">
            AND sdr.date &gt;= #{queryDto.startTime}
        </if>
        <if test="queryDto.endTime != null and queryDto.endTime != ''">
            AND sdr.date &lt;= #{queryDto.endTime}
        </if>

        <!-- 如果有工作组ID，进行IN查询 -->
        <if test="queryDto.workGroupId != null and queryDto.workGroupId != ''">
            AND wg.id IN (
            SELECT wg_id
            FROM b_sec_work_group
            WHERE parent_id = #{queryDto.workGroupId}
            )
        </if>

        <!-- 分包单位筛选 -->
        <if test="queryDto.subcontractUnitCode != null and queryDto.subcontractUnitCode != ''">
            AND p.subcontract_unit_id = #{queryDto.subcontractUnitCode}
        </if>
        <if test="queryDto.subcontractUnitName != null and queryDto.subcontractUnitName != ''">
            AND p.subcontract_unit LIKE CONCAT('%', #{queryDto.subcontractUnitName}, '%')
        </if>

        <!-- 岗位筛选 -->
        <if test="queryDto.personPost != null and queryDto.personPost != ''">
            AND p.person_post LIKE CONCAT('%', #{queryDto.personPost}, '%')
        </if>

        <!-- 门禁点筛选 -->
        <if test="queryDto.securityDeviceDoorName != null and queryDto.securityDeviceDoorName != ''">
            AND sdr.security_device_door_name LIKE CONCAT('%', #{queryDto.securityDeviceDoorName}, '%')
        </if>

        <!-- 工种筛选 -->
        <if test="queryDto.personType != null and queryDto.personType != ''">
            AND (
            CASE
            WHEN #{queryDto.personType} regexp '^\\d+$' THEN
            sdr.person_type = #{queryDto.personType}
            ELSE
            EXISTS (
            SELECT 1
            FROM hm_common_type
            WHERE category = '1'
            AND type_code = sdr.person_type
            AND status = 1
            AND name LIKE CONCAT('%', #{queryDto.personType}, '%')
            )
            END
            )
        </if>

        <!-- 进场方向筛选 -->
        <if test="queryDto.direction != null and queryDto.direction != ''">
            AND sdr.state = #{queryDto.direction}
        </if>

        ORDER BY sdr.date DESC
    </select>

    <select id="eventListByCar" resultType="com.teamway.security.vo.accessManagement.car.EventListCarVo">
        SELECT
        sdr.plate_no AS carNo,
        c.car_type AS carType,
        c.color AS carColor,
        sdr.open_type AS eventType,
        sdr.device_name AS securityDeviceName,
        sdr.date AS time,
        sdr.pic_url AS imageUrl,
        p.person_name AS personName,
        CASE
        WHEN p.person_type = '1' AND p.person_unit IS NOT NULL THEN
        (SELECT ht.name
        FROM hm_common_type ht
        WHERE ht.category = '4'
        AND ht.type_code = p.person_unit
        AND ht.status = 1)
        WHEN p.person_unit IS NOT NULL THEN p.person_unit
        ELSE NULL
        END AS personUnit
        FROM b_sec_security_device_record sdr
        LEFT JOIN b_sec_car c ON c.car_no = sdr.plate_no
        LEFT JOIN b_sec_person p ON p.id = c.person_id
        WHERE sdr.record_type = '1'

        <!-- 根据查询条件动态拼接SQL -->
        <if test="queryDto.eventType != null and queryDto.eventType != ''">
            AND sdr.open_type = #{queryDto.eventType}
        </if>
        <if test="queryDto.carNo != null and queryDto.carNo != ''">
            AND sdr.plate_no LIKE CONCAT('%', #{queryDto.carNo}, '%')
        </if>
        <if test="queryDto.carType != null and queryDto.carType != ''">
            AND c.car_type = #{queryDto.carType}
        </if>
        <if test="queryDto.startTime != null and queryDto.startTime != ''">
            AND sdr.date &gt;= #{queryDto.startTime}
        </if>
        <if test="queryDto.endTime != null and queryDto.endTime != ''">
            AND sdr.date &lt;= #{queryDto.endTime}
        </if>

        ORDER BY sdr.date DESC
    </select>
    <select id="selectTodyRecordCountByPersonId" resultType="java.lang.Integer">
        select count(person_index) count
        from b_sec_security_device_record
        where person_index = #{personId}
          and DATE_FORMAT(date, '%Y-%m-%d')  = CURDATE()
        group by person_index;
    </select>
    <select id="getLatestAccessControlRecord" resultMap="BaseResultMap">
        <![CDATA[SELECT t.*
        FROM b_sec_security_device_record t
        INNER JOIN (
        SELECT person_index, MAX(date) AS latest_date
        FROM b_sec_security_device_record
        WHERE record_type = 0 and state = 1 and person_index <> ''
        and person_index is not null
        GROUP BY person_index
        ) latest
        ON t.person_index = latest.person_index
        AND t.date = latest.latest_date
        WHERE t.record_type = 0 and t.state = 1
        AND NOT EXISTS (
        SELECT 1
        FROM b_sec_security_device_record t2
        WHERE t2.person_index = t.person_index
        AND t2.record_type = 0 and t2.state = 2
        AND t2.date > t.date
        );]]>
    </select>
    <select id="getCarStatistics" resultType="com.teamway.security.vo.statistics.CarTypeCountVO">
        SELECT
            r.car_type AS carType,
            COUNT(DISTINCT r.plate_no) AS count
        FROM b_sec_security_device_record r
        WHERE r.car_type IS NOT NULL
          AND DATE(r.date) = CURDATE()
          AND r.plate_no IS NOT NULL
          AND r.record_type = 1  -- 车辆记录
          AND EXISTS (
            SELECT 1
            FROM (
            SELECT plate_no,
            SUM(CASE WHEN state = 1 THEN 1 ELSE 0 END) as in_count,  -- 进场次数
            SUM(CASE WHEN state = 2 THEN 1 ELSE 0 END) as out_count  -- 出场次数
            FROM b_sec_security_device_record
            WHERE DATE(date) = CURDATE()
          AND record_type = 1
          AND plate_no IS NOT NULL
            GROUP BY plate_no
            HAVING in_count > out_count  -- 进场次数大于出场次数表示在场
            ) t
            WHERE t.plate_no = r.plate_no
            )
        GROUP BY r.car_type;
    </select>
    
    <select id="getCarInOutStatistics" resultType="java.util.Map">
        SELECT
            plate_no AS plateNo,
            state,
            date
        FROM b_sec_security_device_record
        WHERE record_type = 1
          AND plate_no IS NOT NULL
          AND DATE(date) = CURDATE()
        ORDER BY plate_no, date
    </select>
</mapper>