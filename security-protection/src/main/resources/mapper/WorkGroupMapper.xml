<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teamway.security.mapper.WorkGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teamway.security.entity.SecurityConfiguration.WorkGroup">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="pid" property="pid" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 带完整路径的工作组映射结果 -->
    <resultMap id="WorkGroupPathMap" type="com.teamway.security.vo.WorkGroupPathVO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="pid" property="pid" />
        <result column="full_path" property="fullPath" />
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, name, pid, create_time, update_time, create_by, update_by
    </sql>

    <!-- 查询所有工作组及其完整路径 -->
    <select id="selectWithFullPath" resultMap="WorkGroupPathMap">
        WITH RECURSIVE work_group_path AS (
            -- 基础查询：获取所有顶级工作组
            SELECT
                id,
                name,
                pid,
                CAST(name AS CHAR(1000)) AS full_path
            FROM b_sec_work_group
            WHERE pid IS NULL OR pid = 0

            UNION ALL

            -- 递归查询：获取子工作组并拼接路径
            SELECT
                wg.id,
                wg.name,
                wg.pid,
                CONCAT(wp.full_path, '/', wg.name)
            FROM b_sec_work_group wg
                     INNER JOIN work_group_path wp ON wg.pid = wp.id
        )

        SELECT
            id,
            name,
            pid,
            full_path
        FROM work_group_path
        ORDER BY full_path
    </select>

    <!-- 其他方法可以根据需要添加 -->
</mapper>