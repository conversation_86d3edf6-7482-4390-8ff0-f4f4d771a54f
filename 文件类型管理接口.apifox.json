{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "文件类型管理接口", "description": "文件类型信息管理相关接口", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "根目录", "id": 46254030, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "SHARED", "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "文件类型管理", "id": 55296478, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "文件类型信息管理相关接口", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "创建文件类型", "api": {"id": "288109401", "method": "post", "path": "/fileTypeInfo/create", "parameters": {}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "662164001", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回码，0表示成功"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "boolean", "description": "操作结果，true表示成功"}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\r\n  \"code\": 0,\r\n  \"message\": \"成功\",\r\n  \"data\": true\r\n}", "responseId": 662164001, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "string", "title": "类型编号", "description": "文件类型编号，最大长度20个字符", "maxLength": 20}, "typeName": {"type": "string", "title": "类型名称", "description": "文件类型名称，必填，最大长度50个字符", "maxLength": 50}, "relatedFileCount": {"type": "integer", "title": "关联文件数量", "description": "关联的文件数量，默认为0", "default": 0}}, "required": ["typeName"], "x-apifox-orders": ["code", "typeName", "relatedFileCount"]}, "mediaType": "", "examples": [{"mediaType": "application/json", "value": "{\r\n  \"code\": \"DOC_001\",\r\n  \"typeName\": \"文档类型\",\r\n  \"relatedFileCount\": 0\r\n}"}], "oasExtensions": ""}, "description": "创建新的文件类型", "tags": [], "status": "developing", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 10, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.662164001"], "visibility": "INHERITED", "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "根据ID删除文件类型", "api": {"id": "288109402", "method": "delete", "path": "/fileTypeInfo/removeById/{id}", "parameters": {"path": [{"name": "id", "required": true, "schema": {"type": "integer"}, "description": "文件类型ID，精确匹配"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "662164002", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回码，0表示成功"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "boolean", "description": "操作结果，true表示成功"}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\r\n  \"code\": 0,\r\n  \"message\": \"成功\",\r\n  \"data\": true\r\n}", "responseId": 662164002, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "description": "根据ID删除指定的文件类型", "tags": [], "status": "developing", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 20, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.662164002"], "visibility": "INHERITED", "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "批量删除文件类型", "api": {"id": "288109407", "method": "post", "path": "/fileTypeInfo/removeByIds", "parameters": {}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "662164007", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回码，0表示成功"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "boolean", "description": "操作结果，true表示成功"}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\r\n  \"code\": 0,\r\n  \"message\": \"成功\",\r\n  \"data\": true\r\n}", "responseId": 662164007, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "title": "ID列表", "description": "要删除的文件类型ID列表，不能为空"}}, "required": ["ids"], "x-apifox-orders": ["ids"]}, "mediaType": "", "examples": [{"mediaType": "application/json", "value": "{\r\n  \"ids\": [1, 2, 3]\r\n}"}], "oasExtensions": ""}, "description": "批量删除指定的文件类型", "tags": [], "status": "developing", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 25, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.662164007"], "visibility": "INHERITED", "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "更新文件类型", "api": {"id": "288109403", "method": "post", "path": "/fileTypeInfo/update", "parameters": {}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "662164003", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回码，0表示成功"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "boolean", "description": "操作结果，true表示成功"}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\r\n  \"code\": 0,\r\n  \"message\": \"成功\",\r\n  \"data\": true\r\n}", "responseId": 662164003, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "title": "文件类型ID", "description": "要更新的文件类型ID，必填"}, "code": {"type": "string", "title": "类型编号", "description": "文件类型编号，最大长度20个字符"}, "typeName": {"type": "string", "title": "类型名称", "description": "文件类型名称，最大长度50个字符"}, "relatedFileCount": {"type": "integer", "title": "关联文件数量", "description": "关联的文件数量"}}, "required": ["id"], "x-apifox-orders": ["id", "code", "typeName", "relatedFileCount"]}, "mediaType": "", "examples": [{"mediaType": "application/json", "value": "{\r\n  \"id\": 1,\r\n  \"code\": \"DOC_001\",\r\n  \"typeName\": \"文档类型\",\r\n  \"relatedFileCount\": 5\r\n}"}], "oasExtensions": ""}, "description": "更新指定的文件类型信息", "tags": [], "status": "developing", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 30, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.662164003"], "visibility": "INHERITED", "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "根据ID查询文件类型", "api": {"id": "288109404", "method": "get", "path": "/fileTypeInfo/getById/{id}", "parameters": {"path": [{"name": "id", "required": true, "schema": {"type": "integer"}, "description": "文件类型ID，精确匹配"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "662164004", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回码，0表示成功"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "文件类型ID"}, "code": {"type": "string", "description": "类型编号"}, "typeName": {"type": "string", "description": "类型名称"}, "relatedFileCount": {"type": "integer", "description": "关联文件数量"}, "createBy": {"type": "string", "description": "创建人"}, "createDate": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateBy": {"type": "string", "description": "更新人"}, "updateDate": {"type": "string", "format": "date-time", "description": "更新时间"}, "deleteFlag": {"type": "integer", "description": "删除标志，0表示正常，-1表示已删除"}}}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\r\n  \"code\": 0,\r\n  \"message\": \"成功\",\r\n  \"data\": {\r\n    \"id\": 1,\r\n    \"code\": \"DOC_001\",\r\n    \"typeName\": \"文档类型\",\r\n    \"relatedFileCount\": 5,\r\n    \"createBy\": \"admin\",\r\n    \"createDate\": \"2025-07-24T10:00:00\",\r\n    \"updateBy\": \"admin\",\r\n    \"updateDate\": \"2025-07-24T10:00:00\",\r\n    \"deleteFlag\": 0\r\n  }\r\n}", "responseId": 662164004, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "description": "根据ID查询指定的文件类型详情", "tags": [], "status": "developing", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 40, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.662164004"], "visibility": "INHERITED", "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "分页查询文件类型", "api": {"id": "288109405", "method": "get", "path": "/fileTypeInfo/page", "parameters": {"query": [{"name": "currentPage", "required": true, "schema": {"type": "integer", "default": 1, "minimum": 1}, "description": "当前页码，从1开始"}, {"name": "pageSize", "required": true, "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}, "description": "每页大小，范围1-100"}, {"name": "typeName", "required": false, "schema": {"type": "string"}, "description": "类型名称，支持模糊查询"}, {"name": "code", "required": false, "schema": {"type": "string"}, "description": "类型编号，支持模糊查询"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "662164005", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回码，0表示成功"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"records": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "文件类型ID"}, "code": {"type": "string", "description": "类型编号"}, "typeName": {"type": "string", "description": "类型名称"}, "relatedFileCount": {"type": "integer", "description": "关联文件数量"}, "createBy": {"type": "string", "description": "创建人"}, "createDate": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateBy": {"type": "string", "description": "更新人"}, "updateDate": {"type": "string", "format": "date-time", "description": "更新时间"}, "deleteFlag": {"type": "integer", "description": "删除标志，0表示正常，-1表示已删除"}}}, "description": "记录列表"}, "total": {"type": "integer", "description": "总记录数"}, "size": {"type": "integer", "description": "每页大小"}, "current": {"type": "integer", "description": "当前页码"}, "orders": {"type": "array", "items": {"type": "object"}, "description": "排序信息"}, "pages": {"type": "integer", "description": "总页数"}}}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\r\n  \"code\": 0,\r\n  \"message\": \"成功\",\r\n  \"data\": {\r\n    \"records\": [\r\n      {\r\n        \"id\": 1,\r\n        \"code\": \"DOC_001\",\r\n        \"typeName\": \"文档类型\",\r\n        \"relatedFileCount\": 5,\r\n        \"createBy\": \"admin\",\r\n        \"createDate\": \"2025-07-24T10:00:00\",\r\n        \"updateBy\": \"admin\",\r\n        \"updateDate\": \"2025-07-24T10:00:00\",\r\n        \"deleteFlag\": 0\r\n      },\r\n      {\r\n        \"id\": 2,\r\n        \"code\": \"IMG_001\",\r\n        \"typeName\": \"图片类型\",\r\n        \"relatedFileCount\": 3,\r\n        \"createBy\": \"admin\",\r\n        \"createDate\": \"2025-07-24T10:00:00\",\r\n        \"updateBy\": \"admin\",\r\n        \"updateDate\": \"2025-07-24T10:00:00\",\r\n        \"deleteFlag\": 0\r\n      }\r\n    ],\r\n    \"total\": 2,\r\n    \"size\": 10,\r\n    \"current\": 1,\r\n    \"orders\": [],\r\n    \"pages\": 1\r\n  }\r\n}", "responseId": 662164005, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "description": "分页查询文件类型列表，支持按类型名称和编号模糊查询", "tags": [], "status": "developing", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 50, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.662164005"], "visibility": "INHERITED", "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "查询所有文件类型", "api": {"id": "288109406", "method": "get", "path": "/fileTypeInfo/listAll", "parameters": {}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "662164006", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回码，0表示成功"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "文件类型ID"}, "code": {"type": "string", "description": "类型编号"}, "typeName": {"type": "string", "description": "类型名称"}, "relatedFileCount": {"type": "integer", "description": "关联文件数量"}, "createBy": {"type": "string", "description": "创建人"}, "createDate": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateBy": {"type": "string", "description": "更新人"}, "updateDate": {"type": "string", "format": "date-time", "description": "更新时间"}, "deleteFlag": {"type": "integer", "description": "删除标志，0表示正常，-1表示已删除"}}}, "description": "文件类型列表"}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\r\n  \"code\": 0,\r\n  \"message\": \"成功\",\r\n  \"data\": [\r\n    {\r\n      \"id\": 1,\r\n      \"code\": \"DOC_001\",\r\n      \"typeName\": \"文档类型\",\r\n      \"relatedFileCount\": 5,\r\n      \"createBy\": \"admin\",\r\n      \"createDate\": \"2025-07-24T10:00:00\",\r\n      \"updateBy\": \"admin\",\r\n      \"updateDate\": \"2025-07-24T10:00:00\",\r\n      \"deleteFlag\": 0\r\n    },\r\n    {\r\n      \"id\": 2,\r\n      \"code\": \"IMG_001\",\r\n      \"typeName\": \"图片类型\",\r\n      \"relatedFileCount\": 3,\r\n      \"createBy\": \"admin\",\r\n      \"createDate\": \"2025-07-24T10:00:00\",\r\n      \"updateBy\": \"admin\",\r\n      \"updateDate\": \"2025-07-24T10:00:00\",\r\n      \"deleteFlag\": 0\r\n    },\r\n    {\r\n      \"id\": 3,\r\n      \"code\": \"VID_001\",\r\n      \"typeName\": \"视频类型\",\r\n      \"relatedFileCount\": 2,\r\n      \"createBy\": \"admin\",\r\n      \"createDate\": \"2025-07-24T10:00:00\",\r\n      \"updateBy\": \"admin\",\r\n      \"updateDate\": \"2025-07-24T10:00:00\",\r\n      \"deleteFlag\": 0\r\n    }\r\n  ]\r\n}", "responseId": 662164006, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "description": "查询所有文件类型列表，按类型编号升序排列", "tags": [], "status": "developing", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 60, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.662164006"], "visibility": "INHERITED", "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}]}], "socketCollection": [], "docCollection": [], "webSocketCollection": [], "socketIOCollection": [], "responseCollection": [{"_databaseId": 5782737, "updatedAt": "2024-11-29T07:23:59.000Z", "name": "根目录", "type": "root", "children": [], "parentId": 0, "id": 5782737, "items": [{"_databaseId": 340896088, "name": "新建成功", "code": 201, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": false, "folderId": 0, "id": 340896088, "databaseResponseExamples": [], "responseExamples": []}, {"_databaseId": 340896089, "name": "记录不存在", "code": 404, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string", "mock": {"mock": "Not found"}}}, "required": ["code", "message"], "x-apifox-orders": ["code", "message"]}, "defaultEnable": false, "folderId": 0, "id": 340896089, "databaseResponseExamples": [], "responseExamples": []}, {"_databaseId": 340896090, "name": "参数不正确", "code": 401, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "mock": {"mock": "401"}}, "message": {"type": "string", "mock": {"mock": "Invalid Param"}}}, "required": ["code", "message"], "x-apifox-orders": ["code", "message"]}, "defaultEnable": false, "folderId": 0, "id": 340896090, "databaseResponseExamples": [], "responseExamples": []}, {"_databaseId": 340896091, "name": "修改成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": false, "folderId": 0, "id": 340896091, "databaseResponseExamples": [], "responseExamples": []}, {"_databaseId": 340896092, "name": "json成功", "code": 200, "contentType": "json", "jsonSchema": {"title": "", "type": "object", "properties": {"code": {"mock": {"mock": "0"}, "type": "integer"}, "message": {"mock": {"mock": "成功"}, "type": "string"}}, "x-apifox-orders": ["code", "message"], "required": ["code", "message"], "x-apifox-refs": {}}, "defaultEnable": false, "folderId": 0, "id": 340896092, "databaseResponseExamples": [], "responseExamples": []}]}], "schemaCollection": [], "securitySchemeCollection": [], "requestCollection": [], "environments": [], "commonScripts": [], "globalVariables": [], "commonParameters": {}, "projectSetting": {"auth": {}, "securityScheme": {}, "servers": [{"id": "default", "name": "默认服务"}], "gateway": [], "language": "zh-CN", "apiStatuses": ["developing", "testing", "released", "deprecated"], "mockSettings": {}, "advancedSettings": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "isDefaultUrlEncoding": 2, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "LEGACY", "enableResourceKeyStandard": true}}}, "customFunctions": [], "projectAssociations": []}